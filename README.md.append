
## Testing Documentation

This project includes comprehensive testing setup for unit, integration, and end-to-end testing:

### Test Frameworks
- **Jest**: For unit and integration tests
- **React Testing Library**: For testing React components
- **MSW**: For mocking API requests
- **Cypress**: For end-to-end testing

### Running Tests

```bash
# Run unit and integration tests
npm run test

# Run tests in watch mode
npm run test:watch

# Generate test coverage report
npm run test:coverage

# Open Cypress for E2E testing
npm run test:e2e

# Run Cypress tests headless
npm run test:e2e:headless

# Generate a test file from template
npm run test:generate component path/to/Component
# or
npm run test:generate hook path/to/useHook
# or
npm run test:generate integration path/to/Component
```

### Test File Organization

- `src/tests/utils/`: Test utility functions and custom render methods
- `src/tests/mocks/`: Mock service worker handlers and server setup
- `src/tests/data/`: Test data fixtures
- `src/tests/templates/`: Templates for generating new test files
- `cypress/e2e/`: Cypress end-to-end tests
- `cypress/fixtures/`: Test data for Cypress tests
- `cypress/support/`: Cypress support files and custom commands

### Writing Tests

All components and hooks should have corresponding test files. Test files should be co-located with the files they test, using the `.test.tsx` suffix.

### Test Templates

Use the test generation script to create new test files based on templates:

```bash
npm run test:generate component src/components/ui/Button
```

### Testing Best Practices

1. **Test behavior, not implementation**: Focus on what the component does, not how it does it.
2. **Use realistic test data**: Use test data that mimics real-world scenarios.
3. **Mock external dependencies**: Use MSW to mock API calls and other external dependencies.
4. **Test edge cases**: Ensure your tests cover error states, loading states, and edge cases.
5. **Keep tests fast**: Avoid unnecessary setup and teardown.

### Debugging Tests

- Use `screen.debug()` to print the current DOM state
- Use `console.log()` in test files to debug variables
- Run tests in watch mode (`npm run test:watch`) for faster feedback
