// Test script for AI command processor
const userId = 'your-user-id'; // Replace with your actual user ID

// Test commands
const testCommands = [
  "We need more towels at Beach House",
  "Add 3 more wine glasses",
  "We're down to only 2 toilet paper rolls",
  "Increase the minimum stock of paper towels to 10",
  "We're running low on dish soap"
];

// Function to test a command
async function testCommand(command) {
  console.log(`\n\nTesting command: "${command}"`);
  
  try {
    const response = await fetch('https://pwaeknalhosfwuxkpaet.supabase.co/functions/v1/ai-command-processor', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + localStorage.getItem('supabase.auth.token')
      },
      body: JSON.stringify({
        command,
        userId
      })
    });
    
    const result = await response.json();
    console.log('Response:', result);
    return result;
  } catch (error) {
    console.error('Error testing command:', error);
    return { success: false, message: error.message };
  }
}

// Run all tests
async function runTests() {
  for (const command of testCommands) {
    await testCommand(command);
  }
}

// You can run this in your browser console on the StayFuse app
// runTests();
