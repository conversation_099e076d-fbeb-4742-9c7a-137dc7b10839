<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>StayFu Invitation Test</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1, h2 {
      color: #333;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .button {
      background-color: #4CAF50;
      border: none;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 4px 2px;
      cursor: pointer;
      border-radius: 5px;
    }
    .button.secondary {
      background-color: #f1f1f1;
      color: #333;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      overflow: auto;
    }
    .debug {
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 5px;
      padding: 10px;
      margin-top: 20px;
      font-size: 14px;
    }
    .info {
      background-color: #e3f2fd;
      border: 1px solid #bbdefb;
      border-radius: 5px;
      padding: 10px;
      margin-top: 20px;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <h1>StayFu Invitation Test</h1>
  
  <div class="info">
    <h3>Important Information</h3>
    <p>The application is using HashRouter, so the correct URL format for the invitation page is:</p>
    <pre>http://localhost:8081/#/invite?token=8b8e82d9-cb6e-403c-b609-17dea7c1df34</pre>
    <p>If you're seeing the login page instead of the invitation page, it's because you're using the wrong URL format.</p>
  </div>
  
  <div class="card">
    <h2>Invitation Details</h2>
    <p><strong>Team:</strong> All Properties</p>
    <p><strong>Role:</strong> Service Provider</p>
    <p><strong>Email:</strong> <EMAIL></p>
    
    <div class="debug">
      <h3>Debug Information</h3>
      <p><strong>Token:</strong> 8b8e82d9-cb6e-403c-b609-17dea7c1df34</p>
      <p><strong>Team ID:</strong> 3b9e7651-68c3-432f-9a28-7440139250f3</p>
      <p><strong>Status:</strong> pending</p>
    </div>
    
    <div style="margin-top: 20px;">
      <a href="http://localhost:8081/#/invite?token=8b8e82d9-cb6e-403c-b609-17dea7c1df34" class="button">Open Invitation Page (Correct URL)</a>
    </div>
  </div>
  
  <h2>Troubleshooting Steps</h2>
  <ol>
    <li>Make sure the development server is running on port 8081</li>
    <li>Try accessing the home page first: <a href="http://localhost:8081">http://localhost:8081</a></li>
    <li>Check the browser console for error messages</li>
    <li>If you're still seeing the login page, try clearing your browser cache and cookies</li>
    <li>If all else fails, try using a different browser</li>
  </ol>
  
  <h2>Manual URL Construction</h2>
  <p>If the button above doesn't work, you can manually construct the URL:</p>
  <ol>
    <li>Start with the base URL: <code>http://localhost:8081</code></li>
    <li>Add the hash symbol: <code>#</code></li>
    <li>Add the route: <code>/invite</code></li>
    <li>Add the query parameter: <code>?token=8b8e82d9-cb6e-403c-b609-17dea7c1df34</code></li>
    <li>The final URL should be: <code>http://localhost:8081/#/invite?token=8b8e82d9-cb6e-403c-b609-17dea7c1df34</code></li>
  </ol>
</body>
</html>
