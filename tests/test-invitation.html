<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Invitation Flow Test</title>
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #333;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      overflow: auto;
    }
    .success {
      color: green;
      font-weight: bold;
    }
    .error {
      color: red;
      font-weight: bold;
    }
    button {
      background-color: #4CAF50;
      border: none;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 4px 2px;
      cursor: pointer;
      border-radius: 5px;
    }
  </style>
</head>
<body>
  <h1>Invitation Flow Test</h1>
  <p>This page tests the invitation flow to ensure it works correctly.</p>
  
  <button id="runTests">Run Tests</button>
  
  <h2>Test Results:</h2>
  <pre id="results">Click "Run Tests" to start testing...</pre>
  
  <script>
    // NOTE: This is a test file with hardcoded credentials for development testing only
    // For production use, these should be retrieved from environment variables
    
    // Initialize Supabase client
    const supabaseUrl = 'https://pwaeknalhosfwuxkpaet.supabase.co';
    const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTM5MjA5NzYsImV4cCI6MjAyOTQ5Njk3Nn0.Nh0Qs9jXFXYwYpl-lBBKUKR4LCxQS_5RYGIk9sNfGEY';
    const supabase = supabase.createClient(supabaseUrl, supabaseKey);
    
    // Test variables
    const token = '8b8e82d9-cb6e-403c-b609-17dea7c1df34';
    const teamId = '3b9e7651-68c3-432f-9a28-7440139250f3';
    
    // Log function that updates the results element
    function log(message) {
      const resultsElement = document.getElementById('results');
      resultsElement.textContent += message + '\n';
    }
    
    // Step 1: Test direct database query for team name
    async function testTeamNameQuery() {
      try {
        const { data, error } = await supabase
          .from('teams')
          .select('name')
          .eq('id', teamId)
          .single();
          
        log('Team name query result: ' + JSON.stringify(data) + (error ? ', Error: ' + JSON.stringify(error) : ''));
        return data?.name;
      } catch (err) {
        log('Error in team name query: ' + err.message);
        return null;
      }
    }
    
    // Step 2: Test RPC function for team name
    async function testRpcFunction() {
      try {
        const { data, error } = await supabase.rpc(
          'get_team_name_by_id',
          { team_id: teamId }
        );
        
        log('RPC function result: ' + JSON.stringify(data) + (error ? ', Error: ' + JSON.stringify(error) : ''));
        return data;
      } catch (err) {
        log('Error in RPC function: ' + err.message);
        return null;
      }
    }
    
    // Step 3: Test invitation query
    async function testInvitationQuery() {
      try {
        const { data, error } = await supabase
          .from('team_invitations')
          .select('*')
          .eq('token', token)
          .maybeSingle();
          
        log('Invitation query result: ' + JSON.stringify(data) + (error ? ', Error: ' + JSON.stringify(error) : ''));
        return data;
      } catch (err) {
        log('Error in invitation query: ' + err.message);
        return null;
      }
    }
    
    // Run all tests
    async function runTests() {
      const resultsElement = document.getElementById('results');
      resultsElement.textContent = '=== INVITATION FLOW TEST ===\n\n';
      
      log('1. Testing team name query:');
      const teamName = await testTeamNameQuery();
      
      log('\n2. Testing RPC function:');
      const rpcResult = await testRpcFunction();
      
      log('\n3. Testing invitation query:');
      const invitation = await testInvitationQuery();
      
      log('\n=== TEST SUMMARY ===');
      log('Team name from direct query: ' + teamName);
      log('Team name from RPC function: ' + rpcResult);
      log('Invitation details: ' + JSON.stringify(invitation));
      
      if (teamName === 'All Properties' && rpcResult === 'All Properties' && invitation) {
        log('\n✅ All tests passed! The invitation flow should work correctly.');
      } else {
        log('\n❌ Some tests failed. Check the logs for details.');
      }
    }
    
    // Add event listener to the button
    document.getElementById('runTests').addEventListener('click', runTests);
  </script>
</body>
</html>
