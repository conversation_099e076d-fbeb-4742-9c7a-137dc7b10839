/**
 * Team Invitation Flow Test Script
 * 
 * This script tests the complete team invitation flow for both existing and new users,
 * focusing on staff and service provider roles.
 * 
 * To run this test:
 * 1. Start the development server
 * 2. Open the browser console
 * 3. Copy and paste this script into the console
 * 4. The test will run automatically and log results
 */

(async function runTeamInvitationTest() {
  console.log('=== TEAM INVITATION FLOW TEST ===');
  
  // Helper function to generate a random email
  function generateRandomEmail(prefix = 'test') {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `${prefix}.${timestamp}.${random}@example.com`;
  }
  
  // Helper function to log test steps
  function logStep(step, message) {
    console.log(`\n[STEP ${step}] ${message}`);
  }
  
  // Helper function to log test results
  function logResult(success, message) {
    if (success) {
      console.log(`✅ SUCCESS: ${message}`);
    } else {
      console.error(`❌ FAILED: ${message}`);
    }
  }
  
  // Get the Supabase client from the window object
  const supabase = window.supabase;
  if (!supabase) {
    logResult(false, 'Supabase client not found. Make sure you run this in the browser console while the app is running.');
    return;
  }
  
  // Test variables
  const testEmails = {
    newStaff: generateRandomEmail('newstaff'),
    newServiceProvider: generateRandomEmail('newprovider'),
    existingStaff: generateRandomEmail('existingstaff'),
    existingServiceProvider: generateRandomEmail('existingprovider')
  };
  
  const testPasswords = {
    newUser: 'Password123!',
    existingUser: 'Password123!'
  };
  
  const testTokens = {
    newStaff: null,
    newServiceProvider: null,
    existingStaff: null,
    existingServiceProvider: null
  };
  
  // Test data storage
  const testData = {
    teamId: null,
    teamName: 'Test Team ' + Date.now(),
    ownerUserId: null,
    existingStaffUserId: null,
    existingServiceProviderUserId: null
  };
  
  try {
    // Step 1: Check if we're logged in as a property manager or admin
    logStep(1, 'Checking current user authentication');
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      logResult(false, 'Failed to get session: ' + sessionError.message);
      return;
    }
    
    if (!session) {
      logResult(false, 'No active session. Please log in as a property manager or admin before running this test.');
      return;
    }
    
    // Get the current user's profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', session.user.id)
      .single();
    
    if (profileError) {
      logResult(false, 'Failed to get user profile: ' + profileError.message);
      return;
    }
    
    if (profile.role !== 'property_manager' && profile.role !== 'admin' && !profile.is_super_admin) {
      logResult(false, 'Current user is not a property manager or admin. Please log in with appropriate permissions.');
      return;
    }
    
    testData.ownerUserId = session.user.id;
    logResult(true, `Logged in as ${profile.role} (${profile.email})`);
    
    // Step 2: Create a test team
    logStep(2, 'Creating a test team');
    const { data: team, error: teamError } = await supabase
      .from('teams')
      .insert({
        name: testData.teamName,
        owner_id: testData.ownerUserId,
        description: 'Test team for invitation flow testing'
      })
      .select()
      .single();
    
    if (teamError) {
      logResult(false, 'Failed to create test team: ' + teamError.message);
      return;
    }
    
    testData.teamId = team.id;
    logResult(true, `Created test team: ${team.name} (${team.id})`);
    
    // Step 3: Create existing test users
    logStep(3, 'Creating existing test users');
    
    // Create existing staff user
    const { data: existingStaffUser, error: existingStaffError } = await supabase.auth.signUp({
      email: testEmails.existingStaff,
      password: testPasswords.existingUser,
      options: {
        data: {
          first_name: 'Existing',
          last_name: 'Staff',
          role: 'staff'
        }
      }
    });
    
    if (existingStaffError) {
      logResult(false, 'Failed to create existing staff user: ' + existingStaffError.message);
      return;
    }
    
    testData.existingStaffUserId = existingStaffUser.user.id;
    logResult(true, `Created existing staff user: ${testEmails.existingStaff}`);
    
    // Create existing service provider user
    const { data: existingServiceProviderUser, error: existingServiceProviderError } = await supabase.auth.signUp({
      email: testEmails.existingServiceProvider,
      password: testPasswords.existingUser,
      options: {
        data: {
          first_name: 'Existing',
          last_name: 'Provider',
          role: 'service_provider'
        }
      }
    });
    
    if (existingServiceProviderError) {
      logResult(false, 'Failed to create existing service provider user: ' + existingServiceProviderError.message);
      return;
    }
    
    testData.existingServiceProviderUserId = existingServiceProviderUser.user.id;
    logResult(true, `Created existing service provider user: ${testEmails.existingServiceProvider}`);
    
    // Step 4: Generate invitation tokens
    logStep(4, 'Generating invitation tokens');
    
    // Helper function to generate a token
    async function generateInvitationToken(email, role) {
      const token = crypto.randomUUID();
      
      const { data, error } = await supabase.functions.invoke('create-team-invitation', {
        body: {
          teamId: testData.teamId,
          email,
          role,
          invitedBy: testData.ownerUserId,
          token
        }
      });
      
      if (error) {
        throw new Error(`Failed to create invitation for ${email}: ${error.message}`);
      }
      
      return token;
    }
    
    // Generate tokens for all test users
    try {
      testTokens.newStaff = await generateInvitationToken(testEmails.newStaff, 'staff');
      testTokens.newServiceProvider = await generateInvitationToken(testEmails.newServiceProvider, 'service_provider');
      testTokens.existingStaff = await generateInvitationToken(testEmails.existingStaff, 'staff');
      testTokens.existingServiceProvider = await generateInvitationToken(testEmails.existingServiceProvider, 'service_provider');
      
      logResult(true, 'Generated all invitation tokens successfully');
    } catch (error) {
      logResult(false, error.message);
      return;
    }
    
    // Step 5: Verify invitation records in the database
    logStep(5, 'Verifying invitation records in the database');
    
    const { data: invitations, error: invitationsError } = await supabase
      .from('team_invitations')
      .select('*')
      .in('token', [
        testTokens.newStaff,
        testTokens.newServiceProvider,
        testTokens.existingStaff,
        testTokens.existingServiceProvider
      ]);
    
    if (invitationsError) {
      logResult(false, 'Failed to fetch invitations: ' + invitationsError.message);
      return;
    }
    
    if (invitations.length !== 4) {
      logResult(false, `Expected 4 invitations, but found ${invitations.length}`);
      return;
    }
    
    logResult(true, `Found all 4 invitations in the database`);
    
    // Step 6: Test accepting invitation for existing staff
    logStep(6, 'Testing invitation acceptance for existing staff');
    
    // First, sign in as the existing staff user
    const { error: signInError } = await supabase.auth.signInWithPassword({
      email: testEmails.existingStaff,
      password: testPasswords.existingUser
    });
    
    if (signInError) {
      logResult(false, 'Failed to sign in as existing staff: ' + signInError.message);
      return;
    }
    
    logResult(true, `Signed in as existing staff user: ${testEmails.existingStaff}`);
    
    // Accept the invitation
    const { data: acceptResult, error: acceptError } = await supabase.rpc(
      'accept_invitation_and_add_member',
      {
        p_token: testTokens.existingStaff,
        p_user_id: testData.existingStaffUserId
      }
    );
    
    if (acceptError) {
      logResult(false, 'Failed to accept invitation for existing staff: ' + acceptError.message);
      return;
    }
    
    logResult(true, `Existing staff user accepted invitation: ${JSON.stringify(acceptResult)}`);
    
    // Verify team membership
    const { data: teamMembership, error: teamMembershipError } = await supabase
      .from('team_members')
      .select('*')
      .eq('team_id', testData.teamId)
      .eq('user_id', testData.existingStaffUserId)
      .single();
    
    if (teamMembershipError) {
      logResult(false, 'Failed to verify team membership for existing staff: ' + teamMembershipError.message);
      return;
    }
    
    logResult(true, `Verified team membership for existing staff user`);
    
    // Step 7: Test accepting invitation for existing service provider
    logStep(7, 'Testing invitation acceptance for existing service provider');
    
    // Sign in as the existing service provider
    const { error: spSignInError } = await supabase.auth.signInWithPassword({
      email: testEmails.existingServiceProvider,
      password: testPasswords.existingUser
    });
    
    if (spSignInError) {
      logResult(false, 'Failed to sign in as existing service provider: ' + spSignInError.message);
      return;
    }
    
    logResult(true, `Signed in as existing service provider user: ${testEmails.existingServiceProvider}`);
    
    // Accept the invitation
    const { data: spAcceptResult, error: spAcceptError } = await supabase.rpc(
      'accept_invitation_and_add_member',
      {
        p_token: testTokens.existingServiceProvider,
        p_user_id: testData.existingServiceProviderUserId
      }
    );
    
    if (spAcceptError) {
      logResult(false, 'Failed to accept invitation for existing service provider: ' + spAcceptError.message);
      return;
    }
    
    logResult(true, `Existing service provider user accepted invitation: ${JSON.stringify(spAcceptResult)}`);
    
    // Verify team membership
    const { data: spTeamMembership, error: spTeamMembershipError } = await supabase
      .from('team_members')
      .select('*')
      .eq('team_id', testData.teamId)
      .eq('user_id', testData.existingServiceProviderUserId)
      .single();
    
    if (spTeamMembershipError) {
      logResult(false, 'Failed to verify team membership for existing service provider: ' + spTeamMembershipError.message);
      return;
    }
    
    logResult(true, `Verified team membership for existing service provider user`);
    
    // Verify service provider permissions
    const { data: spPermissions, error: spPermissionsError } = await supabase
      .from('user_permissions')
      .select('*')
      .eq('team_id', testData.teamId)
      .eq('user_id', testData.existingServiceProviderUserId);
    
    if (spPermissionsError) {
      logResult(false, 'Failed to verify permissions for existing service provider: ' + spPermissionsError.message);
      return;
    }
    
    if (spPermissions.length < 5) {
      logResult(false, `Expected at least 5 permissions for service provider, but found ${spPermissions.length}`);
      return;
    }
    
    logResult(true, `Verified permissions for existing service provider user: ${spPermissions.length} permissions found`);
    
    // Step 8: Generate invitation URLs for manual testing
    logStep(8, 'Generating invitation URLs for manual testing');
    
    const baseUrl = window.location.origin;
    const newStaffUrl = `${baseUrl}/invite?token=${testTokens.newStaff}`;
    const newServiceProviderUrl = `${baseUrl}/invite?token=${testTokens.newServiceProvider}`;
    
    console.log('\n=== MANUAL TESTING URLS ===');
    console.log(`New Staff Invitation URL: ${newStaffUrl}`);
    console.log(`New Service Provider Invitation URL: ${newServiceProviderUrl}`);
    console.log('\nTo complete the test:');
    console.log('1. Open each URL in a private/incognito browser window');
    console.log('2. Follow the registration process');
    console.log('3. Verify you are added to the team after registration');
    
    // Final results
    console.log('\n=== TEST SUMMARY ===');
    console.log('✅ Created test team:', testData.teamName);
    console.log('✅ Created existing test users');
    console.log('✅ Generated invitation tokens');
    console.log('✅ Verified invitation records');
    console.log('✅ Tested invitation acceptance for existing staff');
    console.log('✅ Tested invitation acceptance for existing service provider');
    console.log('⏳ Manual testing required for new user registration flow');
    
    console.log('\n=== TEST DATA ===');
    console.log('Team ID:', testData.teamId);
    console.log('Team Name:', testData.teamName);
    console.log('Test Emails:', testEmails);
    console.log('Test Tokens:', testTokens);
    
  } catch (error) {
    console.error('Unexpected error during test:', error);
  }
})();
