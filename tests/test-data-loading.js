// Simple test script to verify data loading fixes
// Run this in the browser console after logging <NAME_EMAIL>

// Function to log data loading events
function logDataLoading(component, data) {
  console.log(`[TEST] ${component} loaded ${data.length} items`);
  return data;
}

// Function to check if data is loaded
function checkDataLoaded() {
  console.log('[TEST] Checking if data is loaded...');
  
  // Check dashboard data
  const dashboardData = {
    properties: window.dashboardProperties || [],
    maintenanceTasks: window.dashboardMaintenanceTasks || [],
    damages: window.dashboardDamages || [],
    inventoryItems: window.dashboardInventoryItems || [],
    purchaseOrders: window.dashboardPurchaseOrders || []
  };
  
  console.log('[TEST] Dashboard data:', dashboardData);
  
  // Check if any data is loaded
  const hasData = Object.values(dashboardData).some(arr => arr.length > 0);
  console.log('[TEST] Has data:', hasData);
  
  return hasData;
}

// Function to test navigation and data loading
async function testNavigation() {
  console.log('[TEST] Starting navigation test...');
  
  // Store current location
  const startLocation = window.location.pathname;
  console.log(`[TEST] Starting at ${startLocation}`);
  
  // Navigate to different pages and check data loading
  const pages = [
    '/dashboard',
    '/properties',
    '/maintenance',
    '/inventory',
    '/damages',
    '/teams'
  ];
  
  for (const page of pages) {
    console.log(`[TEST] Navigating to ${page}...`);
    window.location.href = page;
    
    // Wait for navigation and data loading
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check if data is loaded
    console.log(`[TEST] Checking data on ${page}...`);
    const hasData = checkDataLoaded();
    console.log(`[TEST] ${page} has data: ${hasData}`);
  }
  
  // Return to starting page
  console.log(`[TEST] Returning to ${startLocation}...`);
  window.location.href = startLocation;
  
  console.log('[TEST] Navigation test completed');
}

// Function to test the refresh button
async function testRefreshButton() {
  console.log('[TEST] Testing refresh button...');
  
  // Find refresh buttons
  const refreshButtons = Array.from(document.querySelectorAll('button'))
    .filter(button => button.textContent.includes('Refresh'));
  
  console.log(`[TEST] Found ${refreshButtons.length} refresh buttons`);
  
  if (refreshButtons.length > 0) {
    console.log('[TEST] Clicking refresh button...');
    refreshButtons[0].click();
    
    // Wait for refresh to complete
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('[TEST] Refresh completed');
  }
}

// Function to monitor data loading events
function monitorDataLoading() {
  console.log('[TEST] Setting up data loading monitors...');
  
  // Store original console.log
  const originalLog = console.log;
  
  // Override console.log to capture data loading logs
  console.log = function(...args) {
    originalLog.apply(console, args);
    
    // Check for data loading logs
    const logStr = args.join(' ');
    if (logStr.includes('loaded') && logStr.includes('successfully')) {
      console.log('[TEST] Detected data loading:', logStr);
    }
  };
  
  // Monitor React Query cache updates
  if (window.__REACT_QUERY_DEVTOOLS__) {
    console.log('[TEST] React Query DevTools detected, monitoring cache updates');
  }
  
  console.log('[TEST] Data loading monitors set up');
}

// Main test function
async function runTests() {
  console.log('[TEST] Starting data loading tests...');
  
  // Set up monitoring
  monitorDataLoading();
  
  // Check initial data
  console.log('[TEST] Checking initial data...');
  const initialDataLoaded = checkDataLoaded();
  console.log(`[TEST] Initial data loaded: ${initialDataLoaded}`);
  
  // Test refresh button
  await testRefreshButton();
  
  // Check data after refresh
  console.log('[TEST] Checking data after refresh...');
  const refreshedDataLoaded = checkDataLoaded();
  console.log(`[TEST] Data after refresh loaded: ${refreshedDataLoaded}`);
  
  // Test navigation
  await testNavigation();
  
  console.log('[TEST] All tests completed');
}

// Expose test functions to window
window.testDataLoading = {
  runTests,
  checkDataLoaded,
  testRefreshButton,
  testNavigation,
  monitorDataLoading
};

console.log('[TEST] Test script loaded. Run window.testDataLoading.runTests() to start tests.');
