// Test script for invitation flow
const token = '8b8e82d9-cb6e-403c-b609-17dea7c1df34';
const teamId = '3b9e7651-68c3-432f-9a28-7440139250f3';

// Step 1: Test direct database query for team name
async function testTeamNameQuery() {
  try {
    const { data, error } = await supabase
      .from('teams')
      .select('name')
      .eq('id', teamId)
      .single();
      
    console.log('Team name query result:', data, error);
    return data?.name;
  } catch (err) {
    console.error('Error in team name query:', err);
    return null;
  }
}

// Step 2: Test RPC function for team name
async function testRpcFunction() {
  try {
    const { data, error } = await supabase.rpc(
      'get_team_name_by_id',
      { team_id: teamId }
    );
    
    console.log('RPC function result:', data, error);
    return data;
  } catch (err) {
    console.error('Error in RPC function:', err);
    return null;
  }
}

// Step 3: Test invitation query
async function testInvitationQuery() {
  try {
    const { data, error } = await supabase
      .from('team_invitations')
      .select('*')
      .eq('token', token)
      .maybeSingle();
      
    console.log('Invitation query result:', data, error);
    return data;
  } catch (err) {
    console.error('Error in invitation query:', err);
    return null;
  }
}

// Run all tests
async function runTests() {
  console.log('=== INVITATION FLOW TEST ===');
  
  console.log('\n1. Testing team name query:');
  const teamName = await testTeamNameQuery();
  
  console.log('\n2. Testing RPC function:');
  const rpcResult = await testRpcFunction();
  
  console.log('\n3. Testing invitation query:');
  const invitation = await testInvitationQuery();
  
  console.log('\n=== TEST SUMMARY ===');
  console.log('Team name from direct query:', teamName);
  console.log('Team name from RPC function:', rpcResult);
  console.log('Invitation details:', invitation);
  
  if (teamName === 'All Properties' && rpcResult === 'All Properties' && invitation) {
    console.log('\n✅ All tests passed! The invitation flow should work correctly.');
  } else {
    console.log('\n❌ Some tests failed. Check the logs for details.');
  }
}

// Execute the tests
runTests();
