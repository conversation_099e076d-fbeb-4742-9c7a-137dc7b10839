---
applyTo: '**'
---
We have full Api Access to Git and Supabase via cmd line API!

You can use the logins for poperty manager role: <EMAIL> Pass: Newsig1!!! and service provider role: <EMAIL> Pass: Newsig1!!!

IMPORTANT: ALWAYS check current file list and structure in the src folder, schema, db structure, rls policies, and supabase functions, and code, before creating a new solution 
(using find & grep) that may already be built but needs to just be fixed.

Don't stop working, or ask for feedback unless you absolutely need to. 
You are running on a test server and can output unlimited tokens