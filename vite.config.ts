import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import express from 'express'; // Import express
import extensionRoutes from './src/api/extensionRoutes'; // Import your API routes
import automationRoutes from './src/api/automationRoutes'; // Import automation routes

// Custom plugin to add Express middleware for API routes
const apiMiddlewarePlugin = () => ({
  name: 'api-middleware',
  configureServer(server: any) {
    // Create an Express app instance to handle API routes
    const apiApp = express();
    apiApp.use(express.json()); // Add body parsing middleware if needed by your routes

    // Mount your extension API routes under /api
    apiApp.use('/api', extensionRoutes);

    // Mount automation routes
    apiApp.use('/api/automation', automationRoutes);

    // Use the Express app as middleware in Vite's server
    server.middlewares.use(apiApp);
  }
});

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::", // Allows access from network (including extension)
    port: 8080,
    // Remove proxy if it exists, as we are using middleware directly
    hmr: {
      // Explicitly set the HMR protocol to ws
      protocol: 'ws',
      // Use the same host and port as the server
      host: 'localhost',
      port: 8080,
      // Add a timeout for WebSocket connections
      timeout: 5000,
      // Enable overlay for errors
      overlay: true,
    },
    // Add CORS headers to allow service worker to fetch resources
    cors: true,
  },
  plugins: [
    react(),
    // Add the API middleware plugin in all environments
    apiMiddlewarePlugin(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  test: {
    globals: true,
    setupFiles: ['./src/tests/setup.ts'],
  },
}));
