#!/usr/bin/env node

// Test script to verify environment configuration
console.log('🔧 StayFu Environment Configuration Test\n');

// Simulate Vite environment loading
const isDevelopment = process.env.NODE_ENV === 'development' || process.argv.includes('--dev');
const mode = isDevelopment ? 'development' : 'production';

console.log(`Environment Mode: ${mode}`);
console.log(`NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);

// Test environment-specific configuration
const config = {
  development: {
    VITE_SUPABASE_URL: 'http://127.0.0.1:54321',
    VITE_SUPABASE_PUBLISHABLE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN0YXlmdSIsInJvbGUiOiJhbm9uIiwiaWF0IjoxNjQ0NzY5MjAwLCJleHAiOjE5NjAxNDkyMDB9.qQNWn2VFBnKNw7rtLOeqldWddNfYQZgNqYvlYtHQrQM'
  },
  production: {
    VITE_SUPABASE_URL: 'https://pwaeknalhosfwuxkpaet.supabase.co',
    VITE_SUPABASE_PUBLISHABLE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'
  }
};

const currentConfig = config[mode];

console.log('\n📊 Current Configuration:');
console.log(`Supabase URL: ${currentConfig.VITE_SUPABASE_URL}`);
console.log(`Using Local Supabase: ${currentConfig.VITE_SUPABASE_URL.includes('127.0.0.1')}`);
console.log(`Anon Key: ${currentConfig.VITE_SUPABASE_PUBLISHABLE_KEY.substring(0, 50)}...`);

console.log('\n🎯 Environment Files:');
console.log('Development: .env.development (for npm run dev)');
console.log('Production: .env (for npm run build)');

console.log('\n📝 Available Scripts:');
console.log('npm run dev          - Development with local Supabase');
console.log('npm run dev:local    - Explicitly use local Supabase');
console.log('npm run dev:remote   - Development with remote Supabase');
console.log('npm run build        - Production build with remote Supabase');
console.log('npm run supabase:start - Start local Supabase');
console.log('npm run supabase:status - Check local Supabase status');

console.log('\n✅ Environment configuration test completed!');
