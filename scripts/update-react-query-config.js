#!/usr/bin/env node
/**
 * This script updates React Query configuration settings in all data loading hooks
 * to ensure consistent behavior across the application, especially for tab focus
 * changes.
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Configuration to replace
const oldConfig = `    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    enabled: !!userId`;

const newConfig = `    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false, // Changed to false to prevent data disappearing
    keepPreviousData: true, // Added to preserve data during refetching
    refetchOnMount: true,
    enabled: !!userId`;

// Find all query hook files
const hookFiles = glob.sync(path.join(__dirname, '..', 'src', 'hooks', 'use*.ts'));

// Process each file
for (const file of hookFiles) {
  let content = fs.readFileSync(file, 'utf8');
  // Look for refetchOnWindowFocus: true
  if (content.includes('refetchOnWindowFocus: true')) {
    console.log(`Processing ${path.basename(file)}...`);
    
    // Replace the configuration
    const updatedContent = content.replace(/refetchOnWindowFocus: true/g, 'refetchOnWindowFocus: false // Changed to false to prevent data disappearing');
    
    // Add keepPreviousData if not present
    if (!content.includes('keepPreviousData:')) {
      updatedContent = updatedContent.replace(/refetchOnMount: true/g, 'keepPreviousData: true, // Added to preserve data during refetching\n    refetchOnMount: true');
    }
    
    // Write the file back
    fs.writeFileSync(file, updatedContent);
    console.log(`Updated ${path.basename(file)}`);
  }
}

console.log('Done updating React Query configurations');
