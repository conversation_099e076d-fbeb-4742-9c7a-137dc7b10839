#!/usr/bin/env node
/**
 * CRITICAL FIX: Remove retryCount from all queryKey arrays
 * This is causing infinite query creation instead of proper retries
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Find all hook files
const hookFiles = glob.sync(path.join(__dirname, '..', 'src', 'hooks', 'use*.ts'));

console.log('🔧 CRITICAL FIX: Removing retryCount from queryKey arrays...\n');

let totalFixed = 0;

for (const file of hookFiles) {
  const content = fs.readFileSync(file, 'utf8');
  const fileName = path.basename(file);
  
  // Skip if no retryCount in queryKey
  if (!content.includes('retryCount') || !content.includes('queryKey')) {
    continue;
  }

  console.log(`📁 Fixing ${fileName}...`);
  
  let updatedContent = content;
  let fileFixed = false;

  // Pattern 1: queryKey: ['something', retryCount]
  const pattern1 = /queryKey:\s*\[\s*'([^']+)',\s*retryCount\s*\]/g;
  if (pattern1.test(content)) {
    updatedContent = updatedContent.replace(pattern1, "queryKey: ['$1']");
    fileFixed = true;
    console.log(`  ✅ Fixed pattern: queryKey: ['name', retryCount]`);
  }

  // Pattern 2: queryKey: ['something', param, retryCount]
  const pattern2 = /queryKey:\s*\[\s*'([^']+)',\s*([^,]+),\s*retryCount\s*\]/g;
  if (pattern2.test(content)) {
    updatedContent = updatedContent.replace(pattern2, "queryKey: ['$1', $2]");
    fileFixed = true;
    console.log(`  ✅ Fixed pattern: queryKey: ['name', param, retryCount]`);
  }

  // Pattern 3: queryKey: ['something', param1, param2, retryCount]
  const pattern3 = /queryKey:\s*\[\s*'([^']+)',\s*([^,]+),\s*([^,]+),\s*retryCount\s*\]/g;
  if (pattern3.test(content)) {
    updatedContent = updatedContent.replace(pattern3, "queryKey: ['$1', $2, $3]");
    fileFixed = true;
    console.log(`  ✅ Fixed pattern: queryKey: ['name', param1, param2, retryCount]`);
  }

  // Pattern 4: More complex patterns with multiple parameters
  const pattern4 = /queryKey:\s*\[\s*'([^']+)',\s*([^,]+),\s*([^,]+),\s*([^,]+),\s*retryCount\s*\]/g;
  if (pattern4.test(content)) {
    updatedContent = updatedContent.replace(pattern4, "queryKey: ['$1', $2, $3, $4]");
    fileFixed = true;
    console.log(`  ✅ Fixed pattern: queryKey: ['name', param1, param2, param3, retryCount]`);
  }

  // Pattern 5: Handle cases where retryCount is in the middle
  const pattern5 = /queryKey:\s*\[\s*([^,]+),\s*retryCount,\s*([^\]]+)\]/g;
  if (pattern5.test(content)) {
    updatedContent = updatedContent.replace(pattern5, "queryKey: [$1, $2]");
    fileFixed = true;
    console.log(`  ✅ Fixed pattern: queryKey: [param, retryCount, param]`);
  }

  if (fileFixed) {
    fs.writeFileSync(file, updatedContent);
    totalFixed++;
    console.log(`  💾 Saved ${fileName}\n`);
  } else {
    console.log(`  ⚠️  No retryCount patterns found in queryKey\n`);
  }
}

console.log('📊 SUMMARY');
console.log('==========');
console.log(`Files processed: ${hookFiles.length}`);
console.log(`Files fixed: ${totalFixed}`);

if (totalFixed > 0) {
  console.log('\n🎉 CRITICAL FIX APPLIED!');
  console.log('✅ Removed retryCount from all queryKey arrays');
  console.log('✅ This should resolve the stuck query issues');
  console.log('✅ React Query will now properly retry existing queries');
} else {
  console.log('\n✅ No files needed fixing');
}
