import requests
import base64
import json
import os
import re
import yaml
import subprocess
import shutil
import datetime # Moved import to top
from getpass import getpass
from nacl import encoding, public
from dotenv import load_dotenv, set_key, find_dotenv # Added dotenv imports

# --- Configuration ---
DEFAULT_BACKUP_DIR = "supabase/backups" # For GitHub Action
DEFAULT_LOCAL_BACKUP_DIR = "./local_supabase_backups" # For local manual backups
DEFAULT_CRON_SCHEDULE = "0 0 * * *" # Daily at midnight UTC
WORKFLOW_FILE_PATH = ".github/workflows/supabase_backup.yml"
URL_SECRET_NAME = "SUPABASE_DB_URL"
PW_SECRET_NAME = "SUPABASE_DB_PASSWORD"
PREVIEW_BRANCH_NAME = "preview"
ENV_FILE = find_dotenv(usecwd=True) # Find/create .env in current working directory

# Schemas to exclude during dump
EXCLUDE_SCHEMAS_LIST = [
    "auth",
    "extensions",
    "pg*", # Exclude pg_catalog, pg_toast, etc.
    "storage",
    "supabase_functions",
    "_realtime",
    "graphql", # Often present
    "graphql_public", # Often present
    "information_schema" # Standard PG internal
]

# --- Helper Functions ---

def load_env_vars():
    """Loads environment variables from .env file."""
    # Create .env if it doesn't exist, otherwise load_dotenv might fail if it's completely empty
    if not os.path.exists(ENV_FILE):
        try:
            with open(ENV_FILE, 'w') as f:
                f.write("# Supabase Backup Tool Configuration\n") # Add a header
            print(f"Created empty .env file at: {ENV_FILE}")
        except Exception as e:
            print(f"Warning: Could not create .env file: {e}")
            # Proceed without loading, prompts will occur naturally
            return

    loaded = load_dotenv(dotenv_path=ENV_FILE, override=False) # override=False won't overwrite existing OS env vars
    # print(f"Loaded environment variables from: {ENV_FILE} (Loaded: {loaded})") # Optional debug


def save_env_vars(config_to_save):
    """Saves the provided configuration dictionary to the .env file."""
    print("\n--- Save Configuration ---")
    save_choice = input("Save the current configuration to .env file? (y/N): ").strip().lower()
    if save_choice == 'y':
        sensitive_keys = ["GITHUB_PAT", "SUPABASE_DB_PASSWORD", "TARGET_DB_PASSWORD"]
        sensitive_save_confirmed = False

        # Ask separately for sensitive info
        if any(k in config_to_save and config_to_save[k] for k in sensitive_keys):
             sensitive_confirm = input("Save sensitive information (PAT/Passwords)? This is less secure. (y/N): ").strip().lower()
             if sensitive_confirm == 'y':
                 sensitive_save_confirmed = True

        try:
            # Ensure .env file exists before trying to set keys
            if not os.path.exists(ENV_FILE):
                 with open(ENV_FILE, 'w') as f:
                     f.write("# Supabase Backup Tool Configuration\n")

            for key, value in config_to_save.items():
                if value is not None: # Only save if value is not None
                    if key in sensitive_keys and not sensitive_save_confirmed:
                        print(f"Skipping sensitive key: {key}")
                        continue
                    # Convert boolean to string for .env
                    if isinstance(value, bool):
                        value = str(value)
                    set_key(ENV_FILE, key, value, quote_mode="always") # Use quote_mode for robustness
                    # print(f"Saved {key} to {ENV_FILE}") # Optional debug
            print(f"Configuration saved to {ENV_FILE}")
            print("IMPORTANT: Ensure '.env' is listed in your .gitignore file!")
        except Exception as e:
            print(f"Error saving to .env file: {e}")
    else:
        print("Configuration not saved.")


def get_config_value(env_key, prompt_message, is_password=False, current_config=None, default_value=None):
    """Gets a configuration value, checking .env first, then prompting."""
    # 1. Check if already set in this run's config
    if current_config is not None and env_key in current_config and current_config[env_key] is not None:
         return current_config[env_key]

    # 2. Check environment variables (loaded from .env by load_dotenv)
    value = os.getenv(env_key)
    prompt = f"{prompt_message}"
    use_existing = False

    if value:
        display_value = "********" if is_password else value
        if env_key == "GITHUB_PAT" and len(value) > 8:
             display_value = f"********{value[-4:]}" # Show last 4 chars of PAT

        confirm = input(f"{prompt} (found in .env: '{display_value}')\nUse this value? [Y/n]: ").strip().lower()
        if confirm == '' or confirm == 'y':
            use_existing = True
        else:
            value = None # Force re-entry

    # 3. Prompt if not using existing or not found
    if not use_existing:
        if default_value is not None:
            prompt += f" (default: {default_value})"

        if is_password:
            value = getpass(f"{prompt}: ")
        else:
            value = input(f"{prompt}: ").strip()

        # Use default if input is empty and default exists
        if not value and default_value is not None:
            value = default_value
            print(f"Using default value: {value}")

    # 4. Validate non-empty (unless it's explicitly allowed, e.g., default was empty)
    if not value and default_value is None: # Only exit if no value AND no default was possible
        print(f"{env_key} cannot be empty.")
        # Allow potential override if needed, but generally required fields shouldn't be empty
        # Consider adding specific checks for required fields if needed
        # exit(1)
        pass # Allow empty for now, let subsequent functions handle validation if needed

    # 5. Store value in current run's config
    if current_config is not None:
        current_config[env_key] = value
    return value


def check_psql_installed():
    """Checks if psql command is available in PATH."""
    if shutil.which("psql") is None:
        print("\n--- WARNING ---")
        print("'psql' command not found in your PATH.")
        print("Manual Backup and Restore options require PostgreSQL client tools.")
        print("Installation instructions:")
        print("  macOS (Homebrew): brew install postgresql")
        print("  Debian/Ubuntu:  sudo apt-get update && sudo apt-get install postgresql-client")
        print("  Windows: Download installer from https://www.postgresql.org/download/windows/")
        print("----------------\n")
        return False
    return True

def get_github_pat(config):
    """Prompts the user securely for their GitHub Personal Access Token, checking .env first."""
    print("\nRequired scopes: 'repo' (Full control of private repositories) and 'workflow' (Update GitHub Action workflows).")
    print("Generate one here: https://github.com/settings/tokens?type=beta")
    print("The token will not be displayed or stored unless saved to .env.")
    return get_config_value("GITHUB_PAT", "Enter GitHub PAT", is_password=True, current_config=config)


def get_repo_details(config):
    """Gets the GitHub repository owner and name, checking .env first."""
    repo_owner = get_config_value("GITHUB_REPO_OWNER", "Enter GitHub repository owner (e.g., 'mediafill')", current_config=config)
    repo_name = get_config_value("GITHUB_REPO_NAME", "Enter GitHub repository name (e.g., 'stayfuse')", current_config=config)
    print(f"Using Repo: {repo_owner}/{repo_name}")
    return repo_owner, repo_name


def get_supabase_db_details(config, action="backup"):
    """Gets the Supabase DB Pooler URL and Password, checking .env first."""
    target_desc = "target " if action == "restore" else ""
    url_env_key = f"TARGET_DB_URL" if action == "restore" else "SUPABASE_POOLER_URL"
    pw_env_key = f"TARGET_DB_PASSWORD" if action == "restore" else "SUPABASE_DB_PASSWORD"

    print(f"\nEnter the Supabase Database Connection Pooler URL for the {target_desc}database.")
    print("Find it in Supabase project: Settings > Database > Connection pooling > Connection string (use the one for IPv4).")
    print("Format: postgresql://postgres.<project_ref>:[PASSWORD]@[aws-region].pooler.supabase.com:5432/postgres")

    pooler_url = get_config_value(url_env_key, f"Supabase {target_desc.capitalize()}Pooler URL", current_config=config)

    if pooler_url and (".pooler.supabase.com" not in pooler_url or not pooler_url.startswith("postgresql://")):
        print("Warning: URL doesn't look like a standard Supabase Pooler connection string.")
    elif not pooler_url:
         print("Pooler URL cannot be empty.")
         exit(1) # URL is essential

    # Try extracting password from URL first
    password_from_url = None
    if pooler_url:
        password_match = re.search(r"://[^:]+:([^@]+)@", pooler_url)
        if password_match:
            password_from_url = password_match.group(1)
            print(f"Password detected in URL.")

    # Get password, checking env/prompting
    password = get_config_value(pw_env_key, f"Password for {target_desc}database", is_password=True, current_config=config)

    # Reconcile URL password vs entered/env password
    if password_from_url and password != password_from_url:
        print("\nWarning: Password entered/from .env does not match the password embedded in the URL.")
        pw_choice = input("Use password from (1) URL or (2) separate entry/.env? [1]: ").strip()
        if pw_choice == '2':
            print("Using separately entered/env password.")
            # Update URL in config to remove embedded password if saving later
            config[url_env_key] = re.sub(r"://[^:]+:([^@]+)@", r"://postgres:@", pooler_url)
        else:
            print("Using password from URL.")
            password = password_from_url
            config[pw_env_key] = password # Update config to match URL's password
    elif not password and password_from_url:
         # If no password was entered/in env, but found in URL, use the URL one
         print("Using password found in URL.")
         password = password_from_url
         config[pw_env_key] = password
    elif not password and not password_from_url:
         # If no password anywhere, prompt again (should have been caught by get_config_value, but as fallback)
         print("\nPassword not found in URL or .env.")
         password = getpass(f"Enter Password for {target_desc}database: ")
         if not password:
             print("Password cannot be empty.")
             exit(1)
         config[pw_env_key] = password

    return pooler_url, password


def get_backup_details(config):
    """Gets the desired backup directory and cron schedule for GitHub Action setup, checking .env first."""
    backup_dir = get_config_value("GITHUB_BACKUP_DIR", f"GitHub Action backup directory path", current_config=config, default_value=DEFAULT_BACKUP_DIR)
    cron_schedule = get_config_value("GITHUB_CRON_SCHEDULE", f"GitHub Action cron schedule", current_config=config, default_value=DEFAULT_CRON_SCHEDULE)
    if not (5 <= len(cron_schedule.split()) <= 6):
         print(f"Warning: '{cron_schedule}' doesn't look like a standard cron schedule.")
    preview_branch = get_config_value("GITHUB_PREVIEW_BRANCH", f"Preview branch name for push trigger", current_config=config, default_value=PREVIEW_BRANCH_NAME)

    # Ensure defaults are stored in config if they were used
    config["GITHUB_BACKUP_DIR"] = backup_dir
    config["GITHUB_CRON_SCHEDULE"] = cron_schedule
    config["GITHUB_PREVIEW_BRANCH"] = preview_branch

    return backup_dir, cron_schedule, preview_branch

# --- GitHub API Functions ---
# (get_repo_public_key, encrypt_secret, create_or_update_secret, get_file_sha, create_or_update_file remain unchanged)
def get_repo_public_key(owner, repo, headers):
    """Gets the repository's public key for encrypting secrets."""
    url = f"https://api.github.com/repos/{owner}/{repo}/actions/secrets/public-key"
    response = requests.get(url, headers=headers)
    response.raise_for_status() # Raise exception for bad status codes
    key_data = response.json()
    return key_data['key_id'], key_data['key']

def encrypt_secret(public_key_value, secret_value):
    """Encrypts a secret using the repository's public key."""
    public_key = public.PublicKey(public_key_value.encode("utf-8"), encoding.Base64Encoder())
    sealed_box = public.SealedBox(public_key)
    encrypted = sealed_box.encrypt(secret_value.encode("utf-8"))
    return base64.b64encode(encrypted).decode("utf-8")

def create_or_update_secret(owner, repo, secret_name, encrypted_value, key_id, headers):
    """Creates or updates a repository secret."""
    url = f"https://api.github.com/repos/{owner}/{repo}/actions/secrets/{secret_name}"
    payload = {
        'encrypted_value': encrypted_value,
        'key_id': key_id
    }
    response = requests.put(url, headers=headers, json=payload)
    response.raise_for_status()
    if response.status_code == 201:
        print(f"Successfully created secret '{secret_name}'.")
    elif response.status_code == 204:
        print(f"Successfully updated secret '{secret_name}'.")
    else:
        print(f"Unexpected status code {response.status_code} while setting secret.")

def get_file_sha(owner, repo, path, headers):
    """Gets the SHA of an existing file, returns None if not found."""
    # Ensure the path doesn't start with a slash for the API call
    api_path = path.lstrip('/')
    url = f"https://api.github.com/repos/{owner}/{repo}/contents/{api_path}"
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json()['sha']
    elif response.status_code == 404:
        return None # File not found
    else:
        response.raise_for_status() # Raise other errors

def create_or_update_file(owner, repo, path, content, headers, message="Create/update file via script"):
    """Creates or updates a file in the repository."""
    # Ensure the path doesn't start with a slash for the API call
    api_path = path.lstrip('/')
    url = f"https://api.github.com/repos/{owner}/{repo}/contents/{api_path}"
    encoded_content = base64.b64encode(content.encode('utf-8')).decode('utf-8')
    payload = {
        'message': message,
        'content': encoded_content,
    }

    # Check if file exists to get SHA for update
    sha = get_file_sha(owner, repo, api_path, headers)
    if sha:
        payload['sha'] = sha
        print(f"Updating existing file: {path}")
    else:
        print(f"Creating new file: {path}")

    response = requests.put(url, headers=headers, json=payload)
    response.raise_for_status()

    if response.status_code == 200:
        print(f"Successfully updated file '{path}'.")
    elif response.status_code == 201:
        print(f"Successfully created file '{path}'.")
    else:
        print(f"Unexpected status code {response.status_code} while setting file.")

# --- Workflow Generation ---
# (generate_workflow_yaml remains largely unchanged, just uses EXCLUDE_SCHEMAS_LIST)
def generate_workflow_yaml(backup_dir, cron_schedule, preview_branch):
    """Generates the YAML content for the GitHub Actions workflow using pg_dump with updated triggers."""
    # Dump script using standard pg_dump/pg_dumpall
    # Note: Ensure backup_dir is correctly substituted into the f-string below.
    dump_script = f"""#!/bin/bash
set -e # Exit immediately if a command exits with a non-zero status.

# Mask password for logging
# Use standard sed delimiter '#'
MASKED_DB_URL=$(echo "$SUPABASE_DB_URL" | sed 's#:[^@]*@#:********@#')
echo "Using DB URL (Pooler): $MASKED_DB_URL"

# Create the backup directory if it doesn't exist
mkdir -p "{backup_dir}"

# Define schemas to exclude (Supabase internal + standard PG internal)
# Use multiple --exclude-schema flags as pg_dump doesn't always handle comma-separated lists well
# Ensure correct bash array expansion syntax: "${{EXCLUDE_SCHEMAS[@]}}"
EXCLUDE_SCHEMAS=(
  {" ".join([f"--exclude-schema={s}" for s in EXCLUDE_SCHEMAS_LIST])}
)

# Dump global objects (roles) using pg_dumpall (Uses --database flag correctly)
echo "Dumping roles..."
pg_dumpall --roles-only --no-role-passwords --database="$SUPABASE_DB_URL" > "{backup_dir}/roles.sql"

# Dump schema (excluding internal ones) using pg_dump (DB URL is last argument)
echo "Dumping schema..."
pg_dump --schema-only --no-owner --no-privileges "${{EXCLUDE_SCHEMAS[@]}}" "$SUPABASE_DB_URL" > "{backup_dir}/schema.sql"

# Dump data (excluding internal ones) using pg_dump (DB URL is last argument)
echo "Dumping data..."
pg_dump --data-only --no-owner --no-privileges "${{EXCLUDE_SCHEMAS[@]}}" "$SUPABASE_DB_URL" > "{backup_dir}/data.sql"

echo "Dump complete."
"""

    workflow_dict = {
        'name': 'Supabase DB Backup',
        'on': {
            'workflow_dispatch': None, # Allow manual trigger
            'schedule': [{'cron': cron_schedule}],
            'push': { # Add push trigger
                'branches': [
                    'main',
                    preview_branch # Use variable for preview branch name
                ]
            }
        },
        'jobs': {
            'backup_database': {
                'runs-on': 'ubuntu-latest',
                'permissions': {
                    'contents': 'write' # Allow committing back to the repo
                },
                'steps': [
                    {
                        'name': 'Checkout Repository',
                        'uses': 'actions/checkout@v4'
                    },
                    {
                        'name': 'Install PostgreSQL Client (for pg_dump)',
                        'run': 'sudo apt-get update && sudo apt-get install -y postgresql-client'
                    },
                    {
                        'name': 'Perform Database Dump (using pg_dump)', # Updated step name
                        'run': dump_script, # Use the pg_dump script
                        'env': {
                            'SUPABASE_DB_URL': '${{ secrets.SUPABASE_DB_URL }}', # Pooler URL
                            'PGPASSWORD': '${{ secrets.SUPABASE_DB_PASSWORD }}' # Set PGPASSWORD for pg_dump/pg_dumpall
                            }
                    },
                    {
                        'name': 'Commit Backup Files',
                        'uses': 'stefanzweifel/git-auto-commit-action@v5',
                        'with': {
                            'commit_message': 'chore: Automated Supabase DB backup [skip ci]',
                            'file_pattern': f'{backup_dir}/*.sql',
                            'repository': '.',
                            'commit_options': '--no-verify',
                            'add_options': '--force',
                            'push_options': '--force',
                            'skip_dirty_check': 'true',
                            'skip_fetch': 'true'
                        }
                    }
                ]
            }
        }
    }
    # Use sort_keys=False to maintain order, default_flow_style=False for block style
    # Ensure proper formatting for the multi-line script within YAML
    return yaml.dump(workflow_dict, sort_keys=False, default_flow_style=False, width=1000)

# --- Local Operation Functions ---

def run_local_backup(config):
    """Performs a manual backup locally using pg_dump/pg_dumpall."""
    print("\n--- Manual Local Backup ---")
    if not check_psql_installed():
        return False # Indicate failure

    db_url, db_password = get_supabase_db_details(config, action="backup")
    local_backup_dir_base = get_config_value("LOCAL_BACKUP_DIR", f"Base directory for local backups", current_config=config, default_value=DEFAULT_LOCAL_BACKUP_DIR)

    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    local_backup_dir = f"{local_backup_dir_base}_{timestamp}"

    os.makedirs(local_backup_dir, exist_ok=True)
    print(f"Creating backup in: {local_backup_dir}")

    # Set PGPASSWORD environment variable for the subprocess
    env = os.environ.copy()
    env['PGPASSWORD'] = db_password

    exclude_args = [f"--exclude-schema={s}" for s in EXCLUDE_SCHEMAS_LIST]
    success = True

    try:
        # Dump roles
        print("Dumping roles...")
        roles_file = os.path.join(local_backup_dir, "roles.sql")
        cmd_roles = ["pg_dumpall", "--roles-only", "--no-role-passwords", f"--database={db_url}"]
        with open(roles_file, "w") as f_out:
            # Use shell=True on Windows if needed, though generally discouraged
            use_shell = os.name == 'nt'
            result_roles = subprocess.run(cmd_roles, capture_output=True, text=True, env=env, check=False, shell=use_shell) # check=False to handle errors manually
            if result_roles.returncode != 0:
                 print(f"Error during pg_dumpall (roles): RC={result_roles.returncode}")
                 print(result_roles.stderr)
                 success = False
            else:
                 f_out.write(result_roles.stdout)
                 if result_roles.stderr:
                     print("pg_dumpall stderr (roles):\n", result_roles.stderr)

        # Dump schema only if roles succeeded
        if success:
            print("Dumping schema...")
            schema_file = os.path.join(local_backup_dir, "schema.sql")
            cmd_schema = ["pg_dump", "--schema-only", "--no-owner", "--no-privileges"] + exclude_args + [db_url]
            with open(schema_file, "w") as f_out:
                result_schema = subprocess.run(cmd_schema, capture_output=True, text=True, env=env, check=False, shell=use_shell)
                if result_schema.returncode != 0:
                    print(f"Error during pg_dump (schema): RC={result_schema.returncode}")
                    print(result_schema.stderr)
                    success = False
                else:
                    f_out.write(result_schema.stdout)
                    if result_schema.stderr:
                        print("pg_dump stderr (schema):\n", result_schema.stderr)

        # Dump data only if schema succeeded
        if success:
            print("Dumping data...")
            data_file = os.path.join(local_backup_dir, "data.sql")
            cmd_data = ["pg_dump", "--data-only", "--no-owner", "--no-privileges"] + exclude_args + [db_url]
            with open(data_file, "w") as f_out:
                result_data = subprocess.run(cmd_data, capture_output=True, text=True, env=env, check=False, shell=use_shell)
                if result_data.returncode != 0:
                    print(f"Error during pg_dump (data): RC={result_data.returncode}")
                    print(result_data.stderr)
                    success = False
                else:
                    f_out.write(result_data.stdout)
                    if result_data.stderr:
                        print("pg_dump stderr (data):\n", result_data.stderr)

        if success:
            print(f"\nLocal backup complete in {local_backup_dir}")
        else:
            print(f"\nLocal backup failed. Check errors above. Files might be incomplete in {local_backup_dir}")

    except Exception as e:
        print(f"\nAn unexpected error occurred during local backup: {e}")
        success = False

    return success # Return status


def run_restore(config):
    """Restores a backup locally using psql."""
    print("\n--- Restore from Local Backup ---")
    print("!!! WARNING: THIS IS A DESTRUCTIVE OPERATION !!!")
    print("Ensure you are restoring to the correct target database.")
    print("It is STRONGLY recommended to test on a non-production project first.")

    if not check_psql_installed():
        return False # Indicate failure

    target_db_url, target_db_password = get_supabase_db_details(config, action="restore")
    backup_dir = get_config_value("LAST_RESTORE_SOURCE_DIR", "Enter path to local backup directory (containing roles.sql, etc.)", current_config=config)

    if not backup_dir:
         print("Backup directory path cannot be empty.")
         return False

    roles_file = os.path.join(backup_dir, "roles.sql")
    schema_file = os.path.join(backup_dir, "schema.sql")
    data_file = os.path.join(backup_dir, "data.sql")

    if not os.path.isdir(backup_dir) or not os.path.isfile(roles_file) or not os.path.isfile(schema_file) or not os.path.isfile(data_file):
        print(f"Error: Backup directory '{backup_dir}' is invalid or missing required .sql files.")
        return False

    print("\n--- Confirmation Required ---")
    masked_target_url = re.sub(r"://[^:]+:([^@]+)@", r"://postgres:********@", target_db_url) # Mask user and pw
    print(f"Target Database URL (masked): {masked_target_url}")
    print(f"Backup Source Directory:    {backup_dir}")
    print("-----------------------------")

    confirmation = input("Type 'RESTORE' to proceed, or anything else to abort: ")
    if confirmation != "RESTORE":
        print("Restore aborted.")
        return False

    # Set PGPASSWORD environment variable for the subprocess
    env = os.environ.copy()
    env['PGPASSWORD'] = target_db_password
    success = True
    use_shell = os.name == 'nt' # Use shell=True on Windows if needed

    try:
        print("\nStarting restore...")
        # Restore roles
        print("Restoring roles...")
        cmd_roles = ["psql", "--quiet", "--single-transaction", "--variable=ON_ERROR_STOP=1", f"--file={roles_file}", target_db_url]
        result_roles = subprocess.run(cmd_roles, capture_output=True, text=True, env=env, check=False, shell=use_shell)
        if result_roles.returncode != 0:
            print(f"Error during psql (roles): RC={result_roles.returncode}")
            print(result_roles.stderr)
            success = False
        elif result_roles.stderr:
            print("psql stderr (roles):\n", result_roles.stderr)

        # Restore schema only if roles succeeded
        if success:
            print("Restoring schema...")
            cmd_schema = ["psql", "--quiet", "--single-transaction", "--variable=ON_ERROR_STOP=1", f"--file={schema_file}", target_db_url]
            result_schema = subprocess.run(cmd_schema, capture_output=True, text=True, env=env, check=False, shell=use_shell)
            if result_schema.returncode != 0:
                print(f"Error during psql (schema): RC={result_schema.returncode}")
                print(result_schema.stderr)
                success = False
            elif result_schema.stderr:
                print("psql stderr (schema):\n", result_schema.stderr)

        # Restore data only if schema succeeded
        if success:
            print("Restoring data...")
            cmd_data = ["psql", "--quiet", "--single-transaction", "--variable=ON_ERROR_STOP=1", f"--file={data_file}", target_db_url]
            result_data = subprocess.run(cmd_data, capture_output=True, text=True, env=env, check=False, shell=use_shell)
            if result_data.returncode != 0:
                print(f"Error during psql (data): RC={result_data.returncode}")
                print(result_data.stderr)
                success = False
            elif result_data.stderr:
                print("psql stderr (data):\n", result_data.stderr)

        if success:
            print("\nRestore complete.")
            # Store successful restore source dir for next time
            config["LAST_RESTORE_SOURCE_DIR"] = backup_dir
        else:
            print("\nRestore failed. Check errors above.")


    except Exception as e:
        print(f"\nAn unexpected error occurred during restore: {e}")
        success = False

    return success


def push_github_action_setup(config):
    """Handles the setup/update of the GitHub Action workflow and secrets."""
    print("\n--- Push/Update GitHub Action Setup ---")
    github_pat = get_github_pat(config)
    owner, repo = get_repo_details(config)
    supabase_pooler_url, supabase_db_password = get_supabase_db_details(config, action="setup") # Get details for setup
    backup_dir, cron_schedule, preview_branch = get_backup_details(config)

    headers = {
        "Authorization": f"token {github_pat}",
        "Accept": "application/vnd.github.v3+json",
        "X-GitHub-Api-Version": "2022-11-28"
    }
    success = True

    try:
        # 1. Set up the Secrets (Pooler URL and Password)
        print("\nFetching repository public key...")
        key_id, public_key_value = get_repo_public_key(owner, repo, headers)

        print(f"Encrypting Supabase Pooler URL for secret '{URL_SECRET_NAME}'...")
        encrypted_pooler_url = encrypt_secret(public_key_value, supabase_pooler_url)
        print(f"Creating/updating secret '{URL_SECRET_NAME}'...")
        create_or_update_secret(owner, repo, URL_SECRET_NAME, encrypted_pooler_url, key_id, headers)

        print(f"Encrypting Supabase DB Password for secret '{PW_SECRET_NAME}'...")
        encrypted_db_password = encrypt_secret(public_key_value, supabase_db_password)
        print(f"Creating/updating secret '{PW_SECRET_NAME}'...")
        create_or_update_secret(owner, repo, PW_SECRET_NAME, encrypted_db_password, key_id, headers)

        # 2. Create/Update Workflow File
        print("\nGenerating workflow YAML (using pg_dump, updated triggers)...")
        workflow_content = generate_workflow_yaml(backup_dir, cron_schedule, preview_branch)

        print(f"Creating/updating workflow file '{WORKFLOW_FILE_PATH}'...")
        create_or_update_file(
            owner, repo, WORKFLOW_FILE_PATH, workflow_content, headers,
            message=f"ci: Update Supabase backup workflow triggers and use pg_dump"
        )

        print("\n--- GitHub Action Setup Complete! ---")
        print("\nNext Steps:")
        print(f"1. IMPORTANT: Verify secrets '{URL_SECRET_NAME}' and '{PW_SECRET_NAME}' exist in repo settings.")
        print("2. IMPORTANT: If you haven't already, add the GitHub backup directory to your project's .gitignore file:")
        print(f"   echo '{backup_dir}/' >> .gitignore")
        print("   Then commit and push the .gitignore change.")
        print(f"3. Go to your repository's 'Actions' tab on GitHub: https://github.com/{owner}/{repo}/actions")
        print("4. Find the 'Supabase DB Backup' workflow.")
        print("5. You should see the new commit updating the workflow.")
        print(f"6. The workflow will now run automatically on pushes to 'main' and '{preview_branch}', on schedule ('{cron_schedule}'), and manually.")

    except requests.exceptions.RequestException as e:
        print(f"\nAn API error occurred: {e}")
        if e.response is not None:
            try:
                print(f"Response body: {e.response.json()}")
            except json.JSONDecodeError:
                print(f"Response body: {e.response.text}")
        print("Please check your PAT permissions, repository name, and network connection.")
        success = False
    except Exception as e:
        print(f"\nAn unexpected error occurred during GitHub setup: {e}")
        success = False

    return success


# --- Main Execution ---
if __name__ == "__main__":
    load_env_vars() # Load .env at the start
    current_run_config = {} # Dictionary to hold config for this run

    print("--- Supabase Backup Tool ---")
    print("Select an option:")
    print("  1. Manual Local Backup (Default)")
    print("  2. Push/Update GitHub Action Setup")
    print("  3. Restore from Local Backup")

    choice = input("Enter choice (1-3): ").strip()
    action_successful = False

    try:
        if choice == '2':
            action_successful = push_github_action_setup(current_run_config)
        elif choice == '3':
            action_successful = run_restore(current_run_config)
        elif choice == '1' or choice == '': # Default to option 1
            action_successful = run_local_backup(current_run_config)
        else:
            print("Invalid choice.")

        # Ask to save config only if the action was potentially successful
        # and there's something in the config dictionary
        if action_successful and current_run_config:
             save_env_vars(current_run_config)

    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
    except Exception as e:
         print(f"\nAn unexpected error occurred in main execution: {e}")