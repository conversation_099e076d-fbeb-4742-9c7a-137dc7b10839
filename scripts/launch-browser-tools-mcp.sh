#!/bin/bash

# Set the path to the browser tools server executable
BROWSER_TOOLS_SERVER_PATH="$HOME/.npm-global/bin/browser-tools-server"

# Check if the executable exists
if [ ! -f "$BROWSER_TOOLS_SERVER_PATH" ]; then
    echo "Browser tools server executable not found at $BROWSER_TOOLS_SERVER_PATH"
    echo "Trying alternative path..."
    BROWSER_TOOLS_SERVER_PATH="$(which browser-tools-server)"
    
    if [ ! -f "$BROWSER_TOOLS_SERVER_PATH" ]; then
        echo "Browser tools server executable not found. Please install it with:"
        echo "npm install -g @agentdeskai/browser-tools-server"
        exit 1
    fi
fi

# Launch the browser tools server with the correct parameters
echo "Launching browser tools server from $BROWSER_TOOLS_SERVER_PATH"
"$BROWSER_TOOLS_SERVER_PATH" --port 3040 --user-data-dir="$HOME/.config/stayfu-browser-tools"
