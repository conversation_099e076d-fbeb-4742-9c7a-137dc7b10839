#!/usr/bin/env node
/**
 * Verification script to ensure ALL React Query configurations are consistent
 * This script checks every useQuery hook in the codebase for proper configuration
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Expected configuration
const EXPECTED_CONFIG = {
  retry: 3,
  retryDelay: 'attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000)',
  staleTime: '1000 * 60 * 5', // 5 minutes
  gcTime: '30 * 60 * 1000', // 30 minutes
  refetchOnMount: 'true',
  refetchOnReconnect: 'true',
  networkMode: "'always'",
  enabled: '!!userId'
};

// Problematic patterns to check for
const PROBLEMATIC_PATTERNS = [
  { pattern: /refetchOnWindowFocus:\s*true/, issue: 'refetchOnWindowFocus should not be explicitly set to true (use global config)' },
  { pattern: /retryDelay:.*10000/, issue: 'retryDelay should use 30000 max, not 10000' },
  { pattern: /cacheTime:/, issue: 'cacheTime is deprecated, should use gcTime' },
  { pattern: /refetchOnWindowFocus:\s*false/, issue: 'refetchOnWindowFocus should not be explicitly set to false (use global config)' }
];

// Files to check
const hookFiles = glob.sync(path.join(__dirname, '..', 'src', 'hooks', 'use*.ts'));
const utilFiles = glob.sync(path.join(__dirname, '..', 'src', 'utils', '*.ts'));
const providerFiles = glob.sync(path.join(__dirname, '..', 'src', 'providers', '*.tsx'));

const allFiles = [...hookFiles, ...utilFiles, ...providerFiles];

console.log('🔍 Verifying React Query configurations...\n');

let totalIssues = 0;
let filesWithIssues = 0;

for (const file of allFiles) {
  const content = fs.readFileSync(file, 'utf8');
  const fileName = path.basename(file);
  let fileIssues = [];

  // Skip files that don't contain useQuery
  if (!content.includes('useQuery') && !content.includes('QueryClient')) {
    continue;
  }

  console.log(`📁 Checking ${fileName}...`);

  // Check for problematic patterns
  for (const { pattern, issue } of PROBLEMATIC_PATTERNS) {
    const matches = content.match(pattern);
    if (matches) {
      fileIssues.push(`❌ ${issue}`);
      totalIssues++;
    }
  }

  // Check for missing networkMode in useQuery calls
  const useQueryMatches = content.match(/useQuery\s*\(\s*\{[\s\S]*?\}\s*\)/g);
  if (useQueryMatches) {
    for (const match of useQueryMatches) {
      if (!match.includes('networkMode')) {
        fileIssues.push(`⚠️  useQuery missing networkMode: 'always'`);
        totalIssues++;
      }
    }
  }

  // Check for inconsistent retry delays
  const retryDelayMatches = content.match(/retryDelay:.*?(\d+)/g);
  if (retryDelayMatches) {
    for (const match of retryDelayMatches) {
      if (match.includes('10000') && !match.includes('30000')) {
        fileIssues.push(`❌ Old retry delay found: ${match.trim()}`);
        totalIssues++;
      }
    }
  }

  // Check for old cacheTime usage
  if (content.includes('cacheTime:')) {
    fileIssues.push(`❌ Deprecated cacheTime found, should use gcTime`);
    totalIssues++;
  }

  // Report issues for this file
  if (fileIssues.length > 0) {
    filesWithIssues++;
    fileIssues.forEach(issue => console.log(`  ${issue}`));
    console.log('');
  } else {
    console.log(`  ✅ Configuration looks good\n`);
  }
}

// Check global configuration
console.log('🌐 Checking global configuration...');

const appProvidersPath = path.join(__dirname, '..', 'src', 'providers', 'AppProviders.tsx');
if (fs.existsSync(appProvidersPath)) {
  const appProvidersContent = fs.readFileSync(appProvidersPath, 'utf8');
  
  if (!appProvidersContent.includes("refetchOnWindowFocus: 'always'")) {
    console.log('❌ AppProviders.tsx should have refetchOnWindowFocus: "always"');
    totalIssues++;
  } else {
    console.log('✅ AppProviders.tsx has correct refetchOnWindowFocus setting');
  }

  if (!appProvidersContent.includes("refetchOnMount: 'always'")) {
    console.log('❌ AppProviders.tsx should have refetchOnMount: "always"');
    totalIssues++;
  } else {
    console.log('✅ AppProviders.tsx has correct refetchOnMount setting');
  }

  if (!appProvidersContent.includes("networkMode: 'always'")) {
    console.log('❌ AppProviders.tsx should have networkMode: "always"');
    totalIssues++;
  } else {
    console.log('✅ AppProviders.tsx has correct networkMode setting');
  }
}

// Summary
console.log('\n📊 VERIFICATION SUMMARY');
console.log('========================');
console.log(`Files checked: ${allFiles.length}`);
console.log(`Files with issues: ${filesWithIssues}`);
console.log(`Total issues found: ${totalIssues}`);

if (totalIssues === 0) {
  console.log('\n🎉 ALL CONFIGURATIONS ARE CORRECT!');
  console.log('✅ React Query configurations are standardized');
  console.log('✅ No problematic patterns found');
  console.log('✅ Global configuration is correct');
  process.exit(0);
} else {
  console.log('\n⚠️  ISSUES FOUND - CONFIGURATION NEEDS FIXING');
  console.log('❌ Some React Query configurations are inconsistent');
  console.log('❌ Please fix the issues listed above');
  process.exit(1);
}
