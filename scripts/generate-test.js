
#!/usr/bin/env node

/**
 * A utility script to generate test files based on templates
 * 
 * Usage: node scripts/generate-test.js component path/to/Component
 * or: node scripts/generate-test.js hook path/to/useHook
 * or: node scripts/generate-test.js integration path/to/Component
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Get command line arguments
const [,, testType, componentPath] = process.argv;

if (!testType || !componentPath) {
  console.error('Usage: node scripts/generate-test.js [component|hook|integration] path/to/Component');
  process.exit(1);
}

// Map test type to template
const templateMap = {
  component: 'src/tests/templates/componentTest.template.txt',
  hook: 'src/tests/templates/hookTest.template.txt',
  integration: 'src/tests/templates/integrationTest.template.txt'
};

const templatePath = templateMap[testType.toLowerCase()];

if (!templatePath) {
  console.error(`Invalid test type: ${testType}. Must be one of: component, hook, integration`);
  process.exit(1);
}

// Extract component name from path
const componentName = path.basename(componentPath).replace(/\.(jsx|tsx|js|ts)$/, '');
const componentDir = path.dirname(componentPath);

// Create test file path
const testFileName = `${componentName}.test.tsx`;
const testFilePath = path.join('src', componentDir, testFileName);

// Check if test file already exists
if (fs.existsSync(testFilePath)) {
  rl.question(`Test file ${testFilePath} already exists. Overwrite? (y/N) `, (answer) => {
    if (answer.toLowerCase() !== 'y') {
      console.log('Aborted.');
      rl.close();
      process.exit(0);
    } else {
      createTestFile();
      rl.close();
    }
  });
} else {
  createTestFile();
  rl.close();
}

function createTestFile() {
  // Read template
  fs.readFile(templatePath, 'utf8', (err, data) => {
    if (err) {
      console.error(`Error reading template file: ${err}`);
      process.exit(1);
    }
    
    // Replace placeholders in template
    let testContent = data
      .replace(/Component/g, componentName)
      .replace(/path\/to\/Component/g, componentPath.replace(/\.(jsx|tsx|js|ts)$/, ''));
    
    // Ensure directory exists
    const testDir = path.dirname(testFilePath);
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
    
    // Write test file
    fs.writeFile(testFilePath, testContent, 'utf8', (err) => {
      if (err) {
        console.error(`Error writing test file: ${err}`);
        process.exit(1);
      }
      
      console.log(`Test file created: ${testFilePath}`);
    });
  });
}
