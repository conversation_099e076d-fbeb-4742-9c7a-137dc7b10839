
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>StayFu - Offline</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Open Sans, Helvetica Neue, sans-serif;
      height: 100vh;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      background-color: #f9f9f9;
      color: #333;
    }
    
    .container {
      padding: 20px;
      border-radius: 8px;
      background-color: white;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      max-width: 90%;
      width: 400px;
    }
    
    h1 {
      margin-bottom: 10px;
      color: #333;
    }
    
    p {
      margin-bottom: 20px;
      color: #666;
    }
    
    button {
      background-color: #4f46e5;
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      transition: background-color 0.2s;
    }
    
    button:hover {
      background-color: #4338ca;
    }
    
    .icon {
      font-size: 48px;
      margin-bottom: 16px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="icon">📡</div>
    <h1>You're offline</h1>
    <p>Please check your internet connection and try again.</p>
    <button onclick="window.location.reload()">Try Again</button>
  </div>
  
  <script>
    // Check if we're back online and reload automatically
    window.addEventListener('online', () => {
      window.location.reload();
    });
  </script>
</body>
</html>
