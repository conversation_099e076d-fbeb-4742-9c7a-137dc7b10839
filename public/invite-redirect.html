<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Redirecting...</title>
  <script>
    // Get the current URL
    const currentUrl = window.location.href;
    
    // Check if the URL contains '/invite' but not '/#/invite'
    if (currentUrl.includes('/invite') && !currentUrl.includes('/#/invite')) {
      // Extract the query parameters
      const queryParams = window.location.search;
      
      // Create the new URL with the hash format
      const newUrl = window.location.origin + '/#/invite' + queryParams;
      
      // Log the redirect for debugging
      console.log('Redirecting from', currentUrl, 'to', newUrl);
      
      // Redirect to the new URL
      window.location.href = newUrl;
    }
  </script>
</head>
<body>
  <div style="display: flex; justify-content: center; align-items: center; height: 100vh; font-family: system-ui, sans-serif;">
    <div style="text-align: center;">
      <h1>Redirecting...</h1>
      <p>Please wait while we redirect you to the invitation page.</p>
    </div>
  </div>
</body>
</html>
