version: "3.8"
services:
  studio:
    volumes:
      - /home/<USER>/.docker/desktop/docker.sock:/var/run/docker.sock
  kong:
    volumes:
      - /home/<USER>/.docker/desktop/docker.sock:/var/run/docker.sock
  storage:
    volumes:
      - /home/<USER>/.docker/desktop/docker.sock:/var/run/docker.sock
  db:
    volumes:
      - /home/<USER>/.docker/desktop/docker.sock:/var/run/docker.sock
  inbucket:
    volumes:
      - /home/<USER>/.docker/desktop/docker.sock:/var/run/docker.sock
  auth:
    volumes:
      - /home/<USER>/.docker/desktop/docker.sock:/var/run/docker.sock
  rest:
    volumes:
      - /home/<USER>/.docker/desktop/docker.sock:/var/run/docker.sock
  realtime:
    volumes:
      - /home/<USER>/.docker/desktop/docker.sock:/var/run/docker.sock
  meta:
    volumes:
      - /home/<USER>/.docker/desktop/docker.sock:/var/run/docker.sock
  functions:
    volumes:
      - /home/<USER>/.docker/desktop/docker.sock:/var/run/docker.sock
  imgproxy:
    volumes:
      - /home/<USER>/.docker/desktop/docker.sock:/var/run/docker.sock
  vector:
    volumes:
      - /home/<USER>/.docker/desktop/docker.sock:/var/run/docker.sock
  analytics:
    volumes:
      - /home/<USER>/.docker/desktop/docker.sock:/var/run/docker.sock
  logflare:
    volumes:
      - /home/<USER>/.docker/desktop/docker.sock:/var/run/docker.sock
