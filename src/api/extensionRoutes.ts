import express, { NextFunction, Request, Response } from 'express';
import { ExtensionApiService } from '../services/ExtensionApiService';
import { supabase } from '../integrations/supabase/client';

const router = express.Router();

// Define a custom interface extending Express Request
interface AuthenticatedRequest extends Request {
  userId?: string;
}

// Middleware to validate extension API tokens
// Explicitly return Promise<void> and use returns after res.status().json()
const validateExtensionToken = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'Unauthorized', message: 'Missing or invalid authorization token' });
      return; // Ensure function exits after sending response
    }

    const token = authHeader.substring(7);
    const { valid, userId } = await ExtensionApiService.validateToken(token);

    if (!valid) {
      res.status(401).json({ error: 'Unauthorized', message: 'Invalid or expired token' });
      return; // Ensure function exits
    }

    req.userId = userId;
    next();
  } catch (error) {
    next(error);
  }
};

// Simple endpoint to check if the extension API is accessible
// Explicitly return Promise<void>
router.get('/extension-status', async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const isAvailable = await ExtensionApiService.checkStatus();

    if (isAvailable) {
      res.status(200).json({
        status: 'available',
        message: 'StayFu API is available'
      });
    } else {
      res.status(503).json({
        status: 'unavailable',
        message: 'StayFu API is currently unavailable'
      });
    }
  } catch (error) {
    next(error);
  }
});

// Get user properties
// Explicitly return Promise<void>
router.get('/extension/properties', validateExtensionToken, async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.userId) {
      // This case should ideally be caught by validateExtensionToken, but check again for type safety
      res.status(401).json({ error: 'Unauthorized', message: 'User ID not found after validation' });
      return;
    }
    const { data, error } = await supabase
      .from('property_assignments')
      .select('properties:property_id(id, name)')
      .eq('user_id', req.userId);

    if (error) {
      console.error('Error fetching properties for extension API:', error);
      throw new Error(error.message || 'Failed to fetch properties');
    }

    const properties = data
      .filter(item => item.properties)
      .map(item => ({
        id: item.properties.id,
        name: item.properties.name
      }));

    res.status(200).json({ properties });
  } catch (error) {
    console.error('Unexpected error in extension properties endpoint:', error);
    next(error);
  }
});

// Import inventory from the extension
// Explicitly return Promise<void>
router.post('/extension/inventory', validateExtensionToken, async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.userId) {
      res.status(401).json({ error: 'Unauthorized', message: 'User ID not found after validation' });
      return;
    }
    const data = req.body;

    if (!data.propertyId || !data.products || !Array.isArray(data.products) || data.products.length === 0) {
      res.status(400).json({
        error: 'Bad Request',
        message: 'Missing required fields: propertyId and products array'
      });
      return;
    }

    // Define expected result type (adjust based on actual service implementation)
    type ImportResult = {
        success: boolean;
        message?: string;
        stats?: { imported: number; updated: number; errors: number; };
        url?: string;
        error?: string;
        statusCode?: number; // Optional status code
    };

    const result: ImportResult = await ExtensionApiService.importInventory(data, req.userId);

    if (result.success) {
      res.status(200).json(result);
    } else {
      // Use status code from service if available, otherwise default to 400
      res.status(result.statusCode || 400).json(result);
    }
  } catch (error) {
    console.error('Error in extension inventory import endpoint:', error);
    next(error);
  }
});

// Import purchase order from the extension
// Explicitly return Promise<void>
router.post('/extension/purchase-order', validateExtensionToken, async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.userId) {
      res.status(401).json({ error: 'Unauthorized', message: 'User ID not found after validation' });
      return;
    }
    const data = req.body;

    if (!data.property_id || !data.items || !Array.isArray(data.items) || data.items.length === 0) {
      res.status(400).json({
        error: 'Bad Request',
        message: 'Missing required fields: property_id and items array'
      });
      return;
    }

    // Define expected result type (adjust based on actual service implementation)
     type PurchaseOrderResult = {
        success: boolean;
        message?: string;
        orderId?: string;
        url?: string;
        error?: string;
        statusCode?: number; // Optional status code
    };


    const result: PurchaseOrderResult = await ExtensionApiService.importPurchaseOrder(data, req.userId);

    if (result.success) {
      res.status(200).json(result);
    } else {
       // Use status code from service if available, otherwise default to 400
      res.status(result.statusCode || 400).json(result);
    }
  } catch (error) {
    console.error('Error in extension purchase order import endpoint:', error);
    next(error);
  }
});

export default router;
