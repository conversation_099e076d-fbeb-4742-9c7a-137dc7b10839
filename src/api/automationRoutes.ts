import express from 'express';
import { supabase } from '../integrations/supabase/client';

const router = express.Router();

// Process automation queue
router.post('/process-queue', async (req, res) => {
  try {
    // Add a record to the automation_queue table with a special flag
    const { data, error } = await supabase
      .from('automation_queue')
      .insert({
        booking_id: '00000000-0000-0000-0000-000000000000', // Special ID to indicate manual processing
        processed: false,
        manual_trigger: true
      });
      
    if (error) {
      console.error('Error adding queue trigger:', error);
      return res.status(500).json({ error: error.message });
    }
    
    return res.status(200).json({ 
      success: true, 
      message: 'Automation queue processing triggered' 
    });
  } catch (err) {
    console.error('Error triggering queue processing:', err);
    return res.status(500).json({ error: err.message });
  }
});

// Queue all bookings for processing
router.post('/queue-all-bookings', async (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    
    // Get all upcoming bookings
    const { data: bookings, error: bookingsError } = await supabase
      .from('bookings')
      .select('id')
      .gte('check_in_date', new Date().toISOString().split('T')[0]);
      
    if (bookingsError) {
      console.error('Error fetching bookings:', bookingsError);
      return res.status(500).json({ error: bookingsError.message });
    }
    
    if (!bookings || bookings.length === 0) {
      return res.status(200).json({ 
        success: true, 
        message: 'No upcoming bookings found' 
      });
    }
    
    // Add each booking to the automation queue
    const queuePromises = bookings.map(booking => 
      supabase
        .from('automation_queue')
        .insert({
          booking_id: booking.id,
          processed: false
        })
    );
    
    await Promise.all(queuePromises);
    
    return res.status(200).json({ 
      success: true, 
      message: `Queued ${bookings.length} bookings for processing` 
    });
  } catch (err) {
    console.error('Error queueing bookings:', err);
    return res.status(500).json({ error: err.message });
  }
});

export default router;
