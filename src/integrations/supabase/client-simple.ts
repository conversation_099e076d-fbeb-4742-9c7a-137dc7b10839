import { createClient } from '@supabase/supabase-js'

// Get environment variables with secure fallbacks
const supabaseUrl = import.meta.env?.VITE_SUPABASE_URL || 'https://pwaeknalhosfwuxkpaet.supabase.co';
const supabaseAnonKey = import.meta.env?.VITE_SUPABASE_PUBLISHABLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4';

// Validate in development/runtime if needed
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  if (!import.meta.env?.VITE_SUPABASE_URL) {
    console.warn('VITE_SUPABASE_URL not set, using fallback. Set environment variable for production.');
  }
  
  if (!import.meta.env?.VITE_SUPABASE_PUBLISHABLE_KEY) {
    console.warn('VITE_SUPABASE_PUBLISHABLE_KEY not set, using fallback. Set environment variable for production.');
  }
}

// Create a simple, clean Supabase client without any custom fetch logic
export const supabaseSimple = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  }
})

// Export the URL and key for other uses
export { supabaseUrl, supabaseAnonKey }
