// Secure Supabase client configuration using environment variables
import { createClient } from '@supabase/supabase-js';

// Environment-aware configuration
const isDevelopment = import.meta.env.MODE === 'development' || import.meta.env.DEV;

// Get environment variables with environment-specific defaults
const SUPABASE_URL = import.meta.env?.VITE_SUPABASE_URL ||
  (isDevelopment
    ? 'http://127.0.0.1:54321'
    : 'https://pwaeknalhosfwuxkpaet.supabase.co'
  );

const SUPABASE_PUBLISHABLE_KEY = import.meta.env?.VITE_SUPABASE_PUBLISHABLE_KEY ||
  (isDevelopment
    ? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN0YXlmdSIsInJvbGUiOiJhbm9uIiwiaWF0IjoxNjQ0NzY5MjAwLCJleHAiOjE5NjAxNDkyMDB9.qQNWn2VFBnKNw7rtLOeqldWddNfYQZgNqYvlYtHQrQM'
    : 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4'
  );

// Log configuration in development
if (typeof window !== 'undefined' && isDevelopment) {
  console.log('🔧 Supabase Configuration:');
  console.log('Environment:', import.meta.env.MODE);
  console.log('URL:', SUPABASE_URL);
  console.log('Using local Supabase:', SUPABASE_URL.includes('127.0.0.1'));

  if (!import.meta.env?.VITE_SUPABASE_URL) {
    console.warn('⚠️ VITE_SUPABASE_URL not set, using environment default');
  }

  if (!import.meta.env?.VITE_SUPABASE_PUBLISHABLE_KEY) {
    console.warn('⚠️ VITE_SUPABASE_PUBLISHABLE_KEY not set, using environment default');
  }
}

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

// Create the Supabase client with standard configuration - no custom fetch, no complex logic
export const supabase = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  }
});
