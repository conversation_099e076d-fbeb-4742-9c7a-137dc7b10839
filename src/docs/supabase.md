# Supabase Configuration and Functions

This document contains information about Supabase configuration, functions, and common issues.

## Project Information

- **Project Name**: StayFu
- **Project ID**: pwaeknalhosfwuxkpaet
- **Region**: us-west-1
- **URL**: https://pwaeknalhosfwuxkpaet.supabase.co

## Database Functions

### get_team_members

This function retrieves team members for a specific team with user profile information. It was created to fix issues with team members not loading in the permissions management UI.

```sql
CREATE OR REPLACE FUNCTION get_team_members(p_team_id UUID)
RETURNS TABLE (
  id UUID,
  team_id UUID,
  user_id UUID,
  added_by UUID,
  status TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  user_email TEXT,
  user_name TEXT,
  role TEXT
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN QUERY
  SELECT
    tm.id,
    tm.team_id,
    tm.user_id,
    tm.added_by,
    tm.status,
    tm.created_at,
    tm.updated_at,
    p.email AS user_email,
    CONCAT(p.first_name, ' ', p.last_name) AS user_name,
    p.role
  FROM
    team_members tm
  JOIN
    profiles p ON tm.user_id = p.id
  WHERE
    tm.team_id = p_team_id;
END;
$$;
```

Usage:
```javascript
const { data, error } = await supabase.rpc(
  'get_team_members',
  { p_team_id: teamId }
);
```

The function uses SECURITY DEFINER to bypass RLS policies, which helps prevent infinite recursion issues. If this function is missing, the application will fall back to direct database queries, but performance may be affected.

## Known Issues

### Team Members Loading Issues

The Teams dashboard may sometimes fail to load team members correctly. This can happen due to:

1. Missing `get_team_members` function - The function needs to be created in Supabase
2. Caching issues - The application may cache stale data
3. Race conditions - Multiple requests may interfere with each other
4. Unnecessary refreshes - The permissions tab was refreshing every 2-3 seconds
5. Missing `setTeamMembers` function in the PermissionManagement component

**Solution**:
- Created the `get_team_members` function with SECURITY DEFINER to bypass RLS policies
- Added local state for team members in the PermissionManagement component
- Added fallback direct queries in the PermissionManagement component
- Added a "Direct Query" button to force a direct database query when team members aren't loading
- Fixed unnecessary refreshes in the permissions tab by:
  - Removing `permissions.length` from dependency arrays
  - Using refs to track previous values and skip unnecessary updates
  - Optimizing the team members loading logic
  - Ensuring initial user selection happens only once
- Added a force load of team members when the component mounts
- Used a combined team members state that prioritizes local state over hook state

### Infinite Recursion in Team Members Policy

There was an issue with infinite recursion in the team_members policy that prevented service providers from accessing properties.

**Solution**: This issue was identified and fixed by modifying the RLS policies.

## Authentication

### Session Management

Sessions should remain valid for at least 48 hours with a 72-hour authentication timeout and redirect to login page on expiration.

### Email Configuration

Supabase emails should use 'StayFu Support' as sender name with descriptive subjects matching the email purpose.

## Database Backups

The project has backups at `/home/<USER>/code/stayfu/supabase/backups` for restoration if needed.
