# AI Purchase Order Fix - "No valid items found" Issue

## Problem Summary

When testing the AI command "Create a purchase order for all low stock items", the system returned:
> "No valid items found to include in the purchase order."

However, the inventory clearly shows low stock items (e.g., Towel Set with 4 in stock, minimum 12).

## Root Cause Analysis

### 1. **Incorrect SQL Query in Edge Function**
The original Edge Function used an invalid Supabase query:
```typescript
.lt("quantity", supabase.raw("min_quantity"));  // ❌ This doesn't work
```

Supabase doesn't support `supabase.raw()` for column comparisons in this context.

### 2. **User ID Mismatch**
The test was likely performed with a user ID that doesn't have low stock items. The database shows:
- User `e4416a70-7490-4c40-a1c4-a5a6aeadf6ea` has "Towel Set" with 4/12 stock ✅
- Other users don't have low stock items ❌

## Solution Implemented

### 1. **Created RPC Function**
```sql
CREATE OR REPLACE FUNCTION get_low_stock_items(user_id_param UUID, property_id_param UUID DEFAULT NULL)
RETURNS TABLE (
  id UUID,
  name TEXT,
  quantity INTEGER,
  min_quantity INTEGER,
  property_id UUID,
  collection TEXT,
  price DECIMAL,
  amazon_url TEXT,
  walmart_url TEXT,
  property_name TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    i.id, i.name, i.quantity, i.min_quantity, i.property_id, i.collection,
    i.price, i.amazon_url, i.walmart_url, p.name as property_name
  FROM inventory_items i
  LEFT JOIN properties p ON i.property_id = p.id
  WHERE i.user_id = user_id_param
    AND i.quantity < i.min_quantity
    AND i.min_quantity > 0
    AND (property_id_param IS NULL OR i.property_id = property_id_param);
END;
$$;
```

### 2. **Updated Edge Function**
```typescript
// Use RPC function instead of complex query
const { data: lowStockItems, error: itemsError } = await supabase
  .rpc('get_low_stock_items', {
    user_id_param: userId,
    property_id_param: propertyId
  });
```

### 3. **Added Better Error Messages**
```typescript
if (!lowStockItems || lowStockItems.length === 0) {
  return {
    success: false,
    message: `No low stock items found${data.property ? ` at property "${data.property}"` : ''}. Check your inventory to make sure items have minimum quantities set.`
  };
}
```

## Testing Tools Created

### 1. **RPC Function Test**
```javascript
// Test the RPC function directly
testGetLowStockItems('e4416a70-7490-4c40-a1c4-a5a6aeadf6ea')
```

### 2. **Direct Purchase Order Creation**
```javascript
// Bypass AI and create PO directly
testCreatePurchaseOrderDirect('e4416a70-7490-4c40-a1c4-a5a6aeadf6ea')
```

### 3. **Full AI Command Test**
```javascript
// Test the full AI command with known low stock user
testWithKnownLowStockUser()
```

## Verification Results

### ✅ **RPC Function Works**
```sql
SELECT * FROM get_low_stock_items('e4416a70-7490-4c40-a1c4-a5a6aeadf6ea'::UUID);
```
Returns:
- Towel Set: 4/12 stock at Thames (Molly) property
- Price: $23.99
- Amazon URL available

### ✅ **Database Query Correct**
```sql
SELECT name, quantity, min_quantity, (quantity < min_quantity) as is_low_stock 
FROM inventory_items 
WHERE quantity < min_quantity AND min_quantity > 0;
```
Returns the expected low stock items.

## Next Steps

### 1. **Deploy Edge Function**
The updated Edge Function needs to be deployed to Supabase:
```bash
supabase functions deploy ai-command-processor
```

### 2. **Test with Correct User**
Use the test scripts with the user ID that has low stock items:
```javascript
testWithKnownLowStockUser()  // Uses e4416a70-7490-4c40-a1c4-a5a6aeadf6ea
```

### 3. **Verify in UI**
1. Log in as the user with low stock items
2. Use AI command: "Create a purchase order for all low stock items"
3. Should create PO with Towel Set (quantity: 8 to bring 4→12)

## Expected Behavior After Fix

### ✅ **Successful Command**
```
AI Command: "Create a purchase order for all low stock items"
Response: "Successfully created purchase order for 1 low stock item at Thames (Molly)"
```

### ✅ **Purchase Order Created**
- Property: Thames (Molly)
- Items: Towel Set (quantity: 8)
- Total: $191.92 (8 × $23.99)
- Status: Pending

### ✅ **Purchase Order Items**
- Item: Towel Set
- Quantity: 8 (to bring stock from 4 to 12)
- Price: $23.99
- Amazon URL included

## Error Handling Improvements

### **No Low Stock Items**
```
"No low stock items found. Check your inventory to make sure items have minimum quantities set."
```

### **Property Not Found**
```
"No low stock items found at property 'Beach House'. Check your inventory to make sure items have minimum quantities set."
```

### **Database Errors**
```
"Error fetching low stock items: [specific error message]"
```

## Files Modified

1. **`supabase/functions/ai-command-processor/index.ts`**
   - Fixed SQL query using RPC function
   - Added better error messages
   - Added debugging logs

2. **Database Schema**
   - Added `get_low_stock_items` RPC function

3. **Test Scripts**
   - `src/test/test-ai-purchase-orders.js` - Enhanced with known test user
   - `src/test/test-low-stock-direct.js` - Direct testing without AI

## Status: READY FOR TESTING ✅

The fix is implemented and ready for testing. The RPC function works correctly, and the Edge Function has been updated to use it. The issue should be resolved once the Edge Function is deployed.
