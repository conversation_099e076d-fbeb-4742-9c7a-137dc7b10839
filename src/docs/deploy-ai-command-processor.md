# Deploy AI Command Processor - Purchase Order Fix

## Overview

The AI Command Processor Edge Function has been updated to fix the "No valid items found to include in the purchase order" issue. This document provides instructions for deploying the updated function.

## What Was Fixed

### 1. **Broken SQL Query**
**Before (Broken):**
```typescript
.lt("quantity", supabase.raw("min_quantity"));  // ❌ Invalid syntax
```

**After (Fixed):**
```typescript
// Use RPC function instead
const { data: lowStockItems } = await supabase
  .rpc('get_low_stock_items', {
    user_id_param: userId,
    property_id_param: propertyId
  });
```

### 2. **Added RPC Function**
Created a proper SQL function to handle column comparisons:
```sql
CREATE OR REPLACE FUNCTION get_low_stock_items(user_id_param UUID, property_id_param UUID DEFAULT NULL)
RETURNS TABLE (...)
-- Returns items where quantity < min_quantity
```

### 3. **Better Error Messages**
```typescript
if (!lowStockItems || lowStockItems.length === 0) {
  return {
    success: false,
    message: `No low stock items found${data.property ? ` at property "${data.property}"` : ''}. Check your inventory to make sure items have minimum quantities set.`
  };
}
```

## Deployment Steps

### Option 1: Using Supabase CLI (Recommended)

1. **Login to Supabase CLI:**
   ```bash
   supabase login
   ```

2. **Navigate to project directory:**
   ```bash
   cd /home/<USER>/code/stayfu
   ```

3. **Deploy the function:**
   ```bash
   supabase functions deploy ai-command-processor
   ```

4. **Verify deployment:**
   ```bash
   supabase functions list
   ```

### Option 2: Using Supabase Dashboard

1. **Open Supabase Dashboard:**
   - Go to https://supabase.com/dashboard
   - Select the StayFu project (pwaeknalhosfwuxkpaet)

2. **Navigate to Edge Functions:**
   - Click "Edge Functions" in the sidebar
   - Find "ai-command-processor" function

3. **Update the function:**
   - Click on the function name
   - Replace the code with the updated version from `supabase/functions/ai-command-processor/index.ts`
   - Click "Deploy"

### Option 3: Using Git Integration

If you have Git integration set up:

1. **Commit the changes:**
   ```bash
   git add supabase/functions/ai-command-processor/index.ts
   git commit -m "Fix AI purchase order creation - use RPC function for low stock items"
   ```

2. **Push to main branch:**
   ```bash
   git push origin main
   ```

3. **Supabase will auto-deploy** (if auto-deployment is enabled)

## Verification

### 1. **Test the RPC Function**
Run this in the browser console on StayFu app:
```javascript
// Copy and paste the verification script
// src/test/verify-purchase-order-fix.js
verifyPurchaseOrderFix()
```

### 2. **Test the AI Command**
Use the AI Command Center on the main dashboard:
```
"Create a purchase order for all low stock items"
```

### 3. **Expected Results**

**If successful:**
```
✅ "Successfully created a purchase order with 1 item for Thames (Molly)"
```

**If no low stock items:**
```
❌ "No low stock items found. Check your inventory to make sure items have minimum quantities set."
```

## Troubleshooting

### Issue: "Access token not provided"
**Solution:** Login to Supabase CLI first:
```bash
supabase login
```

### Issue: "Function not found"
**Solution:** Make sure you're in the correct project directory:
```bash
cd /home/<USER>/code/stayfu
supabase projects list
```

### Issue: "No low stock items found" (but items exist)
**Possible causes:**
1. **Wrong user ID** - Make sure you're testing with the correct user
2. **RPC function not deployed** - Check if the RPC function exists:
   ```sql
   SELECT * FROM get_low_stock_items('e4416a70-7490-4c40-a1c4-a5a6aeadf6ea'::UUID);
   ```
3. **Edge Function not deployed** - Check function version in dashboard

### Issue: Edge Function deployment fails
**Solution:** Check the function syntax:
```bash
supabase functions deploy ai-command-processor --debug
```

## Files Modified

1. **`supabase/functions/ai-command-processor/index.ts`**
   - Updated `handleCreatePurchaseOrder` function
   - Added RPC function call
   - Enhanced error messages
   - Added property filtering support

2. **Database Schema**
   - Added `get_low_stock_items` RPC function ✅ (Already deployed)

3. **Test Scripts**
   - `src/test/verify-purchase-order-fix.js` - Verification script
   - `src/test/test-ai-purchase-orders.js` - Enhanced testing
   - `src/test/test-low-stock-direct.js` - Direct testing

## Current Status

- ✅ **RPC Function**: Deployed and working
- ✅ **Edge Function Code**: Updated and ready
- ⏳ **Edge Function Deployment**: Needs to be deployed
- ✅ **Test Scripts**: Created and ready

## Next Steps

1. **Deploy the Edge Function** using one of the methods above
2. **Run verification script** to confirm the fix works
3. **Test with real user data** to ensure functionality
4. **Monitor for any issues** in production

## Expected Impact

After deployment:
- ✅ AI command "Create a purchase order for all low stock items" will work
- ✅ Property-specific purchase orders will work
- ✅ Category-based purchase orders will work
- ✅ Better error messages for debugging
- ✅ Improved reliability and performance

The fix addresses the root cause of the "No valid items found" error and provides a robust solution for AI-powered purchase order creation.
