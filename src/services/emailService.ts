import { supabase } from '../integrations/supabase/client';

interface EmailData {
  to: string;
  subject: string;
  html?: string;
  text?: string;
  reply_to?: string;
  from?: string;
}

/**
 * Send an email using the Emailit API via our Edge Function
 */
export const sendEmail = async (emailData: EmailData): Promise<{success: boolean; error?: string}> => {
  try {
    // Set default from address if not provided
    const emailPayload = {
      ...emailData,
      from: emailData.from || "<EMAIL>"
    };

    console.log('Sending email to:', emailData.to, 'subject:', emailData.subject);

    try {
      const { data, error } = await supabase.functions.invoke('send-email', {
        body: emailPayload,
      });

      if (error) {
        console.error('Email service error:', error);
        // Continue with fallback - don't return here
      } else {
        console.log('Email sent successfully:', data);
        return { success: true };
      }
    } catch (invokeError: any) {
      console.error('Error invoking email function:', invokeError);
      // Continue with fallback - don't return here
    }

    // Fallback for development or when the function fails
    console.log('FALLBACK: Email would be sent with this content:', {
      to: emailData.to,
      subject: emailData.subject,
      contentPreview: emailData.html?.substring(0, 100) || emailData.text?.substring(0, 100),
    });

    // Return success in development to allow the app to continue working
    return {
      success: true,
      error: 'Email sending failed, but operation continued. In production, the email would be sent.'
    };
  } catch (error: any) {
    console.error('Email sending error:', error);
    return { success: false, error: error.message || 'Failed to send email' };
  }
};

/**
 * Send a welcome email to new users
 */
export const sendWelcomeEmail = async (email: string, firstName: string = ''): Promise<{success: boolean; error?: string}> => {
  return sendEmail({
    to: email,
    subject: 'Welcome to StayFu',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #333; margin-bottom: 20px;">Welcome ${firstName ? firstName : ''}!</h1>
        <p>Thank you for joining StayFu. We're excited to have you on board.</p>
        <p>Get started by exploring the dashboard and setting up your profile.</p>
        <p>If you have any questions, feel free to reach out to our support team.</p>
        <p>Best regards,<br>The StayFu Team</p>
      </div>
    `,
  });
};

/**
 * Generate an invitation URL
 * Uses hash format for compatibility with HashRouter
 */
const generateInvitationUrl = (invitationToken: string): string => {
  // Get the current origin (works in both development and production)
  const origin = window.location.origin;
  // Use hash format for compatibility with HashRouter
  return `${origin}/#/invite?token=${invitationToken}`;
};

/**
 * Send a team invitation email
 */
export const sendTeamInvitationEmail = async (
  email: string,
  inviterName: string,
  teamName: string,
  role: string,
  invitationToken: string
): Promise<{success: boolean; error?: string}> => {
  const roleDisplay = role === 'service_provider' ? 'Service Provider' : role.charAt(0).toUpperCase() + role.slice(1);
  const inviteUrl = generateInvitationUrl(invitationToken);

  console.log(`Preparing team invitation email to ${email} for team ${teamName} with role ${roleDisplay}`);
  console.log(`Invitation URL: ${inviteUrl}`);

  return sendEmail({
    to: email,
    from: `StayFu Team <<EMAIL>>`,
    subject: `You've been invited to join ${teamName} on StayFu`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #333; margin-bottom: 20px;">Team Invitation</h1>
        <p>${inviterName} has invited you to join <strong>${teamName}</strong> as a <strong>${roleDisplay}</strong>.</p>
        <p>Click the button below to accept this invitation:</p>
        <p style="text-align: center; margin: 30px 0;">
          <a href="${inviteUrl}" style="background-color: #0891b2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block;">
            Accept Invitation
          </a>
        </p>
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 14px;">
          ${inviteUrl}
        </p>
        <p>This invitation will expire in 7 days.</p>
        <p>Best regards,<br>The StayFu Team</p>
      </div>
    `,
  });
};

/**
 * Send a maintenance task notification email
 */
export const sendMaintenanceTaskEmail = async (
  email: string,
  taskTitle: string,
  taskInfo: {
    property?: string;
    severity?: string;
    dueDate?: string;
    description?: string;
  },
  acceptUrl: string,
  rejectUrl: string,
  replyToEmail?: string
): Promise<{success: boolean; error?: string}> => {
  console.log(`Sending maintenance task email to ${email} for task ${taskTitle}`);

  return sendEmail({
    to: email,
    from: `StayFu Maintenance <<EMAIL>>`,
    subject: `New Maintenance Task: ${taskTitle}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>New Maintenance Task: ${taskTitle}</h2>
        <p><strong>Property:</strong> ${taskInfo.property || 'Not specified'}</p>
        <p><strong>Severity:</strong> ${taskInfo.severity || 'Not specified'}</p>
        <p><strong>Due Date:</strong> ${taskInfo.dueDate || 'No due date'}</p>
        <p><strong>Description:</strong> ${taskInfo.description || 'No description provided'}</p>

        <div style="margin: 30px 0;">
          <p><strong>Are you available to handle this task?</strong></p>
          <table cellpadding="0" cellspacing="0" border="0">
            <tr>
              <td style="padding-right: 15px;">
                <a href="${acceptUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
                  Accept Task
                </a>
              </td>
              <td>
                <a href="${rejectUrl}" style="display: inline-block; background-color: #e53935; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
                  Decline Task
                </a>
              </td>
            </tr>
          </table>
        </div>

        <p style="font-size: 0.8em; color: #666;">
          By accepting, you agree to take responsibility for this maintenance task.
          If you decline, the property manager will be notified to find another service provider.
        </p>
      </div>
    `,
    reply_to: replyToEmail
  });
};

/**
 * Send a notification email
 */
export const sendNotificationEmail = async (
  email: string,
  subject: string,
  message: string
): Promise<{success: boolean; error?: string}> => {
  return sendEmail({
    to: email,
    subject,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #333; margin-bottom: 20px;">${subject}</h1>
        <p>${message}</p>
        <p>Best regards,<br>The StayFu Team</p>
      </div>
    `,
  });
};

/**
 * Send a password reset confirmation email
 */
export const sendPasswordResetConfirmationEmail = async (email: string): Promise<{success: boolean; error?: string}> => {
  return sendEmail({
    to: email,
    subject: 'Your Password Has Been Reset',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #333; margin-bottom: 20px;">Password Updated</h1>
        <p>Your password has been successfully updated.</p>
        <p>If you did not make this change, please contact support immediately.</p>
        <p>Thanks,<br>The Support Team</p>
      </div>
    `,
  });
};

export default {
  sendEmail,
  sendWelcomeEmail,
  sendTeamInvitationEmail,
  sendMaintenanceTaskEmail,
  sendNotificationEmail,
  sendPasswordResetConfirmationEmail
};
