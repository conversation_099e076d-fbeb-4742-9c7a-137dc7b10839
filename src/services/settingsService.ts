
import { supabase } from "@/integrations/supabase/client";
import { UserSettings, UserSettingKey } from "@/types/settings";
import { toast } from "sonner";

// Debounce mechanism to prevent rapid consecutive changes
let pendingUpdates = {};
const updateDebounceTime = 300; // ms

export const fetchUserSettings = async (userId: string): Promise<UserSettings | null> => {
  try {
    const { data, error } = await supabase
      .from('user_settings')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    if (error) {
      console.error('Error fetching user settings:', error);
      toast.error('Failed to load user settings');
      return null;
    }

    return data as UserSettings;
  } catch (error) {
    console.error('Unexpected error fetching settings:', error);
    return null;
  }
};

export const updateUserSetting = async (
  userId: string, 
  key: UserSettingKey, 
  value: boolean
): Promise<boolean> => {
  try {
    console.log(`Updating setting in DB: ${key} = ${value}`);
    
    // Create a unique key for this update
    const updateKey = `${userId}-${key}`;
    
    // If there's already a pending update for this key, clear it
    if (pendingUpdates[updateKey]) {
      clearTimeout(pendingUpdates[updateKey]);
    }
    
    // Return a promise that resolves when the debounced update is complete
    return new Promise((resolve) => {
      pendingUpdates[updateKey] = setTimeout(async () => {
        delete pendingUpdates[updateKey];
        
        // Check if the user has settings
        const { data: existingSettings, error: checkError } = await supabase
          .from('user_settings')
          .select('id')
          .eq('user_id', userId)
          .maybeSingle();
          
        if (checkError) {
          console.error('Error checking for existing settings:', checkError);
        }

        let result;
        
        if (existingSettings) {
          // Update existing settings
          result = await supabase
            .from('user_settings')
            .update({ [key]: value, updated_at: new Date().toISOString() })
            .eq('user_id', userId);
        } else {
          // Create new settings
          const defaultSettings = {
            user_id: userId,
            dark_mode: false,
            compact_mode: false,
            animations: true,
            email_notifications: true,
            push_notifications: false,
            weekly_summary: true,
            inventory_alerts: true
          };
          
          // Override the default with the new value
          const newSettings = {
            ...defaultSettings,
            [key]: value
          };
          
          // Insert the new settings
          result = await supabase
            .from('user_settings')
            .insert(newSettings);
        }

        if (result.error) {
          console.error('Error updating setting:', result.error);
          toast.error('Failed to update settings');
          resolve(false);
          return;
        }

        // Immediately apply visual changes for appearance settings
        if (key === 'dark_mode') {
          console.log('Applying dark mode:', value);
          applyDarkMode(value);
          // Also store in localStorage for persistence
          localStorage.setItem('theme-mode', value ? 'dark' : 'light');
        } else if (key === 'compact_mode') {
          applyCompactMode(value);
        }

        resolve(true);
      }, updateDebounceTime);
    });
  } catch (error) {
    console.error('Unexpected error updating setting:', error);
    return false;
  }
};

export const saveAllUserSettings = async (
  userId: string,
  settings: Partial<UserSettings>
): Promise<boolean> => {
  try {
    // Check if the user has settings
    const { data: existingSettings, error: checkError } = await supabase
      .from('user_settings')
      .select('id')
      .eq('user_id', userId)
      .maybeSingle();
      
    if (checkError) {
      console.error('Error checking for existing settings:', checkError);
    }

    const settingsWithUserId = {
      ...settings,
      user_id: userId,
      updated_at: new Date().toISOString()
    };
    
    let result;
    
    if (existingSettings) {
      // Update existing settings
      result = await supabase
        .from('user_settings')
        .update(settingsWithUserId)
        .eq('user_id', userId);
    } else {
      // Create new settings with defaults for any missing values
      const defaultSettings = {
        user_id: userId,
        dark_mode: false,
        compact_mode: false,
        animations: true,
        email_notifications: true,
        push_notifications: false,
        weekly_summary: true,
        inventory_alerts: true
      };
      
      const newSettings = {
        ...defaultSettings,
        ...settings
      };
      
      result = await supabase
        .from('user_settings')
        .insert(newSettings);
    }

    if (result.error) {
      console.error('Error saving settings:', result.error);
      toast.error('Failed to save settings');
      return false;
    }

    // Apply theme settings
    if (settings.dark_mode !== undefined) {
      applyDarkMode(settings.dark_mode);
      localStorage.setItem('theme-mode', settings.dark_mode ? 'dark' : 'light');
    }
    
    if (settings.compact_mode !== undefined) {
      applyCompactMode(settings.compact_mode);
    }

    toast.success('Settings saved successfully');
    return true;
  } catch (error) {
    console.error('Unexpected error saving settings:', error);
    toast.error('An error occurred while saving settings');
    return false;
  }
};

export const applyDarkMode = (isDarkMode: boolean) => {
  console.log('Applying dark mode:', isDarkMode);
  
  // Get current state to avoid unnecessary DOM operations
  const isDarkActive = document.documentElement.classList.contains('dark');
  
  // Only modify the DOM if the state needs to change
  if (isDarkMode && !isDarkActive) {
    document.documentElement.classList.add('dark');
  } else if (!isDarkMode && isDarkActive) {
    document.documentElement.classList.remove('dark');
  }
  
  // Store current mode in local storage for persistence
  localStorage.setItem('theme-mode', isDarkMode ? 'dark' : 'light');
};

export const applyCompactMode = (isCompactMode: boolean) => {
  if (isCompactMode) {
    document.documentElement.classList.add('compact-mode');
  } else {
    document.documentElement.classList.remove('compact-mode');
  }
};
