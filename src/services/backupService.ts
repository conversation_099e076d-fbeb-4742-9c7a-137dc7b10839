import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import J<PERSON><PERSON><PERSON> from 'jszip';

// Define what can be included in a backup
export interface BackupOptions {
  properties: boolean;
  inventory: boolean;
  damageReports: boolean;
  maintenanceTasks: boolean;
  teams: boolean;
  includeImages: boolean;
}

// Define conflict resolution strategy for restores
export type ConflictResolution = 'skip' | 'update' | 'overwrite';

export interface RestoreOptions {
  properties: boolean;
  inventory: boolean;
  damageReports: boolean;
  maintenanceTasks: boolean;
  teams: boolean;
  conflictResolution: ConflictResolution;
}

// Default options
export const defaultBackupOptions: BackupOptions = {
  properties: true,
  inventory: true,
  damageReports: true,
  maintenanceTasks: true,
  teams: true,
  includeImages: false, // Default to false as images can make the backup large
};

export const defaultRestoreOptions: RestoreOptions = {
  properties: true,
  inventory: true,
  damageReports: true,
  maintenanceTasks: true,
  teams: true,
  conflictResolution: 'skip', // Default to skip conflicts
};

/**
 * Convert an array of objects to CSV format
 */
export function objectsToCSV(data: any[]): string {
  if (data.length === 0) return '';

  // Get headers from the first object
  const headers = Object.keys(data[0]);

  // Create CSV header row
  let csv = headers.join(',') + '\n';

  // Add data rows
  data.forEach(item => {
    const row = headers.map(header => {
      const value = item[header];

      // Handle different data types
      if (value === null || value === undefined) {
        return '';
      } else if (typeof value === 'object') {
        // Convert objects to JSON strings
        return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
      } else if (typeof value === 'string') {
        // Escape quotes in strings
        return `"${value.replace(/"/g, '""')}"`;
      } else {
        return value;
      }
    }).join(',');

    csv += row + '\n';
  });

  return csv;
}

/**
 * Create a backup of user data based on selected options
 */
export async function createBackup(userId: string, options: BackupOptions = defaultBackupOptions) {
  try {
    const backupData: Record<string, any> = {
      metadata: {
        created_at: new Date().toISOString(),
        user_id: userId,
        options,
      },
      data: {},
    };

    // Fetch properties
    if (options.properties) {
      toast.loading('Backing up properties...', { id: 'backup-progress' });
      const { data: properties, error: propertiesError } = await supabase
        .from('properties')
        .select('*')
        .eq('user_id', userId);

      if (propertiesError) {
        console.error('Error fetching properties:', propertiesError);
        toast.dismiss('backup-progress');
        toast.error('Failed to backup properties', { duration: 3000 });
      } else {
        backupData.data.properties = properties || [];
        toast.loading(`Backed up ${properties?.length || 0} properties`, { id: 'backup-progress' });
      }
    }

    // Fetch inventory items
    if (options.inventory) {
      toast.loading('Backing up inventory items...', { id: 'backup-progress' });
      const { data: inventoryItems, error: inventoryError } = await supabase
        .from('inventory_items')
        .select('*')
        .eq('user_id', userId);

      if (inventoryError) {
        console.error('Error fetching inventory items:', inventoryError);
        toast.error('Failed to backup inventory items', { id: 'backup-progress' });
      } else {
        backupData.data.inventory_items = inventoryItems || [];
        toast.loading(`Backed up ${inventoryItems?.length || 0} inventory items`, { id: 'backup-progress' });
      }
    }

    // Fetch damage reports
    if (options.damageReports) {
      toast.loading('Backing up damage reports...', { id: 'backup-progress' });
      const { data: damageReports, error: damageReportsError } = await supabase
        .from('damage_reports')
        .select('*')
        .eq('user_id', userId);

      if (damageReportsError) {
        console.error('Error fetching damage reports:', damageReportsError);
        toast.error('Failed to backup damage reports', { id: 'backup-progress' });
      } else {
        backupData.data.damage_reports = damageReports || [];
        toast.loading(`Backed up ${damageReports?.length || 0} damage reports`, { id: 'backup-progress' });

        // Fetch damage photos for each damage report
        if (damageReports && damageReports.length > 0) {
          toast.loading('Backing up damage photos...', { id: 'backup-progress' });
          const damageReportIds = damageReports.map(report => report.id);

          const { data: damagePhotos, error: photosError } = await supabase
            .from('damage_photos')
            .select('*')
            .in('damage_report_id', damageReportIds);

          if (photosError) {
            console.error('Error fetching damage photos:', photosError);
            toast.error('Failed to backup damage photos metadata', { id: 'backup-progress' });
          } else {
            backupData.data.damage_photos = damagePhotos || [];
            toast.loading(`Backed up ${damagePhotos?.length || 0} damage photos`, { id: 'backup-progress' });
          }

          // Fetch damage notes
          toast.loading('Backing up damage notes...', { id: 'backup-progress' });
          const { data: damageNotes, error: notesError } = await supabase
            .from('damage_notes')
            .select('*')
            .in('damage_report_id', damageReportIds);

          if (notesError) {
            console.error('Error fetching damage notes:', notesError);
            toast.error('Failed to backup damage notes', { id: 'backup-progress' });
          } else {
            backupData.data.damage_notes = damageNotes || [];
            toast.loading(`Backed up ${damageNotes?.length || 0} damage notes`, { id: 'backup-progress' });
          }
        }
      }
    }

    // Fetch maintenance tasks
    if (options.maintenanceTasks) {
      toast.loading('Backing up maintenance tasks...', { id: 'backup-progress' });
      const { data: maintenanceTasks, error: tasksError } = await supabase
        .from('maintenance_tasks')
        .select('*')
        .eq('user_id', userId);

      if (tasksError) {
        console.error('Error fetching maintenance tasks:', tasksError);
        toast.error('Failed to backup maintenance tasks', { id: 'backup-progress' });
      } else {
        backupData.data.maintenance_tasks = maintenanceTasks || [];
        toast.loading(`Backed up ${maintenanceTasks?.length || 0} maintenance tasks`, { id: 'backup-progress' });
      }
    }

    // Fetch teams and team members
    if (options.teams) {
      toast.loading('Backing up teams...', { id: 'backup-progress' });
      // First get teams owned by the user
      const { data: teams, error: teamsError } = await supabase
        .from('teams')
        .select('*')
        .eq('owner_id', userId);

      if (teamsError) {
        console.error('Error fetching teams:', teamsError);
        toast.error('Failed to backup teams', { id: 'backup-progress' });
      } else {
        backupData.data.teams = teams || [];
        toast.loading(`Backed up ${teams?.length || 0} teams`, { id: 'backup-progress' });

        // If teams exist, get team members and team properties
        if (teams && teams.length > 0) {
          const teamIds = teams.map(team => team.id);

          // Get team members
          toast.loading('Backing up team members...', { id: 'backup-progress' });
          const { data: teamMembers, error: membersError } = await supabase
            .from('team_members')
            .select('*')
            .in('team_id', teamIds);

          if (membersError) {
            console.error('Error fetching team members:', membersError);
            toast.error('Failed to backup team members', { id: 'backup-progress' });
          } else {
            backupData.data.team_members = teamMembers || [];
            toast.loading(`Backed up ${teamMembers?.length || 0} team members`, { id: 'backup-progress' });
          }

          // Get team properties
          toast.loading('Backing up team properties...', { id: 'backup-progress' });
          const { data: teamProperties, error: propertiesError } = await supabase
            .from('team_properties')
            .select('*')
            .in('team_id', teamIds);

          if (propertiesError) {
            console.error('Error fetching team properties:', propertiesError);
            toast.error('Failed to backup team properties', { id: 'backup-progress' });
          } else {
            backupData.data.team_properties = teamProperties || [];
            toast.loading(`Backed up ${teamProperties?.length || 0} team properties`, { id: 'backup-progress' });
          }
        }
      }
    }

    // If including images, fetch image URLs
    if (options.includeImages) {
      toast.loading('Processing image URLs...', { id: 'backup-progress' });
      // This will just include the URLs in the backup, not the actual images
      // The user would need to download the images separately

      // Add property images
      if (backupData.data.properties) {
        backupData.data.property_images = backupData.data.properties
          .filter((property: any) => property.image_url)
          .map((property: any) => ({
            property_id: property.id,
            image_url: property.image_url
          }));
      }

      // Add inventory item images
      if (backupData.data.inventory_items) {
        backupData.data.inventory_images = backupData.data.inventory_items
          .filter((item: any) => item.image_url)
          .map((item: any) => ({
            inventory_item_id: item.id,
            image_url: item.image_url
          }));
      }
    }

    toast.dismiss('backup-progress');
    toast.loading('Preparing download...', { id: 'backup-final', duration: 3000 });
    return backupData;
  } catch (error) {
    console.error('Error creating backup:', error);
    toast.error('Failed to create backup');
    throw error;
  }
}

/**
 * Download backup data as CSV files (one per table)
 */
export function downloadBackupAsCSV(backupData: Record<string, any>, fileName = 'stayfu-backup') {
  try {
    // Create a zip file containing all CSVs
    const zip = new JSZip();

    // Add metadata file
    const metadataStr = JSON.stringify(backupData.metadata, null, 2);
    zip.file('metadata.json', metadataStr);

    // Add each table as a CSV file
    for (const [tableName, tableData] of Object.entries(backupData.data)) {
      if (Array.isArray(tableData) && tableData.length > 0) {
        const csv = objectsToCSV(tableData);
        zip.file(`${tableName}.csv`, csv);
      }
    }

    // Generate the zip file and trigger download
    zip.generateAsync({ type: 'blob' }).then((content: Blob) => {
      const url = URL.createObjectURL(content);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${fileName}.zip`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    });

    return true;
  } catch (error) {
    console.error('Error downloading backup:', error);
    toast.error('Failed to download backup');
    return false;
  }
}

/**
 * Download a single table as CSV
 */
export function downloadTableAsCSV(data: any[], fileName: string) {
  try {
    if (!data || data.length === 0) {
      toast.error('No data to export');
      return false;
    }

    const csv = objectsToCSV(data);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `${fileName}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    return true;
  } catch (error) {
    console.error('Error downloading CSV:', error);
    toast.error('Failed to download CSV');
    return false;
  }
}

/**
 * Parse CSV data into an array of objects
 */
export function parseCSV(csvText: string): any[] {
  try {
    // Split the CSV text into lines
    const lines = csvText.split('\n').filter(line => line.trim() !== '');
    if (lines.length < 2) return []; // Need at least header and one data row

    // Parse the header row
    const headers = lines[0].split(',').map(header => {
      // Remove quotes if present
      return header.replace(/^"|"$/g, '').trim();
    });

    // Parse data rows
    const results: Record<string, any>[] = [];
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i];
      // Handle quoted values with commas inside them
      const values: string[] = [];
      let inQuote = false;
      let currentValue = '';

      for (let j = 0; j < line.length; j++) {
        const char = line[j];

        if (char === '"') {
          inQuote = !inQuote;
          // For escaped quotes ("")
          if (j < line.length - 1 && line[j + 1] === '"' && inQuote) {
            currentValue += '"';
            j++; // Skip the next quote
          }
        } else if (char === ',' && !inQuote) {
          values.push(currentValue);
          currentValue = '';
        } else {
          currentValue += char;
        }
      }

      // Add the last value
      values.push(currentValue);

      // Create an object from the values
      const obj: Record<string, any> = {};
      for (let j = 0; j < headers.length; j++) {
        const value = j < values.length ? values[j] : '';

        // Try to parse JSON objects
        if (value.startsWith('{') || value.startsWith('[')) {
          try {
            obj[headers[j]] = JSON.parse(value);
          } catch {
            obj[headers[j]] = value;
          }
        } else {
          obj[headers[j]] = value;
        }
      }

      results.push(obj);
    }

    return results;
  } catch (error) {
    console.error('Error parsing CSV:', error);
    throw error;
  }
}

/**
 * Read a zip file and extract its contents
 */
export async function readBackupZip(file: File): Promise<Record<string, any>> {
  try {
    const zip = new JSZip();
    const zipContents = await zip.loadAsync(file);

    const backupData: Record<string, any> = {
      metadata: {},
      data: {},
    };

    // Process each file in the zip
    const filePromises: Promise<void>[] = [];

    zipContents.forEach((relativePath, zipEntry) => {
      if (!zipEntry.dir) {
        const promise = zipEntry.async('string').then(content => {
          if (relativePath === 'metadata.json') {
            try {
              backupData.metadata = JSON.parse(content);
            } catch (e) {
              console.error('Error parsing metadata.json:', e);
            }
          } else if (relativePath.endsWith('.csv')) {
            // Extract table name from filename (remove .csv extension)
            const tableName = relativePath.replace('.csv', '');
            try {
              backupData.data[tableName] = parseCSV(content);
            } catch (e) {
              console.error(`Error parsing ${relativePath}:`, e);
            }
          }
        });

        filePromises.push(promise);
      }
    });

    // Wait for all files to be processed
    await Promise.all(filePromises);

    return backupData;
  } catch (error) {
    console.error('Error reading backup zip:', error);
    throw error;
  }
}

/**
 * Restore data from a backup
 */
export async function restoreBackup(
  userId: string,
  backupData: Record<string, any>,
  options: RestoreOptions = defaultRestoreOptions
): Promise<Record<string, any>> {
  const results: Record<string, any> = {
    success: true,
    tables: {},
    errors: [],
    stats: {
      total: 0,
      inserted: 0,
      updated: 0,
      skipped: 0,
      failed: 0
    }
  };

  // Map to store old IDs to new IDs for maintaining relationships
  const idMap: Record<string, Record<string, string>> = {
    properties: {},
    inventory_items: {},
    damage_reports: {},
    teams: {},
    maintenance_tasks: {},
    team_members: {},
    team_properties: {}
  };

  try {
    // Validate backup data structure
    if (!backupData.data) {
      throw new Error('Invalid backup data: missing data object');
    }

    // Check if this is a cross-account restore by comparing user IDs
    const isOriginalUser = backupData.metadata?.user_id === userId;
    console.log(`Restore type: ${isOriginalUser ? 'Same account' : 'Cross-account'} restore`);

    // For cross-account restores, we'll always generate new IDs
    const forceNewIds = !isOriginalUser;

    // Track which properties were successfully restored
    const restoredPropertyIds: Record<string, string> = {};

    // Restore properties
    if (options.properties && backupData.data.properties) {
      toast.loading('Restoring properties...', { id: 'restore-progress' });

      // Check for existing properties to avoid duplicates
      const { data: existingProperties, error: propsError } = await supabase
        .from('properties')
        .select('id, name')
        .eq('user_id', userId);

      if (propsError) {
        console.error('Error fetching existing properties:', propsError);
      }

      // Create a map of existing property names to IDs
      const existingPropertyMap: Record<string, string> = {};
      if (existingProperties) {
        existingProperties.forEach((prop: any) => {
          existingPropertyMap[prop.name.toLowerCase()] = prop.id;
        });
      }

      // For cross-account restores, generate new IDs for properties or use existing property IDs if name matches
      const propertiesToRestore = backupData.data.properties.map((property: any) => {
        // Check if a property with this name already exists
        const existingPropertyId = property.name ? existingPropertyMap[property.name.toLowerCase()] : null;

        if (existingPropertyId) {
          // Store mapping from old ID to existing ID
          idMap.properties[property.id] = existingPropertyId;

          // Skip this property in the restore process
          return null;
        }

        // Generate a new ID for each property if needed
        const newId = forceNewIds ? crypto.randomUUID() : property.id;

        // Store mapping from old ID to new ID
        if (forceNewIds) {
          idMap.properties[property.id] = newId;
        }

        // Create a new property object with the correct ID and user_id
        const newProperty = {
          ...property,
          id: newId,
          user_id: userId // Set the user to the current user
        };

        // Handle boolean fields that actually exist in the schema
        if ('is_occupied' in newProperty) {
          if (newProperty.is_occupied === '' || newProperty.is_occupied === null || newProperty.is_occupied === undefined) {
            newProperty.is_occupied = false;
          } else if (typeof newProperty.is_occupied === 'string') {
            const value = String(newProperty.is_occupied).toLowerCase();
            newProperty.is_occupied = value === 'true' || value === 't' || value === 'yes' || value === 'y' || value === '1';
          }
        }

        // Remove fields that don't exist in the schema
        const fieldsToRemove = ['is_active', 'is_deleted', 'is_public', 'auto_sync_enabled'];
        for (const field of fieldsToRemove) {
          if (field in newProperty) {
            delete newProperty[field];
          }
        }

        // Handle numeric fields
        const numericFields = ['bedrooms', 'bathrooms', 'budget'];
        for (const field of numericFields) {
          if (field in newProperty) {
            if (newProperty[field] === '' || newProperty[field] === null || newProperty[field] === undefined) {
              newProperty[field] = null;
            } else {
              const num = Number(newProperty[field]);
              newProperty[field] = isNaN(num) ? null : num;
            }
          }
        }

        // Process image URLs to ensure they're valid
        if (newProperty.image_url) {
          // Extract valid image URL if embedded in a string
          const urlMatch = newProperty.image_url.match(/https?:\/\/[^,\s"]+\.(jpg|jpeg|png|gif|webp)/i);
          if (urlMatch && urlMatch[0]) {
            newProperty.image_url = urlMatch[0];
          }
        }

        return newProperty;
      }).filter(Boolean); // Remove null entries (properties that already exist)

      // Restore properties and track which ones were successfully restored
      results.tables.properties = await restoreTable(
        'properties',
        propertiesToRestore,
        userId,
        options.conflictResolution,
        'user_id',
        false, // We've already generated new IDs if needed
        idMap.properties
      );

      // Track which properties were successfully restored
      if (results.tables.properties.inserted > 0 || results.tables.properties.updated > 0) {
        console.log(`Successfully restored ${results.tables.properties.inserted + results.tables.properties.updated} properties`);

        // Get the list of successfully restored properties
        const { data: restoredProps, error: propsError } = await supabase
          .from('properties')
          .select('id')
          .eq('user_id', userId);

        if (!propsError && restoredProps) {
          // Store the IDs of successfully restored properties
          restoredProps.forEach((prop: any) => {
            restoredPropertyIds[prop.id] = prop.id;
          });
          console.log(`Found ${Object.keys(restoredPropertyIds).length} restored properties`);
        } else {
          console.error('Error fetching restored properties:', propsError);
        }
      } else {
        console.warn('No properties were restored successfully');
      }

      results.stats.total += results.tables.properties.total;
      results.stats.inserted += results.tables.properties.inserted;
      results.stats.updated += results.tables.properties.updated;
      results.stats.skipped += results.tables.properties.skipped;
      results.stats.failed += results.tables.properties.failed;

      toast.loading(`Restored ${results.tables.properties.inserted + results.tables.properties.updated} properties`, { id: 'restore-progress' });
    }

    // Restore inventory items
    if (options.inventory && backupData.data.inventory_items) {
      toast.loading('Restoring inventory items...', { id: 'restore-progress' });

      // Initialize ID map for inventory items if needed
      if (!idMap.inventory_items) {
        idMap.inventory_items = {};
      }

      // Check for existing inventory items to avoid duplicates
      const { data: existingItems, error: itemsError } = await supabase
        .from('inventory_items')
        .select('id, name, property_id, collection')
        .eq('user_id', userId);

      if (itemsError) {
        console.error('Error fetching existing inventory items:', itemsError);
      }

      // Create a map of existing item names, property IDs, and collections to item IDs
      const existingItemMap: Record<string, string> = {};
      if (existingItems) {
        existingItems.forEach((item: any) => {
          const key = `${item.name.toLowerCase()}_${item.property_id || 'none'}_${item.collection || 'none'}`;
          existingItemMap[key] = item.id;
        });
      }

      // Filter inventory items to only include those with valid property IDs
      const validInventoryItems = backupData.data.inventory_items.filter((item: any) => {
        // If the item has no property_id, it's valid
        if (!item.property_id || item.property_id === '') {
          return true;
        }

        // If the property was successfully restored, it's valid
        const mappedPropertyId = idMap.properties[item.property_id];
        if (mappedPropertyId && restoredPropertyIds[mappedPropertyId]) {
          return true;
        }

        // If we're not in a cross-account restore and the property exists, it's valid
        if (!forceNewIds && item.property_id) {
          return true;
        }

        console.log(`Skipping inventory item with invalid property_id: ${item.property_id}`);
        return false;
      });

      console.log(`Filtered ${backupData.data.inventory_items.length - validInventoryItems.length} inventory items with invalid property IDs`);

      const mappedInventoryItems = validInventoryItems.map((item: any) => {
        // Map the property ID if needed
        const mappedPropertyId = item.property_id && idMap.properties[item.property_id]
          ? idMap.properties[item.property_id]
          : item.property_id;

        // Check if an item with this name, property, and collection already exists
        const itemKey = `${item.name.toLowerCase()}_${mappedPropertyId || 'none'}_${item.collection || 'none'}`;
        const existingItemId = existingItemMap[itemKey];

        if (existingItemId) {
          // Store mapping from old ID to existing ID
          idMap.inventory_items[item.id] = existingItemId;

          // Skip this item in the restore process
          return null;
        }

        // Generate a new ID for inventory items to avoid conflicts
        const newId = crypto.randomUUID();
        // Store mapping from old ID to new ID
        idMap.inventory_items[item.id] = newId;

        const newItem = {
          ...item,
          id: newId,
          user_id: userId
        };

        // Handle empty or invalid property_id
        if (!newItem.property_id || newItem.property_id === '') {
          delete newItem.property_id;
        } else if (idMap.properties && idMap.properties[newItem.property_id]) {
          // Map to new property ID if available
          newItem.property_id = idMap.properties[newItem.property_id];
        }

        // Handle empty or invalid team_id
        if (!newItem.team_id || newItem.team_id === '') {
          delete newItem.team_id;
        } else if (idMap.teams && idMap.teams[newItem.team_id]) {
          // Map to new team ID if available
          newItem.team_id = idMap.teams[newItem.team_id];
        }

        // Handle empty or invalid collection_id
        if (!newItem.collection_id || newItem.collection_id === '') {
          newItem.collection_id = null;
        }

        // Process image URLs to ensure they're valid
        if (newItem.image_url) {
          // Extract valid image URL if embedded in a string
          const urlMatch = newItem.image_url.match(/https?:\/\/[^,\s"]+\.(jpg|jpeg|png|gif|webp)/i);
          if (urlMatch && urlMatch[0]) {
            newItem.image_url = urlMatch[0];
          }
        }

        // Ensure numeric fields are properly set
        if (newItem.quantity === '' || newItem.quantity === null || newItem.quantity === undefined) {
          newItem.quantity = 1; // Default quantity
        } else {
          newItem.quantity = Number(newItem.quantity) || 1;
        }

        if (newItem.min_quantity === '' || newItem.min_quantity === null || newItem.min_quantity === undefined) {
          newItem.min_quantity = 0; // Default min_quantity
        } else {
          newItem.min_quantity = Number(newItem.min_quantity) || 0;
        }

        if (newItem.price === '' || newItem.price === null || newItem.price === undefined) {
          newItem.price = null; // Default price
        } else {
          const price = Number(newItem.price);
          newItem.price = isNaN(price) ? null : price;
        }

        return newItem;
      }).filter(Boolean); // Remove null entries (items that already exist)

      results.tables.inventory_items = await restoreTable(
        'inventory_items',
        mappedInventoryItems,
        userId,
        options.conflictResolution,
        'user_id',
        false, // We've already generated new IDs
        idMap.inventory_items
      );

      results.stats.total += results.tables.inventory_items.total;
      results.stats.inserted += results.tables.inventory_items.inserted;
      results.stats.updated += results.tables.inventory_items.updated;
      results.stats.skipped += results.tables.inventory_items.skipped;
      results.stats.failed += results.tables.inventory_items.failed;

      toast.loading(`Restored ${results.tables.inventory_items.inserted + results.tables.inventory_items.updated} inventory items`, { id: 'restore-progress' });
    }

    // Restore damage reports
    if (options.damageReports && backupData.data.damage_reports) {
      toast.loading('Restoring damage reports...', { id: 'restore-progress' });

      // Initialize ID map for damage reports
      if (!idMap.damage_reports) {
        idMap.damage_reports = {};
      }

      // Check for existing damage reports to avoid duplicates
      const { data: existingReports, error: reportsError } = await supabase
        .from('damage_reports')
        .select('id, title, property_id')
        .eq('user_id', userId);

      if (reportsError) {
        console.error('Error fetching existing damage reports:', reportsError);
      }

      // Create a map of existing report titles and property IDs to report IDs
      const existingReportMap: Record<string, string> = {};
      if (existingReports) {
        existingReports.forEach((report: any) => {
          const key = `${report.title.toLowerCase()}_${report.property_id || 'none'}`;
          existingReportMap[key] = report.id;
        });
      }

      // Filter damage reports to only include those with valid property IDs
      const validDamageReports = backupData.data.damage_reports.filter((report: any) => {
        // If the report has no property_id, it's valid
        if (!report.property_id || report.property_id === '') {
          return true;
        }

        // If the property was successfully restored, it's valid
        const mappedPropertyId = idMap.properties[report.property_id];
        if (mappedPropertyId && restoredPropertyIds[mappedPropertyId]) {
          return true;
        }

        // If we're not in a cross-account restore and the property exists, it's valid
        if (!forceNewIds && report.property_id) {
          return true;
        }

        console.log(`Skipping damage report with invalid property_id: ${report.property_id}`);
        return false;
      });

      console.log(`Filtered ${backupData.data.damage_reports.length - validDamageReports.length} damage reports with invalid property IDs`);

      // Process damage reports, checking for duplicates
      const damageReportsToRestore = validDamageReports.map((report: any) => {
        // Map the property ID if needed
        const mappedPropertyId = report.property_id && idMap.properties[report.property_id]
          ? idMap.properties[report.property_id]
          : report.property_id;

        // Check if a report with this title and property already exists
        const reportKey = `${report.title.toLowerCase()}_${mappedPropertyId || 'none'}`;
        const existingReportId = existingReportMap[reportKey];

        if (existingReportId) {
          // Store mapping from old ID to existing ID
          idMap.damage_reports[report.id] = existingReportId;

          // Skip this report in the restore process
          return null;
        }

        // Generate a new ID for cross-account restores
        const newId = forceNewIds ? crypto.randomUUID() : report.id;
        // Store mapping from old ID to new ID
        idMap.damage_reports[report.id] = newId;

        return {
          ...report,
          id: newId,
          user_id: userId,
          // Map property ID if needed
          property_id: mappedPropertyId
        };
      }).filter(Boolean); // Remove null entries (reports that already exist)

      results.tables.damage_reports = await restoreTable(
        'damage_reports',
        damageReportsToRestore,
        userId,
        options.conflictResolution,
        'user_id',
        false, // We've already generated new IDs if needed
        idMap.damage_reports
      );

      results.stats.total += results.tables.damage_reports.total;
      results.stats.inserted += results.tables.damage_reports.inserted;
      results.stats.updated += results.tables.damage_reports.updated;
      results.stats.skipped += results.tables.damage_reports.skipped;
      results.stats.failed += results.tables.damage_reports.failed;

      toast.loading(`Restored ${results.tables.damage_reports.inserted + results.tables.damage_reports.updated} damage reports`, { id: 'restore-progress' });

      // Restore damage photos if available
      if (backupData.data.damage_photos) {
        toast.loading('Restoring damage photos...', { id: 'restore-progress' });

        // Check for existing damage photos to avoid duplicates
        const { data: existingPhotos, error: photosError } = await supabase
          .from('damage_photos')
          .select('id, damage_report_id, url');

        if (photosError) {
          console.error('Error fetching existing damage photos:', photosError);
        }

        // Create a map of existing photo URLs and damage report IDs to photo IDs
        const existingPhotoMap: Record<string, string> = {};
        if (existingPhotos) {
          existingPhotos.forEach((photo: any) => {
            if (photo.url) {
              const key = `${photo.damage_report_id || 'none'}_${photo.url}`;
              existingPhotoMap[key] = photo.id;
            }
          });
        }

        // Map damage report IDs in damage photos
        const mappedDamagePhotos = backupData.data.damage_photos.map((photo: any) => {
          // Map damage report ID if needed
          const mappedReportId = photo.damage_report_id && idMap.damage_reports[photo.damage_report_id]
            ? idMap.damage_reports[photo.damage_report_id]
            : photo.damage_report_id;

          // Check if a photo with this URL and damage report already exists
          if (photo.url) {
            const photoKey = `${mappedReportId || 'none'}_${photo.url}`;
            const existingPhotoId = existingPhotoMap[photoKey];

            if (existingPhotoId) {
              // Skip this photo in the restore process
              return null;
            }
          }

          const newPhoto = { ...photo };

          // Always generate a new ID for photos to avoid conflicts
          newPhoto.id = crypto.randomUUID();

          // Map damage report ID if needed
          if (newPhoto.damage_report_id && idMap.damage_reports[newPhoto.damage_report_id]) {
            newPhoto.damage_report_id = idMap.damage_reports[newPhoto.damage_report_id];
          }

          // Process image URLs to ensure they're valid
          if (newPhoto.url) {
            // Extract valid image URL if embedded in a string
            const urlMatch = newPhoto.url.match(/https?:\/\/[^,\s"]+\.(jpg|jpeg|png|gif|webp)/i);
            if (urlMatch && urlMatch[0]) {
              newPhoto.url = urlMatch[0];
            }
          }

          return newPhoto;
        }).filter(Boolean); // Remove null entries (photos that already exist)

        results.tables.damage_photos = await restoreTable(
          'damage_photos',
          mappedDamagePhotos,
          userId,
          options.conflictResolution,
          'user_id',
          false, // We've already generated new IDs
          {}
        );

        results.stats.total += results.tables.damage_photos.total;
        results.stats.inserted += results.tables.damage_photos.inserted;
        results.stats.updated += results.tables.damage_photos.updated;
        results.stats.skipped += results.tables.damage_photos.skipped;
        results.stats.failed += results.tables.damage_photos.failed;

        toast.loading(`Restored ${results.tables.damage_photos.inserted + results.tables.damage_photos.updated} damage photos`, { id: 'restore-progress' });
      }

      // Restore damage notes if available
      if (backupData.data.damage_notes) {
        toast.loading('Restoring damage notes...', { id: 'restore-progress' });

        // Check for existing damage notes to avoid duplicates
        const { data: existingNotes, error: notesError } = await supabase
          .from('damage_notes')
          .select('id, damage_report_id, content');

        if (notesError) {
          console.error('Error fetching existing damage notes:', notesError);
        }

        // Create a map of existing note content and damage report IDs to note IDs
        const existingNoteMap: Record<string, string> = {};
        if (existingNotes) {
          existingNotes.forEach((note: any) => {
            if (note.content) {
              const key = `${note.damage_report_id || 'none'}_${note.content.substring(0, 50)}`;
              existingNoteMap[key] = note.id;
            }
          });
        }

        // Map damage report IDs in damage notes
        const mappedDamageNotes = backupData.data.damage_notes.map((note: any) => {
          // Map damage report ID if needed
          const mappedReportId = note.damage_report_id && idMap.damage_reports[note.damage_report_id]
            ? idMap.damage_reports[note.damage_report_id]
            : note.damage_report_id;

          // Check if a note with this content and damage report already exists
          if (note.content) {
            const noteKey = `${mappedReportId || 'none'}_${note.content.substring(0, 50)}`;
            const existingNoteId = existingNoteMap[noteKey];

            if (existingNoteId) {
              // Skip this note in the restore process
              return null;
            }
          }

          const newNote = { ...note };

          // Always generate a new ID for notes to avoid conflicts
          newNote.id = crypto.randomUUID();

          // Map damage report ID if needed
          if (newNote.damage_report_id && idMap.damage_reports[newNote.damage_report_id]) {
            newNote.damage_report_id = idMap.damage_reports[newNote.damage_report_id];
          }

          return newNote;
        }).filter(Boolean); // Remove null entries (notes that already exist)

        results.tables.damage_notes = await restoreTable(
          'damage_notes',
          mappedDamageNotes,
          userId,
          options.conflictResolution,
          'user_id',
          false, // We've already generated new IDs
          {}
        );

        results.stats.total += results.tables.damage_notes.total;
        results.stats.inserted += results.tables.damage_notes.inserted;
        results.stats.updated += results.tables.damage_notes.updated;
        results.stats.skipped += results.tables.damage_notes.skipped;
        results.stats.failed += results.tables.damage_notes.failed;

        toast.loading(`Restored ${results.tables.damage_notes.inserted + results.tables.damage_notes.updated} damage notes`, { id: 'restore-progress' });
      }
    }

    // Restore maintenance tasks
    if (options.maintenanceTasks && backupData.data.maintenance_tasks) {
      toast.loading('Restoring maintenance tasks...', { id: 'restore-progress' });

      // Initialize ID map for maintenance tasks
      if (!idMap.maintenance_tasks) {
        idMap.maintenance_tasks = {};
      }

      // Check for existing maintenance tasks to avoid duplicates
      const { data: existingTasks, error: tasksError } = await supabase
        .from('maintenance_tasks')
        .select('id, title, property_id')
        .eq('user_id', userId);

      if (tasksError) {
        console.error('Error fetching existing maintenance tasks:', tasksError);
      }

      // Create a map of existing task titles and property IDs to task IDs
      const existingTaskMap: Record<string, string> = {};
      if (existingTasks) {
        existingTasks.forEach((task: any) => {
          const key = `${task.title.toLowerCase()}_${task.property_id || 'none'}`;
          existingTaskMap[key] = task.id;
        });
      }

      // Filter maintenance tasks to only include those with valid property IDs
      const validMaintenanceTasks = backupData.data.maintenance_tasks.filter((task: any) => {
        // If the task has no property_id, it's valid
        if (!task.property_id || task.property_id === '') {
          return true;
        }

        // If the property was successfully restored, it's valid
        const mappedPropertyId = idMap.properties[task.property_id];
        if (mappedPropertyId && restoredPropertyIds[mappedPropertyId]) {
          return true;
        }

        // If we're not in a cross-account restore and the property exists, it's valid
        if (!forceNewIds && task.property_id) {
          return true;
        }

        console.log(`Skipping maintenance task with invalid property_id: ${task.property_id}`);
        return false;
      });

      console.log(`Filtered ${backupData.data.maintenance_tasks.length - validMaintenanceTasks.length} maintenance tasks with invalid property IDs`);

      // Process maintenance tasks, checking for duplicates
      const mappedMaintenanceTasks = validMaintenanceTasks.map((task: any) => {
        // Map the property ID if needed
        const mappedPropertyId = task.property_id && idMap.properties[task.property_id]
          ? idMap.properties[task.property_id]
          : task.property_id;

        // Check if a task with this title and property already exists
        const taskKey = `${task.title.toLowerCase()}_${mappedPropertyId || 'none'}`;
        const existingTaskId = existingTaskMap[taskKey];

        if (existingTaskId) {
          // Store mapping from old ID to existing ID
          idMap.maintenance_tasks[task.id] = existingTaskId;

          // Skip this task in the restore process
          return null;
        }

        // Generate a new ID for each task
        const newId = crypto.randomUUID();
        // Store mapping from old ID to new ID
        idMap.maintenance_tasks[task.id] = newId;

        const newTask = {
          ...task,
          id: newId,
          user_id: userId
        };

        // Handle empty or invalid property_id
        if (!newTask.property_id || newTask.property_id === '') {
          delete newTask.property_id;
        } else if (idMap.properties && idMap.properties[newTask.property_id]) {
          // Map to new property ID if available
          newTask.property_id = idMap.properties[newTask.property_id];
        }

        // Handle empty or invalid assigned_to
        if (!newTask.assigned_to || newTask.assigned_to === '') {
          delete newTask.assigned_to;
        }

        // Fix timestamp fields
        const timestampFields = ['due_date', 'completed_date', 'created_at', 'updated_at'];
        for (const field of timestampFields) {
          if (field in newTask && (!newTask[field] || newTask[field] === '')) {
            delete newTask[field]; // Remove empty timestamp fields
          }
        }

        // Ensure status is valid
        if (!newTask.status || newTask.status === '') {
          newTask.status = 'open'; // Default status
        }

        // Ensure severity is valid
        if (!newTask.severity || newTask.severity === '') {
          newTask.severity = 'medium'; // Default severity
        }

        return newTask;
      }).filter(Boolean); // Remove null entries (tasks that already exist)

      results.tables.maintenance_tasks = await restoreTable(
        'maintenance_tasks',
        mappedMaintenanceTasks,
        userId,
        options.conflictResolution,
        'user_id',
        false, // We've already generated new IDs
        idMap.maintenance_tasks
      );

      results.stats.total += results.tables.maintenance_tasks.total;
      results.stats.inserted += results.tables.maintenance_tasks.inserted;
      results.stats.updated += results.tables.maintenance_tasks.updated;
      results.stats.skipped += results.tables.maintenance_tasks.skipped;
      results.stats.failed += results.tables.maintenance_tasks.failed;

      toast.loading(`Restored ${results.tables.maintenance_tasks.inserted + results.tables.maintenance_tasks.updated} maintenance tasks`, { id: 'restore-progress' });
    }

    // Restore teams
    if (options.teams && backupData.data.teams) {
      toast.loading('Restoring teams...', { id: 'restore-progress' });

      // For all team restores, we need to ensure the owner_id exists in profiles
      // First, get a list of all valid profile IDs
      const { data: validProfiles, error: profilesError } = await supabase
        .from('profiles')
        .select('id');

      if (profilesError) {
        console.error('Error fetching valid profiles:', profilesError);
        toast.error('Failed to fetch valid profiles for team restoration');
      }

      // Create a set of valid profile IDs for quick lookup
      const validProfileIds = new Set(validProfiles?.map((profile: any) => profile.id) || []);

      // Always include the current user's ID
      validProfileIds.add(userId);

      // For cross-account restores, generate new IDs for teams and ensure owner_id is valid
      const teamsToRestore = backupData.data.teams.map((team: any) => {
        const newTeam = { ...team };

        // Generate new ID if forcing new IDs
        if (forceNewIds) {
          newTeam.id = crypto.randomUUID();
        }

        // Check if owner_id exists in profiles, if not set to current user
        if (!newTeam.owner_id || !validProfileIds.has(newTeam.owner_id)) {
          newTeam.owner_id = userId;
        }

        return newTeam;
      });

      // Store the ID mappings for teams
      if (forceNewIds) {
        backupData.data.teams.forEach((team: any, index: number) => {
          idMap.teams[team.id] = teamsToRestore[index].id;
        });
      }

      results.tables.teams = await restoreTable(
        'teams',
        teamsToRestore,
        userId,
        options.conflictResolution,
        'owner_id', // Use owner_id instead of user_id for teams
        forceNewIds,
        idMap.teams
      );

      results.stats.total += results.tables.teams.total;
      results.stats.inserted += results.tables.teams.inserted;
      results.stats.updated += results.tables.teams.updated;
      results.stats.skipped += results.tables.teams.skipped;
      results.stats.failed += results.tables.teams.failed;

      toast.loading(`Restored ${results.tables.teams.inserted + results.tables.teams.updated} teams`, { id: 'restore-progress' });

      // Restore team members if available
      if (backupData.data.team_members) {
        toast.loading('Restoring team members...', { id: 'restore-progress' });

        // Map team IDs in team_members
        const mappedTeamMembers = backupData.data.team_members.map((item: any) => {
          const newItem = { ...item };

          // Handle empty or invalid team_id
          if (!newItem.team_id || newItem.team_id === '') {
            delete newItem.team_id;
          } else if (idMap.teams && idMap.teams[newItem.team_id]) {
            // Map to new team ID if available
            newItem.team_id = idMap.teams[newItem.team_id];
          }

          // For cross-account restores, set the user_id to the current user
          // This ensures the current user is added to the team
          if (forceNewIds) {
            newItem.user_id = userId;
          }

          return newItem;
        });

        results.tables.team_members = await restoreTable(
          'team_members',
          mappedTeamMembers,
          userId,
          options.conflictResolution,
          null, // No user_id field to update automatically
          forceNewIds,
          idMap.team_members
        );

        results.stats.total += results.tables.team_members.total;
        results.stats.inserted += results.tables.team_members.inserted;
        results.stats.updated += results.tables.team_members.updated;
        results.stats.skipped += results.tables.team_members.skipped;
        results.stats.failed += results.tables.team_members.failed;

        toast.loading(`Restored ${results.tables.team_members.inserted + results.tables.team_members.updated} team members`, { id: 'restore-progress' });
      }

      // Restore team properties if available
      if (backupData.data.team_properties) {
        toast.loading('Restoring team properties...', { id: 'restore-progress' });

        // Map team and property IDs in team_properties
        const mappedTeamProperties = backupData.data.team_properties.map((item: any) => {
          const newItem = { ...item };

          // Handle empty or invalid property_id
          if (!newItem.property_id || newItem.property_id === '') {
            delete newItem.property_id;
          } else if (idMap.properties && idMap.properties[newItem.property_id]) {
            // Map to new property ID if available
            newItem.property_id = idMap.properties[newItem.property_id];
          }

          // Handle empty or invalid team_id
          if (!newItem.team_id || newItem.team_id === '') {
            delete newItem.team_id;
          } else if (idMap.teams && idMap.teams[newItem.team_id]) {
            // Map to new team ID if available
            newItem.team_id = idMap.teams[newItem.team_id];
          }

          return newItem;
        });

        results.tables.team_properties = await restoreTable(
          'team_properties',
          mappedTeamProperties,
          userId,
          options.conflictResolution,
          null, // No user_id field in team_properties
          forceNewIds,
          idMap.team_properties
        );

        results.stats.total += results.tables.team_properties.total;
        results.stats.inserted += results.tables.team_properties.inserted;
        results.stats.updated += results.tables.team_properties.updated;
        results.stats.skipped += results.tables.team_properties.skipped;
        results.stats.failed += results.tables.team_properties.failed;

        toast.loading(`Restored ${results.tables.team_properties.inserted + results.tables.team_properties.updated} team properties`, { id: 'restore-progress' });
      }
    }

    // Final summary - dismiss the loading toast
    toast.dismiss('restore-progress');

    return results;
  } catch (error) {
    console.error('Error restoring backup:', error);
    results.success = false;
    results.errors.push(error instanceof Error ? error.message : 'Unknown error');
    toast.dismiss('restore-progress');
    toast.error(`Failed to restore backup: ${error instanceof Error ? error.message : 'Unknown error'}`, { duration: 5000 });
    return results;
  }
}

/**
 * Restore a single table from backup data
 */
async function restoreTable(
  tableName: string,
  data: any[],
  userId: string,
  conflictResolution: ConflictResolution,
  userIdField: string | null = 'user_id',
  forceNewIds: boolean = false,
  idMap: Record<string, string> = {}
): Promise<{
  total: number;
  inserted: number;
  updated: number;
  skipped: number;
  failed: number;
  errors: string[];
}> {
  const result = {
    total: data.length,
    inserted: 0,
    updated: 0,
    skipped: 0,
    failed: 0,
    errors: [] as string[]
  };

  try {
    // Process in batches to avoid overwhelming the database
    const batchSize = 50;
    const batches = Math.ceil(data.length / batchSize);

    for (let i = 0; i < batches; i++) {
      const start = i * batchSize;
      const end = Math.min(start + batchSize, data.length);
      const batch = data.slice(start, end);

      // Process each item in the batch
      for (const item of batch) {
        try {
          // Skip items with empty IDs
          if (!item.id) {
            console.warn(`Skipping ${tableName} item with empty ID`);
            result.skipped++;
            continue;
          }

          // Create a sanitized copy of the item for insertion/update
          const sanitizedItem = sanitizeItem(item, tableName);

          // Ensure the item has the correct user_id
          if (userIdField) {
            sanitizedItem[userIdField] = userId;
          }

          // Store the original ID for mapping
          const originalId = sanitizedItem.id;

          // For cross-account restores, always generate new IDs
          // This is now handled in sanitizeItem, but we'll keep the logic here for backward compatibility
          if (forceNewIds) {
            // Store the mapping from old ID to new ID (sanitizeItem may have already generated a new ID)
            idMap[originalId] = sanitizedItem.id;
            // Item with new ID won't exist, so skip the existence check
            var exists = false;
          } else {
            // Check if the item already exists
            const { data: existingItems, error: checkError } = await supabase
              .from(tableName)
              .select('id')
              .eq('id', sanitizedItem.id)
              .maybeSingle();

            if (checkError) {
              console.error(`Error checking for existing ${tableName}:`, checkError);
              result.failed++;
              result.errors.push(`Failed to check for existing ${tableName}: ${checkError.message}`);
              continue;
            }

            var exists = !!existingItems;
          }

          if (exists) {
            // Handle existing item based on conflict resolution strategy
            if (conflictResolution === 'skip') {
              result.skipped++;
              continue;
            } else if (conflictResolution === 'update') {
              // Update only non-null fields
              const updateData: Record<string, any> = {};
              for (const [key, value] of Object.entries(sanitizedItem)) {
                if (value !== null && value !== undefined && key !== 'id') {
                  updateData[key] = value;
                }
              }

              const { error: updateError } = await supabase
                .from(tableName)
                .update(updateData)
                .eq('id', sanitizedItem.id);

              if (updateError) {
                console.error(`Error updating ${tableName}:`, updateError);
                result.failed++;
                result.errors.push(`Failed to update ${tableName}: ${updateError.message}`);
              } else {
                result.updated++;
              }
            } else if (conflictResolution === 'overwrite') {
              // Delete and re-insert
              const { error: deleteError } = await supabase
                .from(tableName)
                .delete()
                .eq('id', sanitizedItem.id);

              if (deleteError) {
                console.error(`Error deleting ${tableName}:`, deleteError);
                result.failed++;
                result.errors.push(`Failed to delete ${tableName}: ${deleteError.message}`);
                continue;
              }

              const { error: insertError } = await supabase
                .from(tableName)
                .insert(sanitizedItem);

              if (insertError) {
                console.error(`Error inserting ${tableName}:`, insertError);
                result.failed++;
                result.errors.push(`Failed to insert ${tableName}: ${insertError.message}`);
              } else {
                result.updated++; // Count as update since it replaced an existing item
              }
            }
          } else {
            // Insert new item (ID already generated if forceNewIds is true)
            try {
              // Try to insert the item
              const { error: insertError } = await supabase
                .from(tableName)
                .insert(sanitizedItem);

              if (insertError) {
                // Check for duplicate key violation
                if (insertError.code === '23505') { // Unique constraint violation
                  console.warn(`Duplicate key detected for ${tableName}, generating new ID and retrying`);

                  // Generate a new ID and try again
                  const oldId = sanitizedItem.id;
                  sanitizedItem.id = crypto.randomUUID();

                  // Update the ID mapping
                  idMap[originalId] = sanitizedItem.id;

                  // Try again with the new ID
                  const { error: retryError } = await supabase
                    .from(tableName)
                    .insert(sanitizedItem);

                  if (retryError) {
                    console.error(`Error inserting ${tableName} after ID regeneration:`, retryError);
                    result.failed++;
                    result.errors.push(`Failed to insert ${tableName} after ID regeneration: ${retryError.message}`);
                  } else {
                    console.log(`Successfully inserted ${tableName} with regenerated ID`);
                    result.inserted++;
                  }
                } else if (insertError.code === '22P02') { // Invalid input syntax
                  console.error(`Invalid input syntax for ${tableName}:`, insertError);
                  console.log('Problematic item:', sanitizedItem);
                  result.failed++;
                  result.errors.push(`Invalid input syntax for ${tableName}: ${insertError.message}`);
                } else {
                  console.error(`Error inserting ${tableName}:`, insertError);
                  result.failed++;
                  result.errors.push(`Failed to insert ${tableName}: ${insertError.message}`);
                }
              } else {
                result.inserted++;
                // If we're not forcing new IDs but this is a new item, still store the mapping
                // (it will be the same ID, but this ensures the map is complete)
                if (!forceNewIds) {
                  idMap[originalId] = sanitizedItem.id;
                }
              }
            } catch (insertError: any) {
              console.error(`Exception during ${tableName} insertion:`, insertError);
              result.failed++;
              result.errors.push(`Exception during ${tableName} insertion: ${insertError.message || 'Unknown error'}`);
            }
          }
        } catch (itemError) {
          console.error(`Error processing ${tableName} item:`, itemError);
          result.failed++;
          result.errors.push(`Error processing ${tableName} item: ${itemError instanceof Error ? itemError.message : 'Unknown error'}`);
        }
      }
    }

    return result;
  } catch (error) {
    console.error(`Error restoring ${tableName}:`, error);
    result.failed = result.total - result.inserted - result.updated - result.skipped;
    result.errors.push(`Error restoring ${tableName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return result;
  }
}

/**
 * Sanitize an item for insertion/update
 * This function removes empty strings from UUID fields and handles other data type issues
 */
function sanitizeItem(item: Record<string, any>, tableName: string): Record<string, any> {
  const sanitized = { ...item };

  // Handle common UUID fields
  const uuidFields = [
    'id', 'user_id', 'property_id', 'team_id', 'damage_report_id', 'provider_id', 'owner_id',
    'task_id', 'report_id', 'photo_id', 'note_id', 'item_id', 'order_id', 'invitation_id',
    'member_id', 'assigned_to', 'created_by', 'updated_by'
  ];

  for (const field of uuidFields) {
    if (field in sanitized && (sanitized[field] === '' || sanitized[field] === null || sanitized[field] === undefined)) {
      // For primary keys (id), we'll generate a new UUID later
      // For foreign keys, we need to handle them based on the table
      if (field !== 'id') {
        delete sanitized[field]; // Remove empty foreign keys
      } else if (field === 'id') {
        // Always generate a new ID for cross-account restores to avoid conflicts
        sanitized.id = crypto.randomUUID();
      }
    }
  }

  // Handle timestamp fields for all tables
  const timestampFields = ['created_at', 'updated_at', 'due_date', 'completed_date', 'last_ical_sync', 'next_checkin_date'];
  for (const field of timestampFields) {
    if (field in sanitized && (!sanitized[field] || sanitized[field] === '')) {
      delete sanitized[field]; // Remove empty timestamp fields
    }
  }

  // Handle boolean fields for all tables
  const booleanFields = ['is_active', 'is_deleted', 'is_completed', 'is_public', 'is_super_admin', 'is_admin',
                         'is_verified', 'is_enabled', 'is_favorite', 'is_archived', 'is_published',
                         'is_available', 'is_urgent', 'is_recurring', 'is_template', 'is_default',
                         'has_images', 'has_attachments', 'has_notes', 'has_comments', 'has_checklist',
                         'auto_sync_enabled', 'notifications_enabled', 'email_notifications_enabled'];

  for (const field of booleanFields) {
    if (field in sanitized) {
      if (sanitized[field] === '' || sanitized[field] === null || sanitized[field] === undefined) {
        // Set default value for boolean fields (usually false)
        sanitized[field] = false;
      } else if (typeof sanitized[field] === 'string') {
        // Convert string representations to actual boolean
        const value = sanitized[field].toLowerCase();
        sanitized[field] = value === 'true' || value === 't' || value === 'yes' || value === 'y' || value === '1';
      }
    }
  }

  // Handle numeric fields for all tables
  const numericFields = ['quantity', 'min_quantity', 'price', 'bedrooms', 'bathrooms', 'budget',
                         'amount', 'total', 'subtotal', 'tax', 'discount', 'shipping', 'weight',
                         'rating', 'priority', 'order', 'position', 'duration', 'count'];

  for (const field of numericFields) {
    if (field in sanitized) {
      if (sanitized[field] === '' || sanitized[field] === null || sanitized[field] === undefined) {
        // For numeric fields, null is often acceptable
        sanitized[field] = null;
      } else {
        // Convert to number, default to null if NaN
        const num = Number(sanitized[field]);
        sanitized[field] = isNaN(num) ? null : num;
      }
    }
  }

  // Handle table-specific sanitization
  switch (tableName) {
    case 'inventory_items':
      // Ensure numeric fields are numbers with appropriate defaults
      if ('quantity' in sanitized) {
        sanitized.quantity = sanitized.quantity === null ? 0 : sanitized.quantity;
      }
      if ('min_quantity' in sanitized) {
        sanitized.min_quantity = sanitized.min_quantity === null ? 0 : sanitized.min_quantity;
      }
      break;

    case 'properties':
      // Handle specific boolean fields for properties
      if ('is_active' in sanitized && (sanitized.is_active === '' || sanitized.is_active === null)) {
        sanitized.is_active = true; // Default to active
      }
      break;

    case 'teams':
      // For teams, ensure owner_id is valid (will be checked against profiles later)
      // If owner_id is empty or invalid, it will be replaced with the current user's ID
      if ('owner_id' in sanitized && (sanitized.owner_id === '' || sanitized.owner_id === null)) {
        delete sanitized.owner_id; // Remove empty owner_id, will be set to current user later
      }
      break;

    case 'damage_reports':
      // Ensure status is valid
      if ('status' in sanitized && !sanitized.status) {
        sanitized.status = 'open'; // Default status
      }
      break;

    case 'maintenance_tasks':
      // Ensure status and severity are valid
      if ('status' in sanitized && !sanitized.status) {
        sanitized.status = 'open'; // Default status
      }
      if ('severity' in sanitized && !sanitized.severity) {
        sanitized.severity = 'medium'; // Default severity
      }
      break;
  }

  return sanitized;
}
