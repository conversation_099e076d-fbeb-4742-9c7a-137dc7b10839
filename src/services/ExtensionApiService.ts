import { supabase } from '../integrations/supabase/client';
import { toast } from 'sonner';

interface ExtensionInventoryItem {
  name: string;
  sku: string;
  description?: string;
  image_url?: string;
  price?: number;
  supplier?: string;
  supplier_url?: string;
  features?: string[];
  specifications?: Record<string, string>;
  external_id?: string;
  metadata?: Record<string, any>;
}

interface ExtensionInventoryImport {
  importType: 'inventory';
  propertyId: string;
  collection: string;
  products: ExtensionInventoryItem[];
}

interface ExtensionPurchaseOrder {
  property_id: string;
  collection: string;
  supplier: string;
  items: {
    name: string;
    sku: string;
    price: number;
    quantity: number;
    url?: string;
    image_url?: string;
    metadata?: Record<string, any>;
  }[];
}

export class ExtensionApiService {
  /**
   * Check if the extension API is accessible
   */
  static async checkStatus(): Promise<boolean> {
    try {
      // Simple status check - just verify we can access Supabase
      const { data, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('Extension API status check failed:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error in extension API status check:', error);
      return false;
    }
  }
  
  /**
   * Validate an extension API token
   */
  static async validateToken(token: string): Promise<{ valid: boolean; userId?: string }> {
    try {
      // 1. Hash the incoming token using SHA-256 (same as generation function)
      const hashedToken = await crypto.subtle.digest(
        'SHA-256',
        new TextEncoder().encode(token)
      ).then(hash => Array.from(new Uint8Array(hash))
        .map(b => b.toString(16).padStart(2, '0'))
        .join(''));

      // 2. Query using the hashed token against the token_hash column
      const { data, error } = await supabase
        .from('extension_api_tokens')
        .select('user_id, created_at')
        .eq('token_hash', hashedToken) // Compare against the hash
        .single();

      if (error || !data) {
        // Don't log the raw token here for security
        console.log('Invalid or non-existent extension API token hash.');
        return { valid: false };
      }
      
      // Check if token is expired (tokens expire after 90 days)
      const tokenCreatedAt = new Date(data.created_at);
      const expirationDate = new Date(tokenCreatedAt);
      expirationDate.setDate(expirationDate.getDate() + 90);
      
      if (new Date() > expirationDate) {
        console.log('Extension API token has expired:', token);
        return { valid: false };
      }
      
      return { valid: true, userId: data.user_id };
    } catch (error) {
      console.error('Error validating extension API token:', error);
      return { valid: false };
    }
  }
  
  /**
   * Create a new extension API token for a user
   * NOTE: This currently stores the raw token, which is inconsistent
   * with the generate-extension-token Supabase function that stores a hash.
   * This method should likely be updated to hash the token or removed
   * if token generation is solely handled by the Supabase function.
   */
  static async createToken(userId: string): Promise<{ token: string; error?: string }> {
    try {
      // Generate a random token
      const token = crypto.randomUUID();

      // TODO: Hash the token here before inserting if this method is kept.
      // const hashedToken = await crypto.subtle.digest(...)

      // Insert into extension_api_tokens table (currently stores raw token)
      const { error } = await supabase
        .from('extension_api_tokens')
        .insert({
          user_id: userId,
          token,
          created_at: new Date().toISOString()
        });
      
      if (error) {
        console.error('Error creating extension API token:', error);
        return { token: '', error: error.message };
      }
      
      return { token };
    } catch (error: any) {
      console.error('Error creating extension API token:', error);
      return { token: '', error: error.message };
    }
  }
  
  /**
   * Import inventory data from the extension
   */
  static async importInventory(data: ExtensionInventoryImport, userId: string): Promise<{ 
    success: boolean; 
    message?: string; 
    stats?: { imported: number; updated: number; errors: number }; 
    url?: string;
    error?: string 
  }> {
    try {
      const { propertyId, collection, products } = data;
      
      // Verify the user has access to this property
      const { data: propertyAccess, error: accessError } = await supabase
        .from('property_assignments')
        .select('id')
        .eq('property_id', propertyId)
        .eq('user_id', userId)
        .maybeSingle();
      
      if (accessError || !propertyAccess) {
        return { 
          success: false, 
          error: 'You do not have access to this property' 
        };
      }
      
      // Track import statistics
      const stats = {
        imported: 0,
        updated: 0,
        errors: 0
      };
      
      // Prepare items for upsert
      const inventoryItems = products.map(product => ({
        property_id: propertyId, // Ensure this matches your unique constraint column
        name: product.name,
        sku: product.sku,
        description: product.description || '',
        image_url: product.image_url || '',
        price: product.price || 0,
        supplier: product.supplier || 'Amazon',
        supplier_url: product.supplier_url || '',
        collection: collection || 'Uncategorized',
        external_id: product.external_id || product.sku,
        created_by: userId,
        metadata: {
          features: product.features || [],
          specifications: product.specifications || {},
          importedBy: 'extension',
          importDate: new Date().toISOString(),
          ...product.metadata
        }
      }));
      
      // Use upsert for efficient batch insert/update
      // Assumes a unique constraint exists on (property_id, sku)
      const { data: upsertData, error: upsertError, count } = await supabase
        .from('inventory_items')
        .upsert(inventoryItems, {
           onConflict: 'property_id, sku', // Specify conflict columns
           // ignoreDuplicates: false // Default is false, updates conflicting rows
        })
        .select({ count: 'exact' }); // Get count of affected rows

      if (upsertError) {
        console.error('Error upserting inventory items:', upsertError);
        return {
          success: false,
          error: upsertError.message || 'Failed to upsert inventory items'
        };
      }

      // Note: Supabase upsert count might not directly map to inserted vs updated.
      // For simplicity, we'll report the total affected count.
      // More complex logic could pre-fetch existing SKUs if exact stats are needed.
      const affectedCount = count ?? inventoryItems.length; // Fallback if count isn't returned

      return {
        success: true,
        message: `Successfully processed ${affectedCount} inventory items for property ${propertyId}.`,
        // Simplified stats - upsert doesn't easily distinguish insert vs update
        stats: { imported: affectedCount, updated: 0, errors: 0 },
        // Removed duplicate 'stats' shorthand property below
        url: `/inventory?property=${propertyId}&collection=${encodeURIComponent(collection)}`
      };
    } catch (error: any) {
      console.error('Error importing inventory from extension:', error);
      return {
        success: false,
        error: error.message || 'An unknown error occurred while importing inventory',
      };
    }
  }
  
  /**
   * Import purchase order data from the extension
   */
  static async importPurchaseOrder(data: ExtensionPurchaseOrder, userId: string): Promise<{ 
    success: boolean; 
    message?: string; 
    orderId?: string;
    url?: string;
    error?: string 
  }> {
    try {
      const { property_id, collection, supplier, items } = data;
      
      // Verify the user has access to this property
      const { data: propertyAccess, error: accessError } = await supabase
        .from('property_assignments')
        .select('id')
        .eq('property_id', property_id)
        .eq('user_id', userId)
        .maybeSingle();
      
      if (accessError || !propertyAccess) {
        return { 
          success: false, 
          error: 'You do not have access to this property' 
        };
      }
      
      // Create a new purchase order
      const orderName = `Amazon Order - ${collection || 'Items'} - ${new Date().toLocaleDateString()}`;
      const { data: order, error: orderError } = await supabase
        .from('purchase_orders')
        .insert({
          property_id: property_id,
          name: orderName,
          supplier: supplier,
          status: 'draft',
          created_by: userId,
          notes: `Imported from Amazon via StayFu Chrome Extension\nCollection: ${collection}`,
          metadata: {
            importedBy: 'extension',
            importDate: new Date().toISOString(),
            collection
          }
        })
        .select('id')
        .single();
      
      if (orderError || !order) {
        console.error('Error creating purchase order:', orderError);
        return {
          success: false,
          error: orderError?.message || 'Failed to create purchase order'
        };
      }
      
      // Add items to the purchase order
      const purchaseOrderItems = items.map(item => ({
        purchase_order_id: order.id,
        name: item.name,
        sku: item.sku,
        price: item.price,
        quantity: item.quantity,
        url: item.url || '',
        image_url: item.image_url || '',
        metadata: {
          importedBy: 'extension',
          importDate: new Date().toISOString(),
          ...item.metadata
        }
      }));
      
      // Import items in batches
      const batchSize = 50;
      let errorsCount = 0;
      
      for (let i = 0; i < purchaseOrderItems.length; i += batchSize) {
        const batch = purchaseOrderItems.slice(i, i + batchSize);
        
        const { error: itemsError } = await supabase
          .from('purchase_order_items')
          .insert(batch);
        
        if (itemsError) {
          console.error('Error adding items to purchase order:', itemsError);
          errorsCount++;
        }
      }
      
      return {
        success: true,
        message: `Successfully created purchase order "${orderName}" with ${items.length} items.`,
        orderId: order.id,
        url: `/purchase-orders/${order.id}`
      };
    } catch (error: any) {
      console.error('Error creating purchase order from extension:', error);
      return {
        success: false,
        error: error.message || 'An unknown error occurred while creating purchase order',
      };
    }
  }
}
