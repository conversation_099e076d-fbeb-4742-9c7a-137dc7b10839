/**
 * Direct test of low stock functionality using RPC
 * 
 * This script bypasses the AI and directly tests the low stock purchase order creation
 * using the RPC function we created.
 * 
 * To use:
 * 1. Open browser console on StayFu app
 * 2. Make sure you're logged in
 * 3. Copy and paste this script
 * 4. Run: testLowStockDirect()
 */

// Get Supabase client from the app
function getSupabaseClient() {
  // Try to get from window object (if available)
  if (window.supabase) {
    return window.supabase;
  }
  
  // Try to import from the app's modules
  try {
    // This might work if the module is available
    const { supabase } = require('../integrations/supabase/client');
    return supabase;
  } catch (e) {
    console.log('Could not import supabase client');
  }
  
  return null;
}

// Test the RPC function directly
async function testGetLowStockItems(userId = 'e4416a70-7490-4c40-a1c4-a5a6aeadf6ea') {
  const supabase = getSupabaseClient();
  
  if (!supabase) {
    console.error('❌ Could not get Supabase client');
    return;
  }
  
  console.log('🔍 Testing get_low_stock_items RPC function...');
  console.log(`👤 User ID: ${userId}`);
  
  try {
    const { data, error } = await supabase
      .rpc('get_low_stock_items', {
        user_id_param: userId,
        property_id_param: null
      });
    
    if (error) {
      console.error('❌ RPC Error:', error);
      return;
    }
    
    console.log(`✅ Found ${data?.length || 0} low stock items:`);
    if (data && data.length > 0) {
      data.forEach((item, index) => {
        console.log(`  ${index + 1}. ${item.name}`);
        console.log(`     Stock: ${item.quantity}/${item.min_quantity} (need ${item.min_quantity - item.quantity} more)`);
        console.log(`     Property: ${item.property_name}`);
        console.log(`     Price: $${item.price || 'N/A'}`);
        console.log('');
      });
    } else {
      console.log('   No low stock items found');
    }
    
    return data;
    
  } catch (error) {
    console.error('💥 Error calling RPC:', error);
  }
}

// Test creating a purchase order directly
async function testCreatePurchaseOrderDirect(userId = 'e4416a70-7490-4c40-a1c4-a5a6aeadf6ea') {
  const supabase = getSupabaseClient();
  
  if (!supabase) {
    console.error('❌ Could not get Supabase client');
    return;
  }
  
  console.log('🛒 Testing direct purchase order creation...');
  
  // First get low stock items
  const lowStockItems = await testGetLowStockItems(userId);
  
  if (!lowStockItems || lowStockItems.length === 0) {
    console.log('❌ No low stock items to create purchase order for');
    return;
  }
  
  // Group items by property
  const itemsByProperty = {};
  lowStockItems.forEach(item => {
    const propertyId = item.property_id;
    if (!itemsByProperty[propertyId]) {
      itemsByProperty[propertyId] = [];
    }
    itemsByProperty[propertyId].push(item);
  });
  
  console.log(`📋 Creating purchase orders for ${Object.keys(itemsByProperty).length} properties...`);
  
  const createdOrders = [];
  
  for (const [propertyId, items] of Object.entries(itemsByProperty)) {
    console.log(`\n🏠 Creating PO for property: ${items[0].property_name}`);
    
    // Calculate total price
    const totalPrice = items.reduce((sum, item) => {
      const quantity = Math.max(item.min_quantity - item.quantity, 1);
      return sum + (parseFloat(item.price || 0) * quantity);
    }, 0);
    
    try {
      // Create the purchase order
      const { data: order, error: orderError } = await supabase
        .from("purchase_orders")
        .insert({
          user_id: userId,
          property_id: propertyId,
          status: "pending",
          total_price: totalPrice > 0 ? totalPrice : null,
          notes: "Created by AI Assistant for low stock items"
        })
        .select()
        .single();

      if (orderError) {
        console.error(`❌ Error creating purchase order:`, orderError);
        continue;
      }
      
      console.log(`✅ Created purchase order ID: ${order.id}`);
      console.log(`💰 Total price: $${totalPrice.toFixed(2)}`);
      
      // Add items to the purchase order
      const orderItems = items.map((item) => ({
        purchase_order_id: order.id,
        inventory_item_id: item.id,
        item_name: item.name,
        quantity: Math.max(item.min_quantity - item.quantity, 1),
        price: parseFloat(item.price || 0),
        amazon_url: item.amazon_url,
        walmart_url: item.walmart_url
      }));

      const { error: itemsError } = await supabase
        .from("purchase_order_items")
        .insert(orderItems);

      if (itemsError) {
        console.error(`❌ Error adding items to purchase order:`, itemsError);
        continue;
      }
      
      console.log(`📦 Added ${orderItems.length} items to purchase order`);
      orderItems.forEach(item => {
        console.log(`   • ${item.item_name} (qty: ${item.quantity})`);
      });
      
      createdOrders.push(order);
      
    } catch (error) {
      console.error(`💥 Error creating purchase order for property ${propertyId}:`, error);
    }
  }
  
  console.log(`\n🎉 Successfully created ${createdOrders.length} purchase orders!`);
  return createdOrders;
}

// Main test function
async function testLowStockDirect() {
  console.log('🚀 Starting direct low stock purchase order test...');
  console.log('=' .repeat(60));
  
  // Test with known low stock user
  const userId = 'e4416a70-7490-4c40-a1c4-a5a6aeadf6ea';
  
  try {
    const orders = await testCreatePurchaseOrderDirect(userId);
    
    console.log('\n' + '🎯 TEST SUMMARY'.padStart(40, '=').padEnd(60, '='));
    console.log(`✅ Created ${orders?.length || 0} purchase orders`);
    console.log('✨ Test completed successfully!');
    
    return orders;
    
  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

// Export functions
window.testLowStockDirect = testLowStockDirect;
window.testGetLowStockItems = testGetLowStockItems;
window.testCreatePurchaseOrderDirect = testCreatePurchaseOrderDirect;

console.log(`
🧪 Direct Low Stock Test Suite Ready!

Available functions:
• testLowStockDirect() - Full test (recommended)
• testGetLowStockItems() - Just test RPC function
• testCreatePurchaseOrderDirect() - Test PO creation

Example usage:
testLowStockDirect()
`);
