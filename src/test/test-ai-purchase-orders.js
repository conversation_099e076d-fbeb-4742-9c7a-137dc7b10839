/**
 * Test script for AI Purchase Order functionality
 * 
 * This script tests the enhanced AI command processor's ability to handle
 * purchase order commands like "create a purchase order for all low stock items"
 * 
 * To use:
 * 1. Open browser console on StayFu app (localhost:8080 or deployed version)
 * 2. Make sure you're logged in
 * 3. Copy and paste this entire script
 * 4. Run: testPurchaseOrderCommands()
 */

// Test configuration
const TEST_CONFIG = {
  // Replace with your actual user ID - you can get this from localStorage or auth context
  userId: 'your-user-id-here', // Update this!

  // Known test user with low stock items
  testUserId: 'e4416a70-7490-4c40-a1c4-a5a6aeadf6ea', // Has Towel Set with 4/12 stock
  
  // Test commands to try
  commands: [
    "Create a purchase order for all low stock items",
    "Create a PO for all items that need restocking", 
    "Make a purchase order for towels and toilet paper",
    "Order more cleaning supplies",
    "Create purchase order for low stock items at Beach House",
    "Order supplies for Ocean View property",
    "Create PO for bathroom supplies",
    "Order everything we need for the downtown apartment"
  ]
};

// Function to call the AI command processor
async function callAICommand(command, userId) {
  try {
    console.log(`🤖 Testing command: "${command}"`);
    
    const response = await fetch('/functions/v1/ai-command-processor', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
      },
      body: JSON.stringify({
        command: command,
        userId: userId
      })
    });

    const result = await response.json();
    
    if (result.success) {
      console.log(`✅ Success: ${result.message}`);
      if (result.entityType === 'purchase_order') {
        console.log(`📋 Created purchase order ID: ${result.entityId}`);
      }
    } else {
      console.log(`❌ Failed: ${result.message}`);
    }
    
    return result;
    
  } catch (error) {
    console.error(`💥 Error testing command "${command}":`, error);
    return { success: false, message: error.message };
  }
}

// Function to test a single command
async function testCommand(command, userId = TEST_CONFIG.userId) {
  if (!userId || userId === 'your-user-id-here') {
    console.error('❌ Please update the userId in TEST_CONFIG before running tests');
    return;
  }
  
  console.log('\n' + '='.repeat(60));
  const result = await callAICommand(command, userId);
  console.log('='.repeat(60));
  
  return result;
}

// Function to test all purchase order commands
async function testPurchaseOrderCommands(userId = TEST_CONFIG.userId) {
  if (!userId || userId === 'your-user-id-here') {
    console.error('❌ Please update the userId in TEST_CONFIG before running tests');
    console.log('💡 You can find your user ID by running: localStorage.getItem("supabase.auth.user")');
    console.log('🧪 Or use the test user with known low stock items: testPurchaseOrderCommands(TEST_CONFIG.testUserId)');
    return;
  }
  
  console.log('🚀 Starting AI Purchase Order Command Tests');
  console.log(`👤 User ID: ${userId}`);
  console.log(`📝 Testing ${TEST_CONFIG.commands.length} commands\n`);
  
  const results = [];
  
  for (let i = 0; i < TEST_CONFIG.commands.length; i++) {
    const command = TEST_CONFIG.commands[i];
    console.log(`\n📋 Test ${i + 1}/${TEST_CONFIG.commands.length}`);
    
    const result = await testCommand(command, userId);
    results.push({ command, result });
    
    // Add a small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Summary
  console.log('\n' + '🎯 TEST SUMMARY'.padStart(40, '=').padEnd(60, '='));
  const successful = results.filter(r => r.result.success).length;
  const failed = results.length - successful;
  
  console.log(`✅ Successful: ${successful}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Success Rate: ${Math.round((successful / results.length) * 100)}%`);
  
  if (failed > 0) {
    console.log('\n❌ Failed Commands:');
    results.filter(r => !r.result.success).forEach(({ command, result }) => {
      console.log(`   • "${command}" - ${result.message}`);
    });
  }
  
  console.log('\n✨ Tests completed!');
  return results;
}

// Function to get current user ID from auth
function getCurrentUserId() {
  try {
    const authData = localStorage.getItem('supabase.auth.token');
    if (authData) {
      const parsed = JSON.parse(authData);
      return parsed.user?.id;
    }
  } catch (e) {
    console.log('Could not get user ID from localStorage');
  }
  
  // Try alternative method
  try {
    const user = JSON.parse(localStorage.getItem('supabase.auth.user') || '{}');
    return user.id;
  } catch (e) {
    console.log('Could not get user ID from alternative method');
  }
  
  return null;
}

// Helper function to set up the test with current user
function setupTest() {
  const userId = getCurrentUserId();
  if (userId) {
    TEST_CONFIG.userId = userId;
    console.log(`✅ Auto-detected user ID: ${userId}`);
    console.log('🎯 Ready to test! Run: testPurchaseOrderCommands()');
  } else {
    console.log('❌ Could not auto-detect user ID');
    console.log('💡 Please manually set TEST_CONFIG.userId and run testPurchaseOrderCommands()');
  }
  return userId;
}

// Auto-setup when script loads
console.log('🔧 Setting up AI Purchase Order tests...');
setupTest();

// Function to test with known low stock user
async function testWithKnownLowStockUser() {
  console.log('🧪 Testing with known low stock user (has Towel Set with 4/12 stock)');
  return await testPurchaseOrderCommands(TEST_CONFIG.testUserId);
}

// Export functions for manual use
window.testPurchaseOrderCommands = testPurchaseOrderCommands;
window.testCommand = testCommand;
window.setupTest = setupTest;
window.testWithKnownLowStockUser = testWithKnownLowStockUser;

console.log(`
🎯 AI Purchase Order Test Suite Ready!

Available functions:
• testPurchaseOrderCommands() - Run all tests
• testCommand("your command here") - Test a single command
• setupTest() - Re-detect user ID
• testWithKnownLowStockUser() - Test with user that has low stock items

Example usage:
testCommand("Create a purchase order for all low stock items")
testWithKnownLowStockUser() // Test with known low stock data
`);
