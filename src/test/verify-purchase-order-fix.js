/**
 * Verification script for the AI Purchase Order fix
 * 
 * This script verifies that:
 * 1. The RPC function get_low_stock_items works correctly
 * 2. Low stock items can be found
 * 3. Purchase orders can be created for low stock items
 * 
 * To use:
 * 1. Open browser console on StayFu app
 * 2. Make sure you're logged in
 * 3. Copy and paste this script
 * 4. Run: verifyPurchaseOrderFix()
 */

// Test configuration
const VERIFICATION_CONFIG = {
  // Known user with low stock items
  testUserId: 'e4416a70-7490-4c40-a1c4-a5a6aeadf6ea', // Has Towel Set with 4/12 stock
  expectedLowStockItem: {
    name: 'Towel Set',
    quantity: 4,
    min_quantity: 12,
    property_name: '<PERSON> (Molly)'
  }
};

// Get Supabase client
function getSupabaseClient() {
  // Try multiple ways to get the Supabase client
  if (window.supabase) {
    return window.supabase;
  }
  
  // Try to get from React context or global state
  if (window.__SUPABASE_CLIENT__) {
    return window.__SUPABASE_CLIENT__;
  }
  
  console.error('❌ Could not find Supabase client. Make sure you are on the StayFu app.');
  return null;
}

// Test 1: Verify RPC function works
async function testRPCFunction() {
  console.log('🔍 Test 1: Verifying RPC function get_low_stock_items...');
  
  const supabase = getSupabaseClient();
  if (!supabase) return false;
  
  try {
    const { data, error } = await supabase
      .rpc('get_low_stock_items', {
        user_id_param: VERIFICATION_CONFIG.testUserId,
        property_id_param: null
      });
    
    if (error) {
      console.error('❌ RPC function error:', error);
      return false;
    }
    
    if (!data || data.length === 0) {
      console.error('❌ No low stock items returned by RPC function');
      console.log('💡 This might mean:');
      console.log('   - The test user has no low stock items');
      console.log('   - The RPC function is not working correctly');
      return false;
    }
    
    console.log(`✅ RPC function returned ${data.length} low stock items`);
    
    // Verify expected item is present
    const expectedItem = data.find(item => 
      item.name === VERIFICATION_CONFIG.expectedLowStockItem.name
    );
    
    if (expectedItem) {
      console.log(`✅ Found expected low stock item: ${expectedItem.name}`);
      console.log(`   Stock: ${expectedItem.quantity}/${expectedItem.min_quantity}`);
      console.log(`   Property: ${expectedItem.property_name}`);
      console.log(`   Needs: ${expectedItem.min_quantity - expectedItem.quantity} more`);
    } else {
      console.warn('⚠️  Expected low stock item not found, but RPC function works');
    }
    
    return true;
    
  } catch (error) {
    console.error('💥 Error testing RPC function:', error);
    return false;
  }
}

// Test 2: Test AI command directly (if possible)
async function testAICommand() {
  console.log('\n🤖 Test 2: Testing AI command processor...');
  
  try {
    const response = await fetch('/functions/v1/ai-command-processor', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
      },
      body: JSON.stringify({
        command: 'Create a purchase order for all low stock items',
        userId: VERIFICATION_CONFIG.testUserId
      })
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ AI command succeeded:', result.message);
      if (result.entityType === 'purchase_order') {
        console.log(`📋 Created purchase order ID: ${result.entityId}`);
      }
      return true;
    } else {
      console.log('❌ AI command failed:', result.message);
      
      // Check if it's the expected "no items found" error
      if (result.message.includes('No low stock items found')) {
        console.log('💡 This suggests the RPC function is working but returning no results');
        console.log('   The Edge Function has been updated correctly');
      }
      
      return false;
    }
    
  } catch (error) {
    console.error('💥 Error testing AI command:', error);
    return false;
  }
}

// Test 3: Verify database state
async function testDatabaseState() {
  console.log('\n🗄️  Test 3: Verifying database state...');
  
  const supabase = getSupabaseClient();
  if (!supabase) return false;
  
  try {
    // Check if there are any low stock items in the database
    const { data: allItems, error: itemsError } = await supabase
      .from('inventory_items')
      .select('name, quantity, min_quantity, user_id')
      .lt('quantity', supabase.raw('min_quantity'))
      .gt('min_quantity', 0);
    
    if (itemsError) {
      console.error('❌ Error querying inventory items:', itemsError);
      return false;
    }
    
    console.log(`📊 Found ${allItems?.length || 0} low stock items in database`);
    
    if (allItems && allItems.length > 0) {
      console.log('Low stock items:');
      allItems.forEach(item => {
        console.log(`   • ${item.name}: ${item.quantity}/${item.min_quantity} (user: ${item.user_id})`);
      });
    }
    
    // Check if the test user specifically has low stock items
    const testUserItems = allItems?.filter(item => item.user_id === VERIFICATION_CONFIG.testUserId) || [];
    console.log(`📋 Test user has ${testUserItems.length} low stock items`);
    
    return true;
    
  } catch (error) {
    console.error('💥 Error checking database state:', error);
    return false;
  }
}

// Main verification function
async function verifyPurchaseOrderFix() {
  console.log('🚀 Starting Purchase Order Fix Verification...');
  console.log('=' .repeat(60));
  
  const results = {
    rpcFunction: false,
    aiCommand: false,
    databaseState: false
  };
  
  // Test 1: RPC Function
  results.rpcFunction = await testRPCFunction();
  
  // Test 2: AI Command (only if RPC works)
  if (results.rpcFunction) {
    results.aiCommand = await testAICommand();
  } else {
    console.log('\n⏭️  Skipping AI command test (RPC function failed)');
  }
  
  // Test 3: Database State
  results.databaseState = await testDatabaseState();
  
  // Summary
  console.log('\n' + '🎯 VERIFICATION SUMMARY'.padStart(40, '=').padEnd(60, '='));
  console.log(`✅ RPC Function: ${results.rpcFunction ? 'PASS' : 'FAIL'}`);
  console.log(`✅ AI Command: ${results.aiCommand ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Database State: ${results.databaseState ? 'PASS' : 'FAIL'}`);
  
  const passCount = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n📊 Overall: ${passCount}/${totalTests} tests passed`);
  
  if (results.rpcFunction && results.databaseState) {
    console.log('\n🎉 VERIFICATION SUCCESSFUL!');
    console.log('✅ The RPC function is working correctly');
    console.log('✅ Low stock items can be found in the database');
    
    if (results.aiCommand) {
      console.log('✅ The AI command processor is working correctly');
      console.log('🚀 The purchase order fix is fully deployed and working!');
    } else {
      console.log('⚠️  The AI command processor needs to be deployed');
      console.log('💡 The Edge Function update is ready but not yet deployed');
    }
  } else {
    console.log('\n❌ VERIFICATION FAILED');
    console.log('💡 Check the individual test results above for details');
  }
  
  return results;
}

// Helper function to manually test with current user
async function testWithCurrentUser() {
  const currentUserId = getCurrentUserId();
  if (!currentUserId) {
    console.error('❌ Could not detect current user ID');
    return;
  }
  
  console.log(`🧪 Testing with current user: ${currentUserId}`);
  
  const supabase = getSupabaseClient();
  if (!supabase) return;
  
  try {
    const { data, error } = await supabase
      .rpc('get_low_stock_items', {
        user_id_param: currentUserId,
        property_id_param: null
      });
    
    if (error) {
      console.error('❌ Error:', error);
      return;
    }
    
    console.log(`📋 Found ${data?.length || 0} low stock items for current user`);
    if (data && data.length > 0) {
      data.forEach(item => {
        console.log(`   • ${item.name}: ${item.quantity}/${item.min_quantity} at ${item.property_name}`);
      });
    } else {
      console.log('💡 No low stock items found. Make sure you have inventory items with quantity < min_quantity');
    }
    
  } catch (error) {
    console.error('💥 Error:', error);
  }
}

// Helper function to get current user ID
function getCurrentUserId() {
  try {
    const authData = localStorage.getItem('supabase.auth.token');
    if (authData) {
      const parsed = JSON.parse(authData);
      return parsed.user?.id;
    }
  } catch (e) {
    // Try alternative method
    try {
      const user = JSON.parse(localStorage.getItem('supabase.auth.user') || '{}');
      return user.id;
    } catch (e2) {
      return null;
    }
  }
  return null;
}

// Export functions
window.verifyPurchaseOrderFix = verifyPurchaseOrderFix;
window.testWithCurrentUser = testWithCurrentUser;
window.testRPCFunction = testRPCFunction;

console.log(`
🔍 Purchase Order Fix Verification Ready!

Available functions:
• verifyPurchaseOrderFix() - Run full verification (recommended)
• testWithCurrentUser() - Test with your current user account
• testRPCFunction() - Test just the RPC function

Example usage:
verifyPurchaseOrderFix()
`);

// Auto-run verification
console.log('🔧 Auto-running verification in 2 seconds...');
setTimeout(() => {
  verifyPurchaseOrderFix();
}, 2000);
