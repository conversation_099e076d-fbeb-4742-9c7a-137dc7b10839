
#root {
  width: 100%;
  min-height: 100vh;
}

/* Glass effect utilities */
.glass {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(12px);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.dark .glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.glass-hover:hover {
  background: rgba(255, 255, 255, 0.12);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .glass-hover:hover {
  background: rgba(0, 0, 0, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Remove fixed width container on mobile */
@media (max-width: 640px) {
  #root {
    padding: 0;
  }

  /* Ensure modals are properly sized and positioned on mobile */
  [role="dialog"] {
    margin: 0;
    max-height: 100vh !important;
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Mobile optimizations */
input, select, textarea, button {
  font-size: 16px !important; /* Prevents iOS zoom on focus */
}

/* Prevent content from being hidden under fixed bottom navigation */
main {
  padding-bottom: env(safe-area-inset-bottom, 0);
}

/* Override default scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 20px;
}

/* Add touch feedback */
@media (max-width: 768px) {
  .interactive {
    -webkit-tap-highlight-color: transparent;
    transition: opacity 0.2s;
  }

  .interactive:active {
    opacity: 0.7;
  }

  /* Improved touch targets for mobile */
  .button, button,
  [role="button"],
  .clickable,
  input[type="submit"],
  input[type="reset"],
  input[type="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Fix modal display on mobile */
  .overflow-y-auto {
    -webkit-overflow-scrolling: touch;
  }
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-slide-right {
  animation: slideRight 0.3s ease-out;
}

.animate-pulse-once {
  animation: pulse 2s ease-in-out 1;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideRight {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

/* Fix for iOS safe areas */
@supports (padding-top: env(safe-area-inset-top)) {
  body {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Helper for tooltips */
.tooltip-trigger {
  position: relative;
  display: inline-flex;
}

/* Focused elements accessibility improvement */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Improved form field styles on mobile */
@media (max-width: 768px) {
  .form-row {
    margin-bottom: 16px;
  }

  label {
    margin-bottom: 6px;
    display: block;
  }

  /* Full-width mobile dialogs */
  [data-state="open"][role="dialog"] {
    width: 100% !important;
    border-radius: 0 !important;
  }

  /* Ensure modals don't exceed viewport */
  [data-state="open"][role="dialog"] {
    max-height: 90vh !important;
    max-height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom)) !important;
  }
}
