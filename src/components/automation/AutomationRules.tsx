import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, Di<PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Plus, Trash2, Edit, RefreshCw } from 'lucide-react';
import { AutomationRule, useTaskAutomationQueryV2 } from '@/hooks/useTaskAutomationQueryV2';
import { usePropertiesQueryV2 } from '@/hooks/usePropertiesQueryV2';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

const AutomationRules: React.FC = () => {
  const {
    rules,
    loading,
    error,
    createRule,
    updateRule,
    deleteRule,
    processAllBookings
  } = useTaskAutomationQueryV2();

  const { properties } = usePropertiesQueryV2();

  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingRule, setEditingRule] = useState<AutomationRule | null>(null);
  const [processingBookings, setProcessingBookings] = useState(false);

  // Form state
  const [name, setName] = useState('');
  const [triggerType, setTriggerType] = useState<'check_in' | 'check_out'>('check_out');
  const [taskType, setTaskType] = useState<'cleaning' | 'inspection' | 'maintenance'>('cleaning');
  const [timeOffset, setTimeOffset] = useState(0);
  const [selectedPropertyIds, setSelectedPropertyIds] = useState<string[]>([]);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [severity, setSeverity] = useState<'low' | 'medium' | 'high' | 'critical'>('medium');

  const resetForm = () => {
    setName('');
    setTriggerType('check_out');
    setTaskType('cleaning');
    setTimeOffset(0);
    setSelectedPropertyIds([]);
    setTitle('');
    setDescription('');
    setSeverity('medium');
    setEditingRule(null);
  };

  const handleOpenDialog = (rule?: AutomationRule) => {
    if (rule) {
      setEditingRule(rule);
      setName(rule.name);
      setTriggerType(rule.trigger_type as 'check_in' | 'check_out');
      setTaskType(rule.task_type as 'cleaning' | 'inspection' | 'maintenance');
      setTimeOffset(rule.time_offset);
      setSelectedPropertyIds(rule.property_ids || []);
      setTitle(rule.title);
      setDescription(rule.description || '');
      setSeverity(rule.severity as 'low' | 'medium' | 'high' | 'critical');
    } else {
      resetForm();
    }

    setDialogOpen(true);
  };

  const handleSubmit = async () => {
    if (!name || !title) {
      toast.error('Name and title are required');
      return;
    }

    const ruleData = {
      name,
      trigger_type: triggerType,
      task_type: taskType,
      time_offset: timeOffset,
      property_ids: selectedPropertyIds.length > 0 ? selectedPropertyIds : null,
      title,
      description,
      severity,
      assigned_to: null // We'll add assignment functionality later
    };

    if (editingRule) {
      await updateRule(editingRule.id, ruleData);
    } else {
      await createRule(ruleData);
    }

    setDialogOpen(false);
    resetForm();
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this automation rule?')) {
      await deleteRule(id);
    }
  };

  const handleProcessAllBookings = async () => {
    if (processingBookings) return;

    setProcessingBookings(true);
    try {
      // Show loading toast
      toast({
        title: 'Processing automation rules...',
        description: 'Please wait while we process all bookings.',
        duration: 10000
      });

      const success = await processAllBookings();

      if (success) {
        toast({
          title: 'Success!',
          description: 'Automation rules processed successfully. Tasks have been created.',
          duration: 5000
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to process automation rules. Please try again.',
          variant: 'destructive',
          duration: 5000
        });
      }
    } catch (error) {
      console.error('[AutomationRules] Error processing bookings:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while processing automation rules.',
        variant: 'destructive',
        duration: 5000
      });
    } finally {
      setProcessingBookings(false);
    }
  };



  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Task Automation Rules</h2>
          <p className="text-muted-foreground">
            Create rules to automatically generate tasks based on bookings
          </p>
          <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md text-sm">
            <p className="text-blue-800 font-medium">Important:</p>
            <p className="text-blue-700">
              Make sure to set the correct timezone and check-in/check-out times for each property in the property settings.
              These settings are used to calculate the exact time when tasks should be created.
            </p>
          </div>
        </div>
        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            onClick={handleProcessAllBookings}
            disabled={processingBookings || rules.length === 0}
            className="flex-1 md:flex-none"
          >
            {processingBookings ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Run Automation
          </Button>
          <Button
            onClick={() => handleOpenDialog()}
            className="flex-1 md:flex-none"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Rule
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : error ? (
        <Card>
          <CardContent className="p-6">
            <p className="text-red-500">{error}</p>
          </CardContent>
        </Card>
      ) : rules.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground">No automation rules found. Create your first rule to get started.</p>
            <Button className="mt-4" onClick={() => handleOpenDialog()}>
              <Plus className="h-4 w-4 mr-2" />
              Add Rule
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {rules.map(rule => (
            <Card key={rule.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <CardTitle>{rule.name}</CardTitle>
                  <div className="flex gap-1">
                    <Button variant="ghost" size="sm" onClick={() => handleOpenDialog(rule)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDelete(rule.id)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <CardDescription>
                  {rule.trigger_type === 'check_in' ? 'After check-in' : 'After check-out'}
                  {rule.time_offset > 0 ? ` + ${rule.time_offset} hours` :
                   rule.time_offset < 0 ? ` - ${Math.abs(rule.time_offset)} hours` : ''}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div>
                    <span className="font-medium">Task Type:</span> {rule.task_type}
                  </div>
                  <div>
                    <span className="font-medium">Title:</span> {rule.title}
                  </div>
                  {rule.description && (
                    <div>
                      <span className="font-medium">Description:</span> {rule.description}
                    </div>
                  )}
                  <div>
                    <span className="font-medium">Severity:</span>
                    <Badge
                      className={
                        rule.severity === 'critical' ? 'bg-red-500' :
                        rule.severity === 'high' ? 'bg-orange-500' :
                        rule.severity === 'medium' ? 'bg-yellow-500' :
                        'bg-green-500'
                      }
                    >
                      {rule.severity}
                    </Badge>
                  </div>
                  {rule.property_ids && (
                    <div>
                      <span className="font-medium">Properties:</span>{' '}
                      {rule.property_ids.length === 0 ? 'All properties' :
                        `${rule.property_ids.length} selected`}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{editingRule ? 'Edit Automation Rule' : 'Create Automation Rule'}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Rule Name</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="e.g., Post-checkout cleaning"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="trigger-type">Trigger</Label>
                <Select
                  value={triggerType}
                  onValueChange={(value) => setTriggerType(value as 'check_in' | 'check_out')}
                >
                  <SelectTrigger id="trigger-type">
                    <SelectValue placeholder="Select trigger" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="check_in">After Check-in</SelectItem>
                    <SelectItem value="check_out">After Check-out</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="time-offset">Time Offset (hours)</Label>
                <Input
                  id="time-offset"
                  type="number"
                  value={timeOffset}
                  onChange={(e) => setTimeOffset(parseInt(e.target.value))}
                  placeholder="0"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="task-type">Task Type</Label>
              <Select
                value={taskType}
                onValueChange={(value) => setTaskType(value as 'cleaning' | 'inspection' | 'maintenance')}
              >
                <SelectTrigger id="task-type">
                  <SelectValue placeholder="Select task type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cleaning">Cleaning</SelectItem>
                  <SelectItem value="inspection">Inspection</SelectItem>
                  <SelectItem value="maintenance">Maintenance</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="properties">Properties (leave empty for all)</Label>
              <Select
                value={selectedPropertyIds.length > 0 ? 'selected' : 'all'}
                onValueChange={(value) => {
                  if (value === 'all') {
                    setSelectedPropertyIds([]);
                  }
                }}
              >
                <SelectTrigger id="properties">
                  <SelectValue placeholder="Select properties" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Properties</SelectItem>
                  <SelectItem value="selected" disabled={selectedPropertyIds.length === 0}>
                    {selectedPropertyIds.length} Properties Selected
                  </SelectItem>
                </SelectContent>
              </Select>

              {properties && properties.length > 0 && (
                <div className="mt-2 max-h-32 overflow-y-auto border rounded-md p-2">
                  {properties.map(property => (
                    <div key={property.id} className="flex items-center space-x-2 py-1">
                      <input
                        type="checkbox"
                        id={`property-${property.id}`}
                        checked={selectedPropertyIds.includes(property.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedPropertyIds(prev => [...prev, property.id]);
                          } else {
                            setSelectedPropertyIds(prev => prev.filter(id => id !== property.id));
                          }
                        }}
                      />
                      <label htmlFor={`property-${property.id}`}>{property.name}</label>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="title">Task Title</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="e.g., Clean {property}"
              />
              <p className="text-xs text-muted-foreground">
                Use {'{property}'} to include the property name
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Task Description (optional)</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="e.g., Standard cleaning for {property}"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="severity">Severity</Label>
              <Select
                value={severity}
                onValueChange={(value) => setSeverity(value as 'low' | 'medium' | 'high' | 'critical')}
              >
                <SelectTrigger id="severity">
                  <SelectValue placeholder="Select severity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSubmit}>{editingRule ? 'Update' : 'Create'}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AutomationRules;
