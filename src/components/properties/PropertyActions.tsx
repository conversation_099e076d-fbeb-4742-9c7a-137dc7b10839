
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Pencil, Trash2 } from 'lucide-react';
import { usePermissions } from '@/hooks/usePermissions';
import { PermissionType } from '@/types/auth';

interface PropertyActionsProps {
  propertyId: string;
  onEdit: () => void;
}

const PropertyActions: React.FC<PropertyActionsProps> = ({ propertyId, onEdit }) => {
  const navigate = useNavigate();
  const { hasPermission } = usePermissions();

  const handleDelete = async () => {
    if (!hasPermission(PermissionType.MANAGE_PROPERTIES)) {
      toast.error('You do not have permission to delete properties');
      return;
    }

    const confirmed = window.confirm('Are you sure you want to delete this property? This action cannot be undone. All associated inventory items, maintenance tasks, and damage reports will also be deleted.');
    if (!confirmed) return;

    try {
      toast.loading('Deleting property and associated data...', { id: 'delete-property' });

      // Use direct SQL query to delete the property and all related data
      const { data, error } = await supabase.rpc('delete_property_cascade', {
        property_id_param: propertyId
      });

      if (error) {
        console.error('Error in delete_property_cascade function:', error);
        toast.error(`Failed to delete property: ${error.message}`, { id: 'delete-property' });

        // Fallback to manual deletion
        console.log('Falling back to manual deletion...');

        // Use a direct SQL query to delete everything in the correct order
        const { error: sqlError } = await supabase.functions.invoke('execute-sql', {
          body: {
            query: `
              -- Delete maintenance tasks
              DELETE FROM maintenance_tasks WHERE property_id = '${propertyId}';

              -- Delete inventory items
              DELETE FROM inventory_items WHERE property_id = '${propertyId}';

              -- Delete team_properties associations
              DELETE FROM team_properties WHERE property_id = '${propertyId}';

              -- Get damage report IDs
              WITH damage_report_ids AS (
                SELECT id FROM damage_reports WHERE property_id = '${propertyId}'
              )
              -- Delete damage photos
              DELETE FROM damage_photos WHERE damage_report_id IN (SELECT id FROM damage_report_ids);

              -- Delete damage notes
              DELETE FROM damage_notes WHERE damage_report_id IN (SELECT id FROM damage_report_ids);

              -- Delete damage reports
              DELETE FROM damage_reports WHERE property_id = '${propertyId}';

              -- Finally delete the property
              DELETE FROM properties WHERE id = '${propertyId}';
            `
          }
        });

        if (sqlError) {
          console.error('Error executing SQL deletion:', sqlError);
          toast.error(`Failed to delete property: ${sqlError.message}`, { id: 'delete-property' });
          return;
        }
      }

      toast.success('Property and all associated data deleted successfully', { id: 'delete-property' });
      navigate('/properties');
    } catch (error: any) {
      console.error('Error deleting property:', error);
      toast.error(`Failed to delete property: ${error.message || 'Unknown error'}`, { id: 'delete-property' });
    }
  };

  // Log permission status for debugging
  const hasManagePermission = hasPermission(PermissionType.MANAGE_PROPERTIES);
  console.log('User has MANAGE_PROPERTIES permission:', hasManagePermission);

  return (
    <div className="flex space-x-2">
      <Button
        variant="outline"
        size="sm"
        onClick={onEdit}
        disabled={!hasManagePermission}
      >
        <Pencil className="h-4 w-4 mr-1" />
        Edit
      </Button>
      <Button
        variant="destructive"
        size="sm"
        onClick={handleDelete}
        disabled={!hasManagePermission}
      >
        <Trash2 className="h-4 w-4 mr-1" />
        Delete
      </Button>
    </div>
  );
};

export default PropertyActions;
