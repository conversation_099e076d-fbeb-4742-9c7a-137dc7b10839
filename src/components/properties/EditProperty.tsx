
import React from 'react';
import { useParams } from 'react-router-dom';
import { <PERSON>, Card<PERSON>eader, CardContent } from '@/components/ui/card';

const EditProperty = () => {
  const { id } = useParams<{ id: string }>();
  
  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Edit Property</h1>
      
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">Property Information</h2>
        </CardHeader>
        <CardContent>
          <p>Property ID: {id}</p>
          <p className="text-muted-foreground">
            Property edit form will be implemented here.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default EditProperty;
