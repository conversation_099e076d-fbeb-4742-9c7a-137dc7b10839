
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Property } from '@/hooks/useProperties';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import PropertyActions from './PropertyActions';

interface PropertyDetailHeaderProps {
  property: Property;
  onEdit: () => void;
}

const PropertyDetailHeader: React.FC<PropertyDetailHeaderProps> = ({
  property,
  onEdit
}) => {
  const navigate = useNavigate();

  return (
    <div className="flex justify-between items-center mb-8">
      <Button variant="outline" onClick={() => navigate('/properties')}>
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Properties
      </Button>

      <PropertyActions propertyId={property.id} onEdit={onEdit} />
    </div>
  );
};

export default PropertyDetailHeader;
