
import React, { useState } from 'react';
import { Plus, X } from 'lucide-react';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { CollectionWithBudget } from './PropertyCard';

interface CollectionManagerProps {
  collections: CollectionWithBudget[];
  onAddCollection?: (collection: CollectionWithBudget) => void;
  onRemoveCollection?: (index: number) => void;
  onEditCollection?: (index: number, collection: CollectionWithBudget) => void;
  onChange?: (collections: CollectionWithBudget[]) => void;
}

const CollectionManager: React.FC<CollectionManagerProps> = ({ 
  collections, 
  onAddCollection,
  onRemoveCollection,
  onEditCollection,
  onChange 
}) => {
  const [newCollection, setNewCollection] = useState('');
  const [newBudget, setNewBudget] = useState<number | undefined>(undefined);
  const [showBudgetInput, setShowBudgetInput] = useState(false);

  const handleAddCollection = () => {
    if (newCollection.trim() === '') return;
    
    const newCollectionObject = { 
      name: newCollection,
      budget: newBudget
    };
    
    if (onAddCollection) {
      onAddCollection(newCollectionObject);
    } else if (onChange) {
      // Check if collection already exists (case insensitive)
      if (!collections.some(c => c.name.toLowerCase() === newCollection.toLowerCase())) {
        const updatedCollections = [...collections, newCollectionObject];
        onChange(updatedCollections);
      } else {
        // If already exists, just update the budget if different
        const updatedCollections = collections.map(c => 
          c.name.toLowerCase() === newCollection.toLowerCase() 
            ? { ...c, budget: newBudget } 
            : c
        );
        onChange(updatedCollections);
      }
    }
    
    // Reset form
    setNewCollection('');
    setNewBudget(undefined);
    setShowBudgetInput(false);
  };

  const handleRemoveCollection = (collectionName: string, index: number) => {
    if (onRemoveCollection) {
      onRemoveCollection(index);
    } else if (onChange) {
      const updatedCollections = collections.filter(c => c.name !== collectionName);
      onChange(updatedCollections);
    }
  };

  const handleBudgetChange = (collectionName: string, budget: number | undefined, index: number) => {
    if (onEditCollection) {
      const collection = collections.find(c => c.name === collectionName);
      if (collection) {
        onEditCollection(index, { ...collection, budget });
      }
    } else if (onChange) {
      const updatedCollections = collections.map(c => 
        c.name === collectionName ? { ...c, budget } : c
      );
      onChange(updatedCollections);
    }
  };

  return (
    <div className="space-y-3">
      <Label htmlFor="collections">Collections</Label>
      <div className="flex flex-wrap gap-2 mb-2">
        {collections.map((collection, index) => (
          <div 
            key={index} 
            className="flex items-center gap-1 bg-primary/10 text-primary px-2 py-1 rounded-full"
          >
            <span>{collection.name}</span>
            {collection.budget !== undefined && (
              <span className="text-xs text-primary/80">${collection.budget.toLocaleString()}</span>
            )}
            <button
              type="button"
              onClick={() => handleRemoveCollection(collection.name, index)}
              className="text-primary/70 hover:text-primary"
            >
              <X size={14} />
            </button>
          </div>
        ))}
      </div>
      
      {collections.length > 0 && (
        <div className="space-y-3 p-3 border border-border rounded-md mb-3">
          <Label>Collection Budgets</Label>
          {collections.map((collection, index) => (
            <div key={index} className="flex items-center gap-2">
              <span className="min-w-[120px]">{collection.name}:</span>
              <div className="relative flex-1">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-muted-foreground">$</span>
                </div>
                <Input
                  type="number"
                  placeholder="Budget"
                  min={0}
                  className="pl-8"
                  value={collection.budget !== undefined ? collection.budget : ''}
                  onChange={(e) => handleBudgetChange(
                    collection.name, 
                    e.target.value ? parseFloat(e.target.value) : undefined,
                    index
                  )}
                />
              </div>
            </div>
          ))}
        </div>
      )}
      
      <div className="flex flex-col gap-2">
        <div className="flex gap-2">
          <Input
            id="collections"
            value={newCollection}
            onChange={(e) => setNewCollection(e.target.value)}
            placeholder="Add collection"
            className="flex-1"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !showBudgetInput) {
                e.preventDefault();
                setShowBudgetInput(true);
              }
            }}
          />
          {!showBudgetInput ? (
            <Button 
              type="button" 
              size="sm"
              onClick={() => setShowBudgetInput(true)}
              className="flex items-center gap-1"
            >
              <Plus size={16} />
              Add
            </Button>
          ) : (
            <Button 
              type="button" 
              size="sm"
              onClick={handleAddCollection}
              className="flex items-center gap-1"
              variant="outline"
            >
              <X size={16} />
              Cancel
            </Button>
          )}
        </div>
        
        {showBudgetInput && (
          <div className="flex gap-2 items-center">
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-muted-foreground">$</span>
              </div>
              <Input
                type="number"
                placeholder="Budget (optional)"
                min={0}
                className="pl-8"
                value={newBudget !== undefined ? newBudget : ''}
                onChange={(e) => setNewBudget(e.target.value ? parseFloat(e.target.value) : undefined)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddCollection();
                  }
                }}
              />
            </div>
            <Button 
              type="button" 
              size="sm"
              onClick={handleAddCollection}
              className="flex items-center gap-1"
            >
              <Plus size={16} />
              Add
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CollectionManager;
