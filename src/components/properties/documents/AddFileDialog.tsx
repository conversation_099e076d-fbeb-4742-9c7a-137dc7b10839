import React, { useState, useRef } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { FileUp, Loader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface AddFileDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  propertyId: string;
  userId: string;
  onSuccess: () => void;
}

const AddFileDialog: React.FC<AddFileDialogProps> = ({
  open,
  onOpenChange,
  propertyId,
  userId,
  onSuccess
}) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [displayName, setDisplayName] = useState('');
  const [caption, setCaption] = useState('');
  const [isPrivate, setIsPrivate] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [currentFileIndex, setCurrentFileIndex] = useState(0);
  const [totalFiles, setTotalFiles] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files);
      setSelectedFiles(files);
      setTotalFiles(files.length);

      // If only one file is selected, set the display name from the file name
      if (files.length === 1 && !displayName) {
        const file = files[0];
        // Remove file extension and replace underscores/hyphens with spaces
        const nameWithoutExtension = file.name.replace(/\.[^/.]+$/, "")
          .replace(/[_-]/g, " ");
        setDisplayName(nameWithoutExtension);
      } else {
        // Clear display name if multiple files are selected
        setDisplayName('');
      }
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const files = Array.from(e.dataTransfer.files);
      setSelectedFiles(files);
      setTotalFiles(files.length);

      // If only one file is selected, set the display name from the file name
      if (files.length === 1 && !displayName) {
        const file = files[0];
        // Remove file extension and replace underscores/hyphens with spaces
        const nameWithoutExtension = file.name.replace(/\.[^/.]+$/, "")
          .replace(/[_-]/g, " ");
        setDisplayName(nameWithoutExtension);
      } else {
        // Clear display name if multiple files are selected
        setDisplayName('');
      }
    }
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0 || !propertyId || !userId) {
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setCurrentFileIndex(0);

    try {
      // Upload each file sequentially
      for (let i = 0; i < selectedFiles.length; i++) {
        setCurrentFileIndex(i);
        const file = selectedFiles[i];

        // Generate a unique file path
        const timestamp = new Date().getTime();
        const randomString = Math.random().toString(36).substring(2, 10);
        const fileName = `${timestamp}-${randomString}-${file.name.replace(/\s+/g, '_')}`;
        const filePath = `${propertyId}/${fileName}`;

        // Upload file to storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('property-files')
          .upload(filePath, file, {
            cacheControl: '3600',
            upsert: true,
            onUploadProgress: (progress) => {
              const percent = Math.round((progress.loaded / progress.total) * 100);
              setUploadProgress(percent);
            }
          });

        if (uploadError) {
          throw uploadError;
        }

        // For single file upload, use the provided display name and caption
        // For multiple files, use the file name as display name and no caption
        const fileDisplayName = selectedFiles.length === 1
          ? (displayName || file.name)
          : file.name.replace(/\.[^/.]+$/, "").replace(/[_-]/g, " ");

        const fileCaption = selectedFiles.length === 1 ? caption : '';

        // Create database record
        const { error: dbError } = await supabase
          .from('property_files')
          .insert({
            property_id: propertyId,
            user_id: userId,
            filename: file.name,
            display_name: fileDisplayName,
            caption: fileCaption,
            file_path: filePath,
            file_type: file.type || 'application/octet-stream',
            file_size: file.size,
            is_private: isPrivate
          });

        if (dbError) {
          throw dbError;
        }
      }

      toast.success(
        selectedFiles.length === 1
          ? 'File uploaded successfully'
          : `${selectedFiles.length} files uploaded successfully`
      );

      onSuccess();
      setSelectedFiles([]);
      setDisplayName('');
      setCaption('');
      setIsPrivate(false);
    } catch (error) {
      console.error('Error uploading files:', error);
      toast.error('Failed to upload files');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
      setCurrentFileIndex(0);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Upload Files</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div
            className="border-2 border-dashed rounded-lg p-6 text-center cursor-pointer hover:bg-gray-50 transition-colors"
            onClick={() => fileInputRef.current?.click()}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <FileUp className="h-10 w-10 mx-auto text-gray-400 mb-2" />
            <p className="text-sm text-muted-foreground mb-2">
              {selectedFiles.length > 0
                ? `${selectedFiles.length} file${selectedFiles.length > 1 ? 's' : ''} selected`
                : "Click to upload or drag and drop files"}
            </p>
            {selectedFiles.length > 0 && (
              <div className="max-h-32 overflow-y-auto text-left mt-2 border rounded p-2">
                {selectedFiles.map((file, index) => (
                  <div key={index} className="text-xs flex justify-between items-center py-1">
                    <span className="truncate max-w-[200px]">{file.name}</span>
                    <span className="text-muted-foreground ml-2">
                      {(file.size / 1024).toFixed(1)} KB
                    </span>
                  </div>
                ))}
              </div>
            )}
            <input
              ref={fileInputRef}
              type="file"
              multiple
              className="hidden"
              onChange={handleFileChange}
              disabled={isUploading}
            />
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              type="button"
              disabled={isUploading}
              onClick={(e) => {
                e.stopPropagation();
                fileInputRef.current?.click();
              }}
            >
              Select Files
            </Button>
          </div>

          {isUploading && (
            <div className="space-y-2">
              <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                <div
                  className="bg-primary h-2.5 rounded-full"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <p className="text-xs text-center">
                Uploading file {currentFileIndex + 1} of {totalFiles} ({uploadProgress}%)
              </p>
            </div>
          )}

          {selectedFiles.length === 1 && (
            <>
              <div className="space-y-2">
                <Label htmlFor="display-name">Display Name (Optional)</Label>
                <Input
                  id="display-name"
                  value={displayName}
                  onChange={(e) => setDisplayName(e.target.value)}
                  placeholder="Enter a name for this file"
                  disabled={isUploading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="caption">Caption (Optional)</Label>
                <Textarea
                  id="caption"
                  value={caption}
                  onChange={(e) => setCaption(e.target.value)}
                  placeholder="Add a description for this file"
                  disabled={isUploading}
                />
              </div>
            </>
          )}

          {selectedFiles.length > 1 && (
            <div className="p-3 bg-muted/30 rounded-lg text-sm">
              <p>Multiple files selected. Each file will be uploaded with its filename as the display name.</p>
              <p className="mt-1 text-muted-foreground">You can edit individual file details after upload.</p>
            </div>
          )}

          <div className="flex items-center space-x-2">
            <Checkbox
              id="is-private"
              checked={isPrivate}
              onCheckedChange={(checked) => setIsPrivate(!!checked)}
              disabled={isUploading}
            />
            <Label htmlFor="is-private">
              Make this file private (only visible to you)
            </Label>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isUploading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleUpload}
            disabled={selectedFiles.length === 0 || isUploading}
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Uploading...
              </>
            ) : (
              selectedFiles.length === 1 ? 'Upload File' : `Upload ${selectedFiles.length} Files`
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddFileDialog;
