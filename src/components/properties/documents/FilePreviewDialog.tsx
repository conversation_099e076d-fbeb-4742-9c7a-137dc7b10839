import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>eader, 
  <PERSON><PERSON><PERSON><PERSON>le, 
  DialogFooter 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Lock, 
  Globe, 
  Download, 
  FileIcon,
  Image,
  FileText,
  FileType,
  Loader2
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface PropertyFile {
  id: string;
  property_id: string;
  user_id: string;
  filename: string;
  display_name?: string;
  caption?: string;
  file_path: string;
  file_type: string;
  file_size: number;
  is_private: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

interface FilePreviewDialogProps {
  file: PropertyFile | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const FilePreviewDialog: React.FC<FilePreviewDialogProps> = ({
  file,
  open,
  onOpenChange
}) => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (file && open) {
      loadPreview();
    } else {
      // Clean up preview URL when dialog closes
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
        setPreviewUrl(null);
      }
    }
  }, [file, open]);

  const loadPreview = async () => {
    if (!file) return;
    
    setLoading(true);
    try {
      const { data, error } = await supabase.storage
        .from('property-files')
        .download(file.file_path);

      if (error) {
        throw error;
      }

      const url = URL.createObjectURL(data);
      setPreviewUrl(url);
    } catch (error) {
      console.error('Error loading file preview:', error);
      toast.error('Failed to load file preview');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async () => {
    if (!file) return;
    
    try {
      if (previewUrl) {
        // Create a download link
        const a = document.createElement('a');
        a.href = previewUrl;
        a.download = file.filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      } else {
        // If preview URL is not available, download directly
        const { data, error } = await supabase.storage
          .from('property-files')
          .download(file.file_path);

        if (error) {
          throw error;
        }

        const url = URL.createObjectURL(data);
        const a = document.createElement('a');
        a.href = url;
        a.download = file.filename;
        document.body.appendChild(a);
        a.click();
        URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error downloading file:', error);
      toast.error('Failed to download file');
    }
  };

  const renderPreview = () => {
    if (!file || !previewUrl) return null;

    if (file.file_type.startsWith('image/')) {
      return (
        <div className="flex justify-center">
          <img 
            src={previewUrl} 
            alt={file.display_name || file.filename} 
            className="max-w-full max-h-[500px] object-contain"
          />
        </div>
      );
    } else if (file.file_type === 'application/pdf') {
      return (
        <iframe 
          src={previewUrl} 
          className="w-full h-[500px]" 
          title={file.display_name || file.filename}
        />
      );
    } else if (file.file_type.includes('text/') || 
               file.file_type.includes('json') || 
               file.file_type.includes('xml')) {
      return (
        <div className="border rounded p-4 max-h-[500px] overflow-auto bg-muted/30">
          <pre className="whitespace-pre-wrap break-words">
            <code>Loading text content...</code>
          </pre>
        </div>
      );
    } else {
      return (
        <div className="text-center py-8">
          <FileIcon className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
          <p className="text-muted-foreground">
            Preview not available for this file type. Please download the file to view it.
          </p>
        </div>
      );
    }
  };

  if (!file) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle>{file.display_name || file.filename}</DialogTitle>
            <Badge variant={file.is_private ? "outline" : "secondary"}>
              {file.is_private ? (
                <><Lock className="h-3 w-3 mr-1" /> Private</>
              ) : (
                <><Globe className="h-3 w-3 mr-1" /> Shared</>
              )}
            </Badge>
          </div>
          {file.caption && (
            <p className="text-sm text-muted-foreground mt-1">{file.caption}</p>
          )}
          <div className="text-xs text-muted-foreground mt-1">
            Uploaded {formatDistanceToNow(new Date(file.created_at), { addSuffix: true })}
            {file.created_by && ` by ${file.created_by}`}
          </div>
        </DialogHeader>

        <div className="py-4">
          {loading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            renderPreview()
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleDownload}>
            <Download className="mr-2 h-4 w-4" />
            Download
          </Button>
          <Button onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FilePreviewDialog;
