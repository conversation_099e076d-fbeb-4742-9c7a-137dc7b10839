import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Eye, 
  Edit, 
  Trash2, 
  Lock, 
  Globe 
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import ViewDocumentDialog from './ViewDocumentDialog';

interface PropertyDocument {
  id: string;
  property_id: string;
  user_id: string;
  title: string;
  content: string;
  is_private: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

interface PropertyDocumentsListProps {
  documents: PropertyDocument[];
  userId?: string;
  isPropertyOwner: boolean;
  onEdit: (document: PropertyDocument) => void;
  onDelete: (documentId: string) => void;
}

const PropertyDocumentsList: React.FC<PropertyDocumentsListProps> = ({
  documents,
  userId,
  isPropertyOwner,
  onEdit,
  onDelete
}) => {
  const [viewingDocument, setViewingDocument] = React.useState<PropertyDocument | null>(null);

  const canEditDocument = (document: PropertyDocument) => {
    return isPropertyOwner || document.user_id === userId;
  };

  const canDeleteDocument = (document: PropertyDocument) => {
    return isPropertyOwner || document.user_id === userId;
  };

  if (documents.length === 0) {
    return (
      <div className="text-center py-8 bg-muted/30 rounded-lg">
        <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
        <p className="text-muted-foreground">No documents have been added yet.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {documents.map((document) => (
        <div 
          key={document.id} 
          className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
        >
          <div className="flex justify-between items-start">
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-full bg-muted">
                <FileText className="h-4 w-4 text-primary" />
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <h3 className="font-medium">{document.title}</h3>
                  <Badge variant={document.is_private ? "outline" : "secondary"}>
                    {document.is_private ? (
                      <><Lock className="h-3 w-3 mr-1" /> Private</>
                    ) : (
                      <><Globe className="h-3 w-3 mr-1" /> Shared</>
                    )}
                  </Badge>
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Created by {document.created_by} • Updated {formatDistanceToNow(new Date(document.updated_at), { addSuffix: true })}
                </div>
              </div>
            </div>
            <div className="flex gap-2">
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => setViewingDocument(document)}
                title="View document"
              >
                <Eye className="h-4 w-4" />
              </Button>
              
              {canEditDocument(document) && (
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => onEdit(document)}
                  title="Edit document"
                >
                  <Edit className="h-4 w-4" />
                </Button>
              )}
              
              {canDeleteDocument(document) && (
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => onDelete(document.id)}
                  title="Delete document"
                >
                  <Trash2 className="h-4 w-4 text-destructive" />
                </Button>
              )}
            </div>
          </div>
        </div>
      ))}

      {viewingDocument && (
        <ViewDocumentDialog
          document={viewingDocument}
          open={!!viewingDocument}
          onOpenChange={(open) => !open && setViewingDocument(null)}
        />
      )}
    </div>
  );
};

export default PropertyDocumentsList;
