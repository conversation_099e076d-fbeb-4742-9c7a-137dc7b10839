import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Lock, Globe } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import 'react-quill/dist/quill.snow.css';

interface PropertyDocument {
  id: string;
  property_id: string;
  user_id: string;
  title: string;
  content: string;
  is_private: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

interface ViewDocumentDialogProps {
  document: PropertyDocument;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const ViewDocumentDialog: React.FC<ViewDocumentDialogProps> = ({
  document,
  open,
  onOpenChange
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle>{document.title}</DialogTitle>
            <Badge variant={document.is_private ? "outline" : "secondary"}>
              {document.is_private ? (
                <><Lock className="h-3 w-3 mr-1" /> Private</>
              ) : (
                <><Globe className="h-3 w-3 mr-1" /> Shared</>
              )}
            </Badge>
          </div>
          <div className="text-xs text-muted-foreground mt-1">
            {document.created_by && `Created by ${document.created_by} • `}
            Updated {formatDistanceToNow(new Date(document.updated_at), { addSuffix: true })}
          </div>
        </DialogHeader>

        <div className="py-4">
          <div
            className="prose max-w-none dark:prose-invert ql-editor"
            dangerouslySetInnerHTML={{ __html: document.content }}
          />
        </div>

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ViewDocumentDialog;
