import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>eader, 
  <PERSON><PERSON><PERSON><PERSON>le, 
  DialogFooter 
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2, FileIcon, Image, FileText, FileType } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';

interface PropertyFile {
  id: string;
  property_id: string;
  user_id: string;
  filename: string;
  display_name?: string;
  caption?: string;
  file_path: string;
  file_type: string;
  file_size: number;
  is_private: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

interface EditFileDialogProps {
  file: PropertyFile | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

const EditFileDialog: React.FC<EditFileDialogProps> = ({
  file,
  open,
  onOpenChange,
  onSuccess
}) => {
  const [displayName, setDisplayName] = useState('');
  const [caption, setCaption] = useState('');
  const [isPrivate, setIsPrivate] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (file) {
      setDisplayName(file.display_name || file.filename);
      setCaption(file.caption || '');
      setIsPrivate(file.is_private);
    }
  }, [file, open]);

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) {
      return <FileIcon className="h-8 w-8 text-red-500" />;
    } else if (fileType.includes('image')) {
      return <Image className="h-8 w-8 text-blue-500" />;
    } else if (fileType.includes('spreadsheet') || fileType.includes('excel') || fileType.includes('csv')) {
      return <FileType className="h-8 w-8 text-green-500" />;
    } else if (fileType.includes('text') || fileType.includes('doc')) {
      return <FileText className="h-8 w-8 text-amber-500" />;
    } else {
      return <FileIcon className="h-8 w-8 text-primary" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) {
      return bytes + ' B';
    } else if (bytes < 1024 * 1024) {
      return (bytes / 1024).toFixed(1) + ' KB';
    } else {
      return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    }
  };

  const handleSubmit = async () => {
    if (!file || !displayName.trim()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const { error } = await supabase
        .from('property_files')
        .update({
          display_name: displayName,
          caption: caption,
          is_private: isPrivate,
          updated_at: new Date().toISOString()
        })
        .eq('id', file.id);

      if (error) {
        throw error;
      }

      toast.success('File updated successfully');
      onSuccess();
      onOpenChange(false);
    } catch (error) {
      console.error('Error updating file:', error);
      toast.error('Failed to update file');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!file) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit File</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="flex items-center gap-3 p-3 bg-muted/30 rounded-lg">
            {getFileIcon(file.file_type)}
            <div>
              <p className="font-medium">{file.filename}</p>
              <p className="text-xs text-muted-foreground">
                {formatFileSize(file.file_size)} • Uploaded {formatDistanceToNow(new Date(file.created_at), { addSuffix: true })}
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="display-name">Display Name</Label>
            <Input
              id="display-name"
              value={displayName}
              onChange={(e) => setDisplayName(e.target.value)}
              placeholder="Enter a name for this file"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="caption">Caption (Optional)</Label>
            <Textarea
              id="caption"
              value={caption}
              onChange={(e) => setCaption(e.target.value)}
              placeholder="Add a description for this file"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="is-private"
              checked={isPrivate}
              onCheckedChange={(checked) => setIsPrivate(!!checked)}
            />
            <Label htmlFor="is-private">
              Make this file private (only visible to you)
            </Label>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!displayName.trim() || isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </>
            ) : (
              'Save Changes'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditFileDialog;
