import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  File,
  Download,
  Trash2,
  Lock,
  Globe,
  FileText,
  Image,
  FileType,
  FileIcon,
  Eye,
  Edit
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import FilePreviewDialog from './FilePreviewDialog';
import EditFileDialog from './EditFileDialog';

interface PropertyFile {
  id: string;
  property_id: string;
  user_id: string;
  filename: string;
  display_name?: string;
  caption?: string;
  file_path: string;
  file_type: string;
  file_size: number;
  is_private: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

interface PropertyFilesListProps {
  files: PropertyFile[];
  userId?: string;
  isPropertyOwner: boolean;
  onDelete: (fileId: string, filePath: string) => void;
}

const PropertyFilesList: React.FC<PropertyFilesListProps> = ({
  files,
  userId,
  isPropertyOwner,
  onDelete
}) => {
  const [previewFile, setPreviewFile] = useState<PropertyFile | null>(null);
  const [editingFile, setEditingFile] = useState<PropertyFile | null>(null);

  const canEditFile = (file: PropertyFile) => {
    return isPropertyOwner || file.user_id === userId;
  };

  const canDeleteFile = (file: PropertyFile) => {
    return isPropertyOwner || file.user_id === userId;
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) {
      return <FileIcon className="h-4 w-4 text-red-500" />;
    } else if (fileType.includes('image')) {
      return <Image className="h-4 w-4 text-blue-500" />;
    } else if (fileType.includes('spreadsheet') || fileType.includes('excel') || fileType.includes('csv')) {
      return <FileType className="h-4 w-4 text-green-500" />;
    } else if (fileType.includes('text') || fileType.includes('doc')) {
      return <FileText className="h-4 w-4 text-amber-500" />;
    } else {
      return <File className="h-4 w-4 text-primary" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) {
      return bytes + ' B';
    } else if (bytes < 1024 * 1024) {
      return (bytes / 1024).toFixed(1) + ' KB';
    } else {
      return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    }
  };

  const handleDownload = async (file: PropertyFile) => {
    try {
      const { data, error } = await supabase.storage
        .from('property-files')
        .download(file.file_path);

      if (error) {
        throw error;
      }

      // Create a download link
      const url = URL.createObjectURL(data);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.filename;
      document.body.appendChild(a);
      a.click();
      URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading file:', error);
      toast.error('Failed to download file');
    }
  };

  if (files.length === 0) {
    return (
      <div className="text-center py-8 bg-muted/30 rounded-lg">
        <File className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
        <p className="text-muted-foreground">No files have been uploaded yet.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {files.map((file) => (
        <div
          key={file.id}
          className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
        >
          <div className="flex justify-between items-start">
            <div className="flex items-start gap-3">
              <div className="p-2 rounded-full bg-muted">
                {getFileIcon(file.file_type)}
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <h3 className="font-medium">{file.display_name || file.filename}</h3>
                  <Badge variant={file.is_private ? "outline" : "secondary"}>
                    {file.is_private ? (
                      <><Lock className="h-3 w-3 mr-1" /> Private</>
                    ) : (
                      <><Globe className="h-3 w-3 mr-1" /> Shared</>
                    )}
                  </Badge>
                </div>
                {file.caption && (
                  <p className="text-sm text-muted-foreground mt-1">{file.caption}</p>
                )}
                <div className="text-xs text-muted-foreground mt-1">
                  {formatFileSize(file.file_size)} • Uploaded by {file.created_by} • {formatDistanceToNow(new Date(file.created_at), { addSuffix: true })}
                </div>
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setPreviewFile(file)}
                title="Preview file"
              >
                <Eye className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleDownload(file)}
                title="Download file"
              >
                <Download className="h-4 w-4" />
              </Button>

              {canEditFile(file) && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setEditingFile(file)}
                  title="Edit file details"
                >
                  <Edit className="h-4 w-4" />
                </Button>
              )}

              {canDeleteFile(file) && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => onDelete(file.id, file.file_path)}
                  title="Delete file"
                >
                  <Trash2 className="h-4 w-4 text-destructive" />
                </Button>
              )}
            </div>
          </div>
        </div>
      ))}

      <FilePreviewDialog
        file={previewFile}
        open={!!previewFile}
        onOpenChange={(open) => !open && setPreviewFile(null)}
      />

      <EditFileDialog
        file={editingFile}
        open={!!editingFile}
        onOpenChange={(open) => !open && setEditingFile(null)}
        onSuccess={() => {
          // Refresh the file list
          window.dispatchEvent(new CustomEvent('refresh-files'));
        }}
      />
    </div>
  );
};

export default PropertyFilesList;
