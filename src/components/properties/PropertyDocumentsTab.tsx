import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, FileText, File, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import PropertyDocumentsList from './documents/PropertyDocumentsList';
import PropertyFilesList from './documents/PropertyFilesList';
import AddDocumentDialog from './documents/AddDocumentDialog';
import AddFileDialog from './documents/AddFileDialog';
import SimplePagination from '@/components/common/SimplePagination';

interface PropertyDocument {
  id: string;
  property_id: string;
  user_id: string;
  title: string;
  content: string;
  is_private: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

interface PropertyFile {
  id: string;
  property_id: string;
  user_id: string;
  filename: string;
  file_path: string;
  file_type: string;
  file_size: number;
  is_private: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

interface PropertyDocumentsTabProps {
  propertyId: string;
  isPropertyOwner: boolean;
}

const PropertyDocumentsTab: React.FC<PropertyDocumentsTabProps> = ({
  propertyId,
  isPropertyOwner
}) => {
  const { authState } = useAuth();
  const userId = authState.user?.id;

  const [documents, setDocuments] = useState<PropertyDocument[]>([]);
  const [files, setFiles] = useState<PropertyFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('documents');
  const [addDocumentDialogOpen, setAddDocumentDialogOpen] = useState(false);
  const [addFileDialogOpen, setAddFileDialogOpen] = useState(false);
  const [editingDocument, setEditingDocument] = useState<PropertyDocument | null>(null);

  // Pagination state
  const [documentsPage, setDocumentsPage] = useState(1);
  const [filesPage, setFilesPage] = useState(1);
  const [totalDocuments, setTotalDocuments] = useState(0);
  const [totalFiles, setTotalFiles] = useState(0);
  const itemsPerPage = 10;

  // Function to handle edit button click
  const handleEditDocument = (document: PropertyDocument) => {
    setEditingDocument(document);
    setAddDocumentDialogOpen(true);
  };

  const fetchDocuments = async () => {
    try {
      // Get total count first
      const { count: totalCount, error: countError } = await supabase
        .from('property_documents')
        .select('*', { count: 'exact', head: true })
        .eq('property_id', propertyId);

      if (countError) {
        throw countError;
      }

      setTotalDocuments(totalCount || 0);

      // Calculate pagination
      const from = (documentsPage - 1) * itemsPerPage;
      const to = from + itemsPerPage - 1;

      // Get documents with pagination
      const { data: documentsData, error: documentsError } = await supabase
        .from('property_documents')
        .select('*')
        .eq('property_id', propertyId)
        .order('updated_at', { ascending: false })
        .range(from, to);

      if (documentsError) {
        throw documentsError;
      }

      if (!documentsData || documentsData.length === 0) {
        setDocuments([]);
        return;
      }

      // Get user emails for the documents
      const userIds = [...new Set(documentsData.map(doc => doc.user_id))];
      const { data: usersData, error: usersError } = await supabase
        .from('profiles')
        .select('id, email')
        .in('id', userIds);

      if (usersError) {
        console.error('Error fetching user emails:', usersError);
        // Continue with the documents we have
      }

      // Create a map of user IDs to emails
      const userEmailMap = (usersData || []).reduce((map, user) => {
        map[user.id] = user.email;
        return map;
      }, {});

      // Transform data to include created_by from profiles
      const transformedData = documentsData.map(doc => ({
        ...doc,
        created_by: userEmailMap[doc.user_id] || 'Unknown'
      }));

      setDocuments(transformedData);
    } catch (error) {
      console.error('Error fetching documents:', error);
      toast.error('Failed to load documents');
    }
  };

  const fetchFiles = async () => {
    try {
      // Get total count first
      const { count: totalCount, error: countError } = await supabase
        .from('property_files')
        .select('*', { count: 'exact', head: true })
        .eq('property_id', propertyId);

      if (countError) {
        throw countError;
      }

      setTotalFiles(totalCount || 0);

      // Calculate pagination
      const from = (filesPage - 1) * itemsPerPage;
      const to = from + itemsPerPage - 1;

      // Get files with pagination
      const { data: filesData, error: filesError } = await supabase
        .from('property_files')
        .select('*')
        .eq('property_id', propertyId)
        .order('updated_at', { ascending: false })
        .range(from, to);

      if (filesError) {
        throw filesError;
      }

      if (!filesData || filesData.length === 0) {
        setFiles([]);
        return;
      }

      // Get user emails for the files
      const userIds = [...new Set(filesData.map(file => file.user_id))];
      const { data: usersData, error: usersError } = await supabase
        .from('profiles')
        .select('id, email')
        .in('id', userIds);

      if (usersError) {
        console.error('Error fetching user emails:', usersError);
        // Continue with the files we have
      }

      // Create a map of user IDs to emails
      const userEmailMap = (usersData || []).reduce((map, user) => {
        map[user.id] = user.email;
        return map;
      }, {});

      // Transform data to include created_by from profiles
      const transformedData = filesData.map(file => ({
        ...file,
        created_by: userEmailMap[file.user_id] || 'Unknown'
      }));

      setFiles(transformedData);
    } catch (error) {
      console.error('Error fetching files:', error);
      toast.error('Failed to load files');
    }
  };

  const loadData = async () => {
    setLoading(true);
    await Promise.all([fetchDocuments(), fetchFiles()]);
    setLoading(false);
  };

  useEffect(() => {
    if (propertyId && userId) {
      loadData();
    }
  }, [propertyId, userId]);

  // Listen for refresh-files event
  useEffect(() => {
    const handleRefreshFiles = () => {
      fetchFiles();
    };

    window.addEventListener('refresh-files', handleRefreshFiles);

    return () => {
      window.removeEventListener('refresh-files', handleRefreshFiles);
    };
  }, []);

  // Refetch documents when documents page changes
  useEffect(() => {
    if (propertyId && userId) {
      setLoading(true);
      fetchDocuments().then(() => setLoading(false));
    }
  }, [documentsPage]);

  // Refetch files when files page changes
  useEffect(() => {
    if (propertyId && userId) {
      setLoading(true);
      fetchFiles().then(() => setLoading(false));
    }
  }, [filesPage]);

  const handleAddDocument = async (document: Omit<PropertyDocument, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const { data, error } = await supabase
        .from('property_documents')
        .insert({
          ...document,
          property_id: propertyId,
          user_id: userId
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      setDocuments([data, ...documents]);
      toast.success('Document added successfully');
      setAddDocumentDialogOpen(false);
    } catch (error) {
      console.error('Error adding document:', error);
      toast.error('Failed to add document');
    }
  };

  const handleUpdateDocument = async (document: PropertyDocument) => {
    try {
      const { error } = await supabase
        .from('property_documents')
        .update({
          title: document.title,
          content: document.content,
          is_private: document.is_private,
          updated_at: new Date().toISOString()
        })
        .eq('id', document.id);

      if (error) {
        throw error;
      }

      setDocuments(documents.map(doc =>
        doc.id === document.id ? { ...document, updated_at: new Date().toISOString() } : doc
      ));
      toast.success('Document updated successfully');
      setEditingDocument(null);
    } catch (error) {
      console.error('Error updating document:', error);
      toast.error('Failed to update document');
    }
  };

  const handleDeleteDocument = async (documentId: string) => {
    try {
      const { error } = await supabase
        .from('property_documents')
        .delete()
        .eq('id', documentId);

      if (error) {
        throw error;
      }

      setDocuments(documents.filter(doc => doc.id !== documentId));
      toast.success('Document deleted successfully');
    } catch (error) {
      console.error('Error deleting document:', error);
      toast.error('Failed to delete document');
    }
  };

  const handleDeleteFile = async (fileId: string, filePath: string) => {
    try {
      // First delete the file from storage
      const { error: storageError } = await supabase.storage
        .from('property-files')
        .remove([filePath]);

      if (storageError) {
        console.error('Error deleting file from storage:', storageError);
        // Continue anyway to delete the database record
      }

      // Then delete the database record
      const { error } = await supabase
        .from('property_files')
        .delete()
        .eq('id', fileId);

      if (error) {
        throw error;
      }

      setFiles(files.filter(file => file.id !== fileId));
      toast.success('File deleted successfully');
    } catch (error) {
      console.error('Error deleting file:', error);
      toast.error('Failed to delete file');
    }
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="documents" className="flex items-center">
              <FileText className="mr-2 h-4 w-4" />
              Documents
            </TabsTrigger>
            <TabsTrigger value="files" className="flex items-center">
              <File className="mr-2 h-4 w-4" />
              Files
            </TabsTrigger>
          </TabsList>

          {activeTab === 'documents' ? (
            <Button onClick={() => setAddDocumentDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              New Document
            </Button>
          ) : (
            <Button onClick={() => setAddFileDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Upload File
            </Button>
          )}
        </div>

        <TabsContent value="documents">
          <Card>
            <CardHeader>
              <CardTitle>Property Documents</CardTitle>
              <CardDescription>
                Create and manage rich text documents for this property
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : (
                <>
                  <PropertyDocumentsList
                    documents={documents}
                    userId={userId}
                    isPropertyOwner={isPropertyOwner}
                    onEdit={handleEditDocument}
                    onDelete={handleDeleteDocument}
                  />
                  <SimplePagination
                    currentPage={documentsPage}
                    totalItems={totalDocuments}
                    itemsPerPage={itemsPerPage}
                    onPageChange={setDocumentsPage}
                  />
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="files">
          <Card>
            <CardHeader>
              <CardTitle>Property Files</CardTitle>
              <CardDescription>
                Upload and manage files for this property
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : (
                <>
                  <PropertyFilesList
                    files={files}
                    userId={userId}
                    isPropertyOwner={isPropertyOwner}
                    onDelete={handleDeleteFile}
                  />
                  <SimplePagination
                    currentPage={filesPage}
                    totalItems={totalFiles}
                    itemsPerPage={itemsPerPage}
                    onPageChange={setFilesPage}
                  />
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <AddDocumentDialog
        open={addDocumentDialogOpen}
        onOpenChange={setAddDocumentDialogOpen}
        onSave={handleAddDocument}
        editingDocument={editingDocument}
        onUpdate={handleUpdateDocument}
      />

      <AddFileDialog
        open={addFileDialogOpen}
        onOpenChange={setAddFileDialogOpen}
        propertyId={propertyId}
        userId={userId || ''}
        onSuccess={() => {
          fetchFiles();
          setAddFileDialogOpen(false);
        }}
      />
    </div>
  );
};

export default PropertyDocumentsTab;
