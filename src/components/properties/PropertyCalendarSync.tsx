
import React, { useState, memo } from 'react'; // Import memo
import { Property } from '@/hooks/useProperties';
import { Button } from '@/components/ui/button';
import { RefreshCw, Loader2, Link } from 'lucide-react';
import { fetchCalendarData } from '@/utils/propertyUtils';

interface PropertyCalendarSyncProps {
  property: Property;
  userId: string | undefined;
  onSyncComplete: () => Promise<void>;
}

// Wrap the component definition with React.memo
const PropertyCalendarSync: React.FC<PropertyCalendarSyncProps> = memo(({
  property,
  userId,
  onSyncComplete
}) => {
  const [isSyncing, setIsSyncing] = useState(false);
  
  const syncCalendar = async () => {
    if (!property || !property.id || !userId || !property.ical_url) {
      return;
    }
    
    setIsSyncing(true);
    try {
      await fetchCalendarData(property.ical_url, property.id, userId, onSyncComplete);
    } catch (error) {
      console.error('Error syncing calendar:', error);
    } finally {
      setIsSyncing(false);
    }
  };

  if (!property.ical_url) return null;

  console.log("PropertyCalendarSync rendering with:", {
    is_occupied: property.is_occupied,
    current_checkout: property.current_checkout,
    next_checkin_formatted: property.next_checkin_formatted,
    next_checkin_date: property.next_checkin_date,
    next_booking: property.next_booking
  });

  return (
    <div>
      <div className="flex justify-between items-center">
        <h3 className="font-medium">Calendar Sync</h3>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={syncCalendar}
          disabled={isSyncing}
          className="flex items-center gap-1"
        >
          {isSyncing ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
          Sync Now
        </Button>
      </div>
      <p className="text-sm text-muted-foreground mt-1">
        Calendar is being synced via iCal
        {property.last_ical_sync && (
          <> (Last sync: {new Date(property.last_ical_sync).toLocaleDateString()})</>
        )}
      </p>
      
      <div className="p-2 border dark:border-gray-700 rounded-md bg-gray-50 dark:bg-gray-800/60 mt-2">
        <div className="flex items-center gap-2 mb-2">
          <Link size={16} className="text-gray-500 dark:text-gray-400" />
          <span className="text-xs text-muted-foreground break-all">{property.ical_url}</span>
        </div>
        
        {property.is_occupied !== undefined && (
          <div className={`p-2 border rounded text-sm mb-2 ${
            property.is_occupied 
              ? 'border-red-300 bg-red-50 text-red-700 dark:border-red-900 dark:bg-red-950/30 dark:text-red-400' 
              : 'border-green-300 bg-green-50 text-green-700 dark:border-green-900 dark:bg-green-950/30 dark:text-green-400'
          }`}>
            <span className="font-medium">
              {property.is_occupied ? 'Currently Occupied' : 'Currently Vacant'}
            </span>
            {property.is_occupied && property.current_checkout && (
              <span className="ml-1">until {property.current_checkout}</span>
            )}
          </div>
        )}
        
        {!property.is_occupied && property.next_checkin_formatted && (
          <div className="text-sm dark:text-gray-300">
            <span className="font-medium">Next check-in:</span> {property.next_checkin_formatted}
          </div>
        )}
        
        {!property.is_occupied && !property.next_checkin_formatted && property.next_booking && (
          <div className="text-sm mt-1 dark:text-gray-300">
            <span className="font-medium">Booking period:</span> {property.next_booking}
          </div>
        )}
      </div>
    </div>
  );
}); // Close the memo HOC

export default PropertyCalendarSync;
