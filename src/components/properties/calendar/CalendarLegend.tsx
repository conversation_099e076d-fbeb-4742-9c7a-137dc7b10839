import React from 'react';
import { Shopping<PERSON>art, ListTodo, AlertTriangle, Calendar } from 'lucide-react';

interface CalendarLegendProps {
  className?: string;
}

const CalendarLegend: React.FC<CalendarLegendProps> = ({ className = '' }) => {
  return (
    <div className={`space-y-2 ${className}`}>
      <h4 className="text-sm font-medium text-muted-foreground mb-3">Calendar Legend</h4>
      
      <div className="space-y-2 text-xs">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded bg-purple-100 border border-purple-200 flex items-center justify-center">
            <ShoppingCart className="h-2 w-2 text-purple-600" />
          </div>
          <span className="text-purple-700 font-medium">Bookings</span>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded bg-blue-100 border border-blue-200 flex items-center justify-center">
            <ListTodo className="h-2 w-2 text-blue-600" />
          </div>
          <span className="text-blue-700 font-medium">Maintenance</span>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded bg-amber-100 border border-amber-200 flex items-center justify-center">
            <AlertTriangle className="h-2 w-2 text-amber-600" />
          </div>
          <span className="text-amber-700 font-medium">Damage Reports</span>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded bg-red-100 border-2 border-red-500 flex items-center justify-center">
            <Calendar className="h-2 w-2 text-red-600" />
          </div>
          <span className="text-red-700 font-medium">Multiple Events</span>
        </div>
      </div>
      
      <div className="mt-3 pt-2 border-t text-xs text-muted-foreground">
        <p>Click on any date to view detailed events</p>
      </div>
    </div>
  );
};

export default CalendarLegend;
