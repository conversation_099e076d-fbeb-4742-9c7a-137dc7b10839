
import { useState, useEffect, useCallback, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { CalendarEvent } from './types';
import {
  fetchIcalBookings,
  fetchDbBookings,
  fetchMaintenanceTasks,
  fetchDamageReports
} from './eventHelpers';

/**
 * Hook to fetch and manage calendar events for a property using React Query for better performance
 * @param propertyId The ID of the property to fetch events for
 * @returns Object containing events and loading state
 */
export const useCalendarEvents = (propertyId: string) => {
  const fetchCalendarEvents = useCallback(async (): Promise<CalendarEvent[]> => {
    if (!propertyId) return [];

    console.log(`[useCalendarEvents] Fetching calendar events for property: ${propertyId}`);

    try {
      // Fetch property for iCal URL
      const { data: property, error: propertyError } = await supabase
        .from('properties')
        .select('*')
        .eq('id', propertyId)
        .single();

      if (propertyError) {
        console.error('[useCalendarEvents] Error fetching property:', propertyError);
        throw propertyError;
      }

      console.log(`[useCalendarEvents] Property data fetched:`, {
        id: property.id,
        name: property.name,
        ical_url: property.ical_url ? 'exists' : 'missing',
        next_booking: property.next_booking,
        is_occupied: property.is_occupied,
        current_checkout: property.current_checkout
      });

      // If we have ical_url but no next_booking, trigger a sync
      if (property.ical_url && !property.next_booking) {
        console.log(`[useCalendarEvents] Property has iCal URL but no next_booking, triggering sync`);
        try {
          const { data: syncData } = await supabase.functions.invoke('sync-property-calendars', {
            body: { propertyId }
          });

          console.log(`[useCalendarEvents] Calendar sync result:`, syncData);

          if (syncData?.success) {
            // Refetch property after sync
            const { data: refreshedProperty } = await supabase
              .from('properties')
              .select('*')
              .eq('id', propertyId)
              .single();

            if (refreshedProperty) {
              property.next_booking = refreshedProperty.next_booking;
              property.is_occupied = refreshedProperty.is_occupied;
              property.current_checkout = refreshedProperty.current_checkout;
              console.log(`[useCalendarEvents] Property data refreshed after sync:`, {
                next_booking: property.next_booking,
                is_occupied: property.is_occupied,
                current_checkout: property.current_checkout
              });
            }
          }
        } catch (syncError) {
          console.error('[useCalendarEvents] Error during calendar sync:', syncError);
          // Continue with existing data even if sync fails
        }
      }

      // Fetch all event types in parallel for better performance
      console.log(`[useCalendarEvents] Fetching calendar events for property ${property.id}`);

      const [icalBookings, dbBookings, maintenanceTasks, damageReports] = await Promise.all([
        fetchIcalBookings(property),
        fetchDbBookings(propertyId),
        fetchMaintenanceTasks(propertyId),
        fetchDamageReports(propertyId)
      ]);

      console.log(`[useCalendarEvents] Fetched events:`, {
        icalBookings: icalBookings.length,
        dbBookings: dbBookings.length,
        maintenanceTasks: maintenanceTasks.length,
        damageReports: damageReports.length
      });

      const allEvents = [
        ...icalBookings,
        ...dbBookings,
        ...maintenanceTasks,
        ...damageReports
      ];

      console.log(`[useCalendarEvents] Total events for property: ${allEvents.length}`);

      // For debugging, output all booking events
      const bookingEvents = allEvents.filter(e => e.type === 'booking');
      if (bookingEvents.length > 0) {
        console.log('[useCalendarEvents] Booking events for this property:', bookingEvents
          .map(event => ({
            title: event.title,
            startDate: event.date.toLocaleDateString(),
            endDate: event.endDate ? event.endDate.toLocaleDateString() : 'N/A'
          }))
        );
      } else {
        console.log('[useCalendarEvents] No booking events found for this property');
      }

      return allEvents;
    } catch (error) {
      console.error('[useCalendarEvents] Error fetching calendar events:', error);
      throw error;
    }
  }, [propertyId]);

  // Use React Query for better caching and performance
  const {
    data: events = [],
    isLoading: loading,
    error,
    refetch
  } = useQuery({
    queryKey: ['calendarEvents', propertyId],
    queryFn: fetchCalendarEvents,
    enabled: !!propertyId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
    retry: 2,
    retryDelay: 1000
  });

  // Memoize the return value to prevent unnecessary re-renders
  return useMemo(() => ({
    events,
    loading,
    error: error ? String(error) : null,
    refetch
  }), [events, loading, error, refetch]);
};
