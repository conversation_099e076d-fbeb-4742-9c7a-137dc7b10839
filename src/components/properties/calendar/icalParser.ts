
/**
 * Parses iCal data (specifically the `next_booking` string from the properties table)
 * and returns an array of booking date ranges.
 * @param icalData The `next_booking` string from the properties table.
 * @returns An array of `{ startDate: Date, endDate: Date }` objects, or an empty array if parsing fails.
 */
export const parseIcalData = (icalData: string): { startDate: Date, endDate: Date }[] => {
  if (!icalData) {
    return [];
  }

  // First, try to parse full date formats like "March 12, 2025 - March 16, 2025"
  // This will handle the format we're seeing in the database
  const fullDateRegex = /(\w+\s+\d+,\s+\d{4})\s*-\s*(\w+\s+\d+,\s+\d{4})/;
  const fullDateMatch = icalData.match(fullDateRegex);

  if (fullDateMatch) {
    const startDateStr = fullDateMatch[1];
    const endDateStr = fullDateMatch[2];
    
    const startDate = new Date(startDateStr);
    const endDate = new Date(endDateStr);
    
    // Normalize dates to midnight for accurate comparison
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(0, 0, 0, 0);
    
    // Skip invalid dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      console.warn('Invalid date format in full date parsing:', icalData);
      return [];
    }
    
    return [{ startDate, endDate }];
  }

  // Fall back to the original month-day format parsing if the full date format doesn't match
  const bookingRegex = /(\w+)\s+(\d+)(?:\s*-\s*(?:(\w+)\s+)?(\d+))?(?:,?\s*(\d{4}))?/g;
  const bookingMatches = [...icalData.matchAll(bookingRegex)];

  if (!bookingMatches || bookingMatches.length === 0) {
    return [];
  }

  const processedDateRanges: { startDate: Date, endDate: Date }[] = [];

  bookingMatches.forEach((match) => {
    const startMonth = match[1];
    const startDay = parseInt(match[2]);
    const endMonth = match[3] || startMonth; // Use startMonth if endMonth is undefined
    const endDay = parseInt(match[4]);
    const year = parseInt(match[5]) || new Date().getFullYear();

    // Convert month names to month indices (0-11)
    const getMonthIndex = (monthName: string) => {
      const months = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
      return months.findIndex(m => m.toLowerCase().startsWith(monthName.toLowerCase().slice(0, 3)));
    };

    const startMonthIndex = getMonthIndex(startMonth);
    const endMonthIndex = getMonthIndex(endMonth);

    // Create date objects for start and end dates
    const startDate = new Date(year, startMonthIndex, startDay);
    const endDate = new Date(year, endMonthIndex, endDay);

    // Normalize dates to midnight for accurate comparison
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(0, 0, 0, 0);

    // Skip invalid dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      console.warn('Skipping invalid date from ical:', match[0]);
      return;
    }

    // Add the booking if it's not already processed
    const isDuplicate = processedDateRanges.some(range =>
      range.startDate.getTime() === startDate.getTime() &&
      range.endDate.getTime() === endDate.getTime()
    );

    if (!isDuplicate) {
      processedDateRanges.push({ startDate, endDate });
    }
  });

  return processedDateRanges;
};
