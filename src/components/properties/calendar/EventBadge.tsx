
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { ShoppingCart, ListTodo, AlertTriangle, CalendarIcon } from 'lucide-react';

interface EventBadgeProps {
  type: string;
}

const EventBadge: React.FC<EventBadgeProps> = ({ type }) => {
  switch (type) {
    case 'booking':
      return (
        <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 transition-colors">
          <ShoppingCart className="h-3 w-3 mr-1" />
          Booking
        </Badge>
      );
    case 'maintenance':
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 transition-colors">
          <ListTodo className="h-3 w-3 mr-1" />
          Maintenance
        </Badge>
      );
    case 'damage':
      return (
        <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100 transition-colors">
          <AlertTriangle className="h-3 w-3 mr-1" />
          Damage
        </Badge>
      );
    default:
      return (
        <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100 transition-colors">
          <CalendarIcon className="h-3 w-3 mr-1" />
          Event
        </Badge>
      );
  }
};

export default EventBadge;
