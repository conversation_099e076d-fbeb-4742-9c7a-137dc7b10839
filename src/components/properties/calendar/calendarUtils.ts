
import { CalendarEvent, CalendarFilters } from './types';

/**
 * Normalizes a date to midnight to ensure proper date comparison without time components
 * @param date - The date to normalize
 * @returns A new Date object set to midnight
 */
const normalizeDateToMidnight = (date: Date): Date => {
  const normalized = new Date(date);
  normalized.setHours(0, 0, 0, 0);
  return normalized;
};

/**
 * Checks if a specific date has events that match the filter criteria
 * @param date - The date to check for events
 * @param events - Array of calendar events to search through
 * @param filters - Filter settings to determine which event types to include
 * @returns boolean indicating if the date has matching events
 */
export const dateHasEvent = (date: Date, events: CalendarEvent[], filters: CalendarFilters) => {
  // Create a date at midnight for proper date comparison (no time component)
  const compareDate = normalizeDateToMidnight(date);
  const compareDateTimestamp = compareDate.getTime();
  
  // Check if any event matches the date and filter criteria
  return events.some(event => {
    // Skip events types that don't match the current filters
    if (event.type === 'booking' && !filters.bookings) return false;
    if (event.type === 'maintenance' && !filters.maintenance) return false;
    if (event.type === 'damage' && !filters.damages) return false;
    
    // For booking events with an end date, check if the date falls within the booking period
    if (event.type === 'booking' && event.endDate) {
      // Normalize dates for accurate comparison
      const eventStartDate = normalizeDateToMidnight(event.date);
      const eventEndDate = normalizeDateToMidnight(event.endDate);
      
      // Check if compareDate is within the date range (inclusive)
      const isWithinRange = 
        compareDateTimestamp >= eventStartDate.getTime() && 
        compareDateTimestamp <= eventEndDate.getTime();
      
      return isWithinRange;
    }

    // For non-booking events or events without an end date, check for exact date match
    const eventDate = normalizeDateToMidnight(event.date);
    return eventDate.getTime() === compareDateTimestamp;
  });
};

/**
 * Gets the event types that occur on a specific date (for calendar dot styling)
 * @param date - The date to get event types for
 * @param events - Array of calendar events to search through
 * @param filters - Filter settings to determine which event types to include
 * @returns Array of event type strings that occur on the date
 */
export const getEventTypesForDate = (date: Date, events: CalendarEvent[], filters: CalendarFilters) => {
  const types = new Set<string>();
  
  // Create a date at midnight for proper date comparison
  const compareDate = normalizeDateToMidnight(date);
  const compareDateTimestamp = compareDate.getTime();
  
  events.forEach(event => {
    // Skip events types that don't match the current filters
    if (event.type === 'booking' && !filters.bookings) return;
    if (event.type === 'maintenance' && !filters.maintenance) return;
    if (event.type === 'damage' && !filters.damages) return;
    
    // For booking events with an end date, check if the date falls within the booking period
    if (event.type === 'booking' && event.endDate) {
      // Normalize dates for accurate comparison
      const eventStartDate = normalizeDateToMidnight(event.date);
      const eventEndDate = normalizeDateToMidnight(event.endDate);
      
      // Check if compareDate is within the date range (inclusive)
      const isWithinRange = 
        compareDateTimestamp >= eventStartDate.getTime() && 
        compareDateTimestamp <= eventEndDate.getTime();
      
      if (isWithinRange) {
        types.add('booking');
      }
      return;
    }

    // For other event types, check for exact date match
    const eventDate = normalizeDateToMidnight(event.date);
    if (eventDate.getTime() === compareDateTimestamp) {
      types.add(event.type);
    }
  });
  
  return Array.from(types);
};

/**
 * Gets all events that occur on a selected date
 * @param selectedDate - The date to get events for
 * @param events - Array of calendar events to search through
 * @param filters - Filter settings to determine which event types to include
 * @returns Array of events that occur on the selected date
 */
export const getEventsForSelectedDate = (selectedDate: Date | undefined, events: CalendarEvent[], filters: CalendarFilters) => {
  if (!selectedDate) return [];
  
  // Create a date at midnight for proper date comparison
  const compareDate = normalizeDateToMidnight(selectedDate);
  const compareDateTimestamp = compareDate.getTime();
  
  return events.filter(event => {
    // Skip events types that don't match the current filters
    if (event.type === 'booking' && !filters.bookings) return false;
    if (event.type === 'maintenance' && !filters.maintenance) return false;
    if (event.type === 'damage' && !filters.damages) return false;
    
    // For booking events with an end date, check if the date falls within the booking period
    if (event.type === 'booking' && event.endDate) {
      // Normalize dates for accurate comparison
      const eventStartDate = normalizeDateToMidnight(event.date);
      const eventEndDate = normalizeDateToMidnight(event.endDate);
      
      // Check if compareDate is within the date range (inclusive)
      const isWithinRange = 
        compareDateTimestamp >= eventStartDate.getTime() && 
        compareDateTimestamp <= eventEndDate.getTime();
      
      return isWithinRange;
    }

    // For other event types, check for exact date match
    const eventDate = normalizeDateToMidnight(event.date);
    return eventDate.getTime() === compareDateTimestamp;
  });
};
