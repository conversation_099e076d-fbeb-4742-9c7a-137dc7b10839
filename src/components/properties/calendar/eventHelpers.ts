import { supabase } from '@/integrations/supabase/client';
import { CalendarEvent } from './types';
import { parseIcalData } from './icalParser';

/**
 * Fetches and processes iCal bookings from a property's `next_booking` field.
 */
export const fetchIcalBookings = async (property: any): Promise<CalendarEvent[]> => {
  const events: CalendarEvent[] = [];

  if (property.next_booking) {
    console.log(`Processing bookings for property ${property.id}: ${property.next_booking}`);
    const bookingDateRanges = parseIcalData(property.next_booking);
    
    console.log(`Parsed ${bookingDateRanges.length} booking ranges from next_booking field`);
    
    bookingDateRanges.forEach(({ startDate, endDate }) => {
      events.push({
        id: `booking-ical-${startDate.getTime()}-${endDate.getTime()}`,
        title: 'Booking',
        date: startDate,
        endDate: endDate,
        type: 'booking'
      });
      
      console.log(`Added booking event: ${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`);
    });

    // If we have a next check-in date directly on the property, use it as a fallback
    if (bookingDateRanges.length === 0 && property.next_checkin_date && property.next_checkin_formatted) {
      try {
        const checkInDate = new Date(property.next_checkin_date);
        
        // Create a check-out date 1 day after check-in as a fallback if we don't have exact dates
        const checkOutDate = new Date(checkInDate);
        checkOutDate.setDate(checkOutDate.getDate() + 1);
        
        checkInDate.setHours(0, 0, 0, 0);
        checkOutDate.setHours(0, 0, 0, 0);

        if (!isNaN(checkInDate.getTime())) {
          events.push({
            id: `booking-next-checkin-${property.id}`,
            title: 'Next Check-in',
            date: checkInDate,
            endDate: checkOutDate,
            type: 'booking'
          });
          
          console.log(`Added fallback booking from next_checkin_date: ${checkInDate.toLocaleDateString()}`);
        }
      } catch (error) {
        console.warn(`Error parsing next_checkin_date: ${property.next_checkin_date}`, error);
      }
    }

    // Handle current occupancy
    if (property.is_occupied && property.current_checkout) {
      // Try to parse current_checkout in various formats
      let checkoutDate: Date | undefined = undefined;
      
      // First try direct Date parsing (ISO format)
      const parsedDate = new Date(property.current_checkout);
      if (!isNaN(parsedDate.getTime())) {
        checkoutDate = parsedDate;
      }
      
      // If that failed, try parsing the formatted string
      if (!checkoutDate || isNaN(checkoutDate.getTime())) {
        const checkoutDateParts = property.current_checkout.match(/(\w+) (\d+), (\d{4})/);
        if (checkoutDateParts) {
          const months = ['january', 'february', 'march', 'april', 'may', 'june', 'july', 'august', 'september', 'october', 'november', 'december'];
          const monthIndex = months.findIndex(m => m.toLowerCase().startsWith(checkoutDateParts[1].toLowerCase()));
          if (monthIndex !== -1) {
            const day = parseInt(checkoutDateParts[2]);
            const year = parseInt(checkoutDateParts[3]);
            checkoutDate = new Date(year, monthIndex, day);
          }
        }
      }
      
      if (checkoutDate && !isNaN(checkoutDate.getTime())) {
        checkoutDate.setHours(0, 0, 0, 0);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        // Safely check for duplicates with proper type guards
        const isDuplicate = events.some(event => {
          return event.type === 'booking' &&
            event.date.getTime() <= today.getTime() &&
            event.endDate && 
            event.endDate.getTime() >= checkoutDate!.getTime();
        });
        
        if (!isDuplicate) {
          events.push({
            id: 'current-occupancy',
            title: 'Currently Occupied',
            date: today,
            endDate: checkoutDate,
            type: 'booking'
          });
          
          console.log(`Added current occupancy event: ${today.toLocaleDateString()} - ${checkoutDate.toLocaleDateString()}`);
        }
      }
    }
  }

  return events;
};

/**
 * Fetches bookings from the `bookings` table for a given property ID.
 */
export const fetchDbBookings = async (propertyId: string): Promise<CalendarEvent[]> => {
  const { data: bookings, error: bookingsError } = await supabase
    .from('bookings')
    .select('*')
    .eq('property_id', propertyId);

  if (bookingsError) {
    console.error('Error fetching bookings from database:', bookingsError);
    return [];
  }

  const events: CalendarEvent[] = [];

  if (bookings) {
    bookings.forEach(booking => {
      if (booking.check_in_date && booking.check_out_date) {
        const checkInDate = new Date(booking.check_in_date);
        const checkOutDate = new Date(booking.check_out_date);
        checkInDate.setHours(0, 0, 0, 0);
        checkOutDate.setHours(0, 0, 0, 0);

        if (!isNaN(checkInDate.getTime()) && !isNaN(checkOutDate.getTime())) {
          events.push({
            id: `booking-db-${booking.id}`,
            title: 'Booking',
            date: checkInDate,
            endDate: checkOutDate,
            type: 'booking'
          });
        }
      }
    });
  }

  return events;
};

/**
 * Fetches maintenance tasks for a given property ID.
 */
export const fetchMaintenanceTasks = async (propertyId: string): Promise<CalendarEvent[]> => {
  const { data: maintenanceTasks, error: maintenanceError } = await supabase
    .from('maintenance_tasks')
    .select('id, title, due_date, status, severity')
    .eq('property_id', propertyId)
    .not('due_date', 'is', null);

  if (maintenanceError) {
    console.error('Error fetching maintenance tasks:', maintenanceError);
    return [];
  }

  const events: CalendarEvent[] = [];

  if (maintenanceTasks) {
    maintenanceTasks.forEach(task => {
      if (task.due_date) {
        const dueDate = new Date(task.due_date);
        dueDate.setHours(0, 0, 0, 0);

        if (!isNaN(dueDate.getTime())) {
          events.push({
            id: `maintenance-${task.id}`,
            title: task.title,
            date: dueDate,
            type: 'maintenance',
            status: task.status,
            priority: task.severity
          });
        }
      }
    });
  }

  return events;
};

/**
 * Fetches damage reports for a given property ID.
 */
export const fetchDamageReports = async (propertyId: string): Promise<CalendarEvent[]> => {
  const { data: damageReports, error: damageError } = await supabase
    .from('damage_reports')
    .select('id, title, created_at, status')
    .eq('property_id', propertyId)
    .in('status', ['open', 'in_progress']);

  if (damageError) {
    console.error('Error fetching damage reports:', damageError);
    return [];
  }

  const events: CalendarEvent[] = [];

  if (damageReports) {
    damageReports.forEach(report => {
      if (report.created_at) {
        const createdDate = new Date(report.created_at);
        createdDate.setHours(0, 0, 0, 0);

        if (!isNaN(createdDate.getTime())) {
          events.push({
            id: `damage-${report.id}`,
            title: report.title,
            date: createdDate,
            type: 'damage',
            status: report.status
          });
        }
      }
    });
  }

  return events;
};
