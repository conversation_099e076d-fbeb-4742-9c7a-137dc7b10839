
import React, { useState } from 'react';
import { CalendarEvent } from './types';
import EventBadge from './EventBadge';
import { Button } from '@/components/ui/button';
import { ExternalLink, Clock, MapPin, User } from 'lucide-react';

interface EventListProps {
  events: CalendarEvent[];
  loading: boolean;
}

const EventList: React.FC<EventListProps> = ({ events, loading }) => {
  const [expandedEvent, setExpandedEvent] = useState<string | null>(null);

  const toggleEventExpansion = (eventId: string) => {
    setExpandedEvent(expandedEvent === eventId ? null : eventId);
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'booking':
        return <Clock className="h-3 w-3" />;
      case 'maintenance':
        return <MapPin className="h-3 w-3" />;
      case 'damage':
        return <User className="h-3 w-3" />;
      default:
        return <Clock className="h-3 w-3" />;
    }
  };

  const formatEventTime = (event: CalendarEvent) => {
    if (event.type === 'booking' && event.endDate) {
      const startDate = new Date(event.date);
      const endDate = new Date(event.endDate);

      if (startDate.toDateString() === endDate.toDateString()) {
        return `${startDate.toLocaleDateString()}`;
      } else {
        return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
      }
    }
    return new Date(event.date).toLocaleDateString();
  };

  const getPriorityColor = (priority?: string) => {
    switch (priority?.toLowerCase()) {
      case 'critical':
        return 'text-red-600';
      case 'high':
        return 'text-orange-600';
      case 'medium':
        return 'text-yellow-600';
      case 'low':
        return 'text-green-600';
      default:
        return 'text-muted-foreground';
    }
  };

  return (
    <div className="space-y-2 max-h-[350px] overflow-y-auto">
      {loading ? (
        <div className="text-center py-4 text-muted-foreground">
          <div className="animate-pulse">Loading events...</div>
        </div>
      ) : events.length > 0 ? (
        events.map(event => {
          const isExpanded = expandedEvent === event.id;

          return (
            <div
              key={event.id}
              className="border rounded-lg overflow-hidden hover:shadow-sm transition-all duration-200"
            >
              <div
                className="p-3 cursor-pointer hover:bg-muted/30 transition-colors"
                onClick={() => toggleEventExpansion(event.id)}
              >
                <div className="flex justify-between items-start">
                  <div className="flex items-start gap-2 flex-1">
                    <div className="mt-0.5">
                      {getEventIcon(event.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm truncate">{event.title}</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {formatEventTime(event)}
                      </div>
                    </div>
                  </div>
                  <EventBadge type={event.type} />
                </div>

                {(event.status || event.priority) && (
                  <div className="mt-2 flex flex-wrap gap-2 text-xs">
                    {event.status && (
                      <span className="px-2 py-1 bg-muted rounded-full">
                        {event.status.replace(/_/g, ' ')}
                      </span>
                    )}
                    {event.priority && (
                      <span className={`px-2 py-1 rounded-full font-medium ${getPriorityColor(event.priority)}`}>
                        {event.priority} priority
                      </span>
                    )}
                  </div>
                )}
              </div>

              {isExpanded && (
                <div className="px-3 pb-3 border-t bg-muted/20">
                  <div className="pt-3 space-y-2 text-sm">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      <span>Event Details</span>
                    </div>

                    {event.type === 'booking' && (
                      <div className="text-xs space-y-1">
                        <p><strong>Type:</strong> Property Booking</p>
                        {event.endDate && (
                          <p><strong>Duration:</strong> {formatEventTime(event)}</p>
                        )}
                      </div>
                    )}

                    {event.type === 'maintenance' && (
                      <div className="text-xs space-y-1">
                        <p><strong>Type:</strong> Maintenance Task</p>
                        {event.status && (
                          <p><strong>Status:</strong> {event.status.replace(/_/g, ' ')}</p>
                        )}
                        {event.priority && (
                          <p><strong>Priority:</strong> {event.priority}</p>
                        )}
                      </div>
                    )}

                    {event.type === 'damage' && (
                      <div className="text-xs space-y-1">
                        <p><strong>Type:</strong> Damage Report</p>
                        {event.status && (
                          <p><strong>Status:</strong> {event.status.replace(/_/g, ' ')}</p>
                        )}
                      </div>
                    )}

                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2 h-7 text-xs"
                      onClick={(e) => {
                        e.stopPropagation();
                        // TODO: Navigate to specific event details
                        console.log('Navigate to event:', event.id);
                      }}
                    >
                      <ExternalLink className="h-3 w-3 mr-1" />
                      View Details
                    </Button>
                  </div>
                </div>
              )}
            </div>
          );
        })
      ) : (
        <div className="text-center py-8 text-muted-foreground">
          <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p>No events for this date</p>
          <p className="text-xs mt-1">Select a different date or adjust filters</p>
        </div>
      )}
    </div>
  );
};

export default EventList;
