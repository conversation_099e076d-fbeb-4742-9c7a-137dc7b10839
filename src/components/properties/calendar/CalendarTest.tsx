import React, { useState } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

/**
 * Simple test component to verify calendar functionality
 * This can be used for debugging calendar issues
 */
const CalendarTest: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());

  return (
    <Card className="max-w-md mx-auto mt-8">
      <CardHeader>
        <CardTitle>Calendar Test</CardTitle>
      </CardHeader>
      <CardContent>
        <Calendar
          mode="single"
          selected={selectedDate}
          onSelect={setSelectedDate}
          month={currentMonth}
          onMonthChange={setCurrentMonth}
          showOutsideDays={true}
          fixedWeeks={true}
          className="rounded-md border p-3 w-full"
        />
        
        <div className="mt-4 p-3 bg-muted rounded-md">
          <p className="text-sm">
            <strong>Selected Date:</strong> {selectedDate?.toLocaleDateString() || 'None'}
          </p>
          <p className="text-sm">
            <strong>Current Month:</strong> {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default CalendarTest;
