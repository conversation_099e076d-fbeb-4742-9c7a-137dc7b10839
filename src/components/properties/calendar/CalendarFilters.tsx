
import React from 'react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { ListTodo, AlertTriangle, ShoppingCart } from 'lucide-react';
import { CalendarFilters } from './types';

interface CalendarFiltersProps {
  filters: CalendarFilters;
  onFilterChange: (filters: CalendarFilters) => void;
}

const CalendarFilterControls: React.FC<CalendarFiltersProps> = ({ filters, onFilterChange }) => {
  return (
    <div className="space-y-2 mb-4">
      <div className="flex items-center space-x-2">
        <Switch 
          id="show-bookings" 
          checked={filters.bookings} 
          onCheckedChange={(checked) => onFilterChange({ ...filters, bookings: checked })}
        />
        <Label htmlFor="show-bookings" className="flex items-center gap-1">
          <ShoppingCart className="h-3 w-3 text-purple-500" /> Bookings
        </Label>
      </div>
      
      <div className="flex items-center space-x-2">
        <Switch 
          id="show-maintenance" 
          checked={filters.maintenance} 
          onCheckedChange={(checked) => onFilterChange({ ...filters, maintenance: checked })}
        />
        <Label htmlFor="show-maintenance" className="flex items-center gap-1">
          <ListTodo className="h-3 w-3 text-blue-500" /> Maintenance
        </Label>
      </div>
      
      <div className="flex items-center space-x-2">
        <Switch 
          id="show-damages" 
          checked={filters.damages} 
          onCheckedChange={(checked) => onFilterChange({ ...filters, damages: checked })}
        />
        <Label htmlFor="show-damages" className="flex items-center gap-1">
          <AlertTriangle className="h-3 w-3 text-amber-500" /> Damage Reports
        </Label>
      </div>
    </div>
  );
};

export default CalendarFilterControls;
