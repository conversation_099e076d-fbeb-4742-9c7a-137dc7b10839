import React, { useState, useCallback } from 'react';
import { Upload, ImageIcon, Loader2, Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

interface ImageUploaderProps {
  imageUrl: string;
  onImageChange: (url: string) => void;
  propertyId?: string;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({ 
  imageUrl, 
  onImageChange,
  propertyId
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const { authState } = useAuth();
  const userId = authState.user?.id;

  const handleFileChange = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !userId) return;
    
    try {
      setIsUploading(true);
      
      // Create form data for the file upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('userId', userId);

      // Add property ID if available
      if (propertyId) {
        formData.append('propertyId', propertyId);
      }
      
      console.log(`Uploading image for property: ${propertyId || 'new'}, user: ${userId}`);
      
      // Use Supabase Edge Function with proper authorization
      const { data, error } = await supabase.functions.invoke('upload-property-image', {
        body: formData,
        headers: {
          'Accept': 'application/json'
        }
      });

      if (error || !data) {
        throw new Error(error?.message || 'Upload failed');
      }
      
      console.log("Upload response:", data);
      
      if (data && data.url) {
        onImageChange(data.url);
        toast.success('Image uploaded successfully');
      } else {
        throw new Error('No URL returned from upload');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error(`Error uploading image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsUploading(false);
    }
  }, [propertyId, userId, onImageChange]);
  
  const removeImage = useCallback(() => {
    onImageChange('');
  }, [onImageChange]);
  
  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium">Property Image</label>
      
      {imageUrl ? (
        <div className="relative rounded-lg overflow-hidden border h-48">
          <img 
            src={imageUrl} 
            alt="Property" 
            className="w-full h-full object-cover"
            onError={(e) => {
              console.error("Image failed to load:", imageUrl);
              e.currentTarget.src = "/placeholder.svg";
            }}
          />
          <div className="absolute bottom-2 right-2 flex gap-2">
            <Button 
              variant="destructive" 
              size="icon" 
              onClick={removeImage}
              className="h-8 w-8 rounded-full bg-white/80 hover:bg-white text-red-500"
            >
              <Trash className="h-4 w-4" />
            </Button>
            <label className="h-8 w-8 flex items-center justify-center rounded-full bg-white/80 hover:bg-white text-primary cursor-pointer">
              <Upload className="h-4 w-4" />
              <input 
                type="file" 
                accept="image/*" 
                className="hidden" 
                onChange={handleFileChange}
                disabled={isUploading}
              />
            </label>
          </div>
          {isUploading && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <Loader2 className="w-8 h-8 text-white animate-spin" />
            </div>
          )}
        </div>
      ) : (
        <label className="flex flex-col items-center justify-center w-full h-48 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors">
          {isUploading ? (
            <div className="flex flex-col items-center justify-center pt-5 pb-6">
              <Loader2 className="w-8 h-8 text-primary animate-spin mb-2" />
              <p className="text-sm text-gray-500">Uploading image...</p>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center pt-5 pb-6">
              <ImageIcon className="w-10 h-10 text-gray-400 mb-3" />
              <p className="mb-2 text-sm text-gray-500">
                <span className="font-semibold">Click to upload</span> or drag and drop
              </p>
              <p className="text-xs text-gray-500">PNG, JPG up to 10MB</p>
            </div>
          )}
          <input 
            type="file" 
            className="hidden" 
            accept="image/*"
            onChange={handleFileChange}
            disabled={isUploading}
          />
        </label>
      )}
    </div>
  );
};

export default ImageUploader;
