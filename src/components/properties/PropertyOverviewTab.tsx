
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Property } from '@/hooks/useProperties';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Wrench, ShoppingCart, AlertTriangle } from 'lucide-react';
import PropertyCalendarSync from './PropertyCalendarSync';

interface PropertyOverviewTabProps {
  property: Property;
  userId: string | undefined;
  onSyncComplete: () => Promise<void>;
  onManageCollections: () => void;
}

const PropertyOverviewTab: React.FC<PropertyOverviewTabProps> = ({
  property,
  userId,
  onSyncComplete,
  onManageCollections
}) => {
  const navigate = useNavigate();

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>Property Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <PropertyCalendarSync 
              property={property}
              userId={userId}
              onSyncComplete={onSyncComplete}
            />
            
            <div>
              <h3 className="font-medium">Quick Access</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                <Button variant="outline" onClick={() => navigate(`/maintenance?property=${property.id}`)}>
                  <Wrench className="mr-2 h-4 w-4" />
                  Maintenance
                </Button>
                <Button variant="outline" onClick={() => navigate(`/inventory?property=${property.id}`)}>
                  <ShoppingCart className="mr-2 h-4 w-4" />
                  Inventory
                </Button>
                <Button variant="outline" onClick={() => navigate(`/damages?property=${property.id}`)}>
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Damages
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Collections</CardTitle>
        </CardHeader>
        <CardContent>
          {property.collections && property.collections.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {property.collections.map((collection, index) => (
                <Badge key={index} variant="secondary" className="px-3 py-1">
                  {collection.name}
                  {collection.budget && ` ($${collection.budget})`}
                </Badge>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground">No collections have been added yet.</p>
          )}
        </CardContent>
        <CardFooter>
          <Button variant="link" onClick={onManageCollections}>
            Manage Collections
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default PropertyOverviewTab;
