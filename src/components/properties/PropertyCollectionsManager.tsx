
import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { CollectionWithBudget } from './PropertyCard';
import CollectionManager from './CollectionManager';

interface PropertyCollectionsManagerProps {
  propertyId: string;
  collections: CollectionWithBudget[];
  onCollectionsChange: (collections: CollectionWithBudget[]) => void;
}

const PropertyCollectionsManager: React.FC<PropertyCollectionsManagerProps> = ({ 
  propertyId, 
  collections, 
  onCollectionsChange 
}) => {
  return (
    <div className="grid grid-cols-1 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Manage Collections</CardTitle>
          <CardDescription>
            Add, remove, or edit collections for this property and set budgets for each collection.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CollectionManager 
            collections={collections} 
            onChange={onCollectionsChange}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default PropertyCollectionsManager;
