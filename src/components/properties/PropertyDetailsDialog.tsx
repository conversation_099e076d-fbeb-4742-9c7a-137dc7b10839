import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Link, Save, X, Edit, Trash2, RefreshCw, Loader2 } from 'lucide-react';
import { Property } from '@/hooks/useProperties';
import { Property as CardProperty, CollectionWithBudget } from './PropertyCard';
import CollectionManager from './CollectionManager';
import ImageUploader from './ImageUploader';
import { useAuth } from '@/contexts/AuthContext';
import { fetchCalendarData } from '@/utils/propertyUtils';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

interface PropertyDetailsDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedProperty: Property | null;
  editProperty: CardProperty | null;
  setEditProperty: (property: CardProperty | null) => void;
  isEditMode: boolean;
  setIsEditMode: (editMode: boolean) => void;
  handleSaveEdit: () => void;
  handleDeleteProperty: () => void;
  onUpdate?: (id: string, data: any) => Promise<void>;
  onDelete?: () => void;
}

const PropertyDetailsDialog = ({
  isOpen,
  onOpenChange,
  selectedProperty,
  editProperty,
  setEditProperty,
  isEditMode,
  setIsEditMode,
  handleSaveEdit,
  handleDeleteProperty,
  onUpdate,
  onDelete
}: PropertyDetailsDialogProps) => {
  const { authState } = useAuth();
  const userId = authState.user?.id;
  const [isUpdatingIcal, setIsUpdatingIcal] = useState(false);
  
  const handleChange = (field: string, value: any) => {
    // Prevent update if editProperty is null
    if (!editProperty) {
      console.error("handleChange called while editProperty is null");
      return;
    }
    setEditProperty({
      ...editProperty,
      [field]: value
    });
  }
  
  const handleSaveWithMapping = () => {
    if (onUpdate && editProperty) {
      // Properly format collections for storage in the database
      const collectionsToSave = editProperty.collections ? 
        editProperty.collections.map(c => typeof c === 'string' ? c : c.name) : 
        [];
      
      console.log('Collections being saved:', collectionsToSave);
      
      // Make sure all properties are defined (not undefined) to match Property type requirements
      const mappedData = {
        id: editProperty.id,
        name: editProperty.name || '',
        address: editProperty.address || '',
        city: editProperty.city || '',
        state: editProperty.state || '',
        zip: editProperty.zip || '',
        image_url: editProperty.imageUrl || '',
        bedrooms: editProperty.bedrooms || 0,
        bathrooms: editProperty.bathrooms || 0,
        budget: editProperty.budget || 0,
        ical_url: editProperty.ical_url || '',
        next_booking: editProperty.next_booking || '',
        collections: collectionsToSave
      };
      
      onUpdate(editProperty.id, mappedData);
    } else {
      handleSaveEdit();
    }
  };

  const refreshIcalData = async () => {
    try {
      if (!editProperty?.ical_url || !userId) {
        toast.error('Missing iCal URL or user information');
        return;
      }
  
      setIsUpdatingIcal(true);
      
      console.log("Fetching iCal data for URL:", editProperty.ical_url);
      
      const { data, error } = await supabase.functions.invoke('fetch-ical-data', {
        body: {
          url: editProperty.ical_url,
          propertyId: editProperty.id,
          userId
        }
      });
      
      if (error) {
        console.error("Error invoking fetch-ical-data function:", error);
        throw error;
      }
      
      if (data.success && data.next_booking) {
        setEditProperty({
          ...editProperty,
          next_booking: data.next_booking
        });
        
        toast.success('Calendar data updated successfully!');
      } else if (data.success) {
        toast.info('No upcoming bookings found in calendar');
      } else {
        throw new Error(data.error || 'Unknown error refreshing calendar data');
      }
    } catch (error) {
      console.error('Error fetching iCal data:', error);
      toast.error(`Error fetching calendar data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsUpdatingIcal(false);
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? 'Edit Property' : selectedProperty?.name}
          </DialogTitle>
          {!isEditMode && (
            <DialogDescription>
              Property details and management
            </DialogDescription>
          )}
        </DialogHeader>
        
        {isEditMode ? (
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Property Name</Label>
              <Input
                id="name"
                value={editProperty?.name || ''}
                onChange={(e) => handleChange('name', e.target.value)}
                placeholder="Property Name"
              />
            </div>
            
            <ImageUploader 
              imageUrl={editProperty?.imageUrl || ''}
              onImageChange={(url) => handleChange('imageUrl', url)}
              propertyId={editProperty?.id}
            />

            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Input
                id="address"
                value={editProperty?.address || ''}
                onChange={(e) => handleChange('address', e.target.value)}
                placeholder="Address"
              />
            </div>
            
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={editProperty?.city || ''}
                  onChange={(e) => handleChange('city', e.target.value)}
                  placeholder="City"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  value={editProperty?.state || ''}
                  onChange={(e) => handleChange('state', e.target.value)}
                  placeholder="State"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="zip">ZIP Code</Label>
                <Input
                  id="zip"
                  value={editProperty?.zip || ''}
                  onChange={(e) => handleChange('zip', e.target.value)}
                  placeholder="ZIP Code"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bedrooms">Bedrooms</Label>
                <Input
                  id="bedrooms"
                  type="number"
                  value={editProperty?.bedrooms || 1}
                  onChange={(e) => handleChange('bedrooms', parseInt(e.target.value) || 1)}
                  min={1}
                  placeholder="Bedrooms"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="bathrooms">Bathrooms</Label>
                <Input
                  id="bathrooms"
                  type="number"
                  value={editProperty?.bathrooms || 1}
                  onChange={(e) => handleChange('bathrooms', parseInt(e.target.value) || 1)}
                  min={1}
                  placeholder="Bathrooms"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="budget">Budget</Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500">$</span>
                </div>
                <Input
                  id="budget"
                  type="number"
                  value={editProperty?.budget || 0}
                  onChange={(e) => handleChange('budget', parseFloat(e.target.value) || 0)}
                  className="pl-7"
                  placeholder="Budget"
                />
              </div>
            </div>
            
            <CollectionManager 
              collections={editProperty?.collections || []}
              onChange={(collections) => handleChange('collections', collections)}
            />
            
            <div className="space-y-2">
              <Label htmlFor="ical_url">
                iCal URL (Calendar Sync)
              </Label>
              <div className="flex gap-2">
                <div className="relative flex-grow">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Link size={16} className="text-gray-500" />
                  </div>
                  <Input
                    id="ical_url"
                    value={editProperty?.ical_url || ''}
                    onChange={(e) => handleChange('ical_url', e.target.value)}
                    className="pl-8"
                    placeholder="Calendar URL (Airbnb, VRBO, etc.)"
                  />
                </div>
                <Button 
                  variant="outline" 
                  size="icon"
                  onClick={refreshIcalData}
                  disabled={!editProperty?.ical_url || isUpdatingIcal}
                >
                  {isUpdatingIcal ? <Loader2 className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Paste your calendar URL from Airbnb, VRBO, or other booking platform to sync booking dates
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="next_booking">Next Booking</Label>
              <Input
                id="next_booking"
                value={editProperty?.next_booking || ''}
                onChange={(e) => handleChange('next_booking', e.target.value)}
                placeholder="Next Booking (or click refresh above to fetch)"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Click the refresh button above to fetch from calendar, or manually enter here
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-6 py-4">
            <div className="space-y-2">
              <Label>Property Name</Label>
              <p className="text-gray-800">{selectedProperty?.name}</p>
            </div>
            
            {selectedProperty?.image_url && (
              <div className="space-y-2">
                <Label>Property Image</Label>
                <img 
                  src={selectedProperty.image_url} 
                  alt="Property" 
                  className="w-full rounded-md aspect-video object-cover" 
                />
              </div>
            )}
            
            <div className="space-y-2">
              <Label>Address</Label>
              <p className="text-gray-800">{selectedProperty?.address}</p>
            </div>
            
            <div className="space-y-2">
              <Label>City, State, ZIP</Label>
              <p className="text-gray-800">
                {selectedProperty?.city}, {selectedProperty?.state} {selectedProperty?.zip}
              </p>
            </div>
            
            <div className="space-y-2">
              <Label>Bedrooms</Label>
              <p className="text-gray-800">{selectedProperty?.bedrooms}</p>
            </div>
            
            <div className="space-y-2">
              <Label>Bathrooms</Label>
              <p className="text-gray-800">{selectedProperty?.bathrooms}</p>
            </div>
            
            {selectedProperty?.budget && (
              <div className="space-y-2">
                <Label>Budget</Label>
                <p className="text-gray-800">${selectedProperty?.budget.toLocaleString()}</p>
              </div>
            )}
            
            {selectedProperty?.collections && selectedProperty?.collections.length > 0 && (
              <div className="space-y-2">
                <Label>Collections</Label>
                <div className="flex flex-wrap gap-2">
                  {selectedProperty.collections.map((collection, index) => (
                    <Badge key={index} variant="secondary">
                      {typeof collection === 'string' ? collection : collection.name}
                      {typeof collection === 'object' && collection.budget && ` ($${collection.budget})`}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            {selectedProperty?.next_booking && (
              <div className="space-y-2">
                <Label>Next Booking</Label>
                <p className="text-gray-800">{selectedProperty.next_booking}</p>
              </div>
            )}
          </div>
        )}
        
        <DialogFooter>
          {isEditMode ? (
            <>
              <Button 
                variant="outline" 
                onClick={() => setIsEditMode(false)}
              >
                Cancel
              </Button>
              <Button 
                className="flex items-center gap-2"
                onClick={handleSaveWithMapping}
              >
                <Save size={16} />
                Save Changes
              </Button>
            </>
          ) : (
            <>
              <Button 
                variant="outline" 
                className="flex items-center gap-2"
                onClick={() => onOpenChange(false)}
              >
                <X size={16} />
                Close
              </Button>
              <Button 
                variant="destructive" 
                className="flex items-center gap-2"
                onClick={handleDeleteProperty}
              >
                <Trash2 size={16} />
                Delete Property
              </Button>
              <Button 
                className="flex items-center gap-2"
                onClick={() => setIsEditMode(true)}
              >
                <Edit size={16} />
                Edit Property
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PropertyDetailsDialog;
