
import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { CollectionWithBudget } from './PropertyCard';
import CollectionManager from './CollectionManager';

interface CollectionsTabProps {
  collections: CollectionWithBudget[];
  onCollectionsChange: (collections: CollectionWithBudget[]) => void;
}

const CollectionsTab: React.FC<CollectionsTabProps> = ({ collections, onCollectionsChange }) => {
  return (
    <div className="grid grid-cols-1 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Property Collections</CardTitle>
        </CardHeader>
        <CardContent>
          <CollectionManager 
            collections={collections} 
            onChange={onCollectionsChange} 
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default CollectionsTab;
