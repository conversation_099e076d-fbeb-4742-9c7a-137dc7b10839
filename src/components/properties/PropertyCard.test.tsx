
import { render, screen } from '@/tests/utils/test-utils';
import PropertyCard from './PropertyCard';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';

const mockProperty = {
  id: 'test-property-id',
  name: 'Test Property',
  address: '123 Test St',
  city: 'Testville',
  state: 'TS',
  zip: '12345',
  bedrooms: 3,
  bathrooms: 2,
  budget: 200,
  imageUrl: 'https://example.com/test.jpg',
  next_booking: 'Jan 15 - Jan 20, 2024',
  collections: [{ name: 'Luxury' }]
};

describe('PropertyCard Component', () => {
  it('renders property information correctly', () => {
    const handleView = jest.fn();
    
    render(
      <BrowserRouter>
        <PropertyCard 
          property={mockProperty}
          onView={handleView}
        />
      </BrowserRouter>
    );
    
    // Verify that property details are displayed
    expect(screen.getByText(mockProperty.name)).toBeInTheDocument();
    expect(screen.getByText(`${mockProperty.city}, ${mockProperty.state}`)).toBeInTheDocument();
    expect(screen.getByText(`${mockProperty.bedrooms} Beds`)).toBeInTheDocument();
    expect(screen.getByText(`${mockProperty.bathrooms} Baths`)).toBeInTheDocument();
    
    // Verify collection is displayed
    expect(screen.getByText('Luxury')).toBeInTheDocument();
  });
  
  it('calls onClick handler when card is clicked', async () => {
    const handleClick = jest.fn();
    const handleView = jest.fn();
    const user = userEvent.setup();
    
    render(
      <BrowserRouter>
        <PropertyCard 
          property={mockProperty}
          onView={handleView}
          onClick={handleClick}
        />
      </BrowserRouter>
    );
    
    // Click on the card
    await user.click(screen.getByText('View Details'));
    
    // Verify that onClick handler was called
    expect(handleClick).toHaveBeenCalled();
  });
  
  it('calls onView when no onClick is provided', async () => {
    const handleView = jest.fn();
    const user = userEvent.setup();
    
    render(
      <BrowserRouter>
        <PropertyCard 
          property={mockProperty}
          onView={handleView}
        />
      </BrowserRouter>
    );
    
    // Click the view details button
    await user.click(screen.getByText('View Details'));
    
    // Verify that onView was called
    expect(handleView).toHaveBeenCalled();
  });
});
