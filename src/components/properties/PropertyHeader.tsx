
import React from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Plus, Filter, Grid3X3, List, RefreshCw } from 'lucide-react';
import {
  ToggleGroup,
  ToggleGroupItem
} from '@/components/ui/toggle-group';
import { StandardPageHeader } from '@/components/ui/StandardizedUI';

interface PropertyHeaderProps {
  searchQuery?: string;
  setSearchQuery?: (query: string) => void;
  handleFilterClick?: () => void;
  handleAddPropertyClick?: () => void;
  title?: string;
  description?: string;
  onAddProperty?: () => void;
  onToggleView?: (view: 'grid' | 'list') => void;
  onToggleFilters?: () => void;
  viewType?: 'grid' | 'list';
  filtersOpen?: boolean;
  propertyCount?: number;
  onSearch?: (term: string) => void;
  onRefresh?: () => void;
  isLoading?: boolean;
}

const PropertyHeader: React.FC<PropertyHeaderProps> = ({
  searchQuery = '',
  setSearchQuery,
  handleFilterClick,
  handleAddPropertyClick,
  title = 'Properties',
  description = 'Manage your properties and collections',
  onAddProperty,
  onToggleView,
  onToggleFilters,
  viewType = 'grid',
  filtersOpen = false,
  propertyCount,
  onSearch,
  onRefresh,
  isLoading = false
}) => {
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (setSearchQuery) {
      setSearchQuery(e.target.value);
    }
    if (onSearch) {
      onSearch(e.target.value);
    }
  };

  return (
    <StandardPageHeader
      title={title}
      description={description}
      searchQuery={searchQuery}
      onSearchChange={(value) => onSearch && onSearch(value)}
      searchPlaceholder="Search properties..."
      onRefresh={onRefresh}
      isLoading={isLoading}
      onToggleFilters={onToggleFilters || handleFilterClick}
      filtersOpen={filtersOpen}
      onToggleView={onToggleView}
      viewType={viewType}
      primaryActionLabel="Add Property"
      onPrimaryAction={onAddProperty || handleAddPropertyClick}
      primaryActionIcon={<Plus className="h-4 w-4 mr-2" />}
      itemCount={propertyCount}
      showViewToggle={true}
      showSearch={true}
      showFilterToggle={true}
    />
  );
};

export default PropertyHeader;
