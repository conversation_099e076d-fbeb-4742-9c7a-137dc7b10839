
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Property } from '@/hooks/useProperties';
import { Property as CardProperty } from '@/components/properties/PropertyCard';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Building, Link, Loader2, RefreshCw, Save, Clock } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { fetchCalendarData } from '@/utils/propertyUtils';
import ImageUploader from './ImageUploader';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import CollectionManager from './CollectionManager';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';

interface PropertyEditFormProps {
  property: Property;
  editProperty: CardProperty | null;
  setEditProperty: React.Dispatch<React.SetStateAction<CardProperty | null>>;
  onSave: () => void;
  onCancel: () => void;
  userId: string | undefined;
}

const PropertyEditForm: React.FC<PropertyEditFormProps> = ({
  property,
  editProperty,
  setEditProperty,
  onSave,
  onCancel,
  userId
}) => {
  const navigate = useNavigate();
  const [isSyncing, setIsSyncing] = useState(false);
  const [activeTab, setActiveTab] = useState("details");

  const syncCalendar = async () => {
    if (!property || !userId || !editProperty?.ical_url) {
      toast.error("Missing calendar URL or property information");
      return;
    }

    setIsSyncing(true);
    try {
      await fetchCalendarData(editProperty.ical_url, property.id, userId);
      toast.success("Calendar synced successfully");
    } catch (error) {
      console.error('Error syncing calendar:', error);
      toast.error("Failed to sync calendar");
    } finally {
      setIsSyncing(false);
    }
  };

  // Handle image changes from the ImageUploader component
  const handleImageChange = (url: string) => {
    setEditProperty(prev => prev ? { ...prev, imageUrl: url } : null);
  };

  // Handle collections updates
  const handleCollectionsChange = (collections: any[]) => {
    setEditProperty(prev => prev ? { ...prev, collections } : null);
  };

  return (
    <div className="container mx-auto px-4 py-8 pb-32">
      <div className="flex justify-between items-center mb-8">
        <Button variant="outline" onClick={onCancel}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Cancel
        </Button>

        <Button onClick={onSave} className="flex items-center gap-2">
          <Save className="h-4 w-4" />
          Save Changes
        </Button>
      </div>

      <div className="glass rounded-xl p-6 mb-8">
        <h2 className="text-xl font-bold mb-6">Edit Property</h2>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="details">Property Details</TabsTrigger>
            <TabsTrigger value="collections">Collections</TabsTrigger>
          </TabsList>

          <TabsContent value="details">
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <ImageUploader
                    imageUrl={editProperty?.imageUrl || ''}
                    onImageChange={handleImageChange}
                    propertyId={property.id}
                  />
                </div>

                <div className="md:col-span-2 space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Property Name</label>
                    <Input
                      value={editProperty?.name || ''}
                      onChange={(e) => setEditProperty(prev => prev ? { ...prev, name: e.target.value } : null)}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Address</label>
                    <Input
                      value={editProperty?.address || ''}
                      onChange={(e) => setEditProperty(prev => prev ? { ...prev, address: e.target.value } : null)}
                    />
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">City</label>
                      <Input
                        value={editProperty?.city || ''}
                        onChange={(e) => setEditProperty(prev => prev ? { ...prev, city: e.target.value } : null)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">State</label>
                      <Input
                        value={editProperty?.state || ''}
                        onChange={(e) => setEditProperty(prev => prev ? { ...prev, state: e.target.value } : null)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">ZIP</label>
                      <Input
                        value={editProperty?.zip || ''}
                        onChange={(e) => setEditProperty(prev => prev ? { ...prev, zip: e.target.value } : null)}
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium mb-1">Bedrooms</label>
                  <Input
                    type="number"
                    value={editProperty?.bedrooms || 1}
                    onChange={(e) => setEditProperty(prev => prev ? {
                      ...prev,
                      bedrooms: parseInt(e.target.value) || 1
                    } : null)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Bathrooms</label>
                  <Input
                    type="number"
                    value={editProperty?.bathrooms || 1}
                    onChange={(e) => setEditProperty(prev => prev ? {
                      ...prev,
                      bathrooms: parseInt(e.target.value) || 1
                    } : null)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Budget</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-muted-foreground">$</span>
                    </div>
                    <Input
                      className="pl-7"
                      type="number"
                      value={editProperty?.budget || 0}
                      onChange={(e) => setEditProperty(prev => prev ? {
                        ...prev,
                        budget: parseFloat(e.target.value) || 0
                      } : null)}
                    />
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Calendar URL (iCal)</label>
                <div className="flex gap-2">
                  <div className="relative flex-grow">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Link size={16} className="text-gray-500" />
                    </div>
                    <Input
                      className="pl-8"
                      placeholder="Paste iCal URL from Airbnb, VRBO, etc."
                      value={editProperty?.ical_url || ''}
                      onChange={(e) => setEditProperty(prev => prev ? {
                        ...prev,
                        ical_url: e.target.value
                      } : null)}
                    />
                  </div>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={syncCalendar}
                    disabled={!editProperty?.ical_url || isSyncing}
                  >
                    {isSyncing ? <Loader2 className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Paste your calendar URL from Airbnb, VRBO, or other booking platform to sync booking dates
                </p>
              </div>

              <div className="space-y-4 mt-6 p-4 border rounded-md bg-gray-50">
                <h3 className="font-medium">Check-in/Check-out Settings</h3>
                <p className="text-xs text-muted-foreground">
                  These settings are used for task automation and scheduling
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Timezone</label>
                    <Select
                      value={editProperty?.timezone || 'America/Los_Angeles'}
                      onValueChange={(value) => setEditProperty(prev => prev ? {
                        ...prev,
                        timezone: value
                      } : null)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select timezone" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="America/Los_Angeles">Pacific Time (PT)</SelectItem>
                        <SelectItem value="America/Denver">Mountain Time (MT)</SelectItem>
                        <SelectItem value="America/Chicago">Central Time (CT)</SelectItem>
                        <SelectItem value="America/New_York">Eastern Time (ET)</SelectItem>
                        <SelectItem value="America/Anchorage">Alaska Time</SelectItem>
                        <SelectItem value="Pacific/Honolulu">Hawaii Time</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Check-in Time</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Clock size={16} className="text-gray-500" />
                      </div>
                      <Input
                        className="pl-8"
                        type="time"
                        value={editProperty?.check_in_time || '15:00'}
                        onChange={(e) => setEditProperty(prev => prev ? {
                          ...prev,
                          check_in_time: e.target.value
                        } : null)}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Check-out Time</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Clock size={16} className="text-gray-500" />
                      </div>
                      <Input
                        className="pl-8"
                        type="time"
                        value={editProperty?.check_out_time || '11:00'}
                        onChange={(e) => setEditProperty(prev => prev ? {
                          ...prev,
                          check_out_time: e.target.value
                        } : null)}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="collections">
            <Card>
              <CardHeader>
                <CardTitle>Manage Property Collections</CardTitle>
              </CardHeader>
              <CardContent>
                <CollectionManager
                  collections={editProperty?.collections || []}
                  onChange={handleCollectionsChange}
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default PropertyEditForm;
