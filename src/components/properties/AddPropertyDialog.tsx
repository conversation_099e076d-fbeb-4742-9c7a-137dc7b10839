import React, { useState } from 'react';
import { <PERSON>, X } from 'lucide-react';
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '../ui/dialog';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Button } from '../ui/button';
import { Property } from '@/hooks/useProperties';
import { CollectionWithBudget } from './PropertyCard';
import CollectionManager from './CollectionManager';
import ImageUploader from './ImageUploader';
import { toast } from 'sonner';

interface AddPropertyDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  newProperty: any; // Using 'any' temporarily to accommodate both interfaces
  setNewProperty: (property: any) => void;
  handleAddProperty: () => Promise<void>;
  // For backward compatibility
  onAdd?: (newProperty: any) => Promise<void>;
}

const AddPropertyDialog: React.FC<AddPropertyDialogProps> = ({
  isOpen,
  onOpenChange,
  newProperty,
  setNewProperty,
  handleAddProperty,
  onAdd
}) => {
  // State to hold the temporary property ID (will be set after initial creation)
  const [tempPropertyId, setTempPropertyId] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = () => {
    // Check required fields
    if (!newProperty.name) {
      toast.error("Property name is required");
      return false;
    }

    if (!newProperty.address) {
      toast.error("Property address is required");
      return false;
    }

    if (!newProperty.city) {
      toast.error("City is required");
      return false;
    }

    if (!newProperty.state) {
      toast.error("State is required");
      return false;
    }

    if (!newProperty.zip) {
      toast.error("ZIP code is required");
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    if (isSubmitting) {
      return; // Prevent multiple submissions
    }

    setIsSubmitting(true);

    try {
      // Convert collection objects to the format needed for storage
      const processedNewProperty = { ...newProperty };

      if (processedNewProperty.collections && Array.isArray(processedNewProperty.collections)) {
        console.log('Processing collections for storage:', processedNewProperty.collections);
      }

      console.log("Submitting property:", processedNewProperty);

      // Use Promise.race to ensure we have a timeout if the request hangs
      const timeoutPromise = new Promise<void>((_, reject) =>
        setTimeout(() => reject(new Error("Request timed out")), 30000)
      );

      if (onAdd) {
        await Promise.race([onAdd(processedNewProperty), timeoutPromise]);
      } else {
        await Promise.race([handleAddProperty(), timeoutPromise]);
      }

      console.log("Property added successfully");
      // Explicitly close the dialog on success
      onOpenChange(false);

    } catch (error) {
      console.error("Error adding property:", error);
      toast.error(`Failed to add property: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(newOpen) => {
      // Don't allow closing during submission
      if (isSubmitting && !newOpen) return;
      onOpenChange(newOpen);
    }}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New Property</DialogTitle>
          <DialogDescription>
            Enter the details for your new property
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="propertyName">Property Name</Label>
            <Input
              id="propertyName"
              placeholder="Enter property name"
              value={newProperty.name || ''}
              onChange={(e) => setNewProperty({...newProperty, name: e.target.value})}
            />
          </div>

          <ImageUploader
            imageUrl={newProperty.imageUrl || ''}
            onImageChange={(url) => setNewProperty({...newProperty, imageUrl: url})}
            propertyId={tempPropertyId || undefined}
          />

          <div className="space-y-2">
            <Label htmlFor="address">Address</Label>
            <Input
              id="address"
              placeholder="Enter property address"
              value={newProperty.address || ''}
              onChange={(e) => setNewProperty({...newProperty, address: e.target.value})}
            />
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city">City</Label>
              <Input
                id="city"
                placeholder="City"
                value={newProperty.city || ''}
                onChange={(e) => setNewProperty({...newProperty, city: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="state">State</Label>
              <Input
                id="state"
                placeholder="State"
                value={newProperty.state || ''}
                onChange={(e) => setNewProperty({...newProperty, state: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="zip">ZIP Code</Label>
              <Input
                id="zip"
                placeholder="ZIP Code"
                value={newProperty.zip || ''}
                onChange={(e) => setNewProperty({...newProperty, zip: e.target.value})}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="bedrooms">Bedrooms</Label>
              <Input
                id="bedrooms"
                type="number"
                placeholder="Number of bedrooms"
                min={1}
                value={newProperty.bedrooms || 1}
                onChange={(e) => setNewProperty({...newProperty, bedrooms: parseInt(e.target.value) || 1})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="bathrooms">Bathrooms</Label>
              <Input
                id="bathrooms"
                type="number"
                placeholder="Number of bathrooms"
                min={1}
                value={newProperty.bathrooms || 1}
                onChange={(e) => setNewProperty({...newProperty, bathrooms: parseInt(e.target.value) || 1})}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="budget">Property Budget</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-muted-foreground">$</span>
              </div>
              <Input
                id="budget"
                type="number"
                placeholder="Property budget"
                min={0}
                className="pl-8"
                value={newProperty.budget || 0}
                onChange={(e) => setNewProperty({...newProperty, budget: parseFloat(e.target.value) || 0})}
              />
            </div>
          </div>

          <CollectionManager
            collections={newProperty.collections || []}
            onChange={(collections) => setNewProperty({...newProperty, collections})}
          />

          <div className="space-y-2">
            <Label htmlFor="iCalUrl">iCal URL for Calendar Sync (Optional)</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Link size={16} className="text-muted-foreground" />
              </div>
              <Input
                id="iCalUrl"
                placeholder="https://www.airbnb.com/calendar/ical/XXXXX.ics?s=XXXXX"
                value={newProperty.iCalUrl || ''}
                onChange={(e) => setNewProperty({...newProperty, iCalUrl: e.target.value})}
                className="pl-10"
              />
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Example: https://www.airbnb.com/calendar/ical/836891384695100855.ics?s=f04b9457cfab0306d1309259129e618b
            </p>
          </div>
        </div>
        <DialogFooter>
          <Button
            type="submit"
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Adding...' : 'Add Property'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddPropertyDialog;
