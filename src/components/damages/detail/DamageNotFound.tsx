
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const DamageNotFound: React.FC = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6 text-center">
          <h2 className="text-xl font-semibold mb-2">Damage Report Not Found</h2>
          <p className="text-muted-foreground mb-4">
            The damage report you're looking for doesn't exist or has been removed.
          </p>
          <Link to="/#/damages">
            <Button>Back to Damage Reports</Button>
          </Link>
        </CardContent>
      </Card>
    </div>
  );
};

export default DamageNotFound;
