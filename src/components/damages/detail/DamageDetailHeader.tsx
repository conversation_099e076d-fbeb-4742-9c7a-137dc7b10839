
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Download, Pencil } from 'lucide-react';

interface DamageDetailHeaderProps {
  id: string;
  onDownload: () => void;
  isExporting: boolean;
}

const DamageDetailHeader: React.FC<DamageDetailHeaderProps> = ({
  id,
  onDownload,
  isExporting
}) => {
  return (
    <div className="flex justify-between items-center">
      <div className="flex items-center gap-4">
        <Link to="/damages">
          <Button variant="outline">Back</Button>
        </Link>
        <h1 className="text-3xl font-bold">Damage Report</h1>
      </div>
      <div className="space-x-2">
        <Button
          variant="outline"
          onClick={onDownload}
          disabled={isExporting}
        >
          <Download className="h-4 w-4 mr-2" />
          {isExporting ? 'Generating...' : 'Download Report'}
        </Button>
        <Link to={`/damages/${id}/edit`}>
          <Button>
            <Pencil className="h-4 w-4 mr-2" />
            Edit Report
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default DamageDetailHeader;
