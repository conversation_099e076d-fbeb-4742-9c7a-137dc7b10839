
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

const DamageDetailSkeleton: React.FC = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="h-8 w-40 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-10 w-24 bg-gray-200 rounded animate-pulse"></div>
          </div>
          <div className="space-y-4">
            <div className="h-6 w-full bg-gray-200 rounded animate-pulse"></div>
            <div className="h-6 w-3/4 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DamageDetailSkeleton;
