
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2 } from 'lucide-react'; // Import Loader2
import { DamageReport } from '@/types/damages';
import { toast } from 'sonner';

export interface Property {
  id: string;
  name: string;
}

export interface Provider {
  id: string;
  name: string;
  specialty?: string;
}

interface AddDamageReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (report: Omit<DamageReport, 'id' | 'created_at' | 'updated_at' | 'property_name' | 'provider_name'>) => Promise<void>;
  properties: Property[];
  providers: Provider[];
}

const platformOptions = ["Airbnb", "VRBO", "Booking.com", "Expedia", "Other"];

const AddDamageReportDialog = ({ open, onOpenChange, onSave, properties, providers }: AddDamageReportDialogProps) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [propertyId, setPropertyId] = useState('');
  const [providerId, setProviderId] = useState('none');
  const [platform, setPlatform] = useState('none');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Debug log for properties
  React.useEffect(() => {
    console.log('AddDamageReportDialog - properties:', properties);
  }, [properties]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title || !description || !propertyId) {
      toast.error('Please fill out all required fields');
      return;
    }

    setIsSubmitting(true);
    try {
      await onSave({
        title,
        description,
        property_id: propertyId,
        status: 'open',
        provider_id: providerId === 'none' ? undefined : providerId,
        platform: platform === 'none' ? undefined : platform
      });

      toast.success('Damage report submitted successfully!'); // Add success toast
      onOpenChange(false); // Close dialog on success

      // Reset form (can happen after closing)
      setTitle('');
      setDescription('');
      setPropertyId('');
      setProviderId('none');
      setPlatform('none');
    } catch (error) {
      console.error('Error saving damage report:', error);
      toast.error('Failed to save damage report');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    setTitle('');
    setDescription('');
    setPropertyId('');
    setProviderId('none');
    setPlatform('none');
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(newState) => {
        if (!newState) handleReset();
        onOpenChange(newState);
      }}
    >
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Report New Damage</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 mt-4">
          <div className="space-y-2">
            {/* Add required indicator */}
            <Label htmlFor="property">Property <span className="text-destructive">*</span></Label>
            <Select
              value={propertyId}
              onValueChange={(value) => {
                console.log('Property selected:', value);
                setPropertyId(value);
              }}
              required
            >
              <SelectTrigger onClick={() => console.log('Property dropdown clicked, properties:', properties)}>
                <SelectValue placeholder="Select a property" />
              </SelectTrigger>
              <SelectContent>
                {properties.length > 0 ? (
                  properties.map(property => (
                    <SelectItem key={property.id} value={property.id}>{property.name}</SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-properties" disabled>No properties available</SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="provider">Assign Provider (Optional)</Label>
            <Select
              value={providerId}
              onValueChange={setProviderId}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a provider (optional)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                {providers.map(provider => (
                  <SelectItem key={provider.id} value={provider.id}>
                    {provider.name}{provider.specialty ? ` (${provider.specialty})` : ''}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="platform">Platform</Label>
            <Select
              value={platform}
              onValueChange={setPlatform}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a platform (optional)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                {platformOptions.map(platform => (
                  <SelectItem key={platform} value={platform}>{platform}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            {/* Add required indicator */}
            <Label htmlFor="title">Title <span className="text-destructive">*</span></Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Broken window, Water leak, etc."
              required
            />
          </div>

          <div className="space-y-2">
            {/* Add required indicator */}
            <Label htmlFor="description">Description <span className="text-destructive">*</span></Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Please provide details about the damage..."
              rows={5}
              required
            />
          </div>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting} className="flex items-center gap-2">
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Submitting...
                </>
              ) : (
                'Submit Report'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddDamageReportDialog;
