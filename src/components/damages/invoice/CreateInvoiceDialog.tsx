
import React, { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogFooter 
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import InvoiceItemManager, { InvoiceItem } from './InvoiceItemManager';
import { useInvoiceForm } from './useInvoiceForm';

interface CreateInvoiceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  reportId: string;
  onCreated: () => void;
  providers: Array<{
    id: string;
    name: string;
    specialty?: string;
  }>;
  existingInvoiceId?: string;
  isEditing?: boolean;
}

const CreateInvoiceDialog: React.FC<CreateInvoiceDialogProps> = ({
  open,
  onOpenChange,
  reportId,
  onCreated,
  providers,
  existingInvoiceId,
  isEditing = false
}) => {
  const { authState } = useAuth();
  const {
    invoiceNumber,
    setInvoiceNumber,
    selectedProviderId,
    setSelectedProviderId,
    status,
    setStatus,
    issueDate,
    setIssueDate,
    items,
    setItems,
    isLoading,
    setIsLoading,
    invoiceLoaded,
    hasError,
    updateItem,
    handleAddItem,
    handleRemoveItem,
    calculateTotal,
    validateForm,
    generateInvoiceNumber,
  } = useInvoiceForm(reportId, existingInvoiceId, isEditing, onCreated);

  // Due date is calculated as 30 days after issue date
  const getDueDate = () => {
    const date = new Date(issueDate);
    date.setDate(date.getDate() + 30);
    return date.toISOString().split('T')[0];
  };

  const handleSubmit = async () => {
    if (!validateForm() || !authState.user?.id) return;

    try {
      setIsLoading(true);
      
      const total = calculateTotal();
      const dueDate = getDueDate();
      
      // Prepare invoice data
      const invoiceData = {
        invoice_number: invoiceNumber,
        damage_report_id: reportId,
        provider_id: selectedProviderId === 'none' ? null : selectedProviderId,
        total_amount: total,
        issue_date: issueDate,
        due_date: dueDate,
        status: status,
        user_id: authState.user.id
      };

      if (isEditing && existingInvoiceId) {
        // Update existing invoice
        const { error: invoiceError } = await supabase
          .from('damage_invoices')
          .update(invoiceData)
          .eq('id', existingInvoiceId);
        
        if (invoiceError) throw invoiceError;

        // Delete existing invoice items and recreate them
        const { error: deleteError } = await supabase
          .from('invoice_items')
          .delete()
          .eq('invoice_id', existingInvoiceId);
        
        if (deleteError) throw deleteError;

        // Create new invoice items
        const itemsData = items.map(item => ({
          invoice_id: existingInvoiceId,
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          amount: item.amount
        }));
        
        const { error: itemsError } = await supabase
          .from('invoice_items')
          .insert(itemsData);
        
        if (itemsError) throw itemsError;

        // Add activity record
        await supabase
          .from('damage_report_activities')
          .insert({
            damage_report_id: reportId,
            user_id: authState.user.id,
            action: 'update',
            details: `Updated invoice #${invoiceNumber}`
          });
        
        toast.success('Invoice updated successfully');
      } else {
        // Create new invoice
        const { data: invoice, error: invoiceError } = await supabase
          .from('damage_invoices')
          .insert(invoiceData)
          .select()
          .single();
        
        if (invoiceError) throw invoiceError;
        
        // Create invoice items
        const itemsData = items.map(item => ({
          invoice_id: invoice.id,
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          amount: item.amount
        }));
        
        const { error: itemsError } = await supabase
          .from('invoice_items')
          .insert(itemsData);
        
        if (itemsError) throw itemsError;

        // Add activity record
        await supabase
          .from('damage_report_activities')
          .insert({
            damage_report_id: reportId,
            user_id: authState.user.id,
            action: 'create',
            details: `Created invoice #${invoiceNumber}`
          });
        
        toast.success('Invoice created successfully');
      }
      
      // Reset form and close dialog
      onOpenChange(false);
      onCreated();
    } catch (error) {
      console.error('Error creating/updating invoice:', error);
      toast.error(`Failed to ${isEditing ? 'update' : 'create'} invoice`);
    } finally {
      setIsLoading(false);
    }
  };

  if (hasError) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Error</DialogTitle>
          </DialogHeader>
          <div className="py-6">
            <p className="text-center text-red-500">
              An error occurred while loading the invoice data.
            </p>
          </div>
          <DialogFooter>
            <Button onClick={() => onOpenChange(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  if (!invoiceLoaded) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{isEditing ? 'Edit Invoice' : 'Create Invoice'}</DialogTitle>
          </DialogHeader>
          <div className="py-6">
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
            <p className="text-center mt-4">Loading invoice data...</p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Edit Invoice' : 'Create Invoice'}</DialogTitle>
        </DialogHeader>
        
        <div className="grid grid-cols-2 gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="invoice-number">Invoice Number</Label>
            <div className="flex space-x-2">
              <Input
                id="invoice-number"
                value={invoiceNumber}
                onChange={(e) => setInvoiceNumber(e.target.value)}
                className="flex-1"
              />
              {!isEditing && (
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={generateInvoiceNumber}
                  className="whitespace-nowrap"
                >
                  Generate
                </Button>
              )}
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger id="status">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label>Issue Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !issueDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {issueDate ? format(new Date(issueDate), "PPP") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={issueDate ? new Date(issueDate) : undefined}
                  onSelect={(date) => setIssueDate(date ? date.toISOString().split('T')[0] : '')}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="provider">Service Provider</Label>
            <Select value={selectedProviderId} onValueChange={setSelectedProviderId}>
              <SelectTrigger id="provider">
                <SelectValue placeholder="Select provider" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                {providers.map(provider => (
                  <SelectItem key={provider.id} value={provider.id}>
                    {provider.name}{provider.specialty ? ` (${provider.specialty})` : ''}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <InvoiceItemManager
          items={items}
          onAddItem={handleAddItem}
          onRemoveItem={handleRemoveItem}
          onUpdateItem={updateItem}
          calculateTotal={calculateTotal}
        />
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? 'Saving...' : isEditing ? 'Update Invoice' : 'Create Invoice'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreateInvoiceDialog;
