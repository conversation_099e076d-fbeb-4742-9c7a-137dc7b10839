
import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface Provider {
  id: string;
  name: string;
}

interface InvoiceDetailsFormProps {
  invoiceNumber: string;
  setInvoiceNumber: (value: string) => void;
  issueDate: string;
  setIssueDate: (value: string) => void;
  providerId: string;
  setProviderId: (value: string) => void;
  status: string;
  setStatus: (value: string) => void;
  providers: Provider[];
}

const InvoiceDetailsForm: React.FC<InvoiceDetailsFormProps> = ({
  invoiceNumber,
  setInvoiceNumber,
  issueDate,
  setIssueDate,
  providerId,
  setProviderId,
  status,
  setStatus,
  providers,
}) => {
  return (
    <>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="invoice-number">Invoice Number</Label>
          <Input
            id="invoice-number"
            value={invoiceNumber}
            onChange={e => setInvoiceNumber(e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="issue-date">Issue Date</Label>
          <Input
            id="issue-date"
            type="date"
            value={issueDate}
            onChange={e => setIssueDate(e.target.value)}
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="provider">Service Provider</Label>
          <Select value={providerId} onValueChange={setProviderId}>
            <SelectTrigger>
              <SelectValue placeholder="Select provider" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="no-provider">No Provider</SelectItem>
              {providers.map(provider => (
                <SelectItem key={provider.id} value={provider.id}>
                  {provider.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select value={status} onValueChange={setStatus}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="paid">Paid</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </>
  );
};

export default InvoiceDetailsForm;
