
import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface Provider {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  specialty?: string;
}

interface InvoiceFormFieldsProps {
  invoiceNumber: string;
  setInvoiceNumber: (value: string) => void;
  totalAmount: string;
  setTotalAmount: (value: string) => void;
  providerId: string;
  setProviderId: (value: string) => void;
  status: string;
  setStatus: (value: string) => void;
  providers: Provider[];
  isLoading: boolean;
}

const InvoiceFormFields: React.FC<InvoiceFormFieldsProps> = ({
  invoiceNumber,
  setInvoiceNumber,
  totalAmount,
  setTotalAmount,
  providerId,
  setProviderId,
  status,
  setStatus,
  providers,
  isLoading
}) => {
  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="invoice-number">Invoice Number</Label>
        <Input 
          id="invoice-number" 
          value={invoiceNumber}
          onChange={(e) => setInvoiceNumber(e.target.value)}
          placeholder="INV-123456"
          disabled={isLoading}
          required
        />
      </div>

      <div>
        <Label htmlFor="provider">Provider (optional)</Label>
        <Select 
          value={providerId} 
          onValueChange={setProviderId}
          disabled={isLoading}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select provider (optional)" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">No Provider</SelectItem>
            {providers.map(provider => (
              <SelectItem key={provider.id} value={provider.id}>
                {provider.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="total-amount">Total Amount (optional)</Label>
        <Input 
          id="total-amount" 
          value={totalAmount}
          onChange={(e) => setTotalAmount(e.target.value)}
          placeholder="0.00"
          type="number"
          step="0.01"
          min="0"
          disabled={isLoading}
        />
      </div>

      <div>
        <Label htmlFor="status">Status</Label>
        <Select 
          value={status} 
          onValueChange={setStatus}
          disabled={isLoading}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="paid">Paid</SelectItem>
            <SelectItem value="void">Void</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default InvoiceFormFields;
