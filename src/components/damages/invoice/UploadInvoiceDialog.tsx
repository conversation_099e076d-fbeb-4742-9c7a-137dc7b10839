
import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogFooter 
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { UploadCloud, Loader2 } from 'lucide-react';

interface UploadInvoiceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  damageReportId: string;
  onUploaded: () => void;
  providers: Array<{
    id: string;
    name: string;
    specialty?: string;
  }>;
}

const UploadInvoiceDialog: React.FC<UploadInvoiceDialogProps> = ({
  open,
  onOpenChange,
  damageReportId,
  onUploaded,
  providers
}) => {
  const { authState } = useAuth();
  const [invoiceNumber, setInvoiceNumber] = useState('');
  const [selectedProviderId, setSelectedProviderId] = useState('none');
  const [status, setStatus] = useState('pending');
  const [totalAmount, setTotalAmount] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      
      // Check if file is a PDF
      if (file.type !== 'application/pdf') {
        toast.error('Please select a PDF file');
        return;
      }
      
      // Check file size (limit to 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast.error('File size should not exceed 10MB');
        return;
      }
      
      setSelectedFile(file);
    }
  };

  const generateInvoiceNumber = () => {
    const randomNumber = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    const date = new Date().toISOString().split('T')[0].replace(/-/g, '');
    setInvoiceNumber(`INV-${date}-${randomNumber}`);
  };

  const handleSubmit = async () => {
    if (!selectedFile || !damageReportId || !authState.user?.id) {
      toast.error('Please select a PDF file to upload');
      return;
    }
    
    if (!invoiceNumber.trim()) {
      toast.error('Please enter an invoice number');
      return;
    }
    
    setIsUploading(true);
    try {
      console.log('Uploading invoice with data:', {
        damageReportId, 
        userId: authState.user.id,
        invoiceNumber,
        providerId: selectedProviderId,
        totalAmount,
        status,
        fileName: selectedFile.name,
        fileSize: selectedFile.size
      });
      
      // Create form data
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('damageReportId', damageReportId);
      formData.append('userId', authState.user.id);
      formData.append('invoiceNumber', invoiceNumber);
      formData.append('providerId', selectedProviderId === 'none' ? 'none' : selectedProviderId);
      formData.append('totalAmount', totalAmount || '0');
      formData.append('status', status);
      
      // Call the Supabase Edge Function for upload
      const { data, error } = await supabase.functions.invoke('upload-invoice-pdf', {
        body: formData,
      });
      
      if (error) {
        console.error('Error invoking upload-invoice-pdf function:', error);
        throw new Error(error.message || 'Failed to upload invoice');
      }
      
      if (!data) {
        throw new Error('No response from upload function');
      }
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to process invoice upload');
      }
      
      console.log('Upload function response:', data);
      
      // Add activity record
      await supabase
        .from('damage_report_activities')
        .insert({
          damage_report_id: damageReportId,
          user_id: authState.user.id,
          action: 'upload',
          details: `Uploaded invoice PDF: ${invoiceNumber}`
        });
      
      toast.success('Invoice uploaded successfully');
      onOpenChange(false);
      onUploaded();
      
      // Reset form
      setSelectedFile(null);
      setInvoiceNumber('');
      setSelectedProviderId('none');
      setStatus('pending');
      setTotalAmount('');
    } catch (error) {
      console.error('Error uploading invoice:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to upload invoice');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Upload Invoice PDF</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="invoice-file">Select PDF File</Label>
            <Input
              id="invoice-file"
              type="file"
              accept="application/pdf"
              onChange={handleFileChange}
            />
            {selectedFile && (
              <p className="text-sm text-muted-foreground mt-1">
                Selected: {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
              </p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="invoice-number">Invoice Number</Label>
            <div className="flex space-x-2">
              <Input
                id="invoice-number"
                value={invoiceNumber}
                onChange={(e) => setInvoiceNumber(e.target.value)}
                placeholder="Enter invoice number"
                className="flex-1"
              />
              <Button 
                type="button" 
                variant="outline" 
                onClick={generateInvoiceNumber}
                className="whitespace-nowrap"
              >
                Generate
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="provider">Service Provider (Optional)</Label>
              <Select value={selectedProviderId} onValueChange={setSelectedProviderId}>
                <SelectTrigger id="provider">
                  <SelectValue placeholder="Select provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  {providers.map(provider => (
                    <SelectItem key={provider.id} value={provider.id}>
                      {provider.name}{provider.specialty ? ` (${provider.specialty})` : ''}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="total-amount">Total Amount (Optional)</Label>
            <Input
              id="total-amount"
              type="number"
              min="0"
              step="0.01"
              value={totalAmount}
              onChange={(e) => setTotalAmount(e.target.value)}
              placeholder="Enter amount"
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isUploading}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={!selectedFile || isUploading || !invoiceNumber.trim()}
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <UploadCloud className="mr-2 h-4 w-4" />
                Upload Invoice
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default UploadInvoiceDialog;
