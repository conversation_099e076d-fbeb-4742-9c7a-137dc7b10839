
import React from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { FileUp } from 'lucide-react';

interface FileUploadAreaProps {
  selectedFile: File | null;
  onFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  isLoading: boolean;
}

const FileUploadArea: React.FC<FileUploadAreaProps> = ({
  selectedFile,
  onFileChange,
  isLoading
}) => {
  return (
    <div 
      className="border-2 border-dashed rounded-lg p-6 text-center cursor-pointer hover:bg-gray-50 transition-colors"
      onClick={() => document.getElementById('invoice-pdf')?.click()}
    >
      <FileUp className="h-10 w-10 mx-auto text-gray-400 mb-2" />
      <p className="text-sm text-muted-foreground mb-2">
        {selectedFile ? selectedFile.name : "Click to upload or drag and drop"}
      </p>
      {selectedFile && (
        <p className="text-xs text-green-600">File selected</p>
      )}
      <Input
        id="invoice-pdf"
        type="file"
        accept="application/pdf"
        className="hidden"
        onChange={onFileChange}
        disabled={isLoading}
      />
      <Button
        variant="outline"
        size="sm"
        className="mt-2"
        type="button"
        disabled={isLoading}
        onClick={(e) => {
          e.stopPropagation();
          document.getElementById('invoice-pdf')?.click();
        }}
      >
        Select PDF
      </Button>
    </div>
  );
};

export default FileUploadArea;
