
import React from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { PlusCircle, Trash } from 'lucide-react';

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  amount: number;
}

interface InvoiceItemManagerProps {
  items: InvoiceItem[];
  onAddItem: () => void;
  onRemoveItem: (id: string) => void;
  onUpdateItem: (id: string, field: keyof InvoiceItem, value: any) => void;
  calculateTotal: () => number;
}

const InvoiceItemManager: React.FC<InvoiceItemManagerProps> = ({
  items,
  onAddItem,
  onRemoveItem,
  onUpdateItem,
  calculateTotal,
}) => {
  return (
    <div className="space-y-4 mt-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Invoice Items</h3>
        <Button type="button" variant="outline" size="sm" onClick={onAddItem}>
          <PlusCircle className="h-4 w-4 mr-2" />
          Add Item
        </Button>
      </div>

      <div className="space-y-4">
        {items.map((item, index) => (
          <div key={item.id} className="grid grid-cols-12 gap-2 items-end border p-3 rounded-md">
            <div className="col-span-5 space-y-1">
              <Label htmlFor={`item-desc-${index}`}>Description</Label>
              <Input
                id={`item-desc-${index}`}
                value={item.description}
                onChange={e => onUpdateItem(item.id, 'description', e.target.value)}
                placeholder="Item description"
              />
            </div>
            <div className="col-span-2 space-y-1">
              <Label htmlFor={`item-qty-${index}`}>Quantity</Label>
              <Input
                id={`item-qty-${index}`}
                type="number"
                min="1"
                value={item.quantity}
                onChange={e => onUpdateItem(item.id, 'quantity', Number(e.target.value))}
              />
            </div>
            <div className="col-span-2 space-y-1">
              <Label htmlFor={`item-price-${index}`}>Unit Price</Label>
              <Input
                id={`item-price-${index}`}
                type="number"
                min="0"
                step="0.01"
                value={item.unit_price}
                onChange={e => onUpdateItem(item.id, 'unit_price', Number(e.target.value))}
              />
            </div>
            <div className="col-span-2 space-y-1">
              <Label htmlFor={`item-amount-${index}`}>Amount</Label>
              <Input
                id={`item-amount-${index}`}
                readOnly
                value={`$${item.amount.toFixed(2)}`}
              />
            </div>
            <div className="col-span-1 flex justify-center">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onRemoveItem(item.id)}
                disabled={items.length === 1}
              >
                <Trash className="h-4 w-4 text-destructive" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-end mt-4">
        <div className="w-1/3 space-y-2 border p-3 rounded-md">
          <div className="flex justify-between">
            <span className="font-medium">Total:</span>
            <span className="font-bold">${calculateTotal().toFixed(2)}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvoiceItemManager;
