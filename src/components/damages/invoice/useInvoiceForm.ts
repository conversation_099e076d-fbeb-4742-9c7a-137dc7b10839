
import { useState, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { InvoiceItem } from './InvoiceItemManager';

export const useInvoiceForm = (
  reportId: string,
  existingInvoiceId?: string,
  isEditing = false,
  onSuccess?: () => void
) => {
  const [invoiceNumber, setInvoiceNumber] = useState('');
  const [selectedProviderId, setSelectedProviderId] = useState('');
  const [status, setStatus] = useState('draft');
  const [issueDate, setIssueDate] = useState(new Date().toISOString().split('T')[0]);
  const [items, setItems] = useState<InvoiceItem[]>([
    {
      id: uuidv4(),
      description: '',
      quantity: 1,
      unit_price: 0,
      amount: 0
    }
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [invoiceLoaded, setInvoiceLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    if (isEditing && existingInvoiceId) {
      fetchInvoiceData();
    } else {
      generateInvoiceNumber();
      setInvoiceLoaded(true);
    }
  }, [existingInvoiceId, isEditing]);

  const generateInvoiceNumber = () => {
    const randomNumber = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    const date = new Date().toISOString().split('T')[0].replace(/-/g, '');
    setInvoiceNumber(`INV-${date}-${randomNumber}`);
  };

  const fetchInvoiceData = async () => {
    if (!existingInvoiceId) return;
    
    try {
      setIsLoading(true);
      
      // Fetch invoice header
      const { data: invoice, error: invoiceError } = await supabase
        .from('damage_invoices')
        .select('*')
        .eq('id', existingInvoiceId)
        .single();
      
      if (invoiceError) throw invoiceError;
      
      // Set invoice header fields
      setInvoiceNumber(invoice.invoice_number);
      setSelectedProviderId(invoice.provider_id || '');
      setStatus(invoice.status || 'draft');
      setIssueDate(invoice.issue_date || new Date().toISOString().split('T')[0]);
      
      // Fetch invoice items
      const { data: invoiceItems, error: itemsError } = await supabase
        .from('invoice_items')
        .select('*')
        .eq('invoice_id', existingInvoiceId);
      
      if (itemsError) throw itemsError;
      
      if (invoiceItems && invoiceItems.length > 0) {
        // Map DB items to our item format
        const formattedItems = invoiceItems.map(item => ({
          id: item.id || uuidv4(),
          description: item.description || '',
          quantity: item.quantity || 1,
          unit_price: item.unit_price || 0,
          amount: item.amount || 0
        }));
        
        setItems(formattedItems);
      }
      
      setInvoiceLoaded(true);
    } catch (error) {
      console.error('Error fetching invoice data:', error);
      toast.error('Failed to load invoice data');
      setHasError(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddItem = () => {
    setItems([
      ...items,
      {
        id: uuidv4(),
        description: '',
        quantity: 1,
        unit_price: 0,
        amount: 0
      }
    ]);
  };

  const handleRemoveItem = (id: string) => {
    if (items.length > 1) {
      setItems(items.filter(item => item.id !== id));
    } else {
      toast.error('At least one item is required');
    }
  };

  const updateItem = (id: string, field: keyof InvoiceItem, value: any) => {
    setItems(
      items.map(item => {
        if (item.id === id) {
          const updatedItem = { ...item, [field]: value };
          
          // Recalculate amount if quantity or unit_price changed
          if (field === 'quantity' || field === 'unit_price') {
            updatedItem.amount = updatedItem.quantity * updatedItem.unit_price;
          }
          
          return updatedItem;
        }
        return item;
      })
    );
  };

  const calculateTotal = () => {
    return items.reduce((sum, item) => sum + item.amount, 0);
  };

  const validateForm = () => {
    if (!invoiceNumber.trim()) {
      toast.error('Invoice number is required');
      return false;
    }

    if (items.some(item => !item.description.trim() || item.quantity <= 0)) {
      toast.error('All items must have a description and positive quantity');
      return false;
    }

    return true;
  };

  return {
    invoiceNumber,
    setInvoiceNumber,
    selectedProviderId,
    setSelectedProviderId,
    status,
    setStatus,
    issueDate,
    setIssueDate,
    items,
    setItems,
    isLoading,
    setIsLoading,
    invoiceLoaded,
    hasError,
    handleAddItem,
    handleRemoveItem,
    updateItem,
    calculateTotal,
    validateForm,
    generateInvoiceNumber,
  };
};
