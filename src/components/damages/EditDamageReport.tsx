import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { <PERSON>, CardHeader, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { DamageReport, Property, Provider } from '@/types/damages';
import { Loader2, Save, ArrowLeft } from 'lucide-react';

const EditDamageReport = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { authState } = useAuth();
  const userId = authState.user?.id;

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [report, setReport] = useState<DamageReport | null>(null);
  const [properties, setProperties] = useState<Property[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [propertyId, setPropertyId] = useState('');
  const [providerId, setProviderId] = useState('none');  // Changed from empty string to 'none'
  const [status, setStatus] = useState('');
  const [platform, setPlatform] = useState('none');  // Changed from empty string to 'none'

  const statusOptions = [
    { value: 'open', label: 'Open' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'resolved', label: 'Resolved' },
    { value: 'closed', label: 'Closed' }
  ];

  const platformOptions = ["Airbnb", "VRBO", "Booking.com", "Expedia", "Other"];

  useEffect(() => {
    const fetchData = async () => {
      if (!id || !userId) return;

      setIsLoading(true);

      try {
        // Fetch report, properties, and providers in parallel
        const [reportResult, propertiesResult, providersResult] = await Promise.all([
          supabase
            .from('damage_reports')
            .select(`
              *,
              properties:property_id (id, name, address, city, state),
              maintenance_providers:provider_id (id, name, email, phone, specialty)
            `)
            .eq('id', id)
            .single(),
          supabase
            .from('properties')
            .select('id, name, address, city, state')
            .eq('user_id', userId) // Assuming this filter is correct
            .order('name'),
          supabase
            .from('maintenance_providers')
            .select('id, name, email, phone, specialty')
            .eq('user_id', userId) // Assuming this filter is correct
            .order('name')
        ]);

        const { data: reportData, error: reportError } = reportResult;
        const { data: propertiesData, error: propertiesError } = propertiesResult;
        const { data: providersData, error: providersError } = providersResult;

        // Check for errors after all promises resolve
        if (reportError) throw reportError;
        if (propertiesError) throw propertiesError;
        if (providersError) throw providersError;

        // Process report data
        if (reportData) {
          const formattedReport: DamageReport = {
            id: reportData.id,
            title: reportData.title,
            description: reportData.description,
            status: reportData.status,
            property_id: reportData.property_id,
            property_name: reportData.properties?.name,
            created_at: reportData.created_at,
            updated_at: reportData.updated_at,
            provider_id: reportData.provider_id,
            provider_name: reportData.maintenance_providers?.name,
            platform: reportData.platform
          };

          setReport(formattedReport);
          setTitle(formattedReport.title);
          setDescription(formattedReport.description);
          setPropertyId(formattedReport.property_id);
          setProviderId(formattedReport.provider_id || 'none');  // Changed from empty string to 'none'
          setStatus(formattedReport.status);
          setPlatform(formattedReport.platform || 'none');
        } else {
          // Handle case where report data is null/undefined even without error
          setReport(null);
        }

        // Process properties data
        if (propertiesData) {
          setProperties(propertiesData);
        }

        // Process providers data
        if (providersData) {
          setProviders(providersData);
        }

      } catch (error: any) { // Catch specific error type
        console.error('Error fetching data:', error);
        toast.error('Failed to load damage report details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [id, userId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!id || !userId) return;

    setIsSaving(true);

    try {
      const updateData = {
        title,
        description,
        property_id: propertyId,
        provider_id: providerId === 'none' ? null : providerId,
        status,
        platform: platform === 'none' ? null : platform
      };

      const { error } = await supabase
        .from('damage_reports')
        .update(updateData)
        .eq('id', id);

      if (error) throw error;

      toast.success('Damage report updated successfully');
      navigate(`/#/damages/${id}`);

    } catch (error) {
      console.error('Error updating damage report:', error);
      toast.error('Failed to update damage report');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2"
            onClick={() => navigate(`/#/damages/${id}`)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold">Edit Damage Report</h1>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading damage report...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="space-y-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2"
            onClick={() => navigate('/#/damages')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold">Edit Damage Report</h1>
        </div>

        <Card>
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-semibold mb-2">Damage Report Not Found</h2>
            <p className="text-muted-foreground mb-4">
              The damage report you're trying to edit doesn't exist or has been removed.
            </p>
            <Button onClick={() => navigate('/#/damages')}>
              Back to Damage Reports
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Button
          variant="ghost"
          size="sm"
          className="mr-2"
          onClick={() => navigate(`/#/damages/${id}`)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <h1 className="text-3xl font-bold">Edit Damage Report</h1>
      </div>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">Damage Report Information</h2>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="title">Title <span className="text-destructive">*</span></Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="property">Property <span className="text-destructive">*</span></Label>
                <Select value={propertyId} onValueChange={setPropertyId} required>
                  <SelectTrigger>
                    <SelectValue placeholder="Select property" />
                  </SelectTrigger>
                  <SelectContent>
                    {properties.map(property => (
                      <SelectItem key={property.id} value={property.id}>
                        {property.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status <span className="text-destructive">*</span></Label>
                <Select value={status} onValueChange={setStatus} required>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="provider">Assigned Provider (Optional)</Label>
                <Select value={providerId} onValueChange={setProviderId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Assign a provider" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No Provider</SelectItem>
                    {providers.map(provider => (
                      <SelectItem key={provider.id} value={provider.id}>
                        {provider.name}{provider.specialty ? ` (${provider.specialty})` : ''}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="platform">Platform (Optional)</Label>
                <Select value={platform} onValueChange={setPlatform}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select platform" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No Platform</SelectItem>
                    {platformOptions.map(plat => (
                      <SelectItem key={plat} value={plat}>{plat}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="description">Description <span className="text-destructive">*</span></Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={6}
                  required
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate(`/#/damages/${id}`)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSaving}>
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  );
};

export default EditDamageReport;
