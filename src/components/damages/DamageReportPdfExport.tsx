import React, { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { Loader2, Download } from 'lucide-react';
import { DamageReport, DamagePhoto, DamageNote, Invoice } from '@/types/damages'; // Import types

interface DamageReportPdfExportProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  report: DamageReport | null; // Use DamageReport type
  // Consider passing fetched data as props to avoid re-fetching
  // photosData?: DamagePhoto[];
  // notesData?: DamageNote[];
  // invoicesData?: Invoice[];
}

const DamageReportPdfExport: React.FC<DamageReportPdfExportProps> = ({
  open,
  onOpenChange,
  report
  // photosData, // Example of receiving data via props
  // notesData,
  // invoicesData
}) => {
  const [loading, setLoading] = useState(false);
  const [includePhotos, setIncludePhotos] = useState(true);
  const [includeComments, setIncludeComments] = useState(true); // Keep comments for now, though data source is unclear
  const [includeNotes, setIncludeNotes] = useState(true);
  const [includeInvoices, setIncludeInvoices] = useState(true);

  // Function to get signed URL for photos
  const getSignedPhotoUrl = async (filePath: string): Promise<string | null> => {
    try {
      const { data, error } = await supabase.storage
        .from('damage-photos')
        .createSignedUrl(filePath, 3600); // Use createSignedUrl, 1 hour expiry

      if (error) {
        throw error;
      }
      return data?.signedUrl || null;
    } catch (error) {
      console.error('Error getting signed photo URL:', error);
      return null;
    }
  };


  const generatePdf = async () => {
    if (!report?.id) {
      toast.error("No report data available to generate PDF.");
      return;
    }

    setLoading(true);
    try {
      // Create a temporary div to render the report content
      const reportContainer = document.createElement('div');
      reportContainer.className = 'damage-report-pdf';
      reportContainer.style.padding = '20px';
      reportContainer.style.fontFamily = 'Arial, sans-serif';
      reportContainer.style.width = '210mm'; // A4 width
      reportContainer.style.color = '#333'; // Default text color

      // Add report header
      const header = document.createElement('div');
      header.innerHTML = `
        <h1 style="font-size: 24px; margin-bottom: 8px; color: #111;">Damage Report: ${report.title}</h1>
        <p style="margin-bottom: 5px;"><strong>Status:</strong> ${report.status}</p>
        <p style="margin-bottom: 5px;"><strong>Created:</strong> ${new Date(report.created_at).toLocaleDateString()}</p>
        <p style="margin-bottom: 20px;"><strong>Property:</strong> ${report.property_name || 'N/A'}</p>
        <hr style="margin: 20px 0; border: none; border-top: 1px solid #ddd;">
        <h2 style="font-size: 18px; margin-bottom: 10px; color: #111;">Description</h2>
        <p style="margin-bottom: 30px; white-space: pre-wrap;">${report.description}</p>
        <hr style="margin: 20px 0; border: none; border-top: 1px solid #ddd;">
      `;
      reportContainer.appendChild(header);

      // Fetch and add photos if selected
      // TODO: Ideally, pass photosData as prop instead of fetching here
      if (includePhotos) {
        const photosSection = document.createElement('div');
        photosSection.innerHTML = '<h2 style="font-size: 18px; margin: 20px 0 10px; color: #111;">Photos</h2>';

        const { data: photos, error: photosError } = await supabase
          .from('damage_photos')
          .select('*')
          .eq('report_id', report.id);

        if (photosError) {
          console.error('Error fetching photos for PDF:', photosError);
          photosSection.innerHTML += '<p style="margin-top: 10px; color: #cc0000;">Error loading photos</p>';
        } else if (photos && photos.length > 0) {
          const photosGrid = document.createElement('div');
          photosGrid.style.display = 'grid';
          photosGrid.style.gridTemplateColumns = 'repeat(2, 1fr)';
          photosGrid.style.gap = '15px';

          for (const photo of photos as DamagePhoto[]) { // Type cast
            if (photo.file_path) {
              const photoUrl = await getSignedPhotoUrl(photo.file_path); // Use signed URL
              if (photoUrl) {
                const photoItem = document.createElement('div');
                photoItem.style.marginBottom = '20px';
                photoItem.innerHTML = `
                  <img src="${photoUrl}" style="width: 100%; max-width: 90mm; height: auto; border: 1px solid #ddd; border-radius: 4px; object-fit: contain;">
                  <p style="margin-top: 5px; font-size: 14px; color: #555;">${photo.caption || ''}</p>
                `;
                photosGrid.appendChild(photoItem);
              } else {
                 const errorItem = document.createElement('p');
                 errorItem.textContent = `Could not load image: ${photo.file_name}`;
                 errorItem.style.color = '#cc0000';
                 photosGrid.appendChild(errorItem);
              }
            }
          }
          photosSection.appendChild(photosGrid);
        } else {
          photosSection.innerHTML += '<p style="margin-top: 10px; color: #666;">No photos included</p>';
        }
        reportContainer.appendChild(photosSection);
        reportContainer.appendChild(document.createElement('hr'));
      }

      // Fetch and add comments if selected
      // TODO: Refactor this - inefficient user fetching, unclear data source ('damage_report_activities')
      if (includeComments) {
        const commentsSection = document.createElement('div');
        commentsSection.innerHTML = '<h2 style="font-size: 18px; margin: 20px 0 10px; color: #111;">Comments</h2>';

        // Assuming 'damage_report_activities' table exists and has relevant columns
        const { data: activities, error: activitiesError } = await supabase
          .from('damage_report_activities') // This table might not exist or match schema
          .select('*')
          .eq('damage_report_id', report.id)
          .eq('action', 'comment') // Assuming 'action' column exists
          // .eq('include_in_pdf', true) // Assuming 'include_in_pdf' column exists
          .order('created_at', { ascending: false });

        if (activitiesError) {
          console.error('Error fetching comments for PDF:', activitiesError);
          commentsSection.innerHTML += '<p style="margin-top: 10px; color: #cc0000;">Error loading comments</p>';
        } else if (activities && activities.length > 0) {
          const commentsList = document.createElement('div');
          for (const activity of activities) {
             // Inefficient: Fetch user info per comment
            const { data: userData } = await supabase
              .from('profiles') // Assuming 'profiles' table exists
              .select('email, first_name, last_name') // Fetch name too
              .eq('id', activity.user_id) // Assuming 'user_id' column exists
              .single();

            const userName = userData ? `${userData.first_name || ''} ${userData.last_name || ''}`.trim() || userData.email : 'Unknown User';

            const commentItem = document.createElement('div');
            commentItem.style.marginBottom = '15px';
            commentItem.style.padding = '10px';
            commentItem.style.borderLeft = '3px solid #eee';
            commentItem.style.backgroundColor = '#f9f9f9';

            commentItem.innerHTML = `
              <p style="margin-bottom: 5px; font-size: 14px; color: #555;">
                <strong>${userName}</strong> - ${new Date(activity.created_at).toLocaleString()}
              </p>
              <p style="margin: 0; white-space: pre-wrap;">${activity.details || ''}</p> {/* Assuming 'details' column exists */}
            `;
            commentsList.appendChild(commentItem);
          }
          commentsSection.appendChild(commentsList);
        } else {
          commentsSection.innerHTML += '<p style="margin-top: 10px; color: #666;">No comments included</p>';
        }
        reportContainer.appendChild(commentsSection);
        reportContainer.appendChild(document.createElement('hr'));
      }

      // Fetch and add notes if selected
      // TODO: Ideally, pass notesData as prop
      if (includeNotes) {
        const notesSection = document.createElement('div');
        notesSection.innerHTML = '<h2 style="font-size: 18px; margin: 20px 0 10px; color: #111;">Notes</h2>';

        const { data: notes, error: notesError } = await supabase
          .from('damage_notes')
          .select('*')
          .eq('damage_report_id', report.id)
          .eq('private', false) // Only include non-private notes
          .order('created_at', { ascending: false });

        if (notesError) {
          console.error('Error fetching notes for PDF:', notesError);
          notesSection.innerHTML += '<p style="margin-top: 10px; color: #cc0000;">Error loading notes</p>';
        } else if (notes && notes.length > 0) {
          const notesList = document.createElement('div');
          for (const note of notes as DamageNote[]) { // Type cast
             // Inefficient: Fetch user info per note if needed, or use created_by
             // For now, using created_by if available
            const noteAuthor = note.created_by || 'System'; // Placeholder if created_by is missing

            const noteItem = document.createElement('div');
            noteItem.style.marginBottom = '15px';
            noteItem.style.padding = '10px';
            noteItem.style.borderLeft = '3px solid #eee';
            noteItem.style.backgroundColor = '#f9f9f9';

            noteItem.innerHTML = `
              <p style="margin-bottom: 5px; font-size: 14px; color: #555;">
                <strong>${noteAuthor}</strong> - ${new Date(note.created_at).toLocaleString()}
              </p>
              <p style="margin: 0; white-space: pre-wrap;">${note.content}</p>
            `;
            notesList.appendChild(noteItem);
          }
          notesSection.appendChild(notesList);
        } else {
          notesSection.innerHTML += '<p style="margin-top: 10px; color: #666;">No notes included</p>';
        }
        reportContainer.appendChild(notesSection);
        reportContainer.appendChild(document.createElement('hr'));
      }

      // Fetch and add invoices if selected
      // TODO: Ideally, pass invoicesData as prop
      if (includeInvoices) {
        const invoicesSection = document.createElement('div');
        invoicesSection.innerHTML = '<h2 style="font-size: 18px; margin: 20px 0 10px; color: #111;">Invoices</h2>';

        const { data: invoices, error: invoicesError } = await supabase
          .from('damage_invoices')
          .select(`
            *,
            maintenance_providers(name)
          `)
          .eq('damage_report_id', report.id);

        if (invoicesError) {
          console.error('Error fetching invoices for PDF:', invoicesError);
          invoicesSection.innerHTML += '<p style="margin-top: 10px; color: #cc0000;">Error loading invoices</p>';
        } else if (invoices && invoices.length > 0) {
          const invoiceTable = document.createElement('table');
          invoiceTable.style.width = '100%';
          invoiceTable.style.borderCollapse = 'collapse';
          invoiceTable.style.marginTop = '15px';
          invoiceTable.style.fontSize = '14px';

          invoiceTable.innerHTML = `
            <thead style="background-color: #f3f4f6;">
              <tr>
                <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Invoice #</th>
                <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Provider</th>
                <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;">Issue Date</th>
                <th style="text-align: right; padding: 8px; border-bottom: 1px solid #ddd;">Amount</th>
                <th style="text-align: center; padding: 8px; border-bottom: 1px solid #ddd;">Status</th>
              </tr>
            </thead>
            <tbody>
              ${(invoices as Invoice[]).map(invoice => `
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${invoice.invoice_number || 'N/A'}</td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${(invoice as any).maintenance_providers?.name || invoice.provider_name || 'N/A'}</td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${invoice.issue_date ? new Date(invoice.issue_date).toLocaleDateString() : 'N/A'}</td>
                  <td style="text-align: right; padding: 8px; border-bottom: 1px solid #eee;">$${invoice.total_amount?.toFixed(2) || '0.00'}</td>
                  <td style="text-align: center; padding: 8px; border-bottom: 1px solid #eee; text-transform: capitalize;">${invoice.status || 'N/A'}</td>
                </tr>
              `).join('')}
            </tbody>
          `;
          invoicesSection.appendChild(invoiceTable);
        } else {
          invoicesSection.innerHTML += '<p style="margin-top: 10px; color: #666;">No invoices included</p>';
        }
        reportContainer.appendChild(invoicesSection);
      }

      // Append the container to the document body temporarily for rendering
      reportContainer.style.position = 'absolute';
      reportContainer.style.left = '-9999px'; // Position off-screen
      document.body.appendChild(reportContainer);

      // Generate PDF using html2canvas and jsPDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      const canvas = await html2canvas(reportContainer, {
        scale: 2, // Increase scale for better resolution
        logging: false,
        useCORS: true // Important for external images (like signed URLs)
      });

      // Add the image to the PDF, handling pagination
      const imgData = canvas.toDataURL('image/png');
      const imgWidth = pdf.internal.pageSize.getWidth() - 20; // A4 width in mm minus margins
      const pageHeight = pdf.internal.pageSize.getHeight() - 20; // A4 height in mm minus margins
      const imgHeight = canvas.height * imgWidth / canvas.width;
      let heightLeft = imgHeight;
      let position = 10; // Top margin

      pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight); // Add margin
      heightLeft -= pageHeight;

      while (heightLeft > 0) {
        position = heightLeft - imgHeight + 10; // Adjust position for next page with margin
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight); // Add margin
        heightLeft -= pageHeight;
      }

      // Remove the temporary container
      document.body.removeChild(reportContainer);

      // Save the PDF
      pdf.save(`damage-report-${report.id}.pdf`);

      toast.success('Report exported successfully');
      onOpenChange(false); // Close dialog on success

    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Failed to generate PDF report');
    } finally {
      setLoading(false);
    }
  };


  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Export Damage Report</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-1">
            <Label className="text-sm font-medium">
              Include in the PDF:
            </Label>

            <div className="flex items-center space-x-2 mt-2">
              <Checkbox
                id="include-photos"
                checked={includePhotos}
                onCheckedChange={(checked) => setIncludePhotos(checked as boolean)}
              />
              <Label htmlFor="include-photos">Photos</Label>
            </div>

            <div className="flex items-center space-x-2 mt-2">
              <Checkbox
                id="include-comments"
                checked={includeComments}
                onCheckedChange={(checked) => setIncludeComments(checked as boolean)}
              />
              <Label htmlFor="include-comments">Comments</Label>
            </div>

            <div className="flex items-center space-x-2 mt-2">
              <Checkbox
                id="include-notes"
                checked={includeNotes}
                onCheckedChange={(checked) => setIncludeNotes(checked as boolean)}
              />
              <Label htmlFor="include-notes">Notes (Public)</Label>
            </div>

            <div className="flex items-center space-x-2 mt-2">
              <Checkbox
                id="include-invoices"
                checked={includeInvoices}
                onCheckedChange={(checked) => setIncludeInvoices(checked as boolean)}
              />
              <Label htmlFor="include-invoices">Invoices</Label>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={generatePdf} disabled={loading || !report}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Download PDF
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DamageReportPdfExport;
