
import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { Pencil, Trash2, Plus } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { DamageNote } from '@/types/damages';

interface NotesTabProps {
  reportId: string;
  initialNotes?: DamageNote[];
}

const NotesTab: React.FC<NotesTabProps> = ({ reportId, initialNotes }) => {
  const { authState } = useAuth();
  const [notes, setNotes] = useState<DamageNote[]>(initialNotes || []);
  const [loading, setLoading] = useState(!initialNotes);
  const [newNoteContent, setNewNoteContent] = useState('');
  const [isPrivate, setIsPrivate] = useState(false);
  const [editingNote, setEditingNote] = useState<DamageNote | null>(null);
  const [editContent, setEditContent] = useState('');
  const [editPrivate, setEditPrivate] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [noteToDelete, setNoteToDelete] = useState<DamageNote | null>(null);

  useEffect(() => {
    // Only fetch notes if initialNotes is not provided
    if (!initialNotes) {
      fetchNotes();
    }
  }, [reportId, initialNotes]);

  const fetchNotes = async () => {
    if (!reportId) return;

    try {
      setLoading(true);

      const { data, error } = await supabase
        .from('damage_notes')
        .select('*')
        .eq('damage_report_id', reportId)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      setNotes(data || []);
    } catch (error) {
      console.error('Error fetching notes:', error);
      toast.error('Failed to load notes');
    } finally {
      setLoading(false);
    }
  };

  const addNote = async () => {
    if (!newNoteContent.trim() || !reportId || !authState.user?.id) {
      toast.error('Please enter a note');
      return;
    }

    setIsSubmitting(true);
    try {
      const noteData = {
        damage_report_id: reportId,
        user_id: authState.user.id,
        content: newNoteContent.trim(),
        private: isPrivate,
        created_by: authState.user.email
      };

      const { data, error } = await supabase
        .from('damage_notes')
        .insert(noteData)
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Add activity record
      await supabase
        .from('damage_report_activities')
        .insert({
          damage_report_id: reportId,
          user_id: authState.user.id,
          action: 'note',
          details: `Added a note${isPrivate ? ' (private)' : ''}`
        });

      setNotes([data, ...notes]);
      setNewNoteContent('');
      setIsPrivate(false);
      toast.success('Note added successfully');
    } catch (error) {
      console.error('Error adding note:', error);
      toast.error('Failed to add note');
    } finally {
      setIsSubmitting(false);
    }
  };

  const startEditingNote = (note: DamageNote) => {
    setEditingNote(note);
    setEditContent(note.content);
    setEditPrivate(note.private);
  };

  const cancelEditingNote = () => {
    setEditingNote(null);
    setEditContent('');
    setEditPrivate(false);
  };

  const updateNote = async () => {
    if (!editingNote || !editContent.trim() || !authState.user?.id) {
      return;
    }

    setIsSubmitting(true);
    try {
      const noteData = {
        content: editContent.trim(),
        private: editPrivate
      };

      const { error } = await supabase
        .from('damage_notes')
        .update(noteData)
        .eq('id', editingNote.id)
        .eq('user_id', authState.user.id);

      if (error) {
        throw error;
      }

      // Add activity record
      await supabase
        .from('damage_report_activities')
        .insert({
          damage_report_id: reportId,
          user_id: authState.user.id,
          action: 'update',
          details: `Updated a note${editPrivate ? ' (private)' : ''}`
        });

      // Update local state
      setNotes(notes.map(note =>
        note.id === editingNote.id ? { ...note, ...noteData } : note
      ));

      cancelEditingNote();
      toast.success('Note updated successfully');
    } catch (error) {
      console.error('Error updating note:', error);
      toast.error('Failed to update note');
    } finally {
      setIsSubmitting(false);
    }
  };

  const confirmDeleteNote = (note: DamageNote) => {
    setNoteToDelete(note);
    setDeleteDialogOpen(true);
  };

  const deleteNote = async () => {
    if (!noteToDelete || !authState.user?.id) {
      return;
    }

    try {
      const { error } = await supabase
        .from('damage_notes')
        .delete()
        .eq('id', noteToDelete.id)
        .eq('user_id', authState.user.id);

      if (error) {
        throw error;
      }

      // Add activity record
      await supabase
        .from('damage_report_activities')
        .insert({
          damage_report_id: reportId,
          user_id: authState.user.id,
          action: 'delete',
          details: 'Deleted a note'
        });

      // Update local state
      setNotes(notes.filter(note => note.id !== noteToDelete.id));

      setDeleteDialogOpen(false);
      setNoteToDelete(null);
      toast.success('Note deleted successfully');
    } catch (error) {
      console.error('Error deleting note:', error);
      toast.error('Failed to delete note');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4">
        <h3 className="text-lg font-medium">Add a Note</h3>
        <Textarea
          placeholder="Enter your note here..."
          value={newNoteContent}
          onChange={(e) => setNewNoteContent(e.target.value)}
          className="h-24"
        />
        <div className="flex items-center space-x-2 mb-2">
          <Checkbox
            id="is-private"
            checked={isPrivate}
            onCheckedChange={(checked) => setIsPrivate(checked as boolean)}
          />
          <Label htmlFor="is-private">Mark as private (only visible to you)</Label>
        </div>
        <div className="flex justify-end">
          <Button
            onClick={addNote}
            disabled={!newNoteContent.trim() || isSubmitting}
            className="flex items-center"
          >
            <Plus className="mr-2 h-4 w-4" />
            {isSubmitting ? 'Adding...' : 'Add Note'}
          </Button>
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Notes</h3>

        {loading ? (
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-1/4" />
                <Skeleton className="h-24 w-full" />
              </div>
            ))}
          </div>
        ) : notes.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No notes have been added yet.
          </div>
        ) : (
          <div className="space-y-6">
            {notes.map(note => (
              <div key={note.id} className={`border rounded-md p-4 ${note.private ? 'bg-purple-50 border-purple-200' : 'bg-white'}`}>
                {editingNote?.id === note.id ? (
                  <div className="space-y-4">
                    <Textarea
                      value={editContent}
                      onChange={(e) => setEditContent(e.target.value)}
                      className="w-full"
                    />
                    <div className="flex items-center space-x-2 mb-2">
                      <Checkbox
                        id={`edit-private-${note.id}`}
                        checked={editPrivate}
                        onCheckedChange={(checked) => setEditPrivate(checked as boolean)}
                      />
                      <Label htmlFor={`edit-private-${note.id}`}>Mark as private</Label>
                    </div>
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        onClick={cancelEditingNote}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={updateNote}
                        disabled={!editContent.trim() || isSubmitting}
                      >
                        {isSubmitting ? 'Saving...' : 'Save Changes'}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm text-muted-foreground">
                          {note.created_by || 'Unknown'} - {formatDate(note.created_at)}
                        </p>
                        {note.private && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 mt-1">
                            Private
                          </span>
                        )}
                      </div>
                      <div className="flex space-x-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => startEditingNote(note)}
                          className="h-8 w-8"
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => confirmDeleteNote(note)}
                          className="h-8 w-8 text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <p className="mt-2 whitespace-pre-wrap">{note.content}</p>
                  </>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this note. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={deleteNote}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default NotesTab;
