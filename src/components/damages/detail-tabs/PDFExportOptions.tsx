
import React, { useState } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

export interface PDFExportOptions {
  includePhotos: boolean;
  includeNotes: boolean;
  includeInvoices: boolean;
}

interface PDFExportOptionsProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (options: PDFExportOptions) => void;
  isExporting: boolean;
}

export function PDFExportOptions({ isOpen, onClose, onExport, isExporting }: PDFExportOptionsProps) {
  const [options, setOptions] = useState<PDFExportOptions>({
    includePhotos: true,
    includeNotes: true,
    includeInvoices: true,
  });

  const handleOptionChange = (option: keyof PDFExportOptions) => {
    setOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };

  const handleExport = () => {
    console.log("Export button clicked with options:", options);
    onExport(options);
  };

  console.log("PDFExportOptions render - isOpen:", isOpen, "options:", options);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Export Damage Report</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="include-photos" 
              checked={options.includePhotos}
              onCheckedChange={() => handleOptionChange('includePhotos')}
            />
            <label 
              htmlFor="include-photos" 
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Include Photos
            </label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="include-notes" 
              checked={options.includeNotes}
              onCheckedChange={() => handleOptionChange('includeNotes')}
            />
            <label 
              htmlFor="include-notes" 
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Include Notes
            </label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="include-invoices" 
              checked={options.includeInvoices}
              onCheckedChange={() => handleOptionChange('includeInvoices')}
            />
            <label 
              htmlFor="include-invoices" 
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Include Invoices
            </label>
          </div>
        </div>
        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={onClose}
            disabled={isExporting}
            type="button"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleExport}
            disabled={isExporting}
            type="button"
          >
            {isExporting ? 'Generating...' : 'Download Report'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
