
import React from 'react';
import { Button } from '@/components/ui/button';
import { FilePlus, Upload } from 'lucide-react';

interface InvoiceHeaderProps {
  onCreateInvoice: () => void;
  onUploadInvoice: () => void;
}

const InvoiceHeader: React.FC<InvoiceHeaderProps> = ({
  onCreateInvoice,
  onUploadInvoice
}) => {
  return (
    <div className="flex justify-between items-center">
      <h3 className="text-lg font-medium">Invoices</h3>
      <div className="flex space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onUploadInvoice}
          className="flex items-center gap-1"
        >
          <Upload className="h-4 w-4" />
          Upload PDF
        </Button>
        <Button
          size="sm"
          onClick={onCreateInvoice}
          className="flex items-center gap-1"
        >
          <FilePlus className="h-4 w-4" />
          Create Invoice
        </Button>
      </div>
    </div>
  );
};

export default InvoiceHeader;
