
import { supabase } from '@/integrations/supabase/client';
import { DamageReport, Invoice } from '@/types/damages';

/**
 * Fetches invoices for a specific damage report
 */
export const fetchInvoicesForDamageReport = async (damageReportId: string): Promise<Invoice[]> => {
  const { data, error } = await supabase
    .from('damage_invoices')
    .select(`
      *,
      maintenance_providers(name, email, phone, specialty, notes)
    `)
    .eq('damage_report_id', damageReportId)
    .order('created_at', { ascending: false });

  if (error) {
    throw error;
  }

  // Map the database response to our Invoice type
  return (data || []).map((invoice: any): Invoice => ({
    id: invoice.id,
    invoice_number: invoice.invoice_number || '',
    damage_report_id: invoice.damage_report_id,
    provider_id: invoice.provider_id || '',
    total_amount: invoice.total_amount || 0,
    issue_date: invoice.issue_date || '',
    due_date: invoice.due_date || '',
    status: invoice.status || 'draft',
    notes: invoice.notes || '',
    created_at: invoice.created_at,
    updated_at: invoice.updated_at,
    user_id: invoice.user_id,
    maintenance_providers: invoice.maintenance_providers,
    file_path: invoice.file_path || '',
    file_name: invoice.file_name || '',
    file_url: invoice.file_url || '',
    provider_name: invoice.maintenance_providers?.name || ''
  }));
};

/**
 * Fetches a single invoice by ID
 */
export const fetchInvoiceById = async (invoiceId: string): Promise<Invoice | null> => {
  const { data, error } = await supabase
    .from('damage_invoices')
    .select(`
      *,
      maintenance_providers(name, email, phone, specialty, notes)
    `)
    .eq('id', invoiceId)
    .single();
  
  if (error) {
    console.error('Error fetching invoice:', error);
    return null;
  }
  
  if (!data) {
    return null;
  }
  
  // Create a type-safe invoice object 
  const invoice: Invoice = {
    id: data.id,
    invoice_number: data.invoice_number || '',
    damage_report_id: data.damage_report_id,
    provider_id: data.provider_id || '',
    total_amount: data.total_amount || 0,
    issue_date: data.issue_date || '',
    due_date: data.due_date || '',
    status: data.status || 'draft',
    notes: data.notes || '',
    created_at: data.created_at,
    updated_at: data.updated_at,
    user_id: data.user_id,
    maintenance_providers: data.maintenance_providers,
    file_path: data.file_path || '',
    file_name: data.file_name || '',
    file_url: data.file_url || '',
    provider_name: data.maintenance_providers?.name || ''
  };
  
  return invoice;
};

/**
 * Downloads storage file for an invoice
 */
export const downloadInvoiceFile = async (filePath: string): Promise<Blob | null> => {
  try {
    // Check if bucket exists before attempting download
    const bucketExists = await ensureInvoiceBucketExists();
    if (!bucketExists) {
      console.log('Invoice bucket does not exist or is not accessible, attempting to download anyway');
    }
    
    const { data, error } = await supabase.storage
      .from('invoice-files')
      .download(filePath);
    
    if (error) {
      console.error('Storage download error:', error);
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Error downloading file:', error);
    return null;
  }
};

/**
 * Creates or ensures that the invoice-files bucket exists and is accessible
 */
export const ensureInvoiceBucketExists = async (): Promise<boolean> => {
  try {
    // First, check if we can list files in the bucket (this checks permissions)
    const { data: fileList, error: listError } = await supabase.storage
      .from('invoice-files')
      .list('', { limit: 1 }); // Try to list just one file
    
    if (listError) {
      // Check if the error is just that there are no files (which is fine)
      if (listError.message && (
          listError.message.includes('The resource was not found') || 
          listError.message.includes('bucket is empty')
      )) {
        // This means the bucket exists but is empty, which is fine
        console.log('Invoice-files bucket exists but is empty');
        return true;
      }
      
      // Other errors mean we don't have access
      console.error('Error accessing invoice-files bucket:', listError);
      return false;
    }
    
    // If we get here, we successfully listed files, so the bucket exists and we have access
    console.log('Successfully verified invoice-files bucket access');
    return true;
  } catch (error) {
    console.error('Error checking bucket existence:', error);
    return false;
  }
};

// Initialize storage when app starts
export const initializeInvoiceStorage = async (): Promise<void> => {
  try {
    const success = await ensureInvoiceBucketExists();
    if (success) {
      console.log('Invoice storage initialized successfully');
    } else {
      console.log('Invoice storage not initialized, will attempt to create when needed');
    }
  } catch (error) {
    console.error('Error initializing invoice storage:', error);
  }
};
