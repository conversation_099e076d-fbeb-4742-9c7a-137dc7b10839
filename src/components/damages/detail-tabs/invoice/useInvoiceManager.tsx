
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { Invoice } from '@/types/damages';
import { fetchInvoicesForDamageReport, fetchInvoiceById, downloadInvoiceFile } from './api/invoiceApi';
import { generateInvoicePdf } from './utils/pdfGenerator';

export const useInvoiceManager = (damageReportId: string) => {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);
  const navigate = useNavigate();

  /**
   * Fetch all invoices for the current damage report
   */
  const fetchInvoices = async () => {
    setIsLoading(true);
    try {
      const invoiceData = await fetchInvoicesForDamageReport(damageReportId);
      setInvoices(invoiceData);
    } catch (error: any) {
      toast.error(`Failed to fetch invoices: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle creating a new invoice
   */
  const handleCreateInvoice = () => {
    setIsCreateDialogOpen(true);
  };

  /**
   * Handle uploading an invoice
   */
  const handleUploadInvoice = () => {
    setIsUploadDialogOpen(true);
  };

  /**
   * Handle editing an invoice
   */
  const handleEditInvoice = (invoiceId: string) => {
    setSelectedInvoiceId(invoiceId);
    setIsEditDialogOpen(true);
  };

  /**
   * Handle viewing an invoice
   */
  const handleViewInvoice = (invoiceId: string) => {
    setSelectedInvoiceId(invoiceId);
  };

  /**
   * Handle viewing a full invoice
   */
  const handleViewFullInvoice = (invoiceId: string) => {
    navigate(`/damages/invoice/${invoiceId}`);
  };

  /**
   * Handle downloading an invoice
   */
  const handleDownloadInvoice = async (invoiceId: string) => {
    try {
      const invoice = await fetchInvoiceById(invoiceId);

      if (!invoice) {
        toast.error('Invoice not found');
        return;
      }

      // If the invoice has a file path, try to download it from storage
      if (invoice.file_path) {
        const fileData = await downloadInvoiceFile(invoice.file_path);
        
        if (fileData) {
          const url = URL.createObjectURL(fileData);
          const a = document.createElement('a');
          a.href = url;
          a.download = invoice.file_name || `invoice-${invoice.invoice_number}.pdf`;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
          
          toast.success('Invoice downloaded successfully');
          return;
        } else {
          toast.error('Error downloading file. Storage bucket may not exist.');
          return;
        }
      }

      // If no file path or download failed, generate a PDF
      const mockProperty = { 
        id: damageReportId || '', 
        name: 'Property Name' 
      };
      
      const provider = invoice.provider_id ? {
        id: invoice.provider_id,
        name: invoice.maintenance_providers?.name || 'Unknown Provider',
        email: invoice.maintenance_providers?.email,
        phone: invoice.maintenance_providers?.phone,
        specialty: invoice.maintenance_providers?.specialty
      } : {
        id: 'no-provider',
        name: 'No Provider Assigned'
      };

      await generateInvoicePdf(invoice, damageReportId, mockProperty, provider);
      toast.success('Invoice PDF downloaded successfully');
    } catch (error: any) {
      console.error('Error downloading invoice PDF:', error);
      toast.error(`Failed to download invoice PDF: ${error.message}`);
    }
  };

  // Initial fetch of invoices
  useEffect(() => {
    fetchInvoices();
  }, [damageReportId]);

  return {
    invoices,
    isLoading,
    isCreateDialogOpen,
    setIsCreateDialogOpen,
    isUploadDialogOpen,
    setIsUploadDialogOpen,
    isEditDialogOpen,
    setIsEditDialogOpen,
    selectedInvoiceId,
    setSelectedInvoiceId,
    fetchInvoices,
    handleCreateInvoice,
    handleUploadInvoice,
    handleEditInvoice,
    handleViewInvoice,
    handleViewFullInvoice,
    handleDownloadInvoice
  };
};
