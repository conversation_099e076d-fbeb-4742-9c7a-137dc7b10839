
import React from 'react';
import { format } from 'date-fns';
import { Eye, Download, MoreHorizontal, Upload, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Invoice } from '@/types/damages';

interface InvoiceListProps {
  invoices: Invoice[];
  isLoading: boolean;
  onViewInvoice: (invoiceId: string) => void;
  onViewFullInvoice: (invoiceId: string) => void;
  onDownloadInvoice: (invoiceId: string) => void;
  onCreateInvoice: () => void;
  onUploadInvoice: () => void;
}

const InvoiceList: React.FC<InvoiceListProps> = ({
  invoices,
  isLoading,
  onViewInvoice,
  onViewFullInvoice,
  onDownloadInvoice,
  onCreateInvoice,
  onUploadInvoice,
}) => {
  if (isLoading) {
    return (
      <div className="py-8 flex justify-center">
        <p className="text-muted-foreground">Loading invoices...</p>
      </div>
    );
  }

  if (invoices.length === 0) {
    return (
      <div className="py-10 flex flex-col items-center justify-center bg-muted/30 rounded-lg border border-dashed">
        <p className="text-muted-foreground mb-4">No invoices found for this damage report.</p>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={onUploadInvoice}>
            <Upload className="h-4 w-4 mr-2" />
            Upload PDF
          </Button>
          <Button size="sm" onClick={onCreateInvoice}>
            <Plus className="h-4 w-4 mr-2" />
            Create New
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="grid gap-4">
      {invoices.map((invoice) => (
        <div
          key={invoice.id}
          className="bg-white rounded-md border p-4 shadow-sm hover:shadow-md transition-shadow"
        >
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-medium">
                Invoice #{invoice.invoice_number || 'N/A'}
              </h3>
              <p className="text-sm text-gray-500">
                Created on {format(new Date(invoice.created_at), 'PPP')}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onViewInvoice(invoice.id)}
              >
                <Eye className="h-4 w-4 mr-1" />
                Details
              </Button>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onViewFullInvoice(invoice.id)}>
                    <Eye className="h-4 w-4 mr-2" />
                    View Full Invoice
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onDownloadInvoice(invoice.id)}>
                    <Download className="h-4 w-4 mr-2" />
                    Download PDF
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          
          <div className="mt-2">
            <p className="text-sm">
              Amount: ${invoice.total_amount?.toFixed(2) || '0.00'}
            </p>
            <p className="text-sm">
              Status: <span className={`font-medium ${invoice.status === 'paid' ? 'text-green-600' : invoice.status === 'pending' ? 'text-amber-600' : 'text-gray-600'}`}>{invoice.status}</span>
            </p>
            {invoice.file_path && (
              <p className="text-sm text-blue-600 mt-1">
                <Download className="h-3 w-3 inline mr-1" />
                Uploaded PDF available
              </p>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default InvoiceList;
