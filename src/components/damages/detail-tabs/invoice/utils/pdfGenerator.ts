
import { jsPDF } from 'jspdf';
import { Invoice } from '@/types/damages';

interface Property {
  id: string;
  name: string;
}

interface Provider {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  specialty?: string;
}

export const generateInvoicePdf = async (
  invoice: Invoice,
  damageReportId: string,
  property: Property,
  provider: Provider
): Promise<void> => {
  const doc = new jsPDF();
  
  // Set document properties
  doc.setProperties({
    title: `Invoice ${invoice.invoice_number}`,
    subject: 'Damage Report Invoice',
    author: 'Property Manager',
    creator: 'Property Management System'
  });
  
  // Set fonts
  doc.setFont('helvetica', 'bold');
  
  // Add header
  doc.setFontSize(20);
  doc.text('INVOICE', 105, 20, { align: 'center' });
  
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(10);
  doc.text(`Invoice #: ${invoice.invoice_number || 'N/A'}`, 20, 30);
  doc.text(`Date: ${formatDate(invoice.issue_date || new Date().toISOString())}`, 20, 35);
  doc.text(`Due Date: ${formatDate(invoice.due_date || new Date().toISOString())}`, 20, 40);
  doc.text(`Status: ${invoice.status?.toUpperCase() || 'PENDING'}`, 20, 45);
  
  // Property information
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(12);
  doc.text('Property:', 20, 60);
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(10);
  doc.text(property.name, 20, 65);
  
  // Provider information
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(12);
  doc.text('Service Provider:', 120, 60);
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(10);
  doc.text(provider.name, 120, 65);
  if (provider.email) {
    doc.text(`Email: ${provider.email}`, 120, 70);
  }
  if (provider.phone) {
    doc.text(`Phone: ${provider.phone}`, 120, 75);
  }
  if (provider.specialty) {
    doc.text(`Specialty: ${provider.specialty}`, 120, 80);
  }
  
  // Invoice total
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(12);
  doc.text('Total Amount:', 150, 100);
  doc.setFontSize(14);
  doc.text(`$${invoice.total_amount?.toFixed(2) || '0.00'}`, 150, 105);
  
  // Notes
  if (invoice.notes) {
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(12);
    doc.text('Notes:', 20, 120);
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(10);
    
    const lines = doc.splitTextToSize(invoice.notes, 170);
    doc.text(lines, 20, 125);
  }
  
  // Footer
  doc.setFont('helvetica', 'italic');
  doc.setFontSize(8);
  const today = new Date().toLocaleDateString();
  doc.text(`This invoice was generated on ${today} for the damage report.`, 105, 280, { align: 'center' });
  
  // Save the PDF
  doc.save(`invoice-${invoice.invoice_number || damageReportId}.pdf`);
};

const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString();
};
