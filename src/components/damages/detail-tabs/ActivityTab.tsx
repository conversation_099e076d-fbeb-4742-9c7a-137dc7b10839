
import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { FileDown } from 'lucide-react';
import DamageReportPdfExport from '@/components/damages/DamageReportPdfExport';

interface ActivityLog {
  id: string;
  created_at: string;
  user_id: string;
  damage_report_id: string;
  action: string;
  details: string;
  include_in_pdf?: boolean;
  user_email?: string;
}

interface ActivityTabProps {
  damageReport: any;
}

const ActivityTab: React.FC<ActivityTabProps> = ({ damageReport }) => {
  const { authState } = useAuth();
  const [activities, setActivities] = useState<ActivityLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [comment, setComment] = useState('');
  const [includeInPdf, setIncludeInPdf] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [isPdfDialogOpen, setIsPdfDialogOpen] = useState(false);

  useEffect(() => {
    if (!damageReport?.id) return;
    
    const fetchActivities = async () => {
      setLoading(true);
      try {
        // Fetch activity logs for this damage report
        const { data, error } = await supabase
          .from('damage_report_activities')
          .select('*')
          .eq('damage_report_id', damageReport.id)
          .order('created_at', { ascending: false });
        
        if (error) {
          console.error('Error fetching activities:', error);
          return;
        }
        
        // Get user email information for each activity
        const enhancedActivities = await Promise.all(
          (data || []).map(async (activity) => {
            try {
              const { data: userData, error: userError } = await supabase
                .from('profiles')
                .select('email')
                .eq('id', activity.user_id)
                .single();
              
              if (userError) throw userError;
              
              return {
                ...activity,
                user_email: userData?.email || 'Unknown user'
              };
            } catch (err) {
              console.error('Error fetching user data:', err);
              return {
                ...activity,
                user_email: 'Unknown user'
              };
            }
          })
        );
        
        setActivities(enhancedActivities);
      } catch (error) {
        console.error('Error fetching activities:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchActivities();
  }, [damageReport?.id]);

  const addComment = async () => {
    if (!comment.trim() || !damageReport?.id || !authState.user?.id) return;
    
    setSubmitting(true);
    try {
      // Add a new comment activity
      const { error } = await supabase
        .from('damage_report_activities')
        .insert({
          damage_report_id: damageReport.id,
          user_id: authState.user.id,
          action: 'comment',
          details: comment,
          include_in_pdf: includeInPdf
        });
      
      if (error) {
        console.error('Error adding comment:', error);
        return;
      }
      
      // Fetch the updated activities list
      const { data: updatedData, error: fetchError } = await supabase
        .from('damage_report_activities')
        .select('*')
        .eq('damage_report_id', damageReport.id)
        .order('created_at', { ascending: false });
      
      if (fetchError) {
        console.error('Error fetching updated activities:', fetchError);
        return;
      }
      
      // Get user email information for each activity
      const enhancedActivities = await Promise.all(
        (updatedData || []).map(async (activity) => {
          try {
            const { data: userData, error: userError } = await supabase
              .from('profiles')
              .select('email')
              .eq('id', activity.user_id)
              .single();
            
            if (userError) throw userError;
            
            return {
              ...activity,
              user_email: userData?.email || 'Unknown user'
            };
          } catch (err) {
            console.error('Error fetching user data:', err);
            return {
              ...activity,
              user_email: 'Unknown user'
            };
          }
        })
      );
      
      setActivities(enhancedActivities);
      setComment(''); // Clear comment input
    } catch (error) {
      console.error('Error adding comment:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const getInitials = (email: string) => {
    return email.substring(0, 2).toUpperCase();
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Activity History</h3>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setIsPdfDialogOpen(true)}
            className="flex items-center gap-1"
          >
            <FileDown className="h-4 w-4" />
            Download Report
          </Button>
        </div>
        
        {/* Activity list */}
        {loading ? (
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="flex items-start space-x-3">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-1/4" />
                  <Skeleton className="h-12 w-full" />
                </div>
              </div>
            ))}
          </div>
        ) : activities.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No activity recorded yet.
          </div>
        ) : (
          <div className="space-y-4">
            {activities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <Avatar>
                  <AvatarFallback>{getInitials(activity.user_email || '')}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex justify-between items-center">
                    <p className="font-medium">{activity.user_email}</p>
                    <p className="text-sm text-muted-foreground">{formatDate(activity.created_at)}</p>
                  </div>
                  <div className="mt-1 p-3 bg-muted rounded-md">
                    {activity.action === 'comment' ? (
                      <p>{activity.details}</p>
                    ) : (
                      <p>
                        <span className="font-medium capitalize">{activity.action}</span>
                        {activity.details ? `: ${activity.details}` : ''}
                      </p>
                    )}
                  </div>
                  {activity.action === 'comment' && (
                    <div className="mt-1 text-xs text-muted-foreground">
                      {activity.include_in_pdf ? 'Included in PDF reports' : 'Excluded from PDF reports'}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
        
        <Separator className="my-4" />
        
        {/* Add comment form */}
        <div className="space-y-3">
          <h4 className="font-medium">Add a Comment</h4>
          <Textarea
            placeholder="Add your comment here..."
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            className="h-24"
          />
          <div className="flex items-center space-x-2 mb-2">
            <Checkbox 
              id="include-in-pdf" 
              checked={includeInPdf}
              onCheckedChange={(checked) => setIncludeInPdf(checked as boolean)}
            />
            <Label htmlFor="include-in-pdf">Include in PDF reports</Label>
          </div>
          <div className="flex justify-end">
            <Button 
              onClick={addComment} 
              disabled={!comment.trim() || submitting}
            >
              {submitting ? 'Adding...' : 'Add Comment'}
            </Button>
          </div>
        </div>
      </div>

      {/* PDF Export Dialog */}
      <DamageReportPdfExport
        open={isPdfDialogOpen}
        onOpenChange={setIsPdfDialogOpen}
        report={damageReport}
      />
    </div>
  );
};

export default ActivityTab;
