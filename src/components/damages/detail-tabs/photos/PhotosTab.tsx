import React, { useState, useEffect, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/components/ui/alert-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { UploadCloud, Image, MoreHorizontal, Trash2, Eye, PencilLine } from 'lucide-react';
import { DamagePhoto } from '@/types/damages';
import PhotosHeader from './PhotosHeader';
import PhotoUploadDialog from './PhotoUploadDialog';

interface PhotosTabProps {
  reportId: string;
}

const PhotosTab: React.FC<PhotosTabProps> = ({ reportId }) => {
  const { authState } = useAuth();
  const [photos, setPhotos] = useState<DamagePhoto[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [selectedPhoto, setSelectedPhoto] = useState<DamagePhoto | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [editCaption, setEditCaption] = useState('');

  // Track if component is mounted to avoid state updates after unmount
  const isMounted = useRef(true);

  useEffect(() => {
    // Set mounted flag
    isMounted.current = true;

    // Only fetch if we have a reportId
    if (reportId) {
      fetchPhotos();
    }

    // Cleanup function to set mounted flag to false when component unmounts
    return () => {
      isMounted.current = false;
    };
  }, [reportId]);

  const fetchPhotos = async () => {
    if (!reportId) return;

    try {
      // Only set loading if component is still mounted
      if (isMounted.current) {
        setLoading(true);
      } else {
        return; // Exit if component unmounted
      }

      console.log(`Fetching photos for damage report: ${reportId}`);

      // Try our RPC function first
      try {
        const { data: rpcData, error: rpcError } = await supabase.rpc(
          'get_damage_photos',
          { p_damage_report_id: reportId }
        );

        // Check if component is still mounted before continuing
        if (!isMounted.current) return;

        if (rpcError) {
          console.error('Error fetching photos with RPC:', rpcError);
          // Fall back to direct query
        } else if (rpcData && rpcData.length > 0) {
          console.log(`Found ${rpcData.length} photos with RPC`);

          // Format the photos with URLs
          const photosWithUrls = rpcData.map((photo: any) => ({
            ...photo,
            url: photo.public_url
          }));

          // Check if component is still mounted before updating state
          if (!isMounted.current) return;

          console.log(`Processed ${photosWithUrls.length} photos with URLs from RPC`);
          setPhotos(photosWithUrls);
          setLoading(false);
          return;
        }
      } catch (rpcError) {
        console.error('Exception fetching photos with RPC:', rpcError);
        // Continue to fallback if component is still mounted
        if (!isMounted.current) return;
      }

      // Fallback to direct query
      const { data, error } = await supabase
        .from('damage_photos')
        .select('*')
        .eq('damage_report_id', reportId)
        .order('created_at', { ascending: false });

      // Check if component is still mounted before continuing
      if (!isMounted.current) return;

      if (error) {
        throw error;
      }

      // Create public URLs for all photos
      const photosWithUrls = data.map((photo) => {
        if (!photo.file_path) {
          console.error(`Photo ${photo.id} has no file_path`);
          return {
            ...photo,
            url: null
          };
        }

        return {
          ...photo,
          url: `https://pwaeknalhosfwuxkpaet.supabase.co/storage/v1/object/public/damage-photos/${photo.file_path}`
        };
      });

      // Check if component is still mounted before updating state
      if (!isMounted.current) return;

      console.log(`Found ${photosWithUrls.length} photos for damage report: ${reportId}`);
      setPhotos(photosWithUrls);
    } catch (error) {
      console.error('Error fetching photos:', error);
      // Only show toast and update state if component is still mounted
      if (isMounted.current) {
        toast.error('Failed to load photos');
      }
    } finally {
      // Only update loading state if component is still mounted
      if (isMounted.current) {
        setLoading(false);
      }
    }
  };

  const handleView = (photo: DamagePhoto) => {
    setSelectedPhoto(photo);
    setViewDialogOpen(true);
  };

  const handleEdit = (photo: DamagePhoto) => {
    setSelectedPhoto(photo);
    setEditCaption(photo.caption || '');
    setEditDialogOpen(true);
  };

  const handleDelete = (photo: DamagePhoto) => {
    setSelectedPhoto(photo);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!selectedPhoto || !authState.user?.id) return;

    try {
      // Delete from database
      const { error: dbError } = await supabase
        .from('damage_photos')
        .delete()
        .eq('id', selectedPhoto.id)
        .eq('user_id', authState.user.id);

      if (dbError) throw dbError;

      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from('damage-photos')
        .remove([selectedPhoto.file_path]);

      if (storageError) {
        console.error('Error removing file from storage:', storageError);
      }

      // Update local state
      setPhotos(photos.filter(p => p.id !== selectedPhoto.id));

      // Add activity record
      await supabase
        .from('damage_report_activities')
        .insert({
          damage_report_id: reportId,
          user_id: authState.user.id,
          action: 'delete',
          details: `Deleted photo: ${selectedPhoto.file_name}`
        });

      toast.success('Photo deleted successfully');
      setDeleteDialogOpen(false);
    } catch (error) {
      console.error('Error deleting photo:', error);
      toast.error('Failed to delete photo');
    }
  };

  const updateCaption = async () => {
    if (!selectedPhoto || !authState.user?.id) return;

    try {
      const { error } = await supabase
        .from('damage_photos')
        .update({ caption: editCaption })
        .eq('id', selectedPhoto.id)
        .eq('user_id', authState.user.id);

      if (error) throw error;

      // Update local state
      setPhotos(photos.map(p =>
        p.id === selectedPhoto.id ? { ...p, caption: editCaption } : p
      ));

      // Add activity record
      await supabase
        .from('damage_report_activities')
        .insert({
          damage_report_id: reportId,
          user_id: authState.user.id,
          action: 'update',
          details: `Updated photo caption for: ${selectedPhoto.file_name}`
        });

      toast.success('Caption updated successfully');
      setEditDialogOpen(false);
    } catch (error) {
      console.error('Error updating caption:', error);
      toast.error('Failed to update caption');
    }
  };

  return (
    <div className="space-y-4">
      <PhotosHeader onUploadPhotos={() => setUploadDialogOpen(true)} />

      {loading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {[1, 2, 3, 4].map(i => (
            <Skeleton key={i} className="h-[200px] rounded-md" />
          ))}
        </div>
      ) : photos.length === 0 ? (
        <div className="bg-muted py-10 rounded-md flex flex-col items-center justify-center space-y-4">
          <Image className="h-16 w-16 text-muted-foreground" />
          <div className="text-center">
            <h3 className="text-lg font-medium">No Photos Yet</h3>
            <p className="text-muted-foreground mt-1">
              Upload photos to document the damage
            </p>
          </div>
          <Button
            variant="outline"
            onClick={() => setUploadDialogOpen(true)}
            className="mt-2"
          >
            <UploadCloud className="h-4 w-4 mr-2" />
            Upload First Photo
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {photos.map(photo => (
            <Card key={photo.id} className="overflow-hidden">
              <div className="relative pb-[60%] bg-muted">
                {photo.url ? (
                  <img
                    src={photo.url}
                    alt={photo.caption || 'Damage photo'}
                    className="absolute inset-0 w-full h-full object-cover cursor-pointer"
                    onClick={() => handleView(photo)}
                    onError={(e) => {
                      console.error('Card image failed to load:', photo.url);
                      // Replace with a placeholder
                      e.currentTarget.src = 'https://placehold.co/600x400?text=Image+Not+Available';
                    }}
                  />
                ) : (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center p-4">
                      <Image className="h-8 w-8 mx-auto text-muted-foreground" />
                      <p className="text-xs text-muted-foreground mt-2">Image not available</p>
                    </div>
                  </div>
                )}
              </div>
              <CardContent className="p-3">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <p className="text-sm truncate">
                      {photo.caption || photo.file_name || 'Untitled'}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(photo.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleView(photo)}>
                        <Eye className="mr-2 h-4 w-4" />
                        View
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEdit(photo)}>
                        <PencilLine className="mr-2 h-4 w-4" />
                        Edit Caption
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDelete(photo)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* View Photo Dialog */}
      <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
        <DialogContent className="sm:max-w-[90vw] max-w-[800px]">
          <DialogHeader>
            <DialogTitle>{selectedPhoto?.caption || selectedPhoto?.file_name || 'Photo Details'}</DialogTitle>
          </DialogHeader>

          <div className="overflow-hidden rounded-md max-h-[70vh]">
            {selectedPhoto?.url ? (
              <img
                src={selectedPhoto.url}
                alt={selectedPhoto.caption || 'Damage photo'}
                className="w-full h-auto object-contain"
                onError={(e) => {
                  console.error('Image failed to load:', selectedPhoto.url);
                  e.currentTarget.src = 'https://placehold.co/600x400?text=Image+Not+Available';
                }}
              />
            ) : (
              <div className="flex items-center justify-center h-[400px] bg-muted">
                <p className="text-muted-foreground">Image not available</p>
              </div>
            )}
          </div>

          <div className="space-y-2">
            {selectedPhoto?.caption && (
              <p className="text-sm">{selectedPhoto.caption}</p>
            )}
            <p className="text-xs text-muted-foreground">
              Uploaded on {selectedPhoto?.created_at && new Date(selectedPhoto.created_at).toLocaleString()}
            </p>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setViewDialogOpen(false)}>
              Close
            </Button>
            {selectedPhoto && (
              <>
                <Button
                  variant="outline"
                  onClick={() => {
                    setViewDialogOpen(false);
                    handleEdit(selectedPhoto);
                  }}
                >
                  <PencilLine className="mr-2 h-4 w-4" />
                  Edit Caption
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => {
                    setViewDialogOpen(false);
                    handleDelete(selectedPhoto);
                  }}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </Button>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Caption Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Caption</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-caption">Caption</Label>
              <Textarea
                id="edit-caption"
                placeholder="Add a description of the photo..."
                value={editCaption}
                onChange={(e) => setEditCaption(e.target.value)}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={updateCaption}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this photo. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Upload Photos Dialog */}
      <PhotoUploadDialog
        open={uploadDialogOpen}
        onOpenChange={setUploadDialogOpen}
        damageReportId={reportId}
        onPhotoUploaded={fetchPhotos}
      />
    </div>
  );
};

export default PhotosTab;
