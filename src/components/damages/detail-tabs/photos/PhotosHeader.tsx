
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ImagePlus, Upload } from 'lucide-react';

interface PhotosHeaderProps {
  onUploadPhotos: () => void;
}

const PhotosHeader: React.FC<PhotosHeaderProps> = ({
  onUploadPhotos
}) => {
  return (
    <div className="flex justify-between items-center">
      <h3 className="text-lg font-medium">Damage Photos</h3>
      <div className="flex space-x-2">
        <Button
          onClick={onUploadPhotos}
          className="flex items-center gap-1"
        >
          <Upload className="h-4 w-4" />
          Upload Photos
        </Button>
      </div>
    </div>
  );
};

export default PhotosHeader;
