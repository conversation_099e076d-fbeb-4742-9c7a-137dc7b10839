import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { CircleProgress } from "@/components/ui/circle-progress";
import { ImagePlus, Upload } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

interface PhotoUploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  damageReportId: string; // Changed from reportId to damageReportId to match PhotosTab
  onPhotoUploaded: () => void; // Changed from onSuccess to onPhotoUploaded to match PhotosTab
}

const PhotoUploadDialog: React.FC<PhotoUploadD<PERSON>ogProps> = ({
  open,
  onOpenChange,
  damageReportId,
  onPhotoUploaded
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [caption, setCaption] = useState('');
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];

      // Check file type
      if (!selectedFile.type.startsWith('image/')) {
        toast.error('Please select an image file');
        return;
      }

      // Check file size (limit to 5MB)
      if (selectedFile.size > 5 * 1024 * 1024) {
        toast.error('File size exceeds 5MB limit');
        return;
      }

      setFile(selectedFile);
    }
  };

  const handleUpload = async () => {
    if (!file) {
      toast.error('Please select a file to upload');
      return;
    }

    try {
      setUploading(true);
      setProgress(10);

      // Create FormData for Edge Function
      const formData = new FormData();
      formData.append('file', file);
      formData.append('damageReportId', damageReportId); // Use damageReportId instead of reportId
      formData.append('caption', caption);

      // Get user ID
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }
      formData.append('userId', user.id);

      // Log key pieces of information
      console.log(`Uploading photo for damage report: ${damageReportId}`);
      console.log(`File name: ${file.name}, File size: ${file.size} bytes`);
      console.log(`User ID: ${user.id}`);

      // Check if the damage-photos bucket exists
      try {
        const { data: bucketData, error: bucketError } = await supabase.storage
          .getBucket('damage-photos');

        if (bucketError) {
          console.log('Damage photos bucket might not exist or not accessible:', bucketError);
        } else {
          console.log('Damage photos bucket exists:', bucketData);
        }
      } catch (bucketCheckError) {
        console.error('Error checking bucket:', bucketCheckError);
      }

      setProgress(30);

      // Use Supabase client to call the edge function
      console.log('Calling edge function with Supabase client');

      // Add the damage report ID to the form data
      formData.append('damageReportId', damageReportId);

      const { data: functionData, error: functionError } = await supabase.functions
        .invoke('upload-damage-photo', {
          body: formData,
        });

      if (functionError) {
        console.error('Edge function error:', functionError);
        throw new Error(`Edge function error: ${functionError.message || 'Unknown error'}`);
      }

      // Simulate a response object for compatibility with the rest of the code
      const response = {
        ok: true,
        json: () => Promise.resolve(functionData)
      };

      setProgress(80);

      // Process the response data
      const result = await response.json();
      const photoData = result.photo;
      console.log('Successfully uploaded photo via edge function:', photoData);

      setProgress(100);
      toast.success('Photo uploaded successfully');

      // Reset form and close dialog
      setFile(null);
      setCaption('');
      onOpenChange(false);
      onPhotoUploaded(); // Use onPhotoUploaded instead of onSuccess

    } catch (error) {
      console.error('Error uploading photo:', error);
      toast.error('Failed to upload photo');
    } finally {
      setUploading(false);
      setProgress(0);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const droppedFile = e.dataTransfer.files[0];

      // Check file type
      if (!droppedFile.type.startsWith('image/')) {
        toast.error('Please select an image file');
        return;
      }

      // Check file size (limit to 5MB)
      if (droppedFile.size > 5 * 1024 * 1024) {
        toast.error('File size exceeds 5MB limit');
        return;
      }

      setFile(droppedFile);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Upload Photo</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div
            className="border-2 border-dashed rounded-lg p-6 text-center cursor-pointer hover:bg-gray-50 transition-colors"
            onClick={() => fileInputRef.current?.click()}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              className="hidden"
              accept="image/*"
            />

            {file ? (
              <div className="space-y-2">
                <div className="max-h-40 overflow-hidden">
                  <img
                    src={URL.createObjectURL(file)}
                    alt="Preview"
                    className="mx-auto max-h-40 object-contain"
                  />
                </div>
                <p className="text-sm text-gray-500">{file.name} ({Math.round(file.size / 1024)}KB)</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    setFile(null);
                    if (fileInputRef.current) fileInputRef.current.value = '';
                  }}
                >
                  Change Image
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
                  <ImagePlus className="h-6 w-6 text-gray-500" />
                </div>
                <div className="text-sm text-gray-500">
                  <p>Drag and drop an image here or click to browse</p>
                  <p className="text-xs mt-1">JPG, PNG or GIF up to 5MB</p>
                </div>
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="caption">Caption (optional)</Label>
            <Textarea
              id="caption"
              placeholder="Add a description of this photo..."
              value={caption}
              onChange={(e) => setCaption(e.target.value)}
              disabled={uploading}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={uploading}
          >
            Cancel
          </Button>

          <Button
            type="button"
            onClick={handleUpload}
            disabled={!file || uploading}
            className="gap-2"
          >
            {uploading ? (
              <>
                <CircleProgress value={progress} size="sm" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4" />
                Upload Photo
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PhotoUploadDialog;
