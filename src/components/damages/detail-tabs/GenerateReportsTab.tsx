
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Download } from 'lucide-react';
import { toast } from 'sonner';

interface GenerateReportsTabProps {
  isExporting: boolean;
  onExport: (options: {
    includePhotos: boolean;
    includeNotes: boolean;
    includeInvoices: boolean;
  }) => Promise<void>;
}

const GenerateReportsTab: React.FC<GenerateReportsTabProps> = ({
  isExporting,
  onExport
}) => {
  const [options, setOptions] = useState({
    includePhotos: true,
    includeNotes: true,
    includeInvoices: true,
  });

  const handleOptionChange = (option: keyof typeof options) => {
    setOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };

  const handleExport = async () => {
    try {
      console.log("Exporting report with options:", options);
      await onExport(options);
      toast.success('Report downloaded successfully');
    } catch (error) {
      console.error('Failed to export report:', error);
      toast.error('Failed to download report');
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Generate PDF Report</CardTitle>
        <CardDescription>
          Select what content you want to include in your damage report PDF
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="include-photos" 
              checked={options.includePhotos}
              onCheckedChange={() => handleOptionChange('includePhotos')}
            />
            <label 
              htmlFor="include-photos" 
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Include Photos
            </label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="include-notes" 
              checked={options.includeNotes}
              onCheckedChange={() => handleOptionChange('includeNotes')}
            />
            <label 
              htmlFor="include-notes" 
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Include Notes
            </label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="include-invoices" 
              checked={options.includeInvoices}
              onCheckedChange={() => handleOptionChange('includeInvoices')}
            />
            <label 
              htmlFor="include-invoices" 
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Include Invoices
            </label>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          onClick={handleExport}
          disabled={isExporting}
          className="w-full"
        >
          <Download className="h-4 w-4 mr-2" />
          {isExporting ? 'Generating...' : 'Download Report'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default GenerateReportsTab;
