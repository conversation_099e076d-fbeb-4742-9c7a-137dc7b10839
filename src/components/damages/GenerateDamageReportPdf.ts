import jsPDF from 'jspdf';
import { DamageReport } from '@/types/damages';
import { format } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner'; // Keep toast for potential future error reporting

// Re-defining interfaces locally as they might differ slightly from main types
// or to avoid complex imports if this file is isolated.
// Ideally, these should align with or import from '@/types/damages'.
interface DamagePhoto {
  id: string;
  file_name: string;
  file_path: string;
  created_at: string;
  caption?: string;
  url?: string; // URL might be pre-generated and passed in
  damage_report_id: string; // This is the correct column name
}

interface DamageNote {
  id: string;
  content: string;
  created_at: string;
  created_by?: string; // Consider fetching user name if needed
  private?: boolean;
  damage_report_id: string; // This is the correct column name
}

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  amount: number;
}

interface DamageInvoice {
  id: string;
  invoice_number?: string;
  total_amount?: number;
  status: string;
  issue_date?: string;
  due_date?: string;
  notes?: string;
  created_at: string;
  provider_name?: string; // Name might be denormalized or fetched separately
  items?: InvoiceItem[]; // Items should ideally be passed in
  damage_report_id?: string;
}

interface Property {
  id: string;
  name: string;
  address?: string;
  city?: string;
  state?: string;
}

interface Provider {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  specialty?: string;
}

interface GeneratePdfOptions {
  includePhotos: boolean;
  includeNotes: boolean;
  includeInvoices: boolean;
  specificInvoiceId?: string; // Option to generate for a single invoice
}

// Function to get signed URL for photos
const getSignedPhotoUrl = async (filePath: string): Promise<string | undefined> => { // Return undefined instead of null
  try {
    // First try to get a public URL
    const { data: publicData } = await supabase.storage
      .from('damage-photos')
      .getPublicUrl(filePath);

    if (publicData?.publicUrl) {
      console.log('Using public URL for photo:', filePath);
      return publicData.publicUrl;
    }

    // If public URL fails, try signed URL
    console.log('Trying signed URL for photo:', filePath);
    const { data, error } = await supabase.storage
      .from('damage-photos')
      .createSignedUrl(filePath, 3600); // Use createSignedUrl, 1 hour expiry

    if (error) {
      throw error;
    }
    // Return signedUrl (which is string | undefined) or explicitly undefined
    return data?.signedUrl ?? undefined;
  } catch (error) {
    console.error('Error getting photo URL:', error); // Keep essential error logs
    return undefined; // Return undefined on error
  }
};


export const generateDamageReportPdf = async (
  report: DamageReport,
  photos: DamagePhoto[], // Expect photos array to be passed in
  notes: DamageNote[],   // Expect notes array to be passed in
  invoices: DamageInvoice[], // Expect invoices array (potentially with items) to be passed in
  property?: Property,
  provider?: Provider,
  options: GeneratePdfOptions = {
    includePhotos: true,
    includeNotes: true,
    includeInvoices: true
  }
) => {

  // Fetch invoice items if needed (ideally items should be passed with invoices)
  if (options.includeInvoices && invoices.length > 0) {
    const invoicesToProcess = options.specificInvoiceId
      ? invoices.filter(inv => inv.id === options.specificInvoiceId)
      : invoices;

    for (const invoice of invoicesToProcess) {
      // Only fetch if items are missing
      if (!invoice.items || invoice.items.length === 0) {
        try {
          const { data: itemsData, error: itemsError } = await supabase
            .from('invoice_items')
            .select('*')
            .eq('invoice_id', invoice.id)
            .order('created_at', { ascending: true });

          if (itemsError) {
            console.error(`Error fetching items for invoice ${invoice.id}:`, itemsError);
            continue; // Skip items for this invoice on error
          }
          invoice.items = itemsData as InvoiceItem[];
        } catch (error) {
          console.error(`Error processing items for invoice ${invoice.id}:`, error);
        }
      }
    }
  }

  // Pre-process photo URLs if necessary (generate signed URLs if not already present)
  const photosWithUrls: DamagePhoto[] = [];
  if (options.includePhotos && photos.length > 0) {
    console.log('Processing photos for PDF:', photos);

    for (const photo of photos) {
      // First try to use the existing URL
      let url = photo.url;

      // If no URL but we have a file_path, try to get a public URL
      if ((!url || url === 'null' || url === 'undefined') && photo.file_path) {
        console.log('Getting public URL for photo:', photo.file_path);
        try {
          const { data: publicData } = await supabase.storage
            .from('damage-photos')
            .getPublicUrl(photo.file_path);

          if (publicData?.publicUrl) {
            url = publicData.publicUrl;
            console.log('Got public URL:', url);
          }
        } catch (error) {
          console.error('Error getting public URL:', error);
        }
      }

      // If still no URL, try to get a signed URL
      if ((!url || url === 'null' || url === 'undefined') && photo.file_path) {
        console.log('Getting signed URL for photo:', photo.file_path);
        url = await getSignedPhotoUrl(photo.file_path);
      }

      if (url && url !== 'null' && url !== 'undefined') {
        console.log('Adding photo with URL:', url);
        photosWithUrls.push({ ...photo, url }); // Add photo with URL
      } else {
        console.error(`Could not get URL for photo: ${photo.file_name}`); // Log if URL failed
      }
    }

    console.log(`Processed ${photosWithUrls.length} photos with URLs for PDF`);
  }


  // --- PDF Generation Logic ---
  const pdf = new jsPDF('p', 'mm', 'a4');
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  let yPos = 20; // Initial Y position
  const margin = 15; // Page margin
  const contentWidth = pageWidth - 2 * margin;

  // Helper function to add text and manage Y position
  const addText = (text: string, fontSize = 10, isBold = false, xOffset = 0) => {
    if (yPos > pageHeight - margin) { // Check for page break before adding text
      pdf.addPage();
      yPos = margin;
    }
    pdf.setFontSize(fontSize);
    pdf.setFont('helvetica', isBold ? 'bold' : 'normal');
    pdf.text(text, margin + xOffset, yPos);
    yPos += (fontSize / 2.5); // Adjust line spacing based on font size
  };

  // Helper function for wrapped text
  const addWrappedText = (text: string | null | undefined, fontSize = 10, lineHeight = 5) => {
    if (!text) return;
    pdf.setFontSize(fontSize);
    pdf.setFont('helvetica', 'normal');

    const lines = pdf.splitTextToSize(text, contentWidth);
    lines.forEach((line: string) => {
      if (yPos > pageHeight - margin - lineHeight) { // Check space before adding line
        pdf.addPage();
        yPos = margin;
      }
      pdf.text(line, margin, yPos);
      yPos += lineHeight;
    });
  };

  // Helper to check for page break
  const checkNewPage = (requiredSpace: number) => {
    if (yPos + requiredSpace > pageHeight - margin) {
      pdf.addPage();
      yPos = margin;
      return true;
    }
    return false;
  };

  // Helper for section headers
  const addSectionHeader = (text: string) => {
    if (checkNewPage(15)) { // Add extra space if new page
        yPos += 5;
    } else {
        yPos += 8; // Space before header
    }
    addText(text, 14, true);
    pdf.setDrawColor(200, 200, 200);
    pdf.line(margin, yPos, pageWidth - margin, yPos); // Line below header
    yPos += 6; // Space after header line
  };

  // --- PDF Content ---

  // Filter invoices if a specific one is requested
  const filteredInvoices = options.specificInvoiceId
    ? invoices.filter(inv => inv.id === options.specificInvoiceId)
    : invoices;

  // Main Title
  pdf.setFontSize(18);
  pdf.setFont('helvetica', 'bold');
  const mainTitle = options.specificInvoiceId && filteredInvoices.length > 0
    ? `Invoice #${filteredInvoices[0].invoice_number || 'Details'}`
    : 'Damage Report';
  pdf.text(mainTitle, pageWidth / 2, yPos, { align: 'center' });
  yPos += 12;

  // Report Details
  addText(`Report ID: ${report.id}`, 9);
  addText(`Title: ${report.title}`, 11, true);
  addText(`Status: ${report.status?.toUpperCase() || 'N/A'}`, 9);
  addText(`Created: ${format(new Date(report.created_at), 'PPP')}`, 9);
  addText(`Last Updated: ${format(new Date(report.updated_at), 'PPP')}`, 9);
  if (report.platform) {
    addText(`Platform: ${report.platform}`, 9);
  }
  yPos += 5;

  // Property Info
  if (property) {
    addSectionHeader('Property Information');
    addText(`Name: ${property.name}`, 10, true);
    if (property.address) {
      addText(`Address: ${property.address}`, 9);
    }
    if (property.city && property.state) {
      addText(`Location: ${property.city}, ${property.state}`, 9);
    }
    yPos += 3;
  }

  // Provider Info (Show only if generating full report or if it's the provider for the specific invoice)
  const shouldShowProvider = provider && (!options.specificInvoiceId || (filteredInvoices.length > 0 && report.provider_id === provider.id));
  if (shouldShowProvider) {
    addSectionHeader('Assigned Service Provider');
    addText(`Name: ${provider.name}`, 10, true);
    if (provider.specialty) {
      addText(`Specialty: ${provider.specialty}`, 9);
    }
    if (provider.email) {
      addText(`Email: ${provider.email}`, 9);
    }
    if (provider.phone) {
      addText(`Phone: ${provider.phone}`, 9);
    }
    yPos += 3;
  }

  // Description (Only for full report)
  if (!options.specificInvoiceId) {
    addSectionHeader('Description');
    addWrappedText(report.description);
    yPos += 5;
  }

  // Photos (Only for full report)
  if (options.includePhotos && !options.specificInvoiceId) {
    addSectionHeader('Photos');
    if (photosWithUrls.length > 0) {
      for (let i = 0; i < photosWithUrls.length; i++) {
        const photo = photosWithUrls[i];
        if (checkNewPage(70)) { // Check space for image + caption
           addSectionHeader('Photos (Continued)'); // Add header again if page breaks
        }

        if (photo.url) {
          try {
            // --- Image Loading and Drawing ---
            console.log('Loading image from URL:', photo.url);
            let dataUrl;

            try {
              // First try to get a signed URL if we don't already have one
              let imageUrl = photo.url;

              // If we have a file_path but the URL is failing, try to get a fresh signed URL
              if (photo.file_path && (!imageUrl || imageUrl.includes('undefined'))) {
                console.log('Getting fresh signed URL for:', photo.file_path);
                try {
                  const { data: urlData } = await supabase
                    .storage
                    .from('damage-photos')
                    .createSignedUrl(photo.file_path, 3600);

                  if (urlData?.signedUrl) {
                    imageUrl = urlData.signedUrl;
                    console.log('Got fresh signed URL:', imageUrl);
                  }
                } catch (signedUrlError) {
                  console.error('Error getting signed URL:', signedUrlError);
                }
              }

              // This part is complex and browser-dependent. Using jsPDF's built-in
              // image support is generally preferred if possible, but requires
              // the image data (e.g., Base64). Fetching and converting here.
              const response = await fetch(imageUrl, {
                mode: 'cors',
                cache: 'no-cache',
                headers: {
                  'Cache-Control': 'no-cache',
                  'Pragma': 'no-cache'
                }
              }); // Fetch the image with CORS headers

              if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

              const blob = await response.blob();
              const reader = new FileReader();
              dataUrl = await new Promise<string>((resolve, reject) => {
                  reader.onloadend = () => resolve(reader.result as string);
                  reader.onerror = reject;
                  reader.readAsDataURL(blob);
              });
              // --- End Image Loading ---
              console.log('Successfully loaded image data');
            } catch (fetchError) {
              console.error('Error fetching image:', fetchError);
              throw fetchError; // Re-throw to be caught by the outer try/catch
            }

            const imgProps = pdf.getImageProperties(dataUrl as string);
            const aspectRatio = imgProps.width / imgProps.height;
            const maxWidth = contentWidth / 2 - 5; // Max width for 2-column layout
            const maxHeight = 80; // Max height

            let imgWidth = maxWidth;
            let imgHeight = imgWidth / aspectRatio;

            if (imgHeight > maxHeight) {
              imgHeight = maxHeight;
              imgWidth = imgHeight * aspectRatio;
            }

             if (checkNewPage(imgHeight + 10)) { // Check again after getting dimensions
                 addSectionHeader('Photos (Continued)');
             }

            // Add image (consider centering or layout)
            pdf.addImage(dataUrl, 'JPEG', margin, yPos, imgWidth, imgHeight);
            yPos += imgHeight + 3;

            // Add caption
            if (photo.caption) {
              addWrappedText(photo.caption, 8, 4);
            }
            yPos += 7; // Space after photo/caption

          } catch (imgError) {
            console.error('Error processing image for PDF:', imgError);
            addText(`[Image: ${photo.file_name || 'Unnamed'}] - Failed to load`, 9, false);
            yPos += 5;
          }
        } else {
           addText(`[Photo: ${photo.file_name || 'Unnamed'}] - URL missing`, 9, false);
           yPos += 5;
        }
      }
    } else {
      addText('No photos included.', 9, false);
      yPos += 5;
    }
  }

  // Notes (Only for full report)
  if (options.includeNotes && !options.specificInvoiceId) {
    addSectionHeader('Notes');
    console.log('Processing notes for PDF:', notes);

    // Filter notes - include all non-private notes and user's own private notes
    // We're assuming notes are already filtered by the backend
    if (notes && notes.length > 0) {
      notes.forEach((note, index) => {
        if (checkNewPage(20)) { // Check space for note header + some text
            addSectionHeader('Notes (Continued)');
        }
        const noteDate = format(new Date(note.created_at), 'PPP p');
        const noteAuthor = note.created_by || 'User';
        const isPrivate = note.private ? ' (Private)' : '';

        addText(`Note by ${noteAuthor}${isPrivate} on ${noteDate}`, 9, true);
        yPos += 1;
        addWrappedText(note.content, 9, 4.5);
        yPos += 5; // Space after note
      });
    } else {
      addText('No notes included.', 9, false);
      yPos += 5;
    }
  }

  // Invoices
  if (options.includeInvoices && filteredInvoices.length > 0) {
     if (!options.specificInvoiceId) { // Add header only if showing all invoices
        addSectionHeader('Invoices');
     }

    filteredInvoices.forEach((invoice, index) => {
      if (checkNewPage(40)) { // Space for invoice header + some items
         addSectionHeader(options.specificInvoiceId ? 'Invoice Details' : `Invoices (Continued)`);
      }

      // Invoice Header
      const invoiceTitle = invoice.invoice_number ? `Invoice #${invoice.invoice_number}` : `Invoice ${index + 1}`;
      if (!options.specificInvoiceId) { // Don't repeat title if it's the main title
        addText(invoiceTitle, 11, true);
        yPos += 2;
      }

      // Invoice Details
      if (invoice.provider_name) addText(`Provider: ${invoice.provider_name}`, 9);
      if (invoice.total_amount !== undefined) addText(`Total Amount: $${invoice.total_amount.toFixed(2)}`, 9);
      addText(`Status: ${invoice.status || 'N/A'}`, 9);
      if (invoice.issue_date) addText(`Issued: ${format(new Date(invoice.issue_date), 'PPP')}`, 9);
      if (invoice.due_date) addText(`Due: ${format(new Date(invoice.due_date), 'PPP')}`, 9);
      yPos += 4;

      // Invoice Items Table
      if (invoice.items && invoice.items.length > 0) {
        if (checkNewPage(15)) { // Space for table header
             addSectionHeader(options.specificInvoiceId ? 'Invoice Items' : `Invoice ${invoice.invoice_number || index + 1} Items`);
        } else {
             yPos += 4; // Space before table
        }

        // Table Header
        const headerY = yPos;
        pdf.setFontSize(8);
        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(80, 80, 80);
        const col1X = margin;
        const col2X = margin + 15;
        const col3X = margin + 105;
        const col4X = margin + 135;
        pdf.text("Qty", col1X, headerY);
        pdf.text("Description", col2X, headerY);
        pdf.text("Unit Price", col3X, headerY, { align: 'right' });
        pdf.text("Amount", col4X, headerY, { align: 'right' });
        yPos += 4;
        pdf.setDrawColor(200, 200, 200);
        pdf.line(margin, yPos, pageWidth - margin, yPos);
        yPos += 4;

        // Table Body
        pdf.setFontSize(8);
        pdf.setFont('helvetica', 'normal');
        pdf.setTextColor(0, 0, 0);
        invoice.items.forEach(item => {
          const descLines = pdf.splitTextToSize(item.description || '', col3X - col2X - 5); // Width for description
          const requiredLineHeight = descLines.length * 4; // Estimate height needed
          if (checkNewPage(requiredLineHeight + 2)) {
             // Optional: Repeat table header on new page
             pdf.setFontSize(8);
             pdf.setFont('helvetica', 'bold');
             pdf.setTextColor(80, 80, 80);
             pdf.text("Qty", col1X, yPos);
             pdf.text("Description", col2X, yPos);
             pdf.text("Unit Price", col3X, yPos, { align: 'right' });
             pdf.text("Amount", col4X, yPos, { align: 'right' });
             yPos += 4;
             pdf.setDrawColor(200, 200, 200);
             pdf.line(margin, yPos, pageWidth - margin, yPos);
             yPos += 4;
             pdf.setFontSize(8);
             pdf.setFont('helvetica', 'normal');
             pdf.setTextColor(0, 0, 0);
          }

          const itemStartY = yPos;
          pdf.text(item.quantity?.toString() || '0', col1X, itemStartY);
          pdf.text(descLines, col2X, itemStartY);
          pdf.text(`$${item.unit_price?.toFixed(2) || '0.00'}`, col3X, itemStartY, { align: 'right' });
          pdf.text(`$${item.amount?.toFixed(2) || '0.00'}`, col4X, itemStartY, { align: 'right' });
          yPos += requiredLineHeight; // Move yPos down by height of description lines
        });

        // Table Footer (Total)
        yPos += 2;
        pdf.setDrawColor(200, 200, 200);
        pdf.line(margin, yPos, pageWidth - margin, yPos);
        yPos += 4;
        pdf.setFont('helvetica', 'bold');
        pdf.text("Total:", col3X - 10, yPos, { align: 'right' });
        pdf.text(`$${invoice.total_amount?.toFixed(2) || "0.00"}`, col4X, yPos, { align: 'right' });
        yPos += 6;

      } else {
         addText('No items listed for this invoice.', 9, false);
         yPos += 5;
      }

      // Invoice Notes
      if (invoice.notes) {
        if (checkNewPage(15)) {
            addSectionHeader(options.specificInvoiceId ? 'Invoice Notes' : `Invoice ${invoice.invoice_number || index + 1} Notes`);
        } else {
            yPos += 4;
        }
        pdf.setFontSize(9);
        pdf.setFont('helvetica', 'italic');
        addWrappedText(invoice.notes, 9, 4.5);
        yPos += 5;
      }
       yPos += 5; // Space after each invoice
    });
  } else if (options.includeInvoices && !options.specificInvoiceId) {
    addSectionHeader('Invoices');
    addText('No invoices included.', 9, false);
    yPos += 5;
  }

  // PDF Footer
  const pageCount = pdf.internal.pages.length; // Get total pages
  pdf.setFontSize(8);
  pdf.setTextColor(150, 150, 150);
  for (let i = 1; i <= pageCount; i++) {
    pdf.setPage(i);
    pdf.text(`Page ${i} of ${pageCount}`, pageWidth / 2, pageHeight - 10, { align: 'center' });
    pdf.text(`Generated on ${format(new Date(), 'PPP p')}`, margin, pageHeight - 10);
  }

  // Save PDF
  const filename = options.specificInvoiceId && filteredInvoices.length > 0
    ? `Invoice_${filteredInvoices[0].invoice_number || filteredInvoices[0].id.substring(0, 8)}.pdf`
    : `Damage_Report_${report.id.substring(0, 8)}.pdf`;

  pdf.save(filename);
};
