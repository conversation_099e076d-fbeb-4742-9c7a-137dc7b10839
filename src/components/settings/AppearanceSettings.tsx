
import React, { useRef, useEffect } from 'react';
import ToggleOption, { ToggleSetting } from './ToggleOption';
import { UserSettingKey } from '@/types/settings';
import { useAppearanceSettingsQuery } from '@/hooks/useAppearanceSettingsQuery';
import { Skeleton } from '@/components/ui/skeleton';
import { useNavigationRefresh } from '@/hooks/useNavigationRefresh';

interface AppearanceSettingsProps {
  settings?: ToggleSetting[];
  handleToggleSetting?: (id: UserSettingKey, checked: boolean) => void;
}

const AppearanceSettings = ({ settings: propSettings, handleToggleSetting }: AppearanceSettingsProps) => {
  // Use a ref to track if a change is being processed to avoid loops
  const processingChange = useRef(false);
  const { refreshRouteData } = useNavigationRefresh();

  // Use our React Query hook for appearance settings
  const {
    settings: querySettings,
    loading,
    updateSetting,
    refreshSettings
  } = useAppearanceSettingsQuery();

  // Refresh data when component mounts
  useEffect(() => {
    console.log('[AppearanceSettings] Component mounted, refreshing data');
    refreshSettings();
    refreshRouteData('/settings/appearance');
  }, [refreshSettings, refreshRouteData]);

  // Set up a periodic refresh timer
  useEffect(() => {
    console.log('[AppearanceSettings] Setting up periodic refresh timer');

    // Set up periodic refresh every 30 seconds
    const refreshInterval = setInterval(() => {
      // Only refresh if the document is visible
      if (document.visibilityState === 'visible') {
        console.log('[AppearanceSettings] Periodic refresh triggered');
        refreshSettings();
      }
    }, 30000); // 30 seconds

    return () => {
      clearInterval(refreshInterval);
    };
  }, [refreshSettings]);

  // Convert query settings to toggle settings format
  const mappedSettings: ToggleSetting[] = [
    {
      id: 'dark_mode',
      label: 'Dark Mode',
      description: 'Use dark mode for the application interface',
      enabled: querySettings.dark_mode
    },
    {
      id: 'compact_mode',
      label: 'Compact Mode',
      description: 'Show more information with less spacing',
      enabled: querySettings.compact_mode
    },
    {
      id: 'animations',
      label: 'Animations',
      description: 'Enable animations throughout the application',
      enabled: querySettings.animations
    }
  ];

  // Use either prop settings or query settings
  const displaySettings = propSettings || mappedSettings;

  const handleToggleChange = async (id: UserSettingKey, checked: boolean) => {
    // Skip if already processing a change
    if (processingChange.current) return;

    // Set processing flag to prevent re-entry
    processingChange.current = true;

    // Log the change
    console.log(`AppearanceSettings: Toggle changed: ${id} = ${checked}`);

    try {
      // If we have a prop handler, use it
      if (handleToggleSetting) {
        handleToggleSetting(id, checked);
      }
      // Otherwise use our query hook
      else {
        await updateSetting(id, checked);
      }
    } finally {
      // Reset processing flag after a short delay
      setTimeout(() => {
        processingChange.current = false;
      }, 300);
    }
  };

  if (loading && !propSettings) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-start justify-between py-4">
            <div className="space-y-1">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-4 w-64" />
            </div>
            <Skeleton className="h-6 w-12" />
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-2 divide-y divide-border">
      {displaySettings.map((setting) => (
        <ToggleOption
          key={setting.id}
          setting={setting}
          onChange={handleToggleChange}
        />
      ))}
    </div>
  );
};

export default AppearanceSettings;
