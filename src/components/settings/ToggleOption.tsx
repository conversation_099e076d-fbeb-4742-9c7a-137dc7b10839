
import React from 'react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { UserSettingKey } from '@/types/settings';

export interface ToggleSetting {
  id: UserSettingKey;
  label: string;
  description: string;
  enabled: boolean;
}

interface ToggleOptionProps {
  setting: ToggleSetting;
  onChange: (id: UserSettingKey, value: boolean) => void;
}

const ToggleOption = ({ setting, onChange }: ToggleOptionProps) => {
  return (
    <div className="flex items-start justify-between py-4">
      <div className="space-y-1">
        <Label htmlFor={setting.id} className="text-base">{setting.label}</Label>
        <p className="text-sm text-muted-foreground">{setting.description}</p>
      </div>
      <Switch
        id={setting.id}
        checked={setting.enabled}
        onCheckedChange={(checked) => onChange(setting.id, checked)}
      />
    </div>
  );
};

export default ToggleOption;
