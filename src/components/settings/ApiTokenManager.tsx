import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Key, Copy, Trash2, RefreshCw } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

const ApiTokenManager = () => {
  const { authState } = useAuth();
  const [tokens, setTokens] = useState<{id: string, created_at: string}[]>([]);
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);

  useEffect(() => {
    if (authState.user?.id) {
      fetchTokens();
    }
  }, [authState.user?.id]);

  const fetchTokens = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('extension_api_tokens')
        .select('id, created_at')
        .eq('user_id', authState.user?.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setTokens(data || []);
    } catch (err) {
      console.error('Error fetching tokens:', err);
      toast.error('Failed to load API tokens');
    } finally {
      setLoading(false);
    }
  };

  const generateToken = async () => {
    setGenerating(true);
    try {
      const { data, error } = await supabase.functions.invoke('generate-extension-token', {
        body: {}
      });

      if (error) throw error;

      if (data?.token) {
        await navigator.clipboard.writeText(data.token);
        toast.success('New token copied to clipboard');
        fetchTokens();
      }
    } catch (err) {
      console.error('Error generating token:', err);
      toast.error('Failed to generate API token');
    } finally {
      setGenerating(false);
    }
  };

  const deleteToken = async (tokenId: string) => {
    try {
      const { error } = await supabase
        .from('extension_api_tokens')
        .delete()
        .eq('id', tokenId);

      if (error) throw error;
      
      toast.success('Token deleted successfully');
      fetchTokens();
    } catch (err) {
      console.error('Error deleting token:', err);
      toast.error('Failed to delete token');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-semibold tracking-tight">API Tokens</h2>
          <p className="text-muted-foreground">
            Manage API tokens for the StayFu Chrome Extension
          </p>
        </div>
        <Button onClick={generateToken} disabled={generating}>
          <Key className="w-4 h-4 mr-2" />
          {generating ? 'Generating...' : 'Generate Token'}
        </Button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center h-32">
          <RefreshCw className="w-6 h-6 animate-spin text-muted-foreground" />
        </div>
      ) : tokens.length > 0 ? (
        <div className="space-y-4">
          {tokens.map((token) => (
            <Card key={token.id} className="p-4 flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <Key className="w-4 h-4 text-muted-foreground" />
                  <span className="font-medium">Extension API Token</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Created on {formatDate(token.created_at)}
                </p>
              </div>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => deleteToken(token.id)}
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="p-6 text-center text-muted-foreground">
          <Key className="w-8 h-8 mx-auto mb-2 opacity-50" />
          <p>No API tokens found. Generate one to use with the Chrome extension.</p>
        </Card>
      )}

      <div className="bg-muted/50 rounded-lg p-4 text-sm text-muted-foreground">
        <h3 className="font-medium mb-2">About API Tokens</h3>
        <p>
          API tokens are used to authenticate the Chrome extension with your StayFu account.
          The token will only be shown once when generated - make sure to copy it immediately.
        </p>
      </div>
    </div>
  );
};

export default ApiTokenManager;