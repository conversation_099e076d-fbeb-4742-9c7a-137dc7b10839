
import React from 'react';
import { Button } from '@/components/ui/button';
import { HelpCircle } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface HelpSettingsProps {
  resetTutorials: () => void;
}

const HelpSettings = ({ resetTutorials }: HelpSettingsProps) => {
  const handleResetTutorials = () => {
    resetTutorials();
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HelpCircle size={18} />
            Tutorials
          </CardTitle>
          <CardDescription>
            Reset product tutorials to view them again
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            If you want to see the introduction tutorials again, you can reset them here.
          </p>
          <Button 
            variant="outline" 
            onClick={handleResetTutorials}
          >
            Reset All Tutorials
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default HelpSettings;
