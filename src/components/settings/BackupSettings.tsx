import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Loader2, Download, Upload, AlertCircle, Info } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  BackupOptions,
  defaultBackupOptions,
  RestoreOptions,
  defaultRestoreOptions,
  ConflictResolution,
  createBackup,
  downloadBackupAsCSV,
  readBackupZip,
  restoreBackup
} from '@/services/backupService';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';

const BackupSettings: React.FC = () => {
  const { authState } = useAuth();
  const [isBackupLoading, setIsBackupLoading] = useState(false);
  const [isRestoreLoading, setIsRestoreLoading] = useState(false);
  const [backupOptions, setBackupOptions] = useState<BackupOptions>({...defaultBackupOptions});
  const [restoreOptions, setRestoreOptions] = useState<RestoreOptions>({...defaultRestoreOptions});
  const [backupFile, setBackupFile] = useState<File | null>(null);
  const [restorePreview, setRestorePreview] = useState<Record<string, any> | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [progress, setProgress] = useState(0);

  const handleBackupOptionChange = (option: keyof BackupOptions, checked: boolean) => {
    setBackupOptions(prev => ({
      ...prev,
      [option]: checked
    }));
  };

  const handleRestoreOptionChange = (option: keyof RestoreOptions, checked: boolean) => {
    if (option === 'conflictResolution') return; // This is handled by the select

    setRestoreOptions(prev => ({
      ...prev,
      [option]: checked
    }));
  };

  const handleConflictResolutionChange = (value: string) => {
    setRestoreOptions(prev => ({
      ...prev,
      conflictResolution: value as ConflictResolution
    }));
  };

  const handleBackup = async () => {
    if (!authState.user?.id) {
      toast.error('You must be logged in to create a backup');
      return;
    }

    setIsBackupLoading(true);
    toast.loading('Creating backup...', { id: 'backup-toast' });
    setProgress(10);

    try {
      // Create the backup data
      setProgress(30);
      const backupData = await createBackup(authState.user.id, backupOptions);

      // Download as CSV
      setProgress(80);
      const success = downloadBackupAsCSV(backupData, `stayfu-backup-${new Date().toISOString().split('T')[0]}`);

      setProgress(100);
      // Clear the loading toast first
      toast.dismiss('backup-toast');

      if (success) {
        toast.success('Backup created and downloaded successfully', { duration: 5000 });
      } else {
        toast.error('Failed to download backup', { duration: 5000 });
      }
    } catch (error) {
      console.error('Error creating backup:', error);
      toast.dismiss('backup-toast');
      toast.error(`Failed to create backup: ${error instanceof Error ? error.message : 'Unknown error'}`, { duration: 5000 });
    } finally {
      setIsBackupLoading(false);
      // Reset progress after a delay
      setTimeout(() => setProgress(0), 1000);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      setBackupFile(files[0]);
      // Reset preview when a new file is selected
      setRestorePreview(null);
    }
  };

  const handlePreviewBackup = async () => {
    if (!backupFile) {
      toast.error('Please select a backup file first');
      return;
    }

    setIsRestoreLoading(true);
    toast.loading('Reading backup file...', { id: 'restore-toast' });
    setProgress(20);

    try {
      const backupData = await readBackupZip(backupFile);
      setProgress(80);

      // Create a preview of the backup data
      const preview: Record<string, any> = {
        tables: {},
        counts: {}
      };

      // Count items in each table
      for (const [tableName, tableData] of Object.entries(backupData.data)) {
        if (Array.isArray(tableData)) {
          preview.counts[tableName] = tableData.length;
        }
      }

      // Add metadata
      preview.metadata = backupData.metadata;

      setRestorePreview(preview);
      setProgress(100);
      toast.dismiss('restore-toast');
      toast.success('Backup file loaded successfully', { duration: 5000 });
    } catch (error) {
      console.error('Error reading backup file:', error);
      toast.dismiss('restore-toast');
      toast.error(`Failed to read backup file: ${error instanceof Error ? error.message : 'Unknown error'}`, { duration: 5000 });
    } finally {
      setIsRestoreLoading(false);
      // Reset progress after a delay
      setTimeout(() => setProgress(0), 1000);
    }
  };

  const handleRestore = async () => {
    if (!authState.user?.id) {
      toast.error('You must be logged in to restore a backup');
      return;
    }

    if (!backupFile) {
      toast.error('Please select a backup file first');
      return;
    }

    setIsRestoreLoading(true);
    toast.loading('Starting restore process...', { id: 'restore-toast' });
    setProgress(10);

    try {
      // Read the backup file
      const backupData = await readBackupZip(backupFile);
      setProgress(30);

      // Restore the data
      const results = await restoreBackup(authState.user.id, backupData, restoreOptions);
      setProgress(90);

      // Clear the loading toast first
      toast.dismiss('restore-toast');

      // Show a success or error toast that will auto-dismiss
      if (results.success) {
        toast.success(
          `Restore completed: ${results.stats.inserted} items inserted, ${results.stats.updated} updated, ${results.stats.skipped} skipped`,
          { duration: 5000 } // Auto-dismiss after 5 seconds
        );
      } else {
        toast.error(
          `Restore completed with errors: ${results.errors.length} errors occurred`,
          { duration: 5000 } // Auto-dismiss after 5 seconds
        );
      }

      setProgress(100);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      setBackupFile(null);
      setRestorePreview(null);
    } catch (error) {
      console.error('Error restoring backup:', error);
      toast.dismiss('restore-toast');
      toast.error(`Failed to restore backup: ${error instanceof Error ? error.message : 'Unknown error'}`, { duration: 5000 });
    } finally {
      setIsRestoreLoading(false);
      // Reset progress after a delay
      setTimeout(() => setProgress(0), 1000);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Backup & Export</h3>
        <p className="text-sm text-muted-foreground">
          Create a backup of your StayFu data that you can use to restore or transfer to another account.
        </p>
      </div>

      {progress > 0 && (
        <Progress value={progress} className="w-full h-2" />
      )}

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Important</AlertTitle>
        <AlertDescription>
          Backups are downloaded as ZIP files containing CSV files which can be imported into spreadsheet applications or used to restore your data.
          Image files are not included in the backup by default, but you can choose to include image URLs.
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle>Create Backup</CardTitle>
          <CardDescription>
            Select what data you want to include in your backup
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="properties"
                checked={backupOptions.properties}
                onCheckedChange={(checked) => handleBackupOptionChange('properties', checked as boolean)}
              />
              <Label htmlFor="properties">Properties</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="inventory"
                checked={backupOptions.inventory}
                onCheckedChange={(checked) => handleBackupOptionChange('inventory', checked as boolean)}
              />
              <Label htmlFor="inventory">Inventory Items</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="damageReports"
                checked={backupOptions.damageReports}
                onCheckedChange={(checked) => handleBackupOptionChange('damageReports', checked as boolean)}
              />
              <Label htmlFor="damageReports">Damage Reports (includes photos metadata and notes)</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="maintenanceTasks"
                checked={backupOptions.maintenanceTasks}
                onCheckedChange={(checked) => handleBackupOptionChange('maintenanceTasks', checked as boolean)}
              />
              <Label htmlFor="maintenanceTasks">Maintenance Tasks</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="teams"
                checked={backupOptions.teams}
                onCheckedChange={(checked) => handleBackupOptionChange('teams', checked as boolean)}
              />
              <Label htmlFor="teams">Teams & Team Members</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="includeImages"
                checked={backupOptions.includeImages}
                onCheckedChange={(checked) => handleBackupOptionChange('includeImages', checked as boolean)}
              />
              <Label htmlFor="includeImages">Include Image URLs (not the actual images)</Label>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button
            onClick={handleBackup}
            disabled={isBackupLoading || !Object.values(backupOptions).some(Boolean)}
            className="ml-auto"
          >
            {isBackupLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating Backup...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Download Backup
              </>
            )}
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Restore from Backup</CardTitle>
          <CardDescription>
            Upload a backup file to restore your data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <Label htmlFor="backup-file">Backup File</Label>
              <div className="flex gap-2">
                <input
                  id="backup-file"
                  type="file"
                  accept=".zip"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  disabled={isRestoreLoading}
                />
                <Button
                  variant="outline"
                  onClick={handlePreviewBackup}
                  disabled={!backupFile || isRestoreLoading}
                >
                  Preview
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Upload a .zip backup file created by StayFu
              </p>
            </div>

            <Alert variant="default">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Cross-Account Restore</AlertTitle>
              <AlertDescription>
                <p className="text-sm">
                  When restoring data to a different account than the one it was exported from, new IDs will be generated for all items to avoid conflicts.
                  This means relationships between items (like properties and their inventory) will be preserved, but any external references will need to be updated.
                </p>
              </AlertDescription>
            </Alert>

            {restorePreview && (
              <Alert className="bg-muted">
                <Info className="h-4 w-4" />
                <AlertTitle>Backup Preview</AlertTitle>
                <AlertDescription>
                  <div className="text-sm">
                    <p><strong>Created:</strong> {new Date(restorePreview.metadata?.created_at).toLocaleString()}</p>
                    <p className="mt-2"><strong>Contents:</strong></p>
                    <ul className="list-disc pl-5 mt-1">
                      {Object.entries(restorePreview.counts).map(([table, count]) => (
                        <li key={table}>{table.replace('_', ' ')}: {count as number} items</li>
                      ))}
                    </ul>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-4 mt-4">
              <h4 className="text-sm font-medium">Restore Options</h4>

              <div className="grid gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="restore-properties"
                    checked={restoreOptions.properties}
                    onCheckedChange={(checked) => handleRestoreOptionChange('properties', checked as boolean)}
                    disabled={isRestoreLoading}
                  />
                  <Label htmlFor="restore-properties">Properties</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="restore-inventory"
                    checked={restoreOptions.inventory}
                    onCheckedChange={(checked) => handleRestoreOptionChange('inventory', checked as boolean)}
                    disabled={isRestoreLoading}
                  />
                  <Label htmlFor="restore-inventory">Inventory Items</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="restore-damageReports"
                    checked={restoreOptions.damageReports}
                    onCheckedChange={(checked) => handleRestoreOptionChange('damageReports', checked as boolean)}
                    disabled={isRestoreLoading}
                  />
                  <Label htmlFor="restore-damageReports">Damage Reports (includes photos and notes)</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="restore-maintenanceTasks"
                    checked={restoreOptions.maintenanceTasks}
                    onCheckedChange={(checked) => handleRestoreOptionChange('maintenanceTasks', checked as boolean)}
                    disabled={isRestoreLoading}
                  />
                  <Label htmlFor="restore-maintenanceTasks">Maintenance Tasks</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="restore-teams"
                    checked={restoreOptions.teams}
                    onCheckedChange={(checked) => handleRestoreOptionChange('teams', checked as boolean)}
                    disabled={isRestoreLoading}
                  />
                  <Label htmlFor="restore-teams">Teams & Team Members</Label>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="conflict-resolution">When items already exist:</Label>
                <Select
                  value={restoreOptions.conflictResolution}
                  onValueChange={handleConflictResolutionChange}
                  disabled={isRestoreLoading}
                >
                  <SelectTrigger id="conflict-resolution" className="w-full">
                    <SelectValue placeholder="Select how to handle conflicts" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="skip">Skip (don't modify existing items)</SelectItem>
                    <SelectItem value="update">Update (merge with existing items)</SelectItem>
                    <SelectItem value="overwrite">Overwrite (replace existing items)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground mt-1">
                  {restoreOptions.conflictResolution === 'skip' && 'Existing items will be left unchanged. Only new items will be added.'}
                  {restoreOptions.conflictResolution === 'update' && 'Existing items will be updated with new values, preserving any fields not in the backup.'}
                  {restoreOptions.conflictResolution === 'overwrite' && 'Existing items will be completely replaced with the backup version.'}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button
            onClick={handleRestore}
            disabled={isRestoreLoading || !backupFile || !restorePreview || !Object.values(restoreOptions).some(Boolean)}
            className="ml-auto"
          >
            {isRestoreLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Restoring Data...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Restore Backup
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default BackupSettings;
