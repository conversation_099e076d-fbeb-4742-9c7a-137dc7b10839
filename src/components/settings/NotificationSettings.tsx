
import React from 'react';
import ToggleOption, { ToggleSetting } from './ToggleOption';
import { UserSettingKey } from '@/types/settings';

interface NotificationSettingsProps {
  settings: ToggleSetting[];
  handleToggleSetting: (id: UserSettingKey, checked: boolean) => void;
}

const NotificationSettings = ({ settings, handleToggleSetting }: NotificationSettingsProps) => {
  return (
    <div className="space-y-2 divide-y divide-border">
      {settings.map((setting) => (
        <ToggleOption 
          key={setting.id} 
          setting={setting} 
          onChange={handleToggleSetting}
        />
      ))}
    </div>
  );
};

export default NotificationSettings;
