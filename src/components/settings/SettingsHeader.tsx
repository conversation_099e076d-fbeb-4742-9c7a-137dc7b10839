
import React from 'react';
import { Save } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SettingsSections } from './SettingsSidebar';

interface SettingsHeaderProps {
  activeSection: string;
  hasChanges: boolean;
  loading: boolean;
  saveSettings: () => void;
}

const SettingsHeader = ({ 
  activeSection, 
  hasChanges, 
  loading, 
  saveSettings 
}: SettingsHeaderProps) => {
  const currentSection = SettingsSections.find(section => section.id === activeSection);

  return (
    <div className="mb-6 pb-6 border-b border-border">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold mb-2">
            {currentSection?.label}
          </h2>
          <p className="text-muted-foreground">
            {currentSection?.description}
          </p>
        </div>
        {hasChanges && (
          <Button onClick={saveSettings} className="flex items-center gap-2" disabled={loading}>
            <Save size={18} />
            {loading ? 'Saving...' : 'Save Changes'}
          </Button>
        )}
      </div>
    </div>
  );
};

export default SettingsHeader;
