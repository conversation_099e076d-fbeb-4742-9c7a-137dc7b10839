import React from 'react';
import ApiTokenManager from './ApiTokenManager';
import { <PERSON><PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Chrome, Key } from 'lucide-react';

export interface ApiIntegrationsProps {
  // Add any props if needed
}

export const ApiIntegrations: React.FC<ApiIntegrationsProps> = () => {
  return (
    <div className="space-y-6">
      <Tabs defaultValue="extension" className="w-full">
        <TabsList>
          <TabsTrigger value="extension">
            <Chrome className="h-4 w-4 mr-2" />
            Chrome Extension
          </TabsTrigger>
          <TabsTrigger value="api">
            <Key className="h-4 w-4 mr-2" />
            API Access
          </TabsTrigger>
        </TabsList>

        <TabsContent value="extension" className="space-y-4">
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">Chrome Extension Integration</h3>
              <p className="text-sm text-muted-foreground">
                Manage API tokens for the StayFu Chrome Extension to import data from Amazon.
              </p>
            </div>
            <ApiTokenManager />
          </div>
        </TabsContent>

        <TabsContent value="api" className="space-y-4">
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">API Access</h3>
              <p className="text-sm text-muted-foreground">
                Coming soon - Manage API access for custom integrations with your StayFu account.
              </p>
            </div>
            <div className="rounded-lg border p-4 text-sm text-muted-foreground">
              <p>
                Direct API access is coming soon. This will allow you to:
              </p>
              <ul className="list-disc pl-5 mt-2 space-y-1">
                <li>Create custom integrations</li>
                <li>Automate inventory management</li>
                <li>Build your own tools and extensions</li>
                <li>Integrate with other property management systems</li>
              </ul>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ApiIntegrations;