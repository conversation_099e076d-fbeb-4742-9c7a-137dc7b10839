
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useTeamManagement } from '@/hooks/useTeamManagement';
import { useAuth } from '@/contexts/AuthContext';
import { Pencil, Trash2, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { Team } from '@/hooks/useTeamManagement';
import TeamPropertiesManager from './TeamPropertiesManager';

interface TeamSettingsProps {
  team: Team;
  onTeamUpdated: () => void;
}

const TeamSettings: React.FC<TeamSettingsProps> = ({ team, onTeamUpdated }) => {
  const { authState } = useAuth();
  const { updateTeam, deleteTeam, loading } = useTeamManagement();

  // Refresh team properties when the component mounts
  // This is now handled by the TeamPropertiesManager component
  // No need to call fetchTeamProperties here
  const [teamName, setTeamName] = useState(team.name);
  const [isEditing, setIsEditing] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const isTeamOwner = authState.user?.id === team.owner_id;

  const handleUpdateTeam = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!teamName.trim() || !isTeamOwner) {
      return;
    }

    const success = await updateTeam(team.id, teamName);

    if (success) {
      setIsEditing(false);
      onTeamUpdated();
      toast.success('Team updated successfully');
    }
  };

  const handleDeleteTeam = async () => {
    if (!isTeamOwner) {
      toast.error('Only the team owner can delete a team');
      setDeleteDialogOpen(false);
      return;
    }

    const success = await deleteTeam(team.id);

    if (success) {
      setDeleteDialogOpen(false);
      onTeamUpdated();
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Team Information</CardTitle>
          <CardDescription>
            Manage your team's basic information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleUpdateTeam}>
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="team-name">Team Name</Label>
                {isEditing ? (
                  <div className="flex gap-2">
                    <Input
                      id="team-name"
                      value={teamName}
                      onChange={(e) => setTeamName(e.target.value)}
                      placeholder="Enter team name"
                      disabled={loading || !isTeamOwner}
                    />
                    <Button
                      type="submit"
                      disabled={loading || !teamName.trim() || !isTeamOwner}
                    >
                      Save
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setTeamName(team.name);
                        setIsEditing(false);
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center justify-between">
                    <div className="text-lg font-medium">{team.name}</div>
                    {isTeamOwner && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsEditing(true)}
                      >
                        <Pencil className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                    )}
                  </div>
                )}
              </div>

              <div className="grid gap-2">
                <Label>Team Owner</Label>
                <div>
                  {isTeamOwner ? (
                    <span className="text-primary font-medium">You</span>
                  ) : (
                    <span>Another user</span>
                  )}
                </div>
              </div>

              <div className="grid gap-2">
                <Label>Created On</Label>
                <div>
                  {new Date(team.created_at).toLocaleDateString()}
                </div>
              </div>
            </div>
          </form>
        </CardContent>
        {isTeamOwner && (
          <CardFooter className="border-t pt-6">
            <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="destructive" className="ml-auto">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Team
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Delete Team</DialogTitle>
                  <DialogDescription>
                    Are you sure you want to delete this team? This action cannot be undone.
                  </DialogDescription>
                </DialogHeader>
                <div className="flex items-center p-3 bg-destructive/10 text-destructive rounded-md">
                  <AlertCircle className="h-5 w-5 mr-2" />
                  <p className="text-sm">Deleting this team will remove all members and permissions.</p>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setDeleteDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleDeleteTeam}
                  >
                    Delete Team
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </CardFooter>
        )}
      </Card>

      {isTeamOwner && (
        <TeamPropertiesManager
          team={team}
          onPropertiesUpdated={onTeamUpdated}
        />
      )}

      <Card>
        <CardHeader>
          <CardTitle>Team Notifications</CardTitle>
          <CardDescription>
            Manage notification settings for your team
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-8">
            Team notification settings will be implemented in a future update.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default TeamSettings;
