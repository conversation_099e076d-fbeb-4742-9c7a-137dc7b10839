import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { UserRole } from '@/types/supabase';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';

interface InviteTeamMemberDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onInvite: (email: string, role: UserRole) => void;
  canInviteStaff: boolean;
  canInviteProviders: boolean;
  isTeamOwner?: boolean;
  isAdmin?: boolean;
  isPropertyManager?: boolean;
  isLoading: boolean;
}

const inviteSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  role: z.enum(['property_manager', 'staff', 'service_provider']),
});

type InviteFormValues = z.infer<typeof inviteSchema>;

const InviteTeamMemberDialog: React.FC<InviteTeamMemberDialogProps> = ({
  open,
  onOpenChange,
  onInvite,
  canInviteStaff,
  canInviteProviders,
  isTeamOwner = false,
  isAdmin = false,
  isPropertyManager = false,
  isLoading
}) => {
  // Determine the default role based on permissions
  const getDefaultRole = () => {
    // For debugging
    console.log('InviteTeamMemberDialog - Permissions:', {
      isTeamOwner,
      isAdmin,
      isPropertyManager,
      canInviteStaff,
      canInviteProviders
    });

    if (isTeamOwner || isAdmin) {
      return 'property_manager';
    } else if (isPropertyManager) {
      return 'staff';
    } else if (canInviteStaff) {
      return 'staff';
    } else {
      return 'service_provider';
    }
  };

  const form = useForm<InviteFormValues>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      email: '',
      role: getDefaultRole(),
    },
  });

  useEffect(() => {
    if (open) {
      form.reset({
        email: '',
        role: getDefaultRole(),
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, canInviteStaff, canInviteProviders, isTeamOwner, isAdmin, isPropertyManager, form]);

  const handleSubmit = async (values: InviteFormValues) => {
    await onInvite(values.email, values.role as UserRole);
  };

  const handleCancel = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={(newOpen) => {
      if (!newOpen) {
        form.reset();
      }
      onOpenChange(newOpen);
    }}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Invite Team Member</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email address</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter email address"
                      {...field}
                      autoComplete="email"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>Role</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-2"
                    >
                      {canInviteStaff && (
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="staff" id="staff" />
                          </FormControl>
                          <FormLabel
                            htmlFor="staff"
                            className="font-normal cursor-pointer"
                          >
                            Staff
                          </FormLabel>
                        </FormItem>
                      )}

                      {canInviteProviders && (
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="service_provider" id="service_provider" />
                          </FormControl>
                          <FormLabel
                            htmlFor="service_provider"
                            className="font-normal cursor-pointer"
                          >
                            Service Provider
                          </FormLabel>
                        </FormItem>
                      )}

                      {(isTeamOwner || isAdmin || isPropertyManager) && (
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="property_manager" id="property_manager" />
                          </FormControl>
                          <FormLabel
                            htmlFor="property_manager"
                            className="font-normal cursor-pointer"
                          >
                            Property Manager
                          </FormLabel>
                        </FormItem>
                      )}
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
              >
                {isLoading ? 'Sending...' : 'Send Invitation'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default InviteTeamMemberDialog;
