import React, { useCallback } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  LayoutDashboard,
  Building2,
  Wrench,
  Package,
  ShoppingCart,
  AlertTriangle,
  Users,
  Settings,
  Calendar,
  User
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/hooks/usePermissionsFixed';
import { PermissionType } from '@/types/auth';
import { useNavigationRefresh } from '@/hooks/useNavigationRefresh';

interface NavItem {
  name: string;
  path: string;
  icon: React.ReactNode;
  requiredPermission?: PermissionType;
  alwaysShow?: boolean;
}

interface PermissionBasedNavigationProps {
  mobile?: boolean;
  onItemClick?: () => void;
}

const PermissionBasedNavigation: React.FC<PermissionBasedNavigationProps> = ({
  mobile = false,
  onItemClick
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { authState } = useAuth();
  const { hasPermission, isAdmin } = usePermissions();
  const { refreshRouteData } = useNavigationRefresh();

  const isActiveRoute = (path: string) => {
    // Handle both hash and non-hash routes
    // For hash router, location.pathname will be the part after the hash
    return location.pathname === path ||
      (path !== '/' && location.pathname.startsWith(path));
  };

  // Custom navigation handler that refreshes data when navigating
  const handleNavigation = useCallback((path: string, e: React.MouseEvent) => {
    e.preventDefault(); // Prevent default link behavior

    console.log(`[PermissionBasedNavigation] Navigating to ${path} with data refresh`);

    // First refresh the data for the route
    refreshRouteData(path);

    // Then navigate to the route
    // This will be handled by HashRouter as /#/path
    navigate(path);

    // Call the onItemClick callback if provided
    if (onItemClick) {
      onItemClick();
    }

    // Dispatch a custom event to notify that navigation has occurred
    window.dispatchEvent(new CustomEvent('stayfu-navigation-occurred', {
      detail: {
        from: location.pathname,
        to: path
      }
    }));
  }, [navigate, refreshRouteData, location.pathname, onItemClick]);

  // Define navigation items with required permissions
  const navigationItems: NavItem[] = [
    {
      name: 'Dashboard',
      path: '/dashboard',
      icon: <LayoutDashboard size={mobile ? 20 : 18} />,
      alwaysShow: true // Everyone can see dashboard
    },
    {
      name: 'Properties',
      path: '/properties',
      icon: <Building2 size={mobile ? 20 : 18} />,
      requiredPermission: PermissionType.MANAGE_PROPERTIES
    },
    {
      name: 'Maintenance',
      path: '/maintenance',
      icon: <Wrench size={mobile ? 20 : 18} />,
      requiredPermission: PermissionType.VIEW_MAINTENANCE
    },
    {
      name: 'Task Automation',
      path: '/maintenance/automation',
      icon: <Calendar size={mobile ? 20 : 18} />,
      requiredPermission: PermissionType.VIEW_MAINTENANCE
    },
    {
      name: 'Inventory',
      path: '/inventory',
      icon: <Package size={mobile ? 20 : 18} />,
      requiredPermission: PermissionType.VIEW_INVENTORY
    },
    {
      name: 'Purchase Orders',
      path: '/purchase-orders',
      icon: <ShoppingCart size={mobile ? 20 : 18} />,
      requiredPermission: PermissionType.VIEW_PURCHASE_ORDERS
    },
    {
      name: 'Damages',
      path: '/damages',
      icon: <AlertTriangle size={mobile ? 20 : 18} />,
      requiredPermission: PermissionType.VIEW_DAMAGE_REPORTS
    },
    {
      name: 'Teams',
      path: '/teams',
      icon: <Users size={mobile ? 20 : 18} />,
      requiredPermission: PermissionType.VIEW_TEAM
    },
    // Admin link - only for super admins
    {
      name: 'Admin',
      path: '/admin',
      icon: <User size={mobile ? 20 : 18} />,
      // No permission required - we'll filter it manually
    },
    {
      name: 'Settings',
      path: '/settings',
      icon: <Settings size={mobile ? 20 : 18} />,
      alwaysShow: true // Everyone can see settings
    }
  ];

  // Filter navigation items based on user permissions
  const visibleNavItems = navigationItems.filter(item => {
    // REBUILT: Use user metadata instead of profile
    const userRole = authState.user?.user_metadata?.role || 'property_manager';
    const isSuper = authState.user?.user_metadata?.is_super_admin === true;

    // Special case for Admin link - only super admins can see it
    if (item.name === 'Admin') {
      return isSuper;
    }

    // Always show items marked as alwaysShow
    if (item.alwaysShow) return true;

    // Admins can see everything
    if (isAdmin()) return true;

    // Property managers can see everything
    if (userRole === 'property_manager') return true;

    // Service providers should see all relevant navigation items
    if (userRole === 'service_provider') {
      // Always show these items for service providers
      if (['Dashboard', 'Properties', 'Maintenance', 'Inventory', 'Purchase Orders', 'Damages', 'Teams'].includes(item.name)) {
        return true;
      }
    }

    // Check specific permissions for other users
    if (item.requiredPermission) {
      return hasPermission(item.requiredPermission);
    }

    return false;
  });

  // Mobile navigation (sidebar)
  if (mobile) {
    return (
      <nav className="p-3">
        <ul className="space-y-1">
          {visibleNavItems.map((item) => (
            <li key={item.path}>
              <a
                href={item.path}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-md transition-colors",
                  isActiveRoute(item.path)
                    ? "bg-white/20 text-white"
                    : "text-white/80 hover:bg-white/10 hover:text-white"
                )}
                onClick={(e) => handleNavigation(item.path, e)}
              >
                {item.icon}
                <span className="text-sm font-medium">{item.name}</span>
              </a>
            </li>
          ))}
        </ul>
      </nav>
    );
  }

  // Desktop navigation (horizontal)
  return (
    <nav className="hidden md:flex items-center space-x-1 overflow-x-auto scrollbar-none">
      {visibleNavItems.map((item) => (
        <a
          key={item.path}
          href={item.path}
          className={cn(
            "flex items-center gap-1.5 px-2.5 py-1.5 text-sm font-medium rounded-md transition-colors whitespace-nowrap",
            isActiveRoute(item.path)
              ? "bg-primary text-primary-foreground"
              : "text-muted-foreground hover:text-foreground hover:bg-accent"
          )}
          onClick={(e) => handleNavigation(item.path, e)}
        >
          {item.icon}
          {item.name}
        </a>
      ))}
    </nav>
  );
};

export default PermissionBasedNavigation;
