
import { forwardRef } from 'react';
import { cn } from '@/lib/utils';
import Tooltip from './Tooltip';

interface GlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
  hoverEffect?: boolean;
  children: React.ReactNode;
  className?: string;
  interactive?: boolean;
  noPadding?: boolean;
  fullHeight?: boolean;
  tooltip?: string;
  badge?: React.ReactNode;
  animateEntry?: boolean;
}

const GlassCard = forwardRef<HTMLDivElement, GlassCardProps>(
  ({ 
    className, 
    hoverEffect = false, 
    children, 
    interactive = false,
    noPadding = false,
    fullHeight = false,
    tooltip,
    badge,
    animateEntry = false,
    ...props 
  }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'glass rounded-lg sm:rounded-xl transition-all duration-200 relative',
          !noPadding && 'p-2 sm:p-3 md:p-4',
          hoverEffect && 'glass-hover cursor-pointer active:scale-[0.98]',
          interactive && 'hover:border-primary/30 active:border-primary/50',
          fullHeight && 'h-full',
          animateEntry && 'animate-fade-in',
          className
        )}
        {...props}
      >
        {tooltip && (
          <Tooltip 
            text={tooltip} 
            position="top"
            className="z-40"
          >
            <div className="absolute top-2 right-2 text-muted-foreground">
              <span className="sr-only">Info</span>
            </div>
          </Tooltip>
        )}
        
        {badge && (
          <div className="absolute top-0 right-0 -mt-2 -mr-2 z-10">
            {badge}
          </div>
        )}
        
        {children}
      </div>
    );
  }
);

GlassCard.displayName = 'GlassCard';

export default GlassCard;
