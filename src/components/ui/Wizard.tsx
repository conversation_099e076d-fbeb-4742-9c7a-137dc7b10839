
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  ChevronRight, 
  ChevronLeft, 
  Lightbulb,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface WizardStep {
  title: string;
  content: React.ReactNode;
}

interface WizardProps {
  steps: WizardStep[];
  onComplete?: () => void;
  className?: string;
}

const Wizard: React.FC<WizardProps> = ({
  steps,
  onComplete,
  className
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  
  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete && onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const isLastStep = currentStep === steps.length - 1;
  
  return (
    <div className={cn("border rounded-xl", className)}>
      {/* Header */}
      <div className="bg-muted/50 py-3 px-4 flex items-center border-b">
        <Lightbulb className="h-5 w-5 mr-2 text-primary" />
        <h3 className="text-sm font-medium flex-1">
          Step {currentStep + 1} of {steps.length}: {steps[currentStep].title}
        </h3>
      </div>
      
      {/* Progress bar */}
      <div className="w-full bg-muted/30 h-1">
        <div 
          className="bg-primary h-1 transition-all duration-300 ease-out"
          style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
        />
      </div>
      
      {/* Content */}
      <div className="p-4 min-h-[150px] overflow-y-auto max-h-[50vh]">
        {steps[currentStep].content}
      </div>
      
      {/* Footer */}
      <div className="flex justify-between items-center px-4 py-3 border-t bg-muted/20">
        <Button
          variant="outline"
          size="sm"
          onClick={handlePrevious}
          disabled={currentStep === 0}
          className="flex items-center"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Previous
        </Button>
        
        <div className="flex gap-1 text-xs text-muted-foreground">
          {steps.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentStep(index)}
              className={cn(
                "w-2 h-2 rounded-full",
                index === currentStep ? "bg-primary" : "bg-muted"
              )}
              aria-label={`Go to step ${index + 1}`}
            />
          ))}
        </div>
        
        <Button
          size="sm"
          onClick={handleNext}
          className="flex items-center"
        >
          {isLastStep ? (
            <>
              Complete
              <CheckCircle className="h-4 w-4 ml-1" />
            </>
          ) : (
            <>
              Next
              <ChevronRight className="h-4 w-4 ml-1" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default Wizard;
