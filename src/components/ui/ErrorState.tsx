
import React, { ReactNode } from 'react';
import { AlertCircle } from 'lucide-react';

interface ErrorStateProps {
  title: string;
  message?: string;
  action?: ReactNode;
}

const ErrorState: React.FC<ErrorStateProps> = ({
  title,
  message,
  action
}) => {
  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <AlertCircle className="h-12 w-12 text-destructive mb-4" />
      <h3 className="text-lg font-medium mb-2">{title}</h3>
      {message && (
        <p className="text-sm text-muted-foreground mb-4 max-w-md">{message}</p>
      )}
      {action && (
        <div className="mt-2">
          {action}
        </div>
      )}
    </div>
  );
};

export default ErrorState;
