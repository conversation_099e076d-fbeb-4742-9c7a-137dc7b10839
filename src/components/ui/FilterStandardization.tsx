import React, { useState } from 'react';
import { Search, Filter, X, ChevronDown } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Badge } from '@/components/ui/badge';

/**
 * SearchBar - A standardized search input component
 * 
 * Usage:
 * <SearchBar
 *   value={searchQuery}
 *   onChange={setSearchQuery}
 *   placeholder="Search properties..."
 * />
 */
export const SearchBar: React.FC<{
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}> = ({ value, onChange, placeholder = 'Search...', className }) => {
  return (
    <div className={cn('relative flex-1', className)}>
      <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
      <Input
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className="pl-10 w-full"
      />
      {value && (
        <button
          onClick={() => onChange('')}
          className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
          aria-label="Clear search"
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </div>
  );
};

/**
 * FilterBar - A standardized container for filters
 * 
 * Usage:
 * <FilterBar>
 *   <FilterDropdown label="Status" value={statusFilter} onChange={setStatusFilter}>
 *     {statusOptions.map(option => (
 *       <FilterOption key={option.value} value={option.value}>{option.label}</FilterOption>
 *     ))}
 *   </FilterDropdown>
 * </FilterBar>
 */
export const FilterBar: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <div className={cn('flex flex-wrap items-center gap-2 mb-4', className)}>
      {children}
    </div>
  );
};

/**
 * FilterDropdown - A standardized dropdown filter component
 * 
 * Usage:
 * <FilterDropdown 
 *   label="Status" 
 *   value={statusFilter} 
 *   onChange={setStatusFilter}
 * >
 *   <FilterOption value="all">All Statuses</FilterOption>
 *   <FilterOption value="active">Active</FilterOption>
 *   <FilterOption value="inactive">Inactive</FilterOption>
 * </FilterDropdown>
 */
export const FilterDropdown: React.FC<{
  children: React.ReactNode;
  label: string;
  value: string;
  onChange: (value: string) => void;
  className?: string;
}> = ({ children, label, value, onChange, className }) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className={cn('flex items-center gap-2', className)}>
          <span>{label}</span>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-48">
        {React.Children.map(children, (child) => {
          if (React.isValidElement(child)) {
            return React.cloneElement(child as React.ReactElement<any>, {
              active: child.props.value === value,
              onClick: () => onChange(child.props.value),
            });
          }
          return child;
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

/**
 * FilterOption - A standardized option for FilterDropdown
 * 
 * Usage:
 * <FilterOption value="active">Active</FilterOption>
 */
export const FilterOption: React.FC<{
  children: React.ReactNode;
  value: string;
  active?: boolean;
  onClick?: () => void;
}> = ({ children, value, active, onClick }) => {
  return (
    <div
      className={cn(
        'flex items-center px-3 py-2 text-sm cursor-pointer hover:bg-muted',
        active && 'bg-muted font-medium'
      )}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

/**
 * FilterPanel - A collapsible panel for more complex filters
 * 
 * Usage:
 * <FilterPanel>
 *   <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
 *     <div>
 *       <Label>Property</Label>
 *       <Select value={filters.property} onValueChange={(v) => setFilters({...filters, property: v})}>
 *         <SelectTrigger>
 *           <SelectValue placeholder="All properties" />
 *         </SelectTrigger>
 *         <SelectContent>
 *           <SelectItem value="all">All properties</SelectItem>
 *           {properties.map(p => (
 *             <SelectItem key={p.id} value={p.id}>{p.name}</SelectItem>
 *           ))}
 *         </SelectContent>
 *       </Select>
 *     </div>
 *   </div>
 * </FilterPanel>
 */
export const FilterPanel: React.FC<{
  children: React.ReactNode;
  title?: string;
  defaultOpen?: boolean;
  className?: string;
}> = ({ children, title = 'Filters', defaultOpen = false, className }) => {
  const [open, setOpen] = useState(defaultOpen);
  
  return (
    <Collapsible
      open={open}
      onOpenChange={setOpen}
      className={cn('w-full border rounded-lg overflow-hidden mb-4', className)}
    >
      <div className="flex items-center justify-between px-4 py-3 bg-muted/30 border-b">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <h3 className="text-sm font-medium">{title}</h3>
        </div>
        <CollapsibleTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <ChevronDown className={cn('h-4 w-4 transition-transform', open && 'rotate-180')} />
          </Button>
        </CollapsibleTrigger>
      </div>
      <CollapsibleContent>
        <div className="p-4">
          {children}
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
};

/**
 * ActiveFilters - A component to display active filters with the ability to remove them
 * 
 * Usage:
 * <ActiveFilters
 *   filters={[
 *     { id: 'status', label: 'Status', value: 'Active' },
 *     { id: 'property', label: 'Property', value: 'Beach House' }
 *   ]}
 *   onRemove={(id) => handleRemoveFilter(id)}
 *   onClear={() => handleClearFilters()}
 * />
 */
export const ActiveFilters: React.FC<{
  filters: Array<{ id: string; label: string; value: string }>;
  onRemove: (id: string) => void;
  onClear: () => void;
  className?: string;
}> = ({ filters, onRemove, onClear, className }) => {
  if (filters.length === 0) return null;
  
  return (
    <div className={cn('flex flex-wrap items-center gap-2 mb-4', className)}>
      <span className="text-sm text-muted-foreground">Active filters:</span>
      {filters.map((filter) => (
        <Badge key={filter.id} variant="outline" className="flex items-center gap-1">
          <span className="text-xs font-normal">{filter.label}:</span>
          <span className="text-xs">{filter.value}</span>
          <button
            onClick={() => onRemove(filter.id)}
            className="ml-1 text-muted-foreground hover:text-foreground"
            aria-label={`Remove ${filter.label} filter`}
          >
            <X className="h-3 w-3" />
          </button>
        </Badge>
      ))}
      <Button
        variant="ghost"
        size="sm"
        className="h-7 text-xs"
        onClick={onClear}
      >
        Clear all
      </Button>
    </div>
  );
};
