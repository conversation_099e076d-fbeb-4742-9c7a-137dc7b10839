
import React, { useState, useEffect, useRef } from 'react';
import { Input } from './input';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from './command';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import { Check, ChevronsUpDown, MapPin } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AddressResult {
  place_id: number;
  display_name: string;
  lat: string;
  lon: string;
}

interface AddressAutocompleteProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  label?: string;
}

export function AddressAutocomplete({
  value,
  onChange,
  placeholder = "Search for an address...",
  className,
  label
}: AddressAutocompleteProps) {
  const [open, setOpen] = useState(false);
  const [results, setResults] = useState<AddressResult[]>([]);
  const [inputValue, setInputValue] = useState(value);
  const [loading, setLoading] = useState(false);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Update internal state when external value changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Search for addresses when input changes
  const searchAddress = async (query: string) => {
    if (!query || query.length < 3) {
      setResults([]);
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=5&addressdetails=1`,
        {
          headers: {
            'Accept-Language': 'en',
            'User-Agent': 'PropertyManagementApp'
          }
        }
      );
      
      if (response.ok) {
        const data = await response.json();
        setResults(data as AddressResult[]);
      } else {
        console.error('Error fetching address data');
        setResults([]);
      }
    } catch (error) {
      console.error('Error searching for address:', error);
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle input change with debounce
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    
    // Debounce API calls
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    
    debounceTimerRef.current = setTimeout(() => {
      searchAddress(newValue);
    }, 500);
  };

  // Select an address from results
  const handleSelect = (address: AddressResult) => {
    onChange(address.display_name);
    setInputValue(address.display_name);
    setOpen(false);
  };

  return (
    <div className="relative w-full">
      {label && <div className="mb-2 text-sm font-medium">{label}</div>}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div className="relative w-full">
            <Input
              value={inputValue}
              onChange={handleInputChange}
              placeholder={placeholder}
              className={cn("w-full", className)}
              onFocus={() => {
                if (inputValue && inputValue.length >= 3) {
                  searchAddress(inputValue);
                }
                setOpen(true);
              }}
            />
            <ChevronsUpDown className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          </div>
        </PopoverTrigger>
        <PopoverContent className="w-[94vw] p-0 md:w-[500px]" align="start">
          <Command>
            <CommandList>
              {loading ? (
                <CommandEmpty>Loading address suggestions...</CommandEmpty>
              ) : results.length === 0 ? (
                <CommandEmpty>No address found. Type at least 3 characters.</CommandEmpty>
              ) : (
                <CommandGroup>
                  {results.map((address) => (
                    <CommandItem
                      key={address.place_id}
                      value={address.display_name}
                      onSelect={() => handleSelect(address)}
                      className="flex items-start py-2"
                    >
                      <div className="flex items-start">
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4 mt-1 flex-shrink-0",
                            inputValue === address.display_name
                              ? "opacity-100"
                              : "opacity-0"
                          )}
                        />
                        <div className="flex items-start">
                          <MapPin className="mr-2 h-4 w-4 mt-1 flex-shrink-0 text-muted-foreground" />
                          <span className="text-sm">{address.display_name}</span>
                        </div>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
