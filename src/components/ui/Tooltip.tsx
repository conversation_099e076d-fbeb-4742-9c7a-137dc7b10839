
import React, { useState } from 'react';
import { Info } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TooltipProps {
  text: string;
  children?: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  icon?: React.ReactNode;
  className?: string;
  iconClassName?: string;
}

const Tooltip: React.FC<TooltipProps> = ({
  text,
  children,
  position = 'top',
  icon = <Info size={16} />,
  className = '',
  iconClassName = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);

  const positionClasses = {
    top: 'bottom-full mb-2',
    bottom: 'top-full mt-2',
    left: 'right-full mr-2',
    right: 'left-full ml-2'
  };

  return (
    <div className="relative inline-block">
      <div 
        className={cn("inline-flex items-center cursor-help", iconClassName)}
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
        onFocus={() => setIsVisible(true)}
        onBlur={() => setIsVisible(false)}
      >
        {children || icon}
      </div>
      
      {isVisible && (
        <div 
          className={cn(
            "absolute z-50 px-2 py-1 text-xs font-medium text-white bg-black/80 backdrop-blur-sm rounded whitespace-nowrap pointer-events-none transition-opacity duration-200",
            positionClasses[position],
            className
          )}
        >
          {text}
          <div 
            className={cn(
              "absolute w-2 h-2 bg-black/80 rotate-45",
              position === 'top' && "top-full -translate-y-1 left-1/2 -translate-x-1/2",
              position === 'bottom' && "bottom-full translate-y-1 left-1/2 -translate-x-1/2",
              position === 'left' && "left-full -translate-x-1 top-1/2 -translate-y-1/2",
              position === 'right' && "right-full translate-x-1 top-1/2 -translate-y-1/2"
            )}
          />
        </div>
      )}
    </div>
  );
};

export default Tooltip;
