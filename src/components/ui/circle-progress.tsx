
import React from 'react';

interface CircleProgressProps {
  value: number;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const CircleProgress: React.FC<CircleProgressProps> = ({ 
  value, 
  size = 'md', 
  className = '' 
}) => {
  // Calculate sizes based on the size prop
  const dimensions = {
    sm: { size: 16, stroke: 2 },
    md: { size: 24, stroke: 3 },
    lg: { size: 32, stroke: 4 }
  };
  
  const { size: circleSize, stroke } = dimensions[size];
  const radius = (circleSize - stroke) / 2;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (value / 100) * circumference;
  
  return (
    <div className={`inline-flex ${className}`}>
      <svg 
        width={circleSize} 
        height={circleSize} 
        viewBox={`0 0 ${circleSize} ${circleSize}`}
        className="transform -rotate-90"
      >
        <circle
          cx={circleSize / 2}
          cy={circleSize / 2}
          r={radius}
          fill="none"
          stroke="currentColor"
          strokeOpacity={0.2}
          strokeWidth={stroke}
        />
        <circle
          cx={circleSize / 2}
          cy={circleSize / 2}
          r={radius}
          fill="none"
          stroke="currentColor"
          strokeWidth={stroke}
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
        />
      </svg>
    </div>
  );
};
