
import React, { ReactNode } from 'react';
import { Package, Search, AlertCircle } from 'lucide-react';

interface EmptyStateProps {
  icon?: 'package' | 'search' | 'alert' | ReactNode;
  title: string;
  description?: string;
  action?: ReactNode;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  icon = 'package',
  title,
  description,
  action
}) => {
  const renderIcon = () => {
    if (React.isValidElement(icon)) {
      return icon;
    }

    switch (icon) {
      case 'package':
        return <Package className="h-12 w-12 text-muted-foreground" />;
      case 'search':
        return <Search className="h-12 w-12 text-muted-foreground" />;
      case 'alert':
        return <AlertCircle className="h-12 w-12 text-muted-foreground" />;
      default:
        return <Package className="h-12 w-12 text-muted-foreground" />;
    }
  };

  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <div className="mb-4">
        {renderIcon()}
      </div>
      <h3 className="text-lg font-medium mb-2">{title}</h3>
      {description && (
        <p className="text-sm text-muted-foreground mb-4 max-w-md">{description}</p>
      )}
      {action && (
        <div className="mt-2">
          {action}
        </div>
      )}
    </div>
  );
};

export default EmptyState;
