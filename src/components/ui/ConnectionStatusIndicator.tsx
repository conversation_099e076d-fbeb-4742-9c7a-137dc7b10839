import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, WifiOff, Wifi, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ConnectionStatusIndicatorProps {
  className?: string;
}

/**
 * A component that shows the current connection status and listens for network events
 */
const ConnectionStatusIndicator: React.FC<ConnectionStatusIndicatorProps> = ({ className }) => {
  const [isOnline, setIsOnline] = useState<boolean>(navigator.onLine);
  const [isReconnecting, setIsReconnecting] = useState<boolean>(false);
  const [showIndicator, setShowIndicator] = useState<boolean>(false);
  const [retryCount, setRetryCount] = useState<number>(0);

  useEffect(() => {
    // Function to handle online status change
    const handleOnlineStatusChange = () => {
      const wasOffline = !isOnline;
      setIsOnline(navigator.onLine);
      
      if (navigator.onLine && wasOffline) {
        // We're back online after being offline
        toast.success('Connection restored', {
          description: 'Your internet connection has been restored.',
          duration: 3000,
        });
        setShowIndicator(true);
        setTimeout(() => setShowIndicator(false), 5000);
      } else if (!navigator.onLine) {
        // We're offline
        toast.error('Connection lost', {
          description: 'Your internet connection has been lost. Some features may be unavailable.',
          duration: 5000,
        });
        setShowIndicator(true);
      }
    };

    // Function to handle Supabase network status events
    const handleSupabaseNetworkStatus = (event: CustomEvent) => {
      const { online, timestamp } = event.detail;
      console.log(`[ConnectionStatusIndicator] Supabase network status: ${online ? 'online' : 'offline'} at ${timestamp}`);
      
      if (online !== isOnline) {
        setIsOnline(online);
        
        if (online) {
          // We're back online after being offline
          toast.success('Connection to StayFu restored', {
            description: 'Your connection to StayFu has been restored.',
            duration: 3000,
          });
          setShowIndicator(true);
          setTimeout(() => setShowIndicator(false), 5000);
        } else {
          // We're offline
          toast.error('Connection to StayFu lost', {
            description: 'Your connection to StayFu has been lost. Some features may be unavailable.',
            duration: 5000,
          });
          setShowIndicator(true);
        }
      }
    };

    // Function to handle max retries events
    const handleMaxRetries = (event: CustomEvent) => {
      const { url, method, error, time } = event.detail;
      console.log(`[ConnectionStatusIndicator] Max retries reached for ${method} ${url} at ${time}: ${error}`);
      
      setRetryCount(prev => prev + 1);
      setIsReconnecting(true);
      
      // Only show toast for the first few retry failures to avoid spamming
      if (retryCount < 3) {
        toast.error('Connection issue', {
          description: 'Having trouble connecting to StayFu. Will keep trying...',
          duration: 5000,
        });
      }
      
      setShowIndicator(true);
    };

    // Add event listeners
    window.addEventListener('online', handleOnlineStatusChange);
    window.addEventListener('offline', handleOnlineStatusChange);
    window.addEventListener('stayfu-network-status', handleSupabaseNetworkStatus as EventListener);
    window.addEventListener('stayfu-max-retries', handleMaxRetries as EventListener);

    // Initial check
    setIsOnline(navigator.onLine);
    
    // Show indicator if offline
    if (!navigator.onLine) {
      setShowIndicator(true);
    }

    // Cleanup
    return () => {
      window.removeEventListener('online', handleOnlineStatusChange);
      window.removeEventListener('offline', handleOnlineStatusChange);
      window.removeEventListener('stayfu-network-status', handleSupabaseNetworkStatus as EventListener);
      window.removeEventListener('stayfu-max-retries', handleMaxRetries as EventListener);
    };
  }, [isOnline, retryCount]);

  // Don't render anything if we're online and not showing the indicator
  if (isOnline && !showIndicator && !isReconnecting) {
    return null;
  }

  return (
    <div 
      className={cn(
        "fixed bottom-4 right-4 z-50 flex items-center gap-2 rounded-full px-3 py-2 text-sm font-medium shadow-lg",
        isOnline ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800",
        isReconnecting && "bg-yellow-100 text-yellow-800",
        className
      )}
    >
      {isOnline ? (
        isReconnecting ? (
          <>
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span>Reconnecting...</span>
          </>
        ) : (
          <>
            <Wifi className="h-4 w-4" />
            <span>Connected</span>
          </>
        )
      ) : (
        <>
          <WifiOff className="h-4 w-4" />
          <span>Offline</span>
        </>
      )}
    </div>
  );
};

export default ConnectionStatusIndicator;
