import { render, screen, fireEvent } from '@/tests/utils/test-utils';
import AdminCard from './AdminCard';
import { Loader2 } from 'lucide-react';

describe('AdminCard', () => {
  it('renders correctly with title and description', () => {
    render(
      <AdminCard title="Test Title" description="Test Description">
        <div>Test Content</div>
      </AdminCard>
    );
    
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Description')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });
  
  it('renders without description', () => {
    render(
      <AdminCard title="Test Title">
        <div>Test Content</div>
      </AdminCard>
    );
    
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.queryByText('Test Description')).not.toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });
  
  it('renders with footer content', () => {
    render(
      <AdminCard 
        title="Test Title" 
        footer={<button>Footer Button</button>}
      >
        <div>Test Content</div>
      </AdminCard>
    );
    
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
    expect(screen.getByText('Footer Button')).toBeInTheDocument();
  });
  
  it('shows refresh button when onRefresh is provided', () => {
    const mockRefresh = jest.fn();
    
    render(
      <AdminCard 
        title="Test Title" 
        onRefresh={mockRefresh}
        refreshText="Refresh Data"
      >
        <div>Test Content</div>
      </AdminCard>
    );
    
    const refreshButton = screen.getByText('Refresh Data');
    expect(refreshButton).toBeInTheDocument();
    
    fireEvent.click(refreshButton);
    expect(mockRefresh).toHaveBeenCalledTimes(1);
  });
  
  it('disables refresh button when loading', () => {
    const mockRefresh = jest.fn();
    
    render(
      <AdminCard 
        title="Test Title" 
        onRefresh={mockRefresh}
        isLoading={true}
      >
        <div>Test Content</div>
      </AdminCard>
    );
    
    const refreshButton = screen.getByText('Loading...');
    expect(refreshButton).toBeInTheDocument();
    expect(refreshButton).toBeDisabled();
    
    fireEvent.click(refreshButton);
    expect(mockRefresh).not.toHaveBeenCalled();
  });
  
  it('applies test ID when provided', () => {
    render(
      <AdminCard 
        title="Test Title" 
        testId="custom-test-id"
      >
        <div>Test Content</div>
      </AdminCard>
    );
    
    expect(screen.getByTestId('custom-test-id')).toBeInTheDocument();
  });
});
