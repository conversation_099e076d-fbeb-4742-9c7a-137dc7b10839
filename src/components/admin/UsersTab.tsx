
import React from 'react';
import UserList from './UserList';
import { UserProfile } from '@/types/auth';
import AdminCard from './AdminCard';

interface UsersTabProps {
  users: UserProfile[];
  isLoading: boolean;
  isRefreshing: boolean;
  currentUserId?: string;
  onRefresh: () => Promise<void>;
  onImpersonate: (userId: string) => Promise<void>;
  onUserUpdated?: () => Promise<void>;
}

const UsersTab: React.FC<UsersTabProps> = ({
  users,
  isLoading,
  isRefreshing,
  currentUserId,
  onRefresh,
  onImpersonate,
  onUserUpdated
}) => {
  return (
    <AdminCard
      title="User Management"
      description="View and manage all users in the system."
      isLoading={isRefreshing}
      onRefresh={onRefresh}
      refreshText="Refresh Users"
      testId="users-tab"
    >
      <UserList
        users={users}
        isLoading={isLoading}
        isRefreshing={isRefreshing}
        currentUserId={currentUserId}
        onRefresh={onRefresh}
        onImpersonate={onImpersonate}
        onUserUpdated={onUserUpdated || onRefresh}
      />
    </AdminCard>
  );
};

export default UsersTab;
