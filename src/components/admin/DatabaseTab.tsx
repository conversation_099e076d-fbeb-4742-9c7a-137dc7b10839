import React, { useState } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { DownloadCloud, UploadCloud, AlertTriangle, Info, Loader2, Eye, FileCode, FileJson } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface TableStatus {
  exists: boolean;
  dataRows: number;
  columnsMatch: boolean;
  error: string | null;
  restored?: boolean;
}

interface RestorePreviewResult {
  tables: Record<string, TableStatus>;
  success: boolean;
  previewMode: boolean;
  tablesFound: number;
  hasData: boolean;
  message: string;
}

const DatabaseTab: React.FC = () => {
  const { authState } = useAuth();
  const [isBackupLoading, setIsBackupLoading] = useState(false);
  const [isRestoreLoading, setIsRestoreLoading] = useState(false);
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);
  const [backupFile, setBackupFile] = useState<File | null>(null);
  const [backupName, setBackupName] = useState(`backup-${new Date().toISOString().split('T')[0]}`);
  const [previewResult, setPreviewResult] = useState<RestorePreviewResult | null>(null);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [backupFormat, setBackupFormat] = useState<'json' | 'sql'>('json');
  const [showRestoreConfirmDialog, setShowRestoreConfirmDialog] = useState(false);

  const handleBackup = async () => {
    if (!authState?.profile?.is_super_admin) {
      toast.error('Only super admins can perform database backups');
      return;
    }

    setIsBackupLoading(true);
    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) throw sessionError;
      
      const token = sessionData.session?.access_token;
      if (!token) throw new Error('Authentication required');
      
      const { data, error } = await supabase.functions.invoke('admin-backup-database', {
        headers: {
          Authorization: `Bearer ${token}`
        },
        body: { 
          backupName,
          format: backupFormat
        }
      });
      
      if (error) throw error;
      
      if (backupFormat === 'json') {
        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `${backupName}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } else {
      if (!data?.sql) { // Added check for data existence
        throw new Error('SQL data is missing from the response');
      }
      
      // console.log("SQL data received:", data.sql.substring(0, 100) + "..."); // Optional: Keep or remove debug log
      
      const blob = new Blob([data.sql], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `${backupName}.sql`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
      
      toast.success(`Database backup created successfully as ${backupFormat.toUpperCase()}`);
    } catch (error: any) {
      console.error('Error creating database backup:', error);
      toast.error(`Failed to create backup: ${error.message || 'Unknown error'}`);
    } finally {
      setIsBackupLoading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setBackupFile(e.target.files[0]);
      setPreviewResult(null);
    }
  };

  const handlePreviewRestore = async () => {
    if (!backupFile) {
      toast.error('Please select a backup file to preview');
      return;
    }

    setIsPreviewLoading(true);
    try {
      const fileContents = await readFileAsJson(backupFile);
      if (!fileContents || !fileContents.tables) {
        throw new Error('Invalid backup file format');
      }

      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) throw sessionError;
      
      const token = sessionData.session?.access_token;
      if (!token) throw new Error('Authentication required');
      
      const { data, error } = await supabase.functions.invoke('admin-restore-database', {
        headers: {
          Authorization: `Bearer ${token}`
        },
        body: { 
          previewMode: true,
          fileContents
        }
      });
      
      if (error) throw error;
      
      setPreviewResult(data as RestorePreviewResult);
      setShowPreviewDialog(true);
      
    } catch (error: any) {
      console.error('Error previewing restore:', error);
      toast.error(`Failed to preview restore: ${error.message || 'Unknown error'}`);
    } finally {
      setIsPreviewLoading(false);
    }
  };

  const proceedWithRestore = async () => {
    if (!authState?.profile?.is_super_admin || !backupFile) return;

    setIsRestoreLoading(true);
    setShowRestoreConfirmDialog(false); // Close confirm dialog
    try {
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) throw sessionError;
      
      const token = sessionData.session?.access_token;
      if (!token) throw new Error('Authentication required');
      
      const { data: uploadUrlData, error: uploadUrlError } = await supabase.functions.invoke('admin-get-upload-url', {
        headers: {
          Authorization: `Bearer ${token}`
        },
        body: { fileName: backupFile.name }
      });
      
      if (uploadUrlError) throw uploadUrlError;
      
      const uploadResponse = await fetch(uploadUrlData.uploadUrl, {
        method: 'PUT',
        body: backupFile,
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      if (!uploadResponse.ok) {
        throw new Error(`Failed to upload backup file: ${uploadResponse.statusText}`);
      }
      
      const { data, error } = await supabase.functions.invoke('admin-restore-database', {
        headers: {
          Authorization: `Bearer ${token}`
        },
        body: { 
          fileKey: uploadUrlData.fileKey,
          previewMode: false
        }
      });
      
      if (error) throw error;
      
      toast.success('Database restore completed successfully. You may need to refresh the page to see changes.');
    } catch (error: any) {
      console.error('Error restoring database:', error);
      toast.error(`Failed to restore database: ${error.message || 'Unknown error'}`);
    } finally {
      setIsRestoreLoading(false);
      setBackupFile(null);
    }
  };

  const readFileAsJson = (file: File): Promise<any> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const json = JSON.parse(event.target?.result as string);
          resolve(json);
        } catch (e) {
          reject(new Error('Invalid JSON file'));
        }
      };
      reader.onerror = () => reject(new Error('Error reading file'));
      reader.readAsText(file);
    });
  };

  return (
    <Tabs defaultValue="backup">
      <TabsList className="mb-4">
        <TabsTrigger value="backup">Backup</TabsTrigger>
        <TabsTrigger value="restore">Restore</TabsTrigger>
      </TabsList>
      
      <TabsContent value="backup">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DownloadCloud className="h-5 w-5" />
              Database Backup
            </CardTitle>
            <CardDescription>
              Create a backup of your database schema and data
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center p-4 bg-amber-50 text-amber-800 rounded-md">
              <Info className="h-5 w-5 mr-2 flex-shrink-0" />
              <p className="text-sm">
                Database backups include all tables, data, and schema information. 
                Store these backups securely as they contain sensitive information.
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="backup-name">Backup Name</Label>
              <Input 
                id="backup-name" 
                value={backupName} 
                onChange={(e) => setBackupName(e.target.value)}
                placeholder="Enter a name for this backup"
              />
            </div>

            <div className="space-y-2">
              <Label>Backup Format</Label>
              <RadioGroup 
                value={backupFormat} 
                onValueChange={(value) => setBackupFormat(value as 'json' | 'sql')} 
                className="flex space-x-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="json" id="json-format" />
                  <Label htmlFor="json-format" className="flex items-center">
                    <FileJson className="h-4 w-4 mr-2" />
                    JSON (Full Data)
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="sql" id="sql-format" />
                  <Label htmlFor="sql-format" className="flex items-center">
                    <FileCode className="h-4 w-4 mr-2" />
                    SQL (Direct Import)
                  </Label>
                </div>
              </RadioGroup>
              <p className="text-sm text-muted-foreground">
                {backupFormat === 'json' 
                  ? 'JSON format contains all data and is used by this application\'s restore function.' 
                  : 'SQL format can be directly imported into any PostgreSQL database, including Supabase.'}
              </p>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              onClick={handleBackup}
              disabled={isBackupLoading || !authState?.profile?.is_super_admin}
              className="ml-auto"
            >
              {isBackupLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Backup...
                </>
              ) : (
                <>
                  <DownloadCloud className="mr-2 h-4 w-4" />
                  Create Backup
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>
      
      <TabsContent value="restore">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UploadCloud className="h-5 w-5" />
              Database Restore
            </CardTitle>
            <CardDescription>
              Restore your database from a previous backup
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center p-4 bg-destructive/10 text-destructive rounded-md">
              <AlertTriangle className="h-5 w-5 mr-2 flex-shrink-0" />
              <p className="text-sm">
                <strong>Warning:</strong> Restoring a database will overwrite your current data. 
                It's recommended to first use the Preview feature to validate your backup file before proceeding.
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="restore-file">Backup File</Label>
              <Input 
                id="restore-file" 
                type="file" 
                accept=".json"
                onChange={handleFileChange}
              />
              <p className="text-xs text-muted-foreground">
                Only JSON backup files created by this tool are supported for restore via this interface.
              </p>
              {backupFile && (
                <p className="text-sm text-muted-foreground mt-1">
                  Selected file: {backupFile.name} ({(backupFile.size / 1024).toFixed(2)} KB)
                </p>
              )}
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              onClick={handlePreviewRestore}
              disabled={isPreviewLoading || !backupFile || !authState?.profile?.is_super_admin}
              variant="outline"
            >
              {isPreviewLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Analyzing Backup...
                </>
              ) : (
                <>
                  <Eye className="mr-2 h-4 w-4" />
                  Preview Restore
                </>
              )}
            </Button>

            <AlertDialog open={showRestoreConfirmDialog} onOpenChange={setShowRestoreConfirmDialog}>
              <AlertDialogTrigger asChild>
                <Button
                  disabled={isRestoreLoading || !backupFile || !authState?.profile?.is_super_admin}
                  variant="destructive"
                >
                  {isRestoreLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Restoring...
                    </>
                  ) : (
                    <>
                      <UploadCloud className="mr-2 h-4 w-4" />
                      Restore Database
                    </>
                  )}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently overwrite your
                    current database with the data from the backup file{' '}
                    <strong>{backupFile?.name}</strong>.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isRestoreLoading}>Cancel</AlertDialogCancel>
                  <AlertDialogAction 
                    onClick={proceedWithRestore} 
                    disabled={isRestoreLoading}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    {isRestoreLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Restoring...
                      </>
                    ) : (
                      'Yes, restore database'
                    )}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </CardFooter>
        </Card>
        
        <Dialog open={showPreviewDialog} onOpenChange={setShowPreviewDialog}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Backup Preview Results</DialogTitle>
              <DialogDescription>
                This is a preview of what would happen if you restore this backup file.
                No changes have been made to your database.
              </DialogDescription>
            </DialogHeader>
            
            {previewResult && (
              <div className="space-y-4">
                <div className="flex gap-4 flex-wrap">
                  <div className="bg-muted p-3 rounded-md">
                    <p className="text-sm font-medium">Tables Found</p>
                    <p className="text-2xl font-bold">{previewResult.tablesFound}</p>
                  </div>
                  
                  <div className="bg-muted p-3 rounded-md">
                    <p className="text-sm font-medium">Status</p>
                    <Badge variant={previewResult.success ? "default" : "destructive"}>
                      {previewResult.success ? "Valid" : "Issues Found"}
                    </Badge>
                  </div>
                  
                  <div className="bg-muted p-3 rounded-md">
                    <p className="text-sm font-medium">Contains Data</p>
                    <Badge variant={previewResult.hasData ? "default" : "secondary"}>
                      {previewResult.hasData ? "Yes" : "No"}
                    </Badge>
                  </div>
                </div>
                
                <ScrollArea className="h-[350px] rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Table Name</TableHead>
                        <TableHead>Exists</TableHead>
                        <TableHead>Columns Match</TableHead>
                        <TableHead>Rows</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {Object.entries(previewResult.tables).map(([tableName, status]) => (
                        <TableRow key={tableName}>
                          <TableCell className="font-medium">{tableName}</TableCell>
                          <TableCell>
                            <Badge variant={status.exists ? "default" : "destructive"}>
                              {status.exists ? "Yes" : "No"}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {status.columnsMatch !== undefined ? (
                              <Badge variant={status.columnsMatch ? "default" : "destructive"}>
                                {status.columnsMatch ? "Compatible" : "Mismatch"}
                              </Badge>
                            ) : (
                              <Badge variant="secondary">Not Checked</Badge>
                            )}
                          </TableCell>
                          <TableCell>{status.dataRows}</TableCell>
                          <TableCell>
                            {status.error ? (
                              <div className="text-destructive max-w-[200px] text-xs">{status.error}</div>
                            ) : (
                              <Badge variant="default">Ready</Badge>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              </div>
            )}
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowPreviewDialog(false)}>Close</Button>
              {previewResult?.success && (
                <Button 
                  variant="default"
                  onClick={() => {
                    setShowPreviewDialog(false);
                    // Trigger the confirmation dialog instead of direct restore
                    setShowRestoreConfirmDialog(true); 
                  }}
                >
                  Proceed to Restore Confirmation
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </TabsContent>
    </Tabs>
  );
};

export default DatabaseTab;
