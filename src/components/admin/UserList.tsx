
import React, { useState } from 'react';
import { UserProfile } from '@/types/auth';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON><PERSON>t, RefreshCw, UserCog, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import UserManagement from './UserManagement';
import AdminTable from './AdminTable';

interface UserListProps {
  users: UserProfile[];
  isLoading: boolean;
  isRefreshing: boolean;
  currentUserId?: string;
  onRefresh: () => Promise<void>;
  onImpersonate: (userId: string) => Promise<void>;
  onUserUpdated: () => Promise<void>;
}

const UserList: React.FC<UserListProps> = ({
  users,
  isLoading,
  isRefreshing,
  currentUserId,
  onRefresh,
  onImpersonate,
  onUserUpdated
}) => {
  const [impersonatingUserId, setImpersonatingUserId] = useState<string | null>(null);

  const handleImpersonateClick = async (userId: string) => {
    setImpersonatingUserId(userId);
    try {
      console.log("Starting impersonation for user:", userId);
      await onImpersonate(userId);
    } catch (error) {
      console.error("Impersonation failed:", error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      toast.error(`Failed to impersonate user: ${errorMessage}`);
    } finally {
      setImpersonatingUserId(null);
    }
  };

  // Define columns for the AdminTable
  const columns = [
    {
      header: 'Name',
      accessor: (user: UserProfile) => (
        <div className="flex items-center gap-2">
          {user.is_super_admin && <ShieldAlert className="h-4 w-4 text-amber-500" />}
          {user.first_name || user.last_name
            ? `${user.first_name || ''} ${user.last_name || ''}`.trim()
            : 'Unnamed User'}
        </div>
      ),
      className: 'font-medium'
    },
    {
      header: 'Email',
      accessor: 'email' as keyof UserProfile
    },
    {
      header: 'Role',
      accessor: (user: UserProfile) => (
        <Badge variant={user.is_super_admin ? "destructive" : "secondary"}>
          {user.is_super_admin ? 'Super Admin' : user.role || 'User'}
        </Badge>
      )
    },
    {
      header: 'Status',
      accessor: () => (
        <div className="flex items-center">
          <span
            className="h-2 w-2 rounded-full bg-green-500 mr-2"
            aria-label="Active status"
          ></span>
          Active
        </div>
      )
    },
    {
      header: 'Impersonate',
      accessor: (user: UserProfile) => (
        user.id !== currentUserId ? (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 px-2 text-muted-foreground hover:text-foreground flex items-center gap-1"
            onClick={() => handleImpersonateClick(user.id)}
            title="Impersonate user"
            disabled={impersonatingUserId === user.id}
          >
            {impersonatingUserId === user.id ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <UserCog className="h-4 w-4" />
            )}
            <span className="sr-only md:not-sr-only md:inline-block">
              {impersonatingUserId === user.id ? 'Loading...' : 'Impersonate'}
            </span>
          </Button>
        ) : (
          <span className="text-xs text-muted-foreground">Current user</span>
        )
      )
    },
    {
      header: 'Manage',
      accessor: (user: UserProfile) => (
        <UserManagement user={user} onUserUpdated={onUserUpdated} />
      ),
      className: 'text-right'
    }
  ];

  if (users.length === 0 && !isLoading) {
    return (
      <div className="text-center p-8 bg-muted/20 rounded-lg">
        <p className="mb-4 text-muted-foreground">No users found</p>
        <Button
          variant="outline"
          onClick={onRefresh}
          disabled={isRefreshing}
          className="flex items-center gap-2"
        >
          {isRefreshing ? (
            <>
              <RefreshCw className="h-4 w-4 animate-spin" />
              Refreshing...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4" />
              Refresh
            </>
          )}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">{users.length} User{users.length !== 1 ? 's' : ''}</h3>
        <Button
          variant="outline"
          onClick={onRefresh}
          disabled={isRefreshing}
          className="flex items-center gap-2"
        >
          {isRefreshing ? (
            <>
              <RefreshCw className="h-4 w-4 animate-spin" />
              Refreshing...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4" />
              Refresh Users
            </>
          )}
        </Button>
      </div>

      <div className="rounded-md border overflow-hidden">
        <AdminTable
          columns={columns}
          data={users}
          isLoading={isLoading}
          emptyMessage="No users found"
          keyExtractor={(user) => user.id}
          testId="user-list-table"
        />
      </div>
    </div>
  );
};

export default UserList;
