import { render, screen } from '@/tests/utils/test-utils';
import AdminTable from './AdminTable';

// Test data
type TestItem = {
  id: string;
  name: string;
  email: string;
  role: string;
};

const testData: TestItem[] = [
  { id: '1', name: '<PERSON>', email: '<EMAIL>', role: 'Admin' },
  { id: '2', name: '<PERSON>', email: '<EMAIL>', role: 'User' },
];

const testColumns = [
  { header: 'Name', accessor: 'name' as keyof TestItem },
  { header: 'Email', accessor: 'email' as keyof TestItem },
  { header: 'Role', accessor: 'role' as keyof TestItem },
  { 
    header: 'Actions', 
    accessor: (item: TestItem) => <button>Edit {item.name}</button>,
    className: 'text-right'
  }
];

describe('AdminTable', () => {
  it('renders table with data correctly', () => {
    render(
      <AdminTable
        columns={testColumns}
        data={testData}
        keyExtractor={(item) => item.id}
        testId="test-admin-table"
      />
    );
    
    // Check headers
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('Role')).toBeInTheDocument();
    expect(screen.getByText('Actions')).toBeInTheDocument();
    
    // Check data
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Admin')).toBeInTheDocument();
    expect(screen.getByText('User')).toBeInTheDocument();
    
    // Check rendered components
    expect(screen.getByText('Edit John Doe')).toBeInTheDocument();
    expect(screen.getByText('Edit Jane Smith')).toBeInTheDocument();
    
    // Check test ID
    expect(screen.getByTestId('test-admin-table')).toBeInTheDocument();
  });
  
  it('shows loading state', () => {
    render(
      <AdminTable
        columns={testColumns}
        data={testData}
        isLoading={true}
        keyExtractor={(item) => item.id}
      />
    );
    
    // Loading spinner should be visible
    expect(document.querySelector('.animate-spin')).toBeInTheDocument();
  });
  
  it('shows empty message when no data', () => {
    render(
      <AdminTable
        columns={testColumns}
        data={[]}
        emptyMessage="No items found"
        keyExtractor={(item) => item.id}
      />
    );
    
    expect(screen.getByText('No items found')).toBeInTheDocument();
  });
  
  it('uses custom empty message', () => {
    render(
      <AdminTable
        columns={testColumns}
        data={[]}
        emptyMessage="Custom empty message"
        keyExtractor={(item) => item.id}
      />
    );
    
    expect(screen.getByText('Custom empty message')).toBeInTheDocument();
  });
});
