
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Shield<PERSON><PERSON>t, RefreshCw } from 'lucide-react';

interface AdminErrorStateProps {
  error: string;
  isRefreshing: boolean;
  onRefresh: () => Promise<void>;
}

const AdminErrorState: React.FC<AdminErrorStateProps> = ({
  error,
  isRefreshing,
  onRefresh
}) => {
  const navigate = useNavigate();

  return (
    <div className="p-8">
      <div className="mb-8 flex items-center gap-3">
        <ShieldAlert className="h-6 w-6 text-red-500" />
        <h1 className="text-3xl font-bold">Admin Dashboard</h1>
      </div>
      
      <Card className="border-red-200 bg-red-50 dark:bg-red-950/20">
        <CardHeader>
          <CardTitle className="text-red-800 dark:text-red-400">Error Loading Admin Data</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-700 dark:text-red-300 mb-4">{error}</p>
          <p className="text-sm text-red-600 dark:text-red-300 mb-4">
            This could be due to a permissions issue or a database configuration problem.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button 
              variant="outline" 
              onClick={onRefresh}
              disabled={isRefreshing}
              className="flex items-center gap-2"
            >
              {isRefreshing ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  Refreshing...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4" />
                  Refresh Data
                </>
              )}
            </Button>
            <Button variant="outline" onClick={() => navigate('/dashboard')}>
              Return to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminErrorState;
