import React, { ReactNode } from 'react';
import { <PERSON>, <PERSON>H<PERSON>er, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface AdminCardProps {
  title: string;
  description?: string;
  children: ReactNode;
  footer?: ReactNode;
  isLoading?: boolean;
  onRefresh?: () => Promise<void>;
  refreshText?: string;
  testId?: string;
}

/**
 * A standardized card component for admin sections
 */
const AdminCard: React.FC<AdminCardProps> = ({
  title,
  description,
  children,
  footer,
  isLoading = false,
  onRefresh,
  refreshText = 'Refresh',
  testId = 'admin-card'
}) => {
  return (
    <Card className="w-full" data-testid={testId}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </div>
        {onRefresh && (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onRefresh}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              refreshText
            )}
          </Button>
        )}
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
      {footer && (
        <CardFooter>
          {footer}
        </CardFooter>
      )}
    </Card>
  );
};

export default AdminCard;
