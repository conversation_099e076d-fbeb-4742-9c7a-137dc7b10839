
import React from 'react';
import <PERSON> from '@/components/ui/Wizard';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useDisclosure } from '@/hooks/useDisclosure';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { LayoutDashboard, Sparkles, ShoppingCart, Wrench, Building2, X } from 'lucide-react';

const DashboardWizard = () => {
  const { onboardingState, markTutorialSeen } = useOnboarding();
  const { isOpen, onOpen, onClose } = useDisclosure();

  React.useEffect(() => {
    // Only show if user hasn't seen it before
    if (!onboardingState.hasSeenDashboardTutorial) {
      const timer = setTimeout(() => {
        onOpen();
      }, 1000); // Delay to ensure dashboard has loaded
      return () => clearTimeout(timer);
    }
  }, [onboardingState.hasSeenDashboardTutorial, onOpen]);

  const handleComplete = () => {
    markTutorialSeen('hasSeenDashboardTutorial');
    onClose();
    toast.success("Welcome! You're ready to use the dashboard");
  };

  const handleSkip = () => {
    markTutorialSeen('hasSeenDashboardTutorial');
    onClose();
    toast.info("You can access the tutorial later in settings");
  };

  if (!isOpen) return null;

  const steps = [
    {
      title: "Welcome to Your Dashboard",
      content: (
        <div className="space-y-3">
          <div className="mb-2 flex items-center">
            <LayoutDashboard className="mr-2 h-5 w-5 text-primary" />
            <h3 className="text-base font-medium">Dashboard Overview</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            This is your command center for managing your properties and operations.
            At a glance, you can see your property status, maintenance tasks, inventory levels, and recent damage reports.
          </p>
        </div>
      )
    },
    {
      title: "AI Assistant",
      content: (
        <div className="space-y-3">
          <div className="mb-2 flex items-center">
            <Sparkles className="mr-2 h-5 w-5 text-primary" />
            <h3 className="text-base font-medium">AI-Powered Assistance</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Use the AI Assistant to quickly create new items and manage your properties with natural language.
            Try commands like "Add a new property" or "Create a maintenance task for the broken sink" without
            navigating through menus.
          </p>
          <div className="text-xs text-muted-foreground mt-2 bg-muted/40 p-2 rounded">
            Example: "Create a purchase order for all low stock items"
          </div>
        </div>
      )
    },
    {
      title: "Property Management",
      content: (
        <div className="space-y-3">
          <div className="mb-2 flex items-center">
            <Building2 className="mr-2 h-5 w-5 text-primary" />
            <h3 className="text-base font-medium">Property Management</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Track your properties, add new ones, and manage their details.
            You can view property details, manage collections, and track upcoming maintenance.
          </p>
          <Button 
            variant="outline" 
            size="sm"
            className="mt-2"
            onClick={() => window.location.href = '/properties'}
          >
            Go to Properties
          </Button>
        </div>
      )
    },
    {
      title: "Maintenance Tasks",
      content: (
        <div className="space-y-3">
          <div className="mb-2 flex items-center">
            <Wrench className="mr-2 h-5 w-5 text-primary" />
            <h3 className="text-base font-medium">Maintenance Management</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Track and manage maintenance tasks for your properties. Create new tasks manually or use our 
            AI to automatically generate tasks from your notes or messages.
          </p>
          <Button 
            variant="outline" 
            size="sm"
            className="mt-2"
            onClick={() => window.location.href = '/maintenance'}
          >
            Go to Maintenance
          </Button>
        </div>
      )
    },
    {
      title: "Inventory Control",
      content: (
        <div className="space-y-3">
          <div className="mb-2 flex items-center">
            <ShoppingCart className="mr-2 h-5 w-5 text-primary" />
            <h3 className="text-base font-medium">Inventory Management</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            Keep track of supplies and items in your properties. The dashboard shows low-stock items
            that need reordering. Create purchase orders when stock is low to maintain optimal inventory levels.
          </p>
          <Button 
            variant="outline" 
            size="sm"
            className="mt-2"
            onClick={() => window.location.href = '/inventory'}
          >
            Go to Inventory
          </Button>
        </div>
      )
    },
  ];

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50">
      <div className="bg-background rounded-xl w-full max-w-md mx-4 shadow-lg relative">
        <button 
          onClick={handleSkip}
          className="absolute top-3 right-3 text-muted-foreground hover:text-foreground p-1 rounded-full"
          aria-label="Skip tutorial"
        >
          <X className="h-5 w-5" />
        </button>
        <Wizard 
          steps={steps} 
          onComplete={handleComplete}
          className="border-0"
        />
        <div className="p-4 pb-6 pt-0 flex justify-center">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleSkip}
            className="text-muted-foreground"
          >
            Skip tutorial
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DashboardWizard;
