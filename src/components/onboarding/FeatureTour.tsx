
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { X, ArrowRight } from 'lucide-react';

export interface TourStep {
  targetSelector: string;
  title: string;
  content: React.ReactNode;
  position?: 'top' | 'right' | 'bottom' | 'left';
}

interface FeatureTourProps {
  steps: TourStep[];
  isActive: boolean;
  onComplete: () => void;
  onSkip: () => void;
}

const FeatureTour: React.FC<FeatureTourProps> = ({
  steps,
  isActive,
  onComplete,
  onSkip
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0, height: 0 });
  
  useEffect(() => {
    if (!isActive) return;

    const updatePosition = () => {
      const currentStepData = steps[currentStep];
      const targetElement = document.querySelector(currentStepData.targetSelector);
      
      if (targetElement) {
        const rect = targetElement.getBoundingClientRect();
        setPosition({
          top: rect.top + window.scrollY,
          left: rect.left + window.scrollX,
          width: rect.width,
          height: rect.height
        });

        // Scroll element into view if needed
        if (
          rect.top < 0 ||
          rect.bottom > window.innerHeight
        ) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }
    };

    updatePosition();
    window.addEventListener('resize', updatePosition);
    window.addEventListener('scroll', updatePosition);

    return () => {
      window.removeEventListener('resize', updatePosition);
      window.removeEventListener('scroll', updatePosition);
    };
  }, [isActive, currentStep, steps]);

  if (!isActive) return null;

  const currentStepData = steps[currentStep];
  const positionOffset = 20; // Space between target and tooltip

  const getTooltipPosition = () => {
    const pos = currentStepData.position || 'bottom';
    
    switch (pos) {
      case 'top':
        return {
          top: position.top - positionOffset,
          left: position.left + (position.width / 2) - 150,
          transform: 'translateY(-100%)'
        };
      case 'right':
        return {
          top: position.top + (position.height / 2) - 75,
          left: position.left + position.width + positionOffset
        };
      case 'left':
        return {
          top: position.top + (position.height / 2) - 75,
          left: position.left - 300 - positionOffset
        };
      case 'bottom':
      default:
        return {
          top: position.top + position.height + positionOffset,
          left: position.left + (position.width / 2) - 150
        };
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  return (
    <div className="fixed inset-0 z-50 pointer-events-none">
      {/* Backdrop with cutout for target element */}
      <div className="absolute inset-0 bg-black/60 pointer-events-auto" />
      
      {/* Target element highlight */}
      <div 
        style={{
          position: 'absolute',
          top: position.top,
          left: position.left,
          width: position.width,
          height: position.height,
          boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.6)',
          borderRadius: '4px',
          zIndex: 1,
          pointerEvents: 'none'
        }}
      >
        <div className="absolute inset-0 border-2 border-primary animate-pulse rounded-lg" />
      </div>
      
      {/* Tooltip */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="absolute z-10 bg-card border shadow-lg rounded-lg p-4 w-[300px] pointer-events-auto"
          style={getTooltipPosition()}
        >
          <div className="flex justify-between items-start mb-2">
            <h4 className="font-medium">{currentStepData.title}</h4>
            <Button variant="ghost" size="icon" className="h-6 w-6" onClick={onSkip}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="text-sm text-muted-foreground mb-4">
            {currentStepData.content}
          </div>
          
          <div className="flex justify-between">
            <div className="text-xs text-muted-foreground">
              Step {currentStep + 1} of {steps.length}
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={onSkip}>Skip</Button>
              <Button size="sm" onClick={handleNext} className="gap-1">
                {currentStep === steps.length - 1 ? 'Finish' : 'Next'}
                <ArrowRight className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default FeatureTour;
