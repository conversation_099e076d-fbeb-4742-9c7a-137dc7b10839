
import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Download, X } from 'lucide-react';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/shadcn-tooltip';

// Custom type for the BeforeInstallPromptEvent
interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed'; platform: string }>;
}

declare global {
  interface WindowEventMap {
    'beforeinstallprompt': BeforeInstallPromptEvent;
  }
}

const InstallPWAPrompt: React.FC = () => {
  const [installPrompt, setInstallPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [showPrompt, setShowPrompt] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [showSheet, setShowSheet] = useState(false);
  const [showFloatingButton, setShowFloatingButton] = useState(false);

  useEffect(() => {
    // Check if already installed
    if (window.matchMedia('(display-mode: standalone)').matches) {
      setIsInstalled(true);
      return;
    }

    // Check if we should show the floating button
    if (typeof window !== 'undefined' && window.sessionStorage &&
        sessionStorage.getItem('pwa-show-floating-button') === 'true') {
      setShowFloatingButton(true);
    }

    // Detect mobile device
    const checkMobile = () => {
      const mobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      );
      setIsMobile(mobile);
    };

    checkMobile();

    // Show the prompt after a delay on mobile devices
    const timer = setTimeout(() => {
      if (isMobile && isInstallable && !isInstalled) {
        setShowSheet(true);
      }
    }, 5000);

    // Save the install prompt event
    const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {
      // Only prevent default if we're going to show our custom prompt
      if (isMobile) {
        e.preventDefault();
      }

      // Save the event so it can be triggered later
      setInstallPrompt(e);
      setIsInstallable(true);

      // Show the install banner after a short delay
      setTimeout(() => {
        if (isMobile) {
          setShowSheet(true);
        } else {
          setShowPrompt(true);
        }
      }, 2000);

      console.log('App can be installed, saved install prompt');
    };

    // Check if app was installed
    const handleAppInstalled = () => {
      setIsInstallable(false);
      setIsInstalled(true);
      setShowPrompt(false);
      setShowSheet(false);
      console.log('App was installed');

      // Show a confirmation toast or message
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem('pwa-installed', 'true');
      }
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
      clearTimeout(timer);
    };
  }, [isInstallable, isInstalled, isMobile]);

  const handleInstallClick = async () => {
    if (!installPrompt) return;

    // Show the install prompt
    await installPrompt.prompt();

    // Wait for the user to respond to the prompt
    const choiceResult = await installPrompt.userChoice;

    if (choiceResult.outcome === 'accepted') {
      console.log('User accepted the installation');
      setIsInstallable(false);
      setShowPrompt(false);
      setShowSheet(false);
      setShowFloatingButton(false);

      // Clear the floating button state from session storage
      if (typeof window !== 'undefined' && window.sessionStorage) {
        sessionStorage.removeItem('pwa-show-floating-button');
      }
    } else {
      console.log('User dismissed the installation');
      // Hide the prompt but keep the floating button
      setShowPrompt(false);
      setShowSheet(false);
      setShowFloatingButton(true);
    }

    // Clear the saved prompt as it can't be used again
    setInstallPrompt(null);
  };

  const dismissPrompt = () => {
    setShowPrompt(false);
    setShowSheet(false);
    setShowFloatingButton(true);

    // Remember the user dismissed the prompt in this session
    if (typeof window !== 'undefined' && window.sessionStorage) {
      sessionStorage.setItem('pwa-prompt-dismissed', 'true');
      // Don't set the full dismissed flag, just track that we should show the floating button
      sessionStorage.setItem('pwa-show-floating-button', 'true');
    }
  };

  // If the app is not installable or already installed, don't show anything
  if (!isInstallable || isInstalled) {
    return null;
  }

  // If the user has fully dismissed the prompt (not just switched to floating button)
  if (typeof window !== 'undefined' && window.sessionStorage &&
      sessionStorage.getItem('pwa-prompt-dismissed') === 'true' &&
      !showFloatingButton) {
    return null;
  }

  // Mobile sheet for iOS devices
  if (isMobile) {
    // If showing floating button on mobile
    if (showFloatingButton) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="icon"
                className="fixed bottom-16 right-4 z-50 rounded-full shadow-lg h-12 w-12 animate-in fade-in-0 duration-300"
                onClick={handleInstallClick}
              >
                <Download className="h-5 w-5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Install StayFu App</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    // Show the sheet for mobile
    return (
      <Sheet open={showSheet} onOpenChange={(open) => {
        setShowSheet(open);
        if (!open) {
          // When sheet is closed, show the floating button
          setShowFloatingButton(true);
        }
      }}>
        <SheetContent side="bottom" className="rounded-t-xl">
          <SheetHeader className="flex-row items-center justify-between mb-4">
            <SheetTitle>Install StayFu App</SheetTitle>
            <Button variant="ghost" size="icon" onClick={dismissPrompt}>
              <X className="h-4 w-4" />
            </Button>
          </SheetHeader>
          <SheetDescription>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="bg-primary/10 p-3 rounded-full">
                  <img src="/icons/logo.png" alt="StayFu Logo" className="w-10 h-10" />
                </div>
                <div>
                  <h3 className="font-semibold text-foreground">StayFu</h3>
                  <p className="text-sm text-muted-foreground">Property Management App</p>
                </div>
              </div>

              <div className="text-sm space-y-2">
                <p>Install this application on your device:</p>
                <ul className="list-disc list-inside space-y-1 pl-2">
                  <li>Fast access to your properties</li>
                  <li>Works offline</li>
                  <li>No app store required</li>
                </ul>
              </div>

              {/* iOS Instructions */}
              {navigator.userAgent.match(/iPhone|iPad|iPod/i) && (
                <div className="rounded-lg bg-slate-50 dark:bg-slate-900 p-3 text-sm">
                  <p className="font-semibold">How to install:</p>
                  <ol className="list-decimal list-inside space-y-1 pl-2">
                    <li>Tap the share button <span className="px-2 py-1 rounded bg-slate-200 dark:bg-slate-800">􀈂</span></li>
                    <li>Scroll down and tap <span className="font-medium">Add to Home Screen</span></li>
                    <li>Tap <span className="font-medium">Add</span> in the top right corner</li>
                  </ol>
                </div>
              )}

              {/* Android Installation Button */}
              {!navigator.userAgent.match(/iPhone|iPad|iPod/i) && (
                <Button
                  className="w-full flex items-center justify-center gap-2"
                  onClick={handleInstallClick}
                >
                  <Download className="h-4 w-4" />
                  Install StayFu App
                </Button>
              )}
            </div>
          </SheetDescription>
        </SheetContent>
      </Sheet>
    );
  }

  // Floating button (shown after dismissing the main prompt)
  if (showFloatingButton) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              size="icon"
              className="fixed bottom-4 right-4 z-50 rounded-full shadow-lg h-12 w-12 animate-in fade-in-0 duration-300"
              onClick={handleInstallClick}
            >
              <Download className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="left">
            <p>Install StayFu App</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Desktop banner
  return showPrompt ? (
    <div className="fixed bottom-4 right-4 z-50 bg-white dark:bg-slate-900 shadow-lg rounded-lg p-4 border animate-in slide-in-from-bottom-5 w-72">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <img src="/icons/logo.png" alt="StayFu Logo" className="w-6 h-6" />
          <h3 className="font-semibold text-sm">Install StayFu</h3>
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6"
          onClick={dismissPrompt}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
      <p className="text-xs text-muted-foreground mb-3">
        Add to your desktop for quick access and offline use
      </p>
      <Button
        size="sm"
        onClick={handleInstallClick}
        className="w-full flex items-center justify-center gap-1"
      >
        <Download size={14} />
        Install App
      </Button>
    </div>
  ) : null;
};

export default InstallPWAPrompt;
