import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const PropertyDebugger: React.FC = () => {
  const [properties, setProperties] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { authState } = useAuth();

  const fetchPropertiesDirectly = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('[PropertyDebugger] Fetching properties directly');
      const { data, error } = await supabase
        .from('properties')
        .select('*');

      if (error) {
        console.error('[PropertyDebugger] Error fetching properties:', error);
        setError(error.message);
        return;
      }

      console.log('[PropertyDebugger] Properties fetched successfully:', data);
      setProperties(data || []);
    } catch (err: any) {
      console.error('[PropertyDebugger] Exception fetching properties:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchPropertiesViaRPC = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!authState.user?.id) {
        setError('User not authenticated');
        return;
      }

      console.log('[PropertyDebugger] Fetching properties via RPC');
      const { data, error } = await supabase.rpc(
        'get_user_role_properties',
        { p_user_id: authState.user.id }
      );

      if (error) {
        console.error('[PropertyDebugger] Error fetching properties via RPC:', error);
        setError(error.message);
        return;
      }

      console.log('[PropertyDebugger] Properties fetched successfully via RPC:', data);
      setProperties(data || []);
    } catch (err: any) {
      console.error('[PropertyDebugger] Exception fetching properties via RPC:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Property Debugger</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex space-x-4 mb-4">
          <Button onClick={fetchPropertiesDirectly} disabled={loading}>
            Fetch Directly
          </Button>
          <Button onClick={fetchPropertiesViaRPC} disabled={loading}>
            Fetch via RPC
          </Button>
        </div>

        {loading && <p>Loading...</p>}
        {error && <p className="text-red-500">Error: {error}</p>}

        <div className="mt-4">
          <h3 className="font-semibold mb-2">Properties ({properties.length})</h3>
          {properties.length > 0 ? (
            <ul className="space-y-2">
              {properties.map((property) => (
                <li key={property.id} className="border p-2 rounded">
                  <strong>{property.name}</strong> - {property.address}, {property.city}, {property.state}
                </li>
              ))}
            </ul>
          ) : (
            <p>No properties found</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PropertyDebugger;
