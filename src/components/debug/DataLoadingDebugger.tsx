import React, { useState, useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useLocation } from 'react-router-dom';
import { debugAllQueries, debugQueryCache } from '@/utils/debugUtils';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useNavigationRefresh } from '@/hooks/useNavigationRefresh';

/**
 * A debug component for diagnosing data loading issues
 * This component shows detailed information about the React Query cache
 * and window focus events
 */
const DataLoadingDebugger: React.FC = () => {
  const queryClient = useQueryClient();
  const location = useLocation();
  const { refreshRouteData } = useNavigationRefresh();
  const [activeTab, setActiveTab] = useState('focus');
  const [focusEvents, setFocusEvents] = useState<string[]>([]);
  const [visibilityEvents, setVisibilityEvents] = useState<string[]>([]);
  const [queryDebugInfo, setQueryDebugInfo] = useState<Record<string, any>>({});
  const [currentRoute, setCurrentRoute] = useState('');
  const [isVisible, setIsVisible] = useState(document.visibilityState === 'visible');
  const [hasFocus, setHasFocus] = useState(document.hasFocus());
  const focusEventsRef = useRef<string[]>([]);
  const visibilityEventsRef = useRef<string[]>([]);

  // Update current route when location changes
  useEffect(() => {
    setCurrentRoute(location.pathname);
  }, [location]);

  // Track focus and visibility events
  useEffect(() => {
    const handleFocus = () => {
      const timestamp = new Date().toISOString();
      const event = `[${timestamp}] Window gained focus`;
      focusEventsRef.current = [event, ...focusEventsRef.current.slice(0, 19)];
      setFocusEvents([...focusEventsRef.current]);
      setHasFocus(true);
    };

    const handleBlur = () => {
      const timestamp = new Date().toISOString();
      const event = `[${timestamp}] Window lost focus`;
      focusEventsRef.current = [event, ...focusEventsRef.current.slice(0, 19)];
      setFocusEvents([...focusEventsRef.current]);
      setHasFocus(false);
    };

    const handleVisibilityChange = () => {
      const timestamp = new Date().toISOString();
      const isVisible = document.visibilityState === 'visible';
      const event = `[${timestamp}] Visibility changed to ${document.visibilityState}`;
      visibilityEventsRef.current = [event, ...visibilityEventsRef.current.slice(0, 19)];
      setVisibilityEvents([...visibilityEventsRef.current]);
      setIsVisible(isVisible);
    };

    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Update query debug info periodically
  useEffect(() => {
    const updateQueryDebugInfo = () => {
      const allQueries = debugAllQueries(queryClient);
      setQueryDebugInfo(allQueries);
    };

    // Update immediately
    updateQueryDebugInfo();

    // Then update every 2 seconds
    const interval = setInterval(updateQueryDebugInfo, 2000);

    return () => {
      clearInterval(interval);
    };
  }, [queryClient]);

  // Force refresh the current route
  const handleForceRefresh = async () => {
    try {
      await refreshRouteData(currentRoute);
    } catch (error) {
      console.error('Error refreshing route data:', error);
    }
  };

  // Get query keys for the current route
  const getQueryKeysForRoute = () => {
    // Extract the base route
    let baseRoute = currentRoute;
    
    // Handle hash router format
    if (baseRoute.includes('#/')) {
      baseRoute = baseRoute.split('#/')[1] || '';
    } else if (baseRoute.startsWith('#')) {
      baseRoute = baseRoute.substring(1);
    }
    
    // Ensure the route starts with a slash
    if (!baseRoute.startsWith('/')) {
      baseRoute = '/' + baseRoute;
    }
    
    // Special case for property detail pages
    if (baseRoute.match(/^\/properties\/[a-zA-Z0-9-]+$/)) {
      return ['propertyDetail', 'propertyDetailV2', 'properties', 'propertiesV2'];
    }
    
    // Special case for maintenance
    if (baseRoute === '/maintenance') {
      return ['maintenanceTasks', 'maintenanceTasksV2', 'properties', 'providers'];
    }
    
    // Special case for damages
    if (baseRoute === '/damages') {
      return ['damageReports', 'damageReportsV2', 'properties', 'providers'];
    }
    
    // Special case for teams
    if (baseRoute === '/teams') {
      return ['teamsV2', 'teamMembersV2', 'teamPropertiesV2', 'teamInvitationsV2', 'teamPermissionsV2'];
    }
    
    // Special case for settings/appearance
    if (baseRoute === '/settings/appearance') {
      return ['appearanceSettingsV2', 'userSettings', 'appearanceSettings'];
    }
    
    return [];
  };

  return (
    <Card className="p-4 max-w-full overflow-x-auto">
      <h2 className="text-lg font-semibold mb-2">Data Loading Debugger</h2>
      <div className="mb-4">
        <div className="flex items-center gap-2 mb-2">
          <span className="font-medium">Current Route:</span>
          <span>{currentRoute}</span>
        </div>
        <div className="flex items-center gap-2 mb-2">
          <span className="font-medium">Visibility State:</span>
          <span className={isVisible ? 'text-green-500' : 'text-red-500'}>
            {isVisible ? 'Visible' : 'Hidden'}
          </span>
        </div>
        <div className="flex items-center gap-2 mb-2">
          <span className="font-medium">Focus State:</span>
          <span className={hasFocus ? 'text-green-500' : 'text-red-500'}>
            {hasFocus ? 'Focused' : 'Blurred'}
          </span>
        </div>
        <Button onClick={handleForceRefresh} className="mt-2">
          Force Refresh Current Route
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="focus">Focus Events</TabsTrigger>
          <TabsTrigger value="visibility">Visibility Events</TabsTrigger>
          <TabsTrigger value="queries">Query Cache</TabsTrigger>
          <TabsTrigger value="route">Route Queries</TabsTrigger>
        </TabsList>

        <TabsContent value="focus" className="max-h-80 overflow-y-auto">
          <ul className="space-y-1">
            {focusEvents.map((event, index) => (
              <li key={index} className="text-sm font-mono">{event}</li>
            ))}
          </ul>
        </TabsContent>

        <TabsContent value="visibility" className="max-h-80 overflow-y-auto">
          <ul className="space-y-1">
            {visibilityEvents.map((event, index) => (
              <li key={index} className="text-sm font-mono">{event}</li>
            ))}
          </ul>
        </TabsContent>

        <TabsContent value="queries" className="max-h-80 overflow-y-auto">
          <pre className="text-xs font-mono whitespace-pre-wrap">
            {JSON.stringify(queryDebugInfo, null, 2)}
          </pre>
        </TabsContent>

        <TabsContent value="route" className="max-h-80 overflow-y-auto">
          <h3 className="font-medium mb-2">Query Keys for Current Route:</h3>
          <ul className="space-y-1">
            {getQueryKeysForRoute().map((key, index) => {
              const debugInfo = debugQueryCache(queryClient, [key]);
              return (
                <li key={index} className="text-sm font-mono">
                  <div className="flex items-center gap-2">
                    <span>{key}:</span>
                    <span className={debugInfo.exists ? 'text-green-500' : 'text-red-500'}>
                      {debugInfo.exists ? 'Exists' : 'Missing'}
                    </span>
                    {debugInfo.exists && (
                      <>
                        <span className={debugInfo.isStale ? 'text-yellow-500' : 'text-green-500'}>
                          {debugInfo.isStale ? 'Stale' : 'Fresh'}
                        </span>
                        <span>Last Updated: {debugInfo.lastUpdated || 'Never'}</span>
                      </>
                    )}
                  </div>
                </li>
              );
            })}
          </ul>
        </TabsContent>
      </Tabs>
    </Card>
  );
};

export default DataLoadingDebugger;
