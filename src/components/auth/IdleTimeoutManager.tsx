import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { checkSession, refreshAuthToken } from '@/utils/sessionUtils';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

interface IdleTimeoutManagerProps {
  idleTimeout?: number; // Idle timeout in milliseconds (default: 30 minutes)
  warningTime?: number; // Time to show warning before logout in milliseconds (default: 1 minute)
  checkInterval?: number; // How often to check session validity in milliseconds (default: 5 minutes)
}

const IdleTimeoutManager: React.FC<IdleTimeoutManagerProps> = ({
  idleTimeout = 72 * 60 * 60 * 1000, // 72 hours
  warningTime = 30 * 60 * 1000, // 30 minutes
  checkInterval = 60 * 60 * 1000, // 1 hour
}) => {
  const { authState, signOut } = useAuth();
  const navigate = useNavigate();
  const [showWarning, setShowWarning] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(0);
  const idleTimerRef = useRef<NodeJS.Timeout | null>(null);
  const warningTimerRef = useRef<NodeJS.Timeout | null>(null);
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const sessionCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef(Date.now());

  // Function to reset the idle timer
  const resetIdleTimer = () => {
    lastActivityRef.current = Date.now();

    // Clear existing timers
    if (idleTimerRef.current) clearTimeout(idleTimerRef.current);
    if (warningTimerRef.current) clearTimeout(warningTimerRef.current);

    // Hide warning dialog if it's showing
    if (showWarning) {
      setShowWarning(false);
      if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);
    }

    // Set new timers
    warningTimerRef.current = setTimeout(() => {
      setShowWarning(true);
      setTimeRemaining(Math.floor(warningTime / 1000));

      // Start countdown
      countdownIntervalRef.current = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

    }, idleTimeout - warningTime);

    idleTimerRef.current = setTimeout(handleTimeout, idleTimeout);
  };

  // Function to handle timeout (user has been idle for too long)
  const handleTimeout = async () => {
    console.log('User has been idle for too long, logging out');
    setShowWarning(false);

    // Clear all timers and intervals
    clearAllTimers();

    // Store the current path to redirect back after login
    const currentPath = window.location.hash.replace('#', '') || '/dashboard';
    sessionStorage.setItem('redirectAfterLogin', currentPath);

    try {
      // Clear session data
      localStorage.removeItem('stayfu_auth_token');
      localStorage.removeItem('supabase.auth.token');
      sessionStorage.setItem('sessionExpired', 'true');

      // Sign out through auth context
      await signOut();

      // Show a user-friendly message
      toast.info('You have been logged out due to inactivity. Please log in again to continue.');

      // Navigate to login with hash routing
      navigate('/#/login?reason=idle_timeout');
    } catch (error) {
      console.error('Error signing out:', error);

      // Force navigate to login even if signOut fails
      localStorage.removeItem('stayfu_auth_token');
      localStorage.removeItem('supabase.auth.token');
      navigate('/#/login?reason=idle_timeout');
    }
  };

  // Function to clear all timers and intervals
  const clearAllTimers = () => {
    if (idleTimerRef.current) clearTimeout(idleTimerRef.current);
    if (warningTimerRef.current) clearTimeout(warningTimerRef.current);
    if (countdownIntervalRef.current) clearInterval(countdownIntervalRef.current);
    if (sessionCheckIntervalRef.current) clearInterval(sessionCheckIntervalRef.current);
  };

  // Function to check if the session is still valid
  const checkSessionValidity = async () => {
    if (!authState?.isAuthenticated) return;

    console.log('Checking session validity...');
    const isValid = await checkSession();

    if (!isValid) {
      console.log('Session is invalid, attempting to refresh...');
      const refreshed = await refreshAuthToken();

      if (!refreshed) {
        console.log('Failed to refresh session, logging out');
        await signOut();
        toast.error('Your session has expired. Please log in again.');
        navigate('/login?reason=session_expired');
      } else {
        console.log('Session refreshed successfully');
      }
    }
  };

  // Set up event listeners for user activity
  useEffect(() => {
    if (!authState?.isAuthenticated) return;

    const activityEvents = [
      'mousedown', 'mousemove', 'keydown',
      'scroll', 'touchstart', 'click', 'focus'
    ];

    const handleUserActivity = () => {
      resetIdleTimer();
    };

    // Add event listeners
    activityEvents.forEach(event => {
      window.addEventListener(event, handleUserActivity);
    });

    // Set up periodic session check
    sessionCheckIntervalRef.current = setInterval(checkSessionValidity, checkInterval);

    // Initial setup
    resetIdleTimer();
    checkSessionValidity();

    // Cleanup
    return () => {
      activityEvents.forEach(event => {
        window.removeEventListener(event, handleUserActivity);
      });
      clearAllTimers();
    };
  }, [authState?.isAuthenticated]);

  // Removed visibility change handler to rely on React Query's built-in functionality

  // Handle component unmount
  useEffect(() => {
    return () => {
      clearAllTimers();
    };
  }, []);

  // Function to handle "Stay Logged In" button click
  const handleStayLoggedIn = async () => {
    // Check if session is still valid
    const isValid = await checkSession();

    if (!isValid) {
      // Try to refresh the token
      const refreshed = await refreshAuthToken();

      if (!refreshed) {
        // If refresh fails, log out
        handleTimeout();
        return;
      }
    }

    // Reset the idle timer
    resetIdleTimer();
  };

  if (!authState?.isAuthenticated) {
    return null;
  }

  return (
    <>
      <Dialog open={showWarning} onOpenChange={(open) => {
        if (!open) handleStayLoggedIn();
      }}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Session Timeout Warning</DialogTitle>
            <DialogDescription>
              Your session will expire in {timeRemaining} seconds due to inactivity.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex flex-col sm:flex-row sm:justify-between">
            <Button variant="outline" onClick={handleTimeout}>
              Logout Now
            </Button>
            <Button onClick={handleStayLoggedIn}>
              Stay Logged In
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default IdleTimeoutManager;
