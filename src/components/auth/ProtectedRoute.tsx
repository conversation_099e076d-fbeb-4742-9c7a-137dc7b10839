import React, { useEffect, useState, useRef } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { PermissionType } from '@/types/auth';
import { usePermissions } from '@/hooks/usePermissions';
import { supabase } from '@/integrations/supabase/client';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
  requireSuperAdmin?: boolean;
  permission?: PermissionType;
  teamId?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAdmin = false,
  requireSuperAdmin = false,
  permission,
  teamId
}) => {
  const { authState, refreshProfile } = useAuth();
  const { hasPermission, loading: permissionsLoading } = usePermissions();
  // Make sure authState exists before destructuring
  const user = authState?.user;
  const profile = authState?.profile;
  const isLoading = authState?.isLoading || false;
  const [isCheckingSession, setIsCheckingSession] = useState(true);
  const location = useLocation();

  // Use a ref to track if we've already checked the session
  const sessionCheckedRef = useRef(false);

  useEffect(() => {
    const checkSession = async () => {
      // Skip if we've already checked the session
      if (sessionCheckedRef.current) {
        return;
      }

      try {
        console.log('ProtectedRoute: Checking session...');
        setIsCheckingSession(true);

        // First check if we already have a user in auth state
        if (authState?.user && authState?.isAuthenticated) {
          console.log('ProtectedRoute: User already authenticated in state');

          // Verify the session is still valid with Supabase
          const { data, error } = await supabase.auth.getSession();

          if (error) {
            console.error('ProtectedRoute: Error checking session:', error);
            setIsCheckingSession(false);
            return;
          }

          if (!data.session) {
            console.log('ProtectedRoute: Auth state shows authenticated but no valid session found');
            // Force a redirect to login
            window.location.href = '/#/login';
            return;
          }

          setIsCheckingSession(false);
          sessionCheckedRef.current = true;
          return;
        }

        // If not authenticated in state, check with Supabase directly
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('ProtectedRoute: Error checking session:', error);
          setIsCheckingSession(false);
          return;
        }

        if (data.session) {
          console.log('ProtectedRoute: Valid session found, refreshing profile');
          try {
            // Try to refresh the profile
            await refreshProfile();

            // Double-check that we now have a user in auth state
            if (!authState?.user || !authState?.isAuthenticated) {
              console.log('ProtectedRoute: Profile refresh did not update auth state');
              // Force a redirect to login
              window.location.href = '/#/login';
              return;
            }
          } catch (refreshError) {
            console.error('ProtectedRoute: Error refreshing profile:', refreshError);
            // Force a redirect to login
            window.location.href = '/#/login';
            return;
          }
        } else {
          console.log('ProtectedRoute: No valid session found');
          // Force a redirect to login
          window.location.href = '/#/login';
          return;
        }
      } catch (e) {
        console.error('ProtectedRoute: Exception checking session:', e);
      } finally {
        setIsCheckingSession(false);
        sessionCheckedRef.current = true;
      }
    };

    checkSession();

    // Set up a timer to periodically check the session, but much less frequently
    const intervalId = setInterval(checkSession, 600000); // Check every 10 minutes

    return () => clearInterval(intervalId);
  }, [authState?.user, authState?.isAuthenticated, refreshProfile]);

  // If still loading auth state OR permissions, show loading indicator
  if (isLoading || permissionsLoading || isCheckingSession) {
    return <div className="flex items-center justify-center h-screen">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" role="status">
        <span className="sr-only">Loading...</span>
      </div>
    </div>;
  }

  // Check if user is authenticated
  if (!user || !authState?.isAuthenticated) {
    console.log('ProtectedRoute: User not authenticated, redirecting to login');
    return <Navigate to="/#/login" state={{ from: location }} replace />;
  }

  // For super admin routes
  if (requireSuperAdmin) {
    // Note: isImpersonating feature not implemented yet
    // if (authState?.isImpersonating) {
    //   console.log('ProtectedRoute: User is impersonating, redirecting to dashboard');
    //   return <Navigate to="/#/dashboard" replace />;
    // }

    if (!profile?.is_super_admin) {
      console.log('ProtectedRoute: User is not super admin, redirecting to dashboard');
      return <Navigate to="/#/dashboard" replace />;
    }
    console.log('ProtectedRoute: User is super admin, rendering protected content');
    return <>{children}</>;
  }

  // If route requires admin privileges
  if (requireAdmin && (!profile || (profile.role !== 'admin' && !profile.is_super_admin))) {
    console.log('ProtectedRoute: User is not admin, redirecting to dashboard');
    return <Navigate to="/#/dashboard" replace />;
  }

  // If route requires specific permission
  if (permission && !hasPermission(permission, teamId)) {
    console.log(`ProtectedRoute: User lacks permission ${permission}, redirecting to dashboard`);
    return <Navigate to="/#/dashboard" replace />;
  }

  // User is authenticated and has required privileges
  console.log('ProtectedRoute: User has required permissions, rendering protected content');
  return <>{children}</>;
};

export default ProtectedRoute;
