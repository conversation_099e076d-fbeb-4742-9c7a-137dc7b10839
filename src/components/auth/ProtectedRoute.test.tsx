
import { render, screen } from '@/tests/utils/test-utils';
import '@testing-library/jest-dom';
import ProtectedRoute from './ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import { BrowserRouter, Routes, Route } from 'react-router-dom';

// Mock useAuth hook
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: jest.fn()
}));

// Mock Navigate component
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  Navigate: jest.fn(({ to }) => <div data-testid="navigate-mock">{`Navigating to ${to}`}</div>)
}));

// Mock usePermissions hook
jest.mock('@/hooks/usePermissions', () => ({
  usePermissions: () => ({
    userPermissions: [],
    hasPermission: jest.fn().mockReturnValue(false),
    isLoading: false
  })
}));

describe('ProtectedRoute Component', () => {
  it('renders loading state when auth is loading', () => {
    (useAuth as jest.Mock).mockReturnValue({
      authState: {
        user: null,
        profile: null,
        isLoading: true
      }
    });
    
    render(
      <ProtectedRoute>
        <div>Protected Content</div>
      </ProtectedRoute>,
      { withRouter: false }
    );
    
    expect(screen.getByRole('status')).toBeInTheDocument();
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
  });
  
  it('redirects to login when user is not authenticated', () => {
    (useAuth as jest.Mock).mockReturnValue({
      authState: {
        user: null,
        profile: null,
        isLoading: false
      }
    });
    
    render(
      <BrowserRouter>
        <ProtectedRoute>
          <div>Protected Content</div>
        </ProtectedRoute>
      </BrowserRouter>,
      {
        authState: {
          user: { id: 'test-user', email: '<EMAIL>' },
          profile: { id: 'test-profile', first_name: 'Test', last_name: 'User' },
          isLoading: false,
          isAuthenticated: true
        }
      }
    );
    
    expect(screen.getByTestId('navigate-mock')).toBeInTheDocument();
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
  });
  
  it('renders children when user is authenticated', () => {
    (useAuth as jest.Mock).mockReturnValue({
      authState: {
        user: { id: 'test-user', email: '<EMAIL>' },
        profile: { id: 'test-profile', first_name: 'Test', last_name: 'User' },
        isLoading: false
      }
    });
    
    render(
      <BrowserRouter>
        <ProtectedRoute>
          <div>Protected Content</div>
        </ProtectedRoute>
      </BrowserRouter>
    );
    
    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });
  
  it('redirects non-admin user from admin route', () => {
    (useAuth as jest.Mock).mockReturnValue({
      authState: {
        user: { id: 'test-user', email: '<EMAIL>' },
        profile: { id: 'test-profile', first_name: 'Test', last_name: 'User', is_super_admin: false },
        isLoading: false
      }
    });
    
    render(
      <ProtectedRoute requireAdmin={true}>
        <div>Admin Content</div>
      </ProtectedRoute>,
      { withRouter: false }
    );
    
    expect(screen.getByTestId('navigate-mock')).toBeInTheDocument();
    expect(screen.queryByText('Admin Content')).not.toBeInTheDocument();
  });
  
  it('allows admin user to access admin route', () => {
    (useAuth as jest.Mock).mockReturnValue({
      authState: {
        user: { id: 'test-user', email: '<EMAIL>' },
        profile: { id: 'test-profile', first_name: 'Test', last_name: 'User', is_super_admin: true },
        isLoading: false
      }
    });
    
    render(
      <ProtectedRoute requireAdmin={true}>
        <div>Admin Content</div>
      </ProtectedRoute>,
      { withRouter: false }
    );
    
    expect(screen.getByText('Admin Content')).toBeInTheDocument();
  });
});
