import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { checkSession, refreshAuthToken } from '@/utils/sessionUtils';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

interface SessionRefreshManagerProps {
  refreshInterval?: number; // How often to check and refresh the session in milliseconds
}

const SessionRefreshManager: React.FC<SessionRefreshManagerProps> = ({
  refreshInterval = 24 * 60 * 60 * 1000, // 24 hours - increased to match longer session duration
}) => {
  const { authState, signOut } = useAuth();
  const navigate = useNavigate();
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const MAX_REFRESH_ATTEMPTS = 3;

  // Function to refresh the session
  const refreshSession = async () => {
    if (!authState?.isAuthenticated || isRefreshing) {
      console.log(`[SessionRefreshManager] Skipping refresh: ${!authState?.isAuthenticated ? 'Not authenticated' : 'Already refreshing'}`);
      return;
    }

    setIsRefreshing(true);
    let refreshAttempt = 0;

    try {
      // Check if we're online first
      if (typeof navigator !== 'undefined' && 'onLine' in navigator && !navigator.onLine) {
        console.log(`[SessionRefreshManager] Network is offline, skipping session check`);
        setIsRefreshing(false);
        return;
      }

      console.log(`[SessionRefreshManager] Checking session validity`);

      // First check if the session is still valid
      const isValid = await checkSession();

      if (!isValid) {
        console.log(`[SessionRefreshManager] Session needs refresh, attempting to refresh token`);

        // Try to refresh the token up to MAX_REFRESH_ATTEMPTS times
        let refreshed = false;

        while (!refreshed && refreshAttempt < MAX_REFRESH_ATTEMPTS) {
          refreshAttempt++;
          console.log(`[SessionRefreshManager] Refresh attempt ${refreshAttempt}/${MAX_REFRESH_ATTEMPTS}`);

          // Check if we're still online before attempting refresh
          if (typeof navigator !== 'undefined' && 'onLine' in navigator && !navigator.onLine) {
            console.log(`[SessionRefreshManager] Network is offline, pausing refresh attempts`);
            break;
          }

          refreshed = await refreshAuthToken();

          if (refreshed) {
            console.log(`[SessionRefreshManager] Session refreshed successfully`);

            // Dispatch a custom event that components can listen for
            try {
              window.dispatchEvent(new CustomEvent('stayfu-session-refreshed', {
                detail: { timestamp: new Date().toISOString() }
              }));
              console.log('[SessionRefreshManager] Dispatched stayfu-session-refreshed event');
            } catch (eventError) {
              console.error('[SessionRefreshManager] Error dispatching session refreshed event:', eventError);
            }

            break;
          } else {
            console.log(`[SessionRefreshManager] Refresh attempt ${refreshAttempt} failed`);

            // Wait a bit before trying again
            if (refreshAttempt < MAX_REFRESH_ATTEMPTS) {
              await new Promise(resolve => setTimeout(resolve, 1000 * refreshAttempt));
            }
          }
        }

        // If all refresh attempts failed, handle session expiration
        if (!refreshed) {
          // Only handle session expired if we're online
          if (typeof navigator !== 'undefined' && 'onLine' in navigator && navigator.onLine) {
            console.log(`[SessionRefreshManager] Failed to refresh session after ${MAX_REFRESH_ATTEMPTS} attempts`);
            await handleSessionExpired();
          } else {
            console.log(`[SessionRefreshManager] Network is offline, will try again when online`);
          }
        }
      } else {
        console.log(`[SessionRefreshManager] Session is still valid, no refresh needed`);
      }
    } catch (error) {
      console.error(`[SessionRefreshManager] Error refreshing session:`, error);

      // Only handle session expired if we're online
      if (typeof navigator !== 'undefined' && 'onLine' in navigator && navigator.onLine) {
        await handleSessionExpired();
      } else {
        console.log(`[SessionRefreshManager] Network is offline, skipping session expired handling`);
      }
    } finally {
      setIsRefreshing(false);
    }
  };

  // Function to handle session expiration
  const handleSessionExpired = async () => {
    // Check if we're online first
    if (typeof navigator !== 'undefined' && 'onLine' in navigator && !navigator.onLine) {
      console.log(`[SessionRefreshManager] Network is offline, delaying session expired handling`);

      // Set up a listener to handle session expiration when we're back online
      const handleOnline = () => {
        console.log('[SessionRefreshManager] Network is back online, handling delayed session expiration');
        window.removeEventListener('online', handleOnline);
        handleSessionExpired();
      };

      window.addEventListener('online', handleOnline);
      return;
    }

    try {
      console.log('[SessionRefreshManager] Session expired, handling expiration');

      // Store the current path to redirect back after login
      const currentPath = window.location.hash.replace('#', '') || '/dashboard';
      sessionStorage.setItem('redirectAfterLogin', currentPath);

      // Clear all session data
      localStorage.removeItem('stayfu_auth_token');
      localStorage.removeItem('supabase.auth.token');
      sessionStorage.setItem('sessionExpired', 'true');

      // Dispatch a custom event that components can listen for
      try {
        window.dispatchEvent(new CustomEvent('stayfu-session-expired', {
          detail: { timestamp: new Date().toISOString() }
        }));
        console.log('[SessionRefreshManager] Dispatched stayfu-session-expired event');
      } catch (eventError) {
        console.error('[SessionRefreshManager] Error dispatching session expired event:', eventError);
      }

      // Sign out through the auth context
      console.log('[SessionRefreshManager] Calling signOut method from auth context');
      await signOut();

      // Show a user-friendly message
      toast.error('Your session has expired. Please log in again to continue.');

      // Force a hard redirect to the login page with hash routing
      // Use a short timeout to ensure the toast is visible
      setTimeout(() => {
        console.log('[SessionRefreshManager] Redirecting to login page');
        window.location.href = '/#/login?reason=session_expired';
      }, 1000);
    } catch (error) {
      console.error('[SessionRefreshManager] Error signing out:', error);

      // Force a hard redirect even if signOut fails
      localStorage.removeItem('stayfu_auth_token');
      localStorage.removeItem('supabase.auth.token');
      sessionStorage.setItem('sessionExpired', 'true');

      // Store the current path to redirect back after login
      const currentPath = window.location.hash.replace('#', '') || '/dashboard';
      sessionStorage.setItem('redirectAfterLogin', currentPath);

      setTimeout(() => {
        window.location.href = '/#/login?reason=session_expired';
      }, 1000);
    }
  };

  // Set up session refresh interval
  useEffect(() => {
    if (!authState?.isAuthenticated) {
      console.log('[SessionRefreshManager] Not authenticated, skipping session refresh setup');
      return;
    }

    console.log(`[SessionRefreshManager] Setting up session refresh interval (${refreshInterval / (60 * 1000)} minutes)`);

    // Check session validity immediately on mount
    const initialCheck = async () => {
      console.log('[SessionRefreshManager] Performing initial session check');
      const isValid = await checkSession();

      if (!isValid) {
        console.log(`[SessionRefreshManager] Initial session check failed, attempting refresh...`);
        await refreshSession();
      } else {
        console.log(`[SessionRefreshManager] Initial session check passed, session is valid`);
      }
    };

    initialCheck();

    // Set up interval for regular session refreshes
    refreshIntervalRef.current = setInterval(() => {
      console.log(`[SessionRefreshManager] Refresh interval triggered`);
      refreshSession();
    }, refreshInterval);

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log(`[SessionRefreshManager] Auth state changed: ${event}`);

        if (event === 'SIGNED_OUT') {
          console.log('[SessionRefreshManager] User signed out, clearing refresh interval');
          if (refreshIntervalRef.current) {
            clearInterval(refreshIntervalRef.current);
          }
        }
      }
    );

    // Cleanup
    return () => {
      console.log('[SessionRefreshManager] Cleaning up session refresh interval');
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      subscription.unsubscribe();
    };
  }, [authState?.isAuthenticated, refreshInterval]);

  // Handle page beforeunload and network status events
  useEffect(() => {
    console.log('[SessionRefreshManager] Setting up beforeunload and network status handlers');

    // Handle page refresh/beforeunload
    const handleBeforeUnload = () => {
      // Save the current session to localStorage before page refresh
      if (authState?.session) {
        try {
          localStorage.setItem('stayfu_auth_token', JSON.stringify(authState.session));
        } catch (e) {
          console.error('[SessionRefreshManager] Error saving session before unload:', e);
        }
      }
    };

    // Handle online status change
    const handleOnline = () => {
      console.log('[SessionRefreshManager] Network is back online, checking session');
      if (authState?.isAuthenticated) {
        // Trigger a session check when we're back online
        setTimeout(() => {
          refreshSession();
        }, 2000); // Small delay to ensure network is stable
      }
    };

    // Handle offline status change
    const handleOffline = () => {
      console.log('[SessionRefreshManager] Network is offline, pausing session checks');
    };

    // Handle Supabase network status events
    const handleSupabaseNetworkStatus = (event: CustomEvent) => {
      const { online, timestamp } = event.detail;
      console.log(`[SessionRefreshManager] Supabase network status: ${online ? 'online' : 'offline'} at ${timestamp}`);

      if (online && authState?.isAuthenticated) {
        // Trigger a session check when Supabase connection is restored
        setTimeout(() => {
          refreshSession();
        }, 2000); // Small delay to ensure connection is stable
      }
    };

    // Add event listeners
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    window.addEventListener('stayfu-network-status', handleSupabaseNetworkStatus as EventListener);

    // Set up a timer to periodically check the session
    const sessionCheckTimer = setInterval(() => {
      if (authState?.isAuthenticated) {
        // Only check if we're online
        if (typeof navigator !== 'undefined' && 'onLine' in navigator && navigator.onLine) {
          console.log('[SessionRefreshManager] Periodic session check');
          checkSession().then(isValid => {
            if (!isValid) {
              console.log('[SessionRefreshManager] Session needs refresh from periodic check');
              refreshSession();
            }
          });
        } else {
          console.log('[SessionRefreshManager] Skipping periodic session check - offline');
        }
      }
    }, 10 * 60 * 1000); // Check every 10 minutes

    // Cleanup
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('stayfu-network-status', handleSupabaseNetworkStatus as EventListener);
      clearInterval(sessionCheckTimer);
    };
  }, [authState?.isAuthenticated, authState?.session, refreshSession]);

  // Add a global debug method to window
  useEffect(() => {
    // @ts-ignore
    window.getSessionDebugInfo = () => {
      const debugInfo = {
        isAuthenticated: authState?.isAuthenticated || false,
        isRefreshing,
        sessionInfo: authState?.session ? {
          expiresAt: authState.session.expires_at ? new Date(authState.session.expires_at * 1000).toISOString() : null,
          user: authState.session.user?.email || 'unknown'
        } : null,
        localStorage: {
          hasAuthToken: !!localStorage.getItem('stayfu_auth_token'),
          hasSupabaseToken: !!localStorage.getItem('supabase.auth.token'),
          lastTokenRefresh: localStorage.getItem('stayfu_last_token_refresh')
        }
      };

      console.log('[SessionRefreshManager] Debug Info:', debugInfo);
      return debugInfo;
    };

    return () => {
      // @ts-ignore
      delete window.getSessionDebugInfo;
    };
  }, [authState, isRefreshing]);

  // This component doesn't render anything
  return null;
};

export default SessionRefreshManager;
