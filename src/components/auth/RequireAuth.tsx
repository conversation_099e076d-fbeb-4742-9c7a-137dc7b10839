import React, { useEffect, useState, useRef, useCallback } from 'react';
import { Navigate, useLocation, Outlet } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export const RequireAuth = () => {
  const { authState, refreshProfile } = useAuth();
  const location = useLocation();
  const [isCheckingSession, setIsCheckingSession] = useState(true);
  const [hasSession, setHasSession] = useState<boolean | null>(null);
  const [redirectToLogin, setRedirectToLogin] = useState(false);

  // Use a ref to track if we've already checked the session
  const sessionCheckedRef = useRef(false);
  const refreshAttemptedRef = useRef(false);
  const lastSessionCheckRef = useRef(0);
  const sessionCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Function to check if a session is valid based on expiry time
  // More lenient to prevent unnecessary logouts
  const isSessionValid = useCallback((session: any): boolean => {
    if (!session) return false;

    // If no expires_at, assume it's valid (safer approach)
    if (!session.expires_at) return true;

    const now = Math.floor(Date.now() / 1000);

    // Consider valid even if it's expired but within the last 24 hours
    // This gives us a chance to refresh it rather than forcing logout
    const gracePeriod = 24 * 60 * 60; // 24 hours in seconds
    return (session.expires_at + gracePeriod) > now;
  }, []);

  // Function to get a session from localStorage
  const getLocalSession = useCallback((): any => {
    try {
      // First try our custom storage key
      const localSession = localStorage.getItem('stayfu_auth_token');
      if (localSession) {
        try {
          const parsedSession = JSON.parse(localSession);
          if (parsedSession?.user) {
            console.log('RequireAuth: Found session in stayfu_auth_token');
            return parsedSession;
          }
        } catch (parseError) {
          console.error('RequireAuth: Error parsing stayfu_auth_token:', parseError);
        }
      }

      // Then try sessionStorage
      const sessionStorageSession = sessionStorage.getItem('stayfu_auth_token');
      if (sessionStorageSession) {
        try {
          const parsedSession = JSON.parse(sessionStorageSession);
          if (parsedSession?.user) {
            console.log('RequireAuth: Found session in sessionStorage');
            return parsedSession;
          }
        } catch (parseError) {
          console.error('RequireAuth: Error parsing sessionStorage session:', parseError);
        }
      }

      // Also check the default Supabase storage location
      const supabaseSession = localStorage.getItem('supabase.auth.token');
      if (supabaseSession) {
        try {
          const parsedSupabaseSession = JSON.parse(supabaseSession);
          if (parsedSupabaseSession?.currentSession?.user) {
            console.log('RequireAuth: Found session in supabase.auth.token');
            return parsedSupabaseSession.currentSession;
          }
        } catch (parseError) {
          console.error('RequireAuth: Error parsing supabase session:', parseError);
        }
      }

      return null;
    } catch (e) {
      console.error('RequireAuth: Error accessing storage:', e);
      return null;
    }
  }, []);

  // Function to save a session to localStorage
  const saveSessionToStorage = useCallback((session: any): void => {
    if (!session) return;

    try {
      // Save to both localStorage and sessionStorage for redundancy
      localStorage.setItem('stayfu_auth_token', JSON.stringify(session));
      sessionStorage.setItem('stayfu_auth_token', JSON.stringify(session));

      // Also save a flag indicating we have a valid session
      localStorage.setItem('stayfu_has_session', 'true');

      // Save expiry time for debugging
      if (session.expires_at) {
        const expiresAt = new Date(session.expires_at * 1000).toISOString();
        localStorage.setItem('stayfu_session_expires', expiresAt);
      }
    } catch (e) {
      console.error('RequireAuth: Error saving session to storage:', e);
    }
  }, []);

  // Simplified checkSession function to avoid complexity and potential issues
  const checkSession = useCallback(async (force = false) => {
    // Skip if we've already checked the session recently (within 10 seconds)
    // unless force=true is passed
    const now = Date.now();
    if (!force && sessionCheckedRef.current && (now - lastSessionCheckRef.current < 10000)) {
      return;
    }

    // Update the last check timestamp
    lastSessionCheckRef.current = now;

    try {
      console.log('RequireAuth: Checking session...');
      setIsCheckingSession(true);

      // First check if we already have a user in auth state
      if (authState?.user && authState?.isAuthenticated) {
        console.log('RequireAuth: User already authenticated in state');

        // Save the current session to storage for redundancy
        if (authState.session) {
          saveSessionToStorage(authState.session);
        }

        setHasSession(true);
        setIsCheckingSession(false);
        sessionCheckedRef.current = true;
        return;
      }

      // If not authenticated in state, check if we have a valid session in storage
      const localSession = getLocalSession();
      if (localSession && isSessionValid(localSession)) {
        console.log('RequireAuth: Found valid session in storage, using it');

        // Try to refresh the profile with this session
        try {
          if (!refreshAttemptedRef.current) {
            refreshAttemptedRef.current = true;
            await refreshProfile();

            // Check if auth state was updated
            if (authState?.user && authState?.isAuthenticated) {
              setHasSession(true);
              setIsCheckingSession(false);
              sessionCheckedRef.current = true;
              return;
            }
          }
        } catch (refreshError) {
          console.error('RequireAuth: Error refreshing profile with local session:', refreshError);
        }

        // Even if profile refresh failed, we still have a valid session
        setHasSession(true);
        setIsCheckingSession(false);
        sessionCheckedRef.current = true;
        return;
      }

      // If no valid session in state or storage, check with Supabase directly
      try {
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('RequireAuth: Error checking session:', error);
          setHasSession(false);
          setIsCheckingSession(false);
          setRedirectToLogin(true);
          return;
        }

        if (data.session) {
          console.log('RequireAuth: Valid session found, refreshing profile');
          // Save the session
          saveSessionToStorage(data.session);

          try {
            // Only try to refresh profile once to prevent loops
            if (!refreshAttemptedRef.current) {
              refreshAttemptedRef.current = true;
              await refreshProfile();
            }

            // Even if profile refresh didn't update auth state, we still have a valid session
            setHasSession(true);
            setIsCheckingSession(false);
            sessionCheckedRef.current = true;
          } catch (refreshError) {
            console.error('RequireAuth: Error refreshing profile:', refreshError);

            // Even if profile refresh failed, we still have a valid session
            setHasSession(true);
            setIsCheckingSession(false);
            sessionCheckedRef.current = true;
          }
        } else {
          console.log('RequireAuth: No valid session found');
          setHasSession(false);
          setIsCheckingSession(false);
          setRedirectToLogin(true);
        }
      } catch (e) {
        console.error('RequireAuth: Exception checking session:', e);

        // One last check for a valid local session
        const localSession = getLocalSession();
        if (localSession && isSessionValid(localSession)) {
          console.log('RequireAuth: Using valid session from storage after exception');
          setHasSession(true);
          setIsCheckingSession(false);
          sessionCheckedRef.current = true;
          return;
        }

        setHasSession(false);
        setIsCheckingSession(false);
        setRedirectToLogin(true);
      }
    } catch (e) {
      console.error('RequireAuth: Exception in checkSession:', e);
      setHasSession(false);
      setIsCheckingSession(false);
      setRedirectToLogin(true);
    }
  }, [authState?.user, authState?.isAuthenticated, authState?.session, refreshProfile, getLocalSession, isSessionValid, saveSessionToStorage]);

  // We're now relying on the periodic session check instead of visibility change
  // This helps prevent conflicts with React Query's built-in window focus handling
  useEffect(() => {
    console.log('RequireAuth: Using periodic session check instead of visibility change');

    // Check session when the component mounts
    checkSession(true);

    // No need for visibility change handler as we have a periodic check
  }, [checkSession]);

  // Set up beforeunload listener to save session state
  useEffect(() => {
    const handleBeforeUnload = () => {
      // If we have a valid session, ensure it's saved to localStorage and sessionStorage
      if (authState?.session && authState?.user) {
        try {
          // Save to both localStorage and sessionStorage for redundancy
          localStorage.setItem('stayfu_auth_token', JSON.stringify(authState.session));
          sessionStorage.setItem('stayfu_auth_token', JSON.stringify(authState.session));

          // Also save a flag indicating we have a valid session
          localStorage.setItem('stayfu_has_session', 'true');

          // Save expiry time for debugging
          if (authState.session.expires_at) {
            const expiresAt = new Date(authState.session.expires_at * 1000).toISOString();
            localStorage.setItem('stayfu_session_expires', expiresAt);
          }

          // Set a flag to indicate this was a page refresh, not a navigation
          sessionStorage.setItem('stayfu_page_refreshed', 'true');

          console.log('RequireAuth: Saved session before page unload');
        } catch (e) {
          console.error('RequireAuth: Error saving session before unload:', e);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Also handle page load after refresh
    const handlePageLoad = () => {
      // Check if this was a page refresh
      const wasRefreshed = sessionStorage.getItem('stayfu_page_refreshed') === 'true';
      if (wasRefreshed) {
        console.log('RequireAuth: Page was refreshed, checking session immediately');
        sessionStorage.removeItem('stayfu_page_refreshed');

        // Force a session check
        checkSession(true);
      }
    };

    // Run the page load handler once
    handlePageLoad();

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [authState?.session, authState?.user, checkSession]);

  // Main effect to check session on mount and periodically
  useEffect(() => {
    // Initial session check
    checkSession();

    // Set up a timer to periodically check the session
    if (sessionCheckIntervalRef.current) {
      clearInterval(sessionCheckIntervalRef.current);
    }

    sessionCheckIntervalRef.current = setInterval(() => {
      checkSession();
    }, 3600000); // Check every 60 minutes (increased from 5 minutes)

    // Cleanup function
    return () => {
      if (sessionCheckIntervalRef.current) {
        clearInterval(sessionCheckIntervalRef.current);
      }
    };
  }, [checkSession]);

  // Show loading state while checking authentication
  if (authState?.isLoading || isCheckingSession) {
    // Use a more subtle loading indicator to prevent the full-screen spinner
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary opacity-70"></div>
      </div>
    );
  }

  // Redirect to login if needed
  if (redirectToLogin) {
    console.log('RequireAuth: Redirecting to login');

    // Store the current location so we can redirect back after login
    const currentPath = location.pathname + location.search + location.hash;
    sessionStorage.setItem('redirectAfterLogin', currentPath);

    // Clear any local session data before redirecting
    try {
      localStorage.removeItem('stayfu_auth_token');
      localStorage.removeItem('supabase.auth.token');
      sessionStorage.setItem('sessionExpired', 'true');
    } catch (e) {
      console.error('RequireAuth: Error clearing session data:', e);
    }

    // Show a user-friendly toast notification
    toast.error('Your session has expired. Please log in again to continue.');

    // Use Navigate component for React Router compatibility
    // Include the hash prefix for proper navigation with HashRouter
    return <Navigate to="/#/login" state={{ reason: 'session_expired' }} replace />;
  }

  // User is authenticated, render the protected route
  console.log('RequireAuth: User authenticated, rendering protected content');
  return <Outlet />;
};

export default RequireAuth;
