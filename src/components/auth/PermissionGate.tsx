import React from 'react';
import { usePermissions } from '@/hooks/usePermissions';
import { PermissionType } from '@/types/auth';
import { useAuth } from '@/contexts/AuthContext';

interface PermissionGateProps {
  permission?: PermissionType;
  teamId?: string;
  requireSuperAdmin?: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

const PermissionGate: React.FC<PermissionGateProps> = ({
  permission,
  teamId,
  requireSuperAdmin = false,
  children,
  fallback = null,
}) => {
  const { authState } = useAuth();
  const { hasPermission, loading } = usePermissions();
  
  // Handle loading state
  if (loading || authState?.isLoading) {
    return null;
  }

  // If not authenticated, show fallback
  if (!authState?.user) {
    return <>{fallback}</>;
  }

  // Super admin check
  if (requireSuperAdmin) {
    return authState?.profile?.is_super_admin ? <>{children}</> : <>{fallback}</>;
  }

  // If permission is specified, check if user has it
  if (permission) {
    return hasPermission(permission, teamId) ? <>{children}</> : <>{fallback}</>;
  }

  // If no specific permission required, render children
  return <>{children}</>;
};

export default PermissionGate;
