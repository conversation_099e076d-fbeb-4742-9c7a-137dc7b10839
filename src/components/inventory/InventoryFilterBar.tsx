
import React from 'react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Property } from '@/types/inventory';
import { Filter, X } from 'lucide-react';

interface InventoryFilterBarProps {
  filters: {
    property: string;
    collection: string;
    stockStatus: string;
  };
  setFilters: (filters: {
    property: string;
    collection: string;
    stockStatus: string;
  }) => void;
  properties: Property[];
  collections: string[];
}

const InventoryFilterBar: React.FC<InventoryFilterBarProps> = ({
  filters,
  setFilters,
  properties,
  collections
}) => {
  const resetFilters = () => {
    setFilters({
      property: 'all',
      collection: 'all',
      stockStatus: 'all'
    });
  };

  const hasActiveFilters = filters.property !== 'all' ||
    filters.collection !== 'all' ||
    filters.stockStatus !== 'all';

  return (
    <div className="p-4 mb-6 bg-muted/30 rounded-lg border">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Filter size={16} className="text-muted-foreground" />
          <h3 className="font-medium">Filters</h3>
          {hasActiveFilters && (
            <span className="text-xs bg-primary text-primary-foreground px-2 py-1 rounded-full">
              Active
            </span>
          )}
        </div>
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={resetFilters}
            className="text-muted-foreground hover:text-foreground"
          >
            <X size={14} className="mr-1" />
            Reset
          </Button>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Label htmlFor="property-filter" className="mb-1.5 block text-sm">
            Property
          </Label>
          <Select
            value={filters.property}
            onValueChange={(value) => setFilters({ ...filters, property: value })}
          >
            <SelectTrigger id="property-filter" className="w-full">
              <SelectValue placeholder="Select Property" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Properties</SelectItem>
              {properties && properties.length > 0 ? (
                properties.map((property) => (
                  <SelectItem key={property.id} value={property.id}>
                    {property.name}
                  </SelectItem>
                ))
              ) : (
                <SelectItem value="" disabled>No properties available</SelectItem>
              )}
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="collection-filter" className="mb-1.5 block text-sm">
            Collection
          </Label>
          <Select
            value={filters.collection}
            onValueChange={(value) => setFilters({ ...filters, collection: value })}
          >
            <SelectTrigger id="collection-filter" className="w-full">
              <SelectValue placeholder="Select Collection" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Collections</SelectItem>
              {collections && collections.length > 0 ? (
                collections.map((collection, index) => (
                  <SelectItem key={index} value={collection}>
                    {collection}
                  </SelectItem>
                ))
              ) : (
                <SelectItem value="" disabled>No collections available</SelectItem>
              )}
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="stock-filter" className="mb-1.5 block text-sm">
            Stock Status
          </Label>
          <Select
            value={filters.stockStatus}
            onValueChange={(value) => setFilters({ ...filters, stockStatus: value })}
          >
            <SelectTrigger id="stock-filter" className="w-full">
              <SelectValue placeholder="Select Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Items</SelectItem>
              <SelectItem value="ok">In Stock</SelectItem>
              <SelectItem value="low">Low Stock</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};

export default InventoryFilterBar;
