import React from 'react';
import { FormattedInventoryItem } from '@/types/inventory';
import { Package, AlertTriangle, TrendingUp, DollarSign } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface InventorySummaryProps {
  items: FormattedInventoryItem[];
  filteredItems: FormattedInventoryItem[];
  isLoading?: boolean;
}

const InventorySummary: React.FC<InventorySummaryProps> = ({
  items,
  filteredItems,
  isLoading = false
}) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 bg-muted rounded animate-pulse"></div>
              </CardTitle>
              <div className="h-4 w-4 bg-muted rounded animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted rounded animate-pulse mb-1"></div>
              <div className="h-3 bg-muted rounded animate-pulse w-2/3"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const totalItems = items.length;
  const filteredCount = filteredItems.length;
  const lowStockItems = items.filter(item => item.quantity < item.minQuantity);
  const totalValue = items.reduce((sum, item) => sum + (item.price || 0) * item.quantity, 0);
  const uniqueProperties = new Set(items.map(item => item.propertyId)).size;

  const stats = [
    {
      title: "Total Items",
      value: totalItems.toLocaleString(),
      description: filteredCount !== totalItems ? `${filteredCount} shown` : "All items",
      icon: Package,
      color: "text-blue-600"
    },
    {
      title: "Low Stock",
      value: lowStockItems.length.toLocaleString(),
      description: lowStockItems.length > 0 ? "Need attention" : "All good",
      icon: AlertTriangle,
      color: lowStockItems.length > 0 ? "text-red-600" : "text-green-600"
    },
    {
      title: "Properties",
      value: uniqueProperties.toLocaleString(),
      description: "Locations covered",
      icon: TrendingUp,
      color: "text-purple-600"
    },
    {
      title: "Total Value",
      value: `$${totalValue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
      description: "Inventory worth",
      icon: DollarSign,
      color: "text-green-600"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {stat.title}
            </CardTitle>
            <stat.icon className={`h-4 w-4 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">
              {stat.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default InventorySummary;
