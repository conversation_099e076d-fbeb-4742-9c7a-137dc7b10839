import React, { useState, useEffect } from 'react';
import { Package } from 'lucide-react';
import { cn } from '@/lib/utils';

interface InventoryImageProps {
  imageUrl?: string;
  amazonUrl?: string;
  itemName: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showPlaceholder?: boolean;
}

const InventoryImage: React.FC<InventoryImageProps> = ({
  imageUrl,
  amazonUrl,
  itemName,
  className,
  size = 'md',
  showPlaceholder = true
}) => {
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);

  const sizeClasses = {
    sm: 'h-12 w-12',
    md: 'h-24 w-24',
    lg: 'h-32 w-32'
  };

  // Process and load image with fallbacks
  useEffect(() => {
    if (!imageUrl) {
      setImageError(true);
      setImageLoading(false);
      return;
    }

    setImageLoading(true);
    setImageError(false);

    const img = new Image();
    
    img.onload = () => {
      setImageSrc(imageUrl);
      setImageLoading(false);
      setImageError(false);
    };

    img.onerror = () => {
      console.log(`[InventoryImage] Primary image failed: ${imageUrl}`);
      
      // Try Amazon image fallback
      if (amazonUrl) {
        const asinMatch = amazonUrl.match(/\/([A-Z0-9]{10})(\/|\?|$)/);
        if (asinMatch && asinMatch[1]) {
          const asin = asinMatch[1];
          const amazonImageUrl = `https://m.media-amazon.com/images/I/${asin}.jpg`;
          
          const amazonImg = new Image();
          amazonImg.onload = () => {
            setImageSrc(amazonImageUrl);
            setImageLoading(false);
            setImageError(false);
          };
          amazonImg.onerror = () => {
            console.log(`[InventoryImage] Amazon fallback failed: ${amazonImageUrl}`);
            setImageError(true);
            setImageLoading(false);
          };
          amazonImg.src = amazonImageUrl;
          return;
        }
      }

      // Try extracting ASIN from item name
      const asinMatch = itemName.match(/\b([A-Z0-9]{10})\b/);
      if (asinMatch && asinMatch[1]) {
        const asin = asinMatch[1];
        const amazonImageUrl = `https://m.media-amazon.com/images/I/${asin}.jpg`;
        
        const nameImg = new Image();
        nameImg.onload = () => {
          setImageSrc(amazonImageUrl);
          setImageLoading(false);
          setImageError(false);
        };
        nameImg.onerror = () => {
          console.log(`[InventoryImage] Name-based Amazon fallback failed: ${amazonImageUrl}`);
          setImageError(true);
          setImageLoading(false);
        };
        nameImg.src = amazonImageUrl;
        return;
      }

      // All fallbacks failed
      setImageError(true);
      setImageLoading(false);
    };

    img.src = imageUrl;
  }, [imageUrl, amazonUrl, itemName]);

  if (imageLoading) {
    return (
      <div className={cn(
        "flex items-center justify-center bg-muted rounded",
        sizeClasses[size],
        className
      )}>
        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (imageError || !imageSrc) {
    if (!showPlaceholder) return null;
    
    return (
      <div className={cn(
        "flex items-center justify-center bg-muted rounded",
        sizeClasses[size],
        className
      )}>
        <Package className="h-6 w-6 text-muted-foreground" />
      </div>
    );
  }

  return (
    <img
      src={imageSrc}
      alt={itemName}
      className={cn(
        "object-cover rounded",
        sizeClasses[size],
        className
      )}
      loading="lazy"
    />
  );
};

export default InventoryImage;
