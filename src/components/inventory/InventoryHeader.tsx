
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Search,
  Plus,
  ShoppingCart,
  Upload,
  Filter,
  RefreshCw,
  Package,
  Trash2
} from 'lucide-react';
import { StandardPageHeader, PageHeaderButtons } from '@/components/ui/StandardizedUI';
import AdvancedSearch, { SearchCriteria } from './AdvancedSearch';
import ExportInventory from './ExportInventory';
import { Property, FormattedInventoryItem } from '@/types/inventory';

interface InventoryHeaderProps {
  onAddItem: () => void;
  onCreateOrder: () => void;
  onBulkImport: () => void;
  setSearchQuery: (query: string) => void;
  searchQuery: string;
  toggleFilters: () => void;
  onRefresh?: () => void;
  isLoading?: boolean;
  onAdvancedSearch?: (criteria: SearchCriteria) => void;
  properties?: Property[];
  collections?: string[];
  allItems?: FormattedInventoryItem[];
  filteredItems?: FormattedInventoryItem[];
}

const InventoryHeader: React.FC<InventoryHeaderProps> = ({
  onAddItem,
  onCreateOrder,
  onBulkImport,
  setSearchQuery,
  searchQuery,
  toggleFilters,
  onRefresh,
  isLoading = false,
  onAdvancedSearch,
  properties = [],
  collections = [],
  allItems = [],
  filteredItems = []
}) => {
  return (
    <StandardPageHeader
      title="Inventory"
      description="Manage your inventory items"
      searchQuery={searchQuery}
      onSearchChange={setSearchQuery}
      searchPlaceholder="Search inventory..."
      onRefresh={onRefresh}
      isLoading={isLoading}
      onToggleFilters={toggleFilters}
      secondaryActions={
        <>
          {onAdvancedSearch && (
            <AdvancedSearch
              onSearch={onAdvancedSearch}
              properties={properties}
              collections={collections}
            />
          )}
          <ExportInventory
            items={allItems}
            filteredItems={filteredItems}
          />
          <Button
            variant="outline"
            size="sm"
            onClick={onBulkImport}
            className="flex items-center gap-1.5"
            disabled={isLoading}
          >
            <Upload size={16} />
            <span className="hidden sm:inline">Bulk Import</span>
            <span className="sm:hidden">Import</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onCreateOrder}
            className="flex items-center gap-1.5"
          >
            <ShoppingCart size={16} />
            <span className="hidden sm:inline">Create Order</span>
            <span className="sm:hidden">Order</span>
          </Button>
        </>
      }
      primaryActionLabel="Add Item"
      onPrimaryAction={onAddItem}
      primaryActionIcon={<Plus size={16} />}
    />
  );
};

export default InventoryHeader;
