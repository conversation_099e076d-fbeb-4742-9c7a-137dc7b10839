
import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ShoppingCart, Trash2, ExternalLink, CheckCircle, Archive, Package, Edit, Save, X, Undo } from 'lucide-react';
import { POStatus } from '@/types/inventory';
import { format } from 'date-fns';
import { toast } from 'sonner';
import PermissionGuard from '@/components/common/PermissionGuard';
import { PermissionType } from '@/types/auth';

interface PurchaseOrderDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  order: any;
  onDelete: (id: string) => void;
  onCheckout: (id: string) => void;
  onUpdateStatus: (id: string, status: POStatus) => void;
  onEditQuantity: (orderId: string, itemId: string, quantity: number) => void;
}

const PurchaseOrderDetailsDialog: React.FC<PurchaseOrderDetailsDialogProps> = ({
  isOpen,
  onClose,
  order,
  onDelete,
  onCheckout,
  onUpdateStatus,
  onEditQuantity,
}) => {
  const [editingItemId, setEditingItemId] = useState<string | null>(null);
  const [editedQuantity, setEditedQuantity] = useState<number>(0);
  const [amazonItems, setAmazonItems] = useState<any[]>([]);
  const [walmartItems, setWalmartItems] = useState<any[]>([]);
  const [targetItems, setTargetItems] = useState<any[]>([]);
  const [otherItems, setOtherItems] = useState<any[]>([]);

  const AMAZON_ASSOCIATE_TAG = 'stayfu-20';
  const WALMART_IMPACT_PUBLISHER_ID = '12345'; // Replace with your actual Impact Publisher ID
  const WALMART_AD_ID = '67890'; // Replace with your actual Ad ID
  const WALMART_IMPACT_CAMPAIGN_ID = '54321'; // Replace with your actual Impact Campaign ID
  const WALMART_SOURCE_ID = 'imp_000011112222333344'; // Replace with your actual Source ID
  const TARGET_AFFILIATE_ID = 'target123'; // Replace with your actual Target affiliate ID

  useEffect(() => {
    if (order && order.items) {
      const amazon: any[] = [];
      const walmart: any[] = [];
      const target: any[] = [];
      const other: any[] = [];

      order.items.forEach((item: any) => {
        if (item.amazon_url) {
          amazon.push(item);
        } else if (item.walmart_url) {
          walmart.push(item);
        } else if (item.target_url) {
          target.push(item);
        } else {
          other.push(item);
        }
      });

      setAmazonItems(amazon);
      setWalmartItems(walmart);
      setTargetItems(target);
      setOtherItems(other);
    }
  }, [order]);

  if (!order) return null;

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch {
      return dateString;
    }
  };

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this purchase order?')) {
      onDelete(order.id);
    }
  };

  const handleCheckout = () => {
    onCheckout(order.id);

    if (amazonItems.length > 0) {
      setTimeout(() => {
        window.open(generateAmazonCartUrl(), '_blank');
        toast.info('Amazon cart opened in a new tab');
      }, 500);
    }

    if (walmartItems.length > 0) {
      setTimeout(() => {
        window.open(generateWalmartCartUrl(), '_blank');
        toast.info('Walmart cart opened in a new tab');
      }, 1000);
    }

    if (targetItems.length > 0) {
      setTimeout(() => {
        window.open(generateTargetCartUrl(), '_blank');
        toast.info('Target cart opened in a new tab');
      }, 1500);
    }
  };

  const handleUpdateStatus = (status: POStatus) => {
    onUpdateStatus(order.id, status);
  };

  const handleRevertToPending = () => {
    onUpdateStatus(order.id, 'pending');
    toast.info('Order status changed to pending');
  };

  const handleEditQuantity = (itemId: string) => {
    const item = order.items.find((i: any) => i.id === itemId);
    if (item) {
      setEditingItemId(itemId);
      setEditedQuantity(item.quantity);
    }
  };

  const handleSaveQuantity = (itemId: string) => {
    onEditQuantity(order.id, itemId, editedQuantity);
    setEditingItemId(null);
  };

  const handleCancelEdit = () => {
    setEditingItemId(null);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      case 'ordered':
        return 'bg-yellow-100 text-yellow-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'archived':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const generateAmazonCartUrl = () => {
    if (!amazonItems.length) return 'https://www.amazon.com/cart';

    let url = 'https://www.amazon.com/gp/aws/cart/add.html?';

    amazonItems.forEach((item, index) => {
      let asin = '';
      if (item.amazon_url) {
        const asinMatch = item.amazon_url.match(/\/([A-Z0-9]{10})(?:\/|\?|$)/);
        asin = asinMatch ? asinMatch[1] : '';
      }

      if (!asin) {
        console.warn('Could not extract ASIN for item:', item.item_name);
        return;
      }

      url += `ASIN.${index + 1}=${asin}&Quantity.${index + 1}=${item.quantity}&`;
    });

    url += `AssociateTag=${AMAZON_ASSOCIATE_TAG}`;

    return url;
  };

  const generateWalmartCartUrl = () => {
    if (!walmartItems.length) return 'https://www.walmart.com/cart';

    let itemsParam = '';

    walmartItems.forEach((item, index) => {
      let itemId = '';
      if (item.walmart_url) {
        const itemIdMatch = item.walmart_url.match(/\/ip\/[^\/]+\/(\d+)/);
        itemId = itemIdMatch ? itemIdMatch[1] : '';
      }

      if (!itemId) {
        console.warn('Could not extract item ID for Walmart item:', item.item_name);
        return;
      }

      if (index > 0) itemsParam += ',';
      itemsParam += `${itemId}|${item.quantity}`;
    });

    const addToCartUrl = `https://affil.walmart.com/cart/addToCart?items=${itemsParam}&recipeIds=1`;

    const encodedAddToCartUrl = encodeURIComponent(addToCartUrl);

    return `https://goto.walmart.com/m/${WALMART_IMPACT_PUBLISHER_ID}/${WALMART_AD_ID}/${WALMART_IMPACT_CAMPAIGN_ID}?veh=aff&sourceid=${WALMART_SOURCE_ID}&u=${encodedAddToCartUrl}`;
  };

  const generateTargetCartUrl = () => {
    if (!targetItems.length) return 'https://www.target.com/cart';

    // Base URL for Target affiliate add to cart
    let url = `https://www.target.com/p/afid/${TARGET_AFFILIATE_ID}/cart?`;

    targetItems.forEach((item, index) => {
      let tcin = '';
      if (item.target_url) {
        // Extract TCIN (Target's product ID) from URL - typically in format /p/product-name/-/A-TCIN
        const tcinMatch = item.target_url.match(/\/A-(\d+)/);
        tcin = tcinMatch ? tcinMatch[1] : '';
      }

      if (!tcin) {
        console.warn('Could not extract TCIN for Target item:', item.item_name);
        return;
      }

      if (index > 0) url += '&';
      url += `items=${tcin}-${item.quantity}-0`;
    });

    return url;
  };

  const openAmazonCart = () => {
    window.open(generateAmazonCartUrl(), '_blank');
  };

  const openWalmartCart = () => {
    window.open(generateWalmartCartUrl(), '_blank');
  };

  const openTargetCart = () => {
    window.open(generateTargetCartUrl(), '_blank');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">Purchase Order Details</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 mt-2">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="font-medium text-lg">{order.property_name || 'Unknown Property'}</h3>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-sm text-muted-foreground">Created: {formatDate(order.created_at)}</span>
                <span className={`text-xs px-2 py-0.5 rounded-full ${getStatusBadgeClass(order.status)}`}>
                  {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                </span>
              </div>
            </div>
            <div className="text-right">
              <div className="font-medium text-lg">${order.total_price?.toFixed(2) || '0.00'}</div>
              <div className="text-sm text-muted-foreground">{order.items?.length || 0} items</div>
            </div>
          </div>

          {order.notes && (
            <div className="bg-muted/30 p-3 rounded-md">
              <h4 className="text-sm font-medium mb-1">Notes</h4>
              <p className="text-sm text-muted-foreground">{order.notes}</p>
            </div>
          )}

          <div>
            <h4 className="text-sm font-medium mb-2">Order Items</h4>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Item</TableHead>
                  <TableHead className="text-right">Quantity</TableHead>
                  <TableHead className="text-right">Price</TableHead>
                  <TableHead className="text-right">Total</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {order.items && order.items.length > 0 ? (
                  order.items.map((item: any) => (
                    <TableRow key={item.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{item.item_name}</div>
                          <div className="flex gap-2 mt-1">
                            {item.amazon_url && (
                              <a
                                href={item.amazon_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs text-blue-600 hover:underline flex items-center gap-0.5"
                              >
                                Amazon <ExternalLink size={10} />
                              </a>
                            )}
                            {item.walmart_url && (
                              <a
                                href={item.walmart_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs text-blue-600 hover:underline flex items-center gap-0.5"
                              >
                                Walmart <ExternalLink size={10} />
                              </a>
                            )}
                            {item.target_url && (
                              <a
                                href={item.target_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-xs text-blue-600 hover:underline flex items-center gap-0.5"
                              >
                                Target <ExternalLink size={10} />
                              </a>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        {editingItemId === item.id ? (
                          <input
                            type="number"
                            min="1"
                            value={editedQuantity}
                            onChange={(e) => setEditedQuantity(parseInt(e.target.value) || 1)}
                            className="w-16 p-1 border rounded text-right"
                            autoFocus
                          />
                        ) : (
                          item.quantity
                        )}
                      </TableCell>
                      <TableCell className="text-right">${item.price?.toFixed(2) || '0.00'}</TableCell>
                      <TableCell className="text-right">
                        ${((item.price || 0) * item.quantity).toFixed(2)}
                      </TableCell>
                      <TableCell className="text-right">
                        {editingItemId === item.id ? (
                          <div className="flex justify-end gap-1">
                            <PermissionGuard permission={PermissionType.MANAGE_PURCHASE_ORDERS}>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleSaveQuantity(item.id)}
                                className="h-7 w-7"
                              >
                                <Save size={14} />
                              </Button>
                            </PermissionGuard>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={handleCancelEdit}
                              className="h-7 w-7"
                            >
                              <X size={14} />
                            </Button>
                          </div>
                        ) : (
                          <PermissionGuard
                            permission={PermissionType.MANAGE_PURCHASE_ORDERS}
                            fallback={<div className="h-7 w-7"></div>}
                          >
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleEditQuantity(item.id)}
                              className="h-7 w-7"
                              disabled={order.status !== 'pending'}
                            >
                              <Edit size={14} />
                            </Button>
                          </PermissionGuard>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4 text-muted-foreground">
                      No items in this order
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {order.status === 'ordered' && (
            <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-md">
              <h4 className="text-sm font-medium mb-1 flex items-center gap-1.5">
                <ShoppingCart size={14} />
                Order in Progress
              </h4>
              <p className="text-sm text-muted-foreground mb-2">
                This order has been marked as ordered. Once all items have been delivered, mark it as delivered.
              </p>
              <div className="flex flex-wrap gap-2">
                {amazonItems.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={openAmazonCart}
                    className="text-xs"
                  >
                    Open Amazon Cart
                  </Button>
                )}
                {walmartItems.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={openWalmartCart}
                    className="text-xs"
                  >
                    Open Walmart Cart
                  </Button>
                )}
                {targetItems.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={openTargetCart}
                    className="text-xs"
                  >
                    Open Target Cart
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between items-center gap-2 pt-4 border-t">
          <div>
            <PermissionGuard permission={PermissionType.MANAGE_PURCHASE_ORDERS}>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDelete}
                className="flex items-center gap-1.5"
              >
                <Trash2 size={14} />
                Delete
              </Button>
            </PermissionGuard>
          </div>
          <div className="flex flex-wrap gap-2">
            {order.status === 'pending' && (
              <PermissionGuard permission={PermissionType.MANAGE_PURCHASE_ORDERS}>
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleCheckout}
                  className="flex items-center gap-1.5"
                >
                  <ShoppingCart size={14} />
                  Checkout
                </Button>
              </PermissionGuard>
            )}
            {order.status === 'ordered' && (
              <>
                <PermissionGuard permission={PermissionType.MANAGE_PURCHASE_ORDERS}>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => handleUpdateStatus('delivered')}
                    className="flex items-center gap-1.5"
                  >
                    <CheckCircle size={14} />
                    Mark Delivered
                  </Button>
                </PermissionGuard>
                <PermissionGuard permission={PermissionType.MANAGE_PURCHASE_ORDERS}>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRevertToPending}
                    className="flex items-center gap-1.5"
                  >
                    <Undo size={14} />
                    Return to Pending
                  </Button>
                </PermissionGuard>
              </>
            )}
            {order.status === 'delivered' && (
              <PermissionGuard permission={PermissionType.MANAGE_PURCHASE_ORDERS}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRevertToPending}
                  className="flex items-center gap-1.5"
                >
                  <Undo size={14} />
                  Return to Pending
                </Button>
              </PermissionGuard>
            )}
            {order.status !== 'archived' && (
              <PermissionGuard permission={PermissionType.MANAGE_PURCHASE_ORDERS}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleUpdateStatus('archived')}
                  className="flex items-center gap-1.5"
                >
                  <Archive size={14} />
                  Archive
                </Button>
              </PermissionGuard>
            )}
            {order.status === 'archived' && (
              <PermissionGuard permission={PermissionType.MANAGE_PURCHASE_ORDERS}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleUpdateStatus('pending')}
                  className="flex items-center gap-1.5"
                >
                  <Package size={14} />
                  Restore
                </Button>
              </PermissionGuard>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PurchaseOrderDetailsDialog;
