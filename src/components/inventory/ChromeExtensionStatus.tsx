import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle2, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { toast } from 'sonner';

const ChromeExtensionStatus = () => {
  const [status, setStatus] = useState<'checking' | 'not_installed' | 'not_connected' | 'connected'>('checking');

  useEffect(() => {
    checkExtensionStatus();
  }, []);

  const checkExtensionStatus = () => {
    setStatus('checking'); // Explicitly set checking status

    // Check 1: Chrome API availability
    if (typeof chrome === "undefined" || !chrome.runtime || !chrome.runtime.sendMessage) {
      console.log("ChromeExtensionStatus: Chrome runtime not available.");
      setStatus('not_installed');
      return;
    }

    // Check 2: Environment Variable for Extension ID
    const extensionId = import.meta.env.VITE_CHROME_EXTENSION_ID;
    console.log("ChromeExtensionStatus: VITE_CHROME_EXTENSION_ID value:", extensionId); // Log the ID value

    if (!extensionId || typeof extensionId !== 'string' || extensionId === 'YOUR_EXTENSION_ID_HERE' || extensionId.trim() === '') {
      console.error("ChromeExtensionStatus: Extension ID is missing, invalid, or placeholder in .env.local (VITE_CHROME_EXTENSION_ID).");
      setStatus('not_installed'); // Treat as not installed if ID is missing
      return;
    }

    // Check 3: Attempt to Ping the Extension
    console.log(`ChromeExtensionStatus: Attempting to ping extension with ID: ${extensionId}`);
    try {
      chrome.runtime.sendMessage(extensionId, { action: 'ping' }, (pingResponse) => {
        // Check for errors during the async callback
        if (chrome.runtime.lastError) {
          console.warn(`ChromeExtensionStatus: Extension ping failed (ID: ${extensionId}). Is it installed and enabled? Error:`, chrome.runtime.lastError.message);
          setStatus('not_installed');
        } else if (pingResponse?.success) {
          // Ping successful! Extension is installed.
          console.log("ChromeExtensionStatus: Extension ping successful. Checking connection...");

          // Check 4: Check Connection Status via Extension
          chrome.runtime.sendMessage(extensionId, { action: 'checkStayfuConnection' }, (connectionResponse) => {
            console.log("ChromeExtensionStatus: Received connectionResponse:", connectionResponse); // Log the full response object
            if (chrome.runtime.lastError) {
              console.error("ChromeExtensionStatus: Error during checkStayfuConnection callback:", chrome.runtime.lastError.message);
              setStatus('not_connected'); // Assume not connected on error
            } else if (connectionResponse && typeof connectionResponse.connected === 'boolean') {
               // Explicitly check if the property exists and is a boolean
              setStatus(connectionResponse.connected ? 'connected' : 'not_connected');
              console.log("ChromeExtensionStatus: Connection status set to:", connectionResponse.connected);
            } else {
              // Response received but malformed or missing 'connected' property
              console.warn("ChromeExtensionStatus: Received invalid connectionResponse object:", connectionResponse);
              setStatus('not_connected'); // Treat invalid response as not connected
            }
          });
        } else {
          // Ping responded, but not successfully (unexpected)
          console.warn("ChromeExtensionStatus: Extension ping responded without success:", pingResponse);
          setStatus('not_installed'); // Treat as not installed
        }
      });
    } catch (error) {
      // Catch synchronous errors during sendMessage setup (less common)
      console.error("ChromeExtensionStatus: Error initiating ping:", error);
      setStatus('not_installed'); // Treat synchronous errors as not installed
    }
  };

   // --- Helper function to open settings ---
   const openExtensionSettings = () => {
     try {
       // Check chrome API availability again just in case
       if (typeof chrome !== "undefined" && chrome.runtime?.getURL) {
          window.open(chrome.runtime.getURL('settings.html'), '_blank');
       } else {
          toast.error("Cannot open settings: Chrome API not available in this context.");
       }
     } catch (e) {
       console.error("Could not open settings page", e);
       toast.error("Failed to open extension settings.");
     }
  };

  const getStatusDisplay = () => {
    switch (status) {
      case 'checking':
        return (
          <Alert>
            <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2" />
            <AlertTitle>Checking Extension Status</AlertTitle>
            <AlertDescription>
              Verifying connection to the StayFu Chrome Extension...
            </AlertDescription>
          </Alert>
        );

      case 'not_installed':
         // Check if the ID is missing/placeholder as a specific reason
         const extensionId = import.meta.env.VITE_CHROME_EXTENSION_ID;
         const idMissing = !extensionId || typeof extensionId !== 'string' || extensionId === 'YOUR_EXTENSION_ID_HERE' || extensionId.trim() === '';
        return (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Extension Not Found</AlertTitle>
            <AlertDescription className="space-y-2">
               <p>
                 {idMissing
                   ? "Extension ID is not configured in the application environment. Please contact support."
                   : "The StayFu Chrome Extension was not detected. Please ensure it's installed and enabled in your browser."}
               </p>
               <p className="text-xs text-muted-foreground">You may need to refresh this page after installing or enabling the extension.</p>
            </AlertDescription>
          </Alert>
        );

      case 'not_connected':
        return (
          <Alert variant="default"> {/* Using default variant for warning */}
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Extension Not Connected</AlertTitle>
            <AlertDescription className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
               <span>
                 The extension is installed but not connected to your StayFu account.
               </span>
               <Button
                 variant="outline"
                 size="sm"
                 onClick={openExtensionSettings}
                 className="mt-2 sm:mt-0 sm:ml-4 flex-shrink-0" // Added flex-shrink-0
               >
                 <Settings className="mr-2 h-4 w-4" />
                 Open Extension Settings
               </Button>
             </AlertDescription>
          </Alert>
        );

      case 'connected':
        return (
          <Alert className="bg-green-50 border-green-600">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">Extension Connected</AlertTitle>
            <AlertDescription className="text-green-700">
              Ready to search and import Amazon products.
            </AlertDescription>
          </Alert>
        );
    }
  };

  return (
    <div className="mb-6">
      {getStatusDisplay()}
    </div>
  );
};

export default ChromeExtensionStatus;
