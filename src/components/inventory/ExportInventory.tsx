import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Download, FileText, Table } from 'lucide-react';
import { FormattedInventoryItem } from '@/types/inventory';
import { toast } from 'sonner';

interface ExportInventoryProps {
  items: FormattedInventoryItem[];
  filteredItems: FormattedInventoryItem[];
}

interface ExportOptions {
  format: 'csv' | 'json';
  includeImages: boolean;
  includeUrls: boolean;
  useFiltered: boolean;
  fields: {
    name: boolean;
    property: boolean;
    collection: boolean;
    quantity: boolean;
    minQuantity: boolean;
    price: boolean;
    amazonUrl: boolean;
    walmartUrl: boolean;
    imageUrl: boolean;
  };
}

const ExportInventory: React.FC<ExportInventoryProps> = ({
  items,
  filteredItems
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [options, setOptions] = useState<ExportOptions>({
    format: 'csv',
    includeImages: true,
    includeUrls: true,
    useFiltered: false,
    fields: {
      name: true,
      property: true,
      collection: true,
      quantity: true,
      minQuantity: true,
      price: true,
      amazonUrl: false,
      walmartUrl: false,
      imageUrl: false
    }
  });

  const exportData = () => {
    const dataToExport = options.useFiltered ? filteredItems : items;
    
    if (dataToExport.length === 0) {
      toast.error('No data to export');
      return;
    }

    try {
      if (options.format === 'csv') {
        exportToCSV(dataToExport);
      } else {
        exportToJSON(dataToExport);
      }
      
      toast.success(`Exported ${dataToExport.length} items successfully`);
      setIsOpen(false);
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export data');
    }
  };

  const exportToCSV = (data: FormattedInventoryItem[]) => {
    const headers: string[] = [];
    const fieldMap: { [key: string]: string } = {
      name: 'Name',
      property: 'Property',
      collection: 'Collection',
      quantity: 'Quantity',
      minQuantity: 'Min Quantity',
      price: 'Price',
      amazonUrl: 'Amazon URL',
      walmartUrl: 'Walmart URL',
      imageUrl: 'Image URL'
    };

    // Build headers based on selected fields
    Object.entries(options.fields).forEach(([field, selected]) => {
      if (selected) {
        headers.push(fieldMap[field]);
      }
    });

    // Build CSV content
    const csvContent = [
      headers.join(','),
      ...data.map(item => {
        const row: string[] = [];
        Object.entries(options.fields).forEach(([field, selected]) => {
          if (selected) {
            let value = '';
            switch (field) {
              case 'name':
                value = item.name || '';
                break;
              case 'property':
                value = item.propertyName || '';
                break;
              case 'collection':
                value = item.collection || '';
                break;
              case 'quantity':
                value = item.quantity?.toString() || '0';
                break;
              case 'minQuantity':
                value = item.minQuantity?.toString() || '0';
                break;
              case 'price':
                value = item.price?.toString() || '';
                break;
              case 'amazonUrl':
                value = item.amazonUrl || '';
                break;
              case 'walmartUrl':
                value = item.walmartUrl || '';
                break;
              case 'imageUrl':
                value = item.imageUrl || '';
                break;
            }
            // Escape commas and quotes in CSV
            if (value.includes(',') || value.includes('"')) {
              value = `"${value.replace(/"/g, '""')}"`;
            }
            row.push(value);
          }
        });
        return row.join(',');
      })
    ].join('\n');

    downloadFile(csvContent, 'inventory-export.csv', 'text/csv');
  };

  const exportToJSON = (data: FormattedInventoryItem[]) => {
    const exportData = data.map(item => {
      const exportItem: any = {};
      Object.entries(options.fields).forEach(([field, selected]) => {
        if (selected) {
          switch (field) {
            case 'name':
              exportItem.name = item.name;
              break;
            case 'property':
              exportItem.property = item.propertyName;
              break;
            case 'collection':
              exportItem.collection = item.collection;
              break;
            case 'quantity':
              exportItem.quantity = item.quantity;
              break;
            case 'minQuantity':
              exportItem.minQuantity = item.minQuantity;
              break;
            case 'price':
              exportItem.price = item.price;
              break;
            case 'amazonUrl':
              exportItem.amazonUrl = item.amazonUrl;
              break;
            case 'walmartUrl':
              exportItem.walmartUrl = item.walmartUrl;
              break;
            case 'imageUrl':
              exportItem.imageUrl = item.imageUrl;
              break;
          }
        }
      });
      return exportItem;
    });

    const jsonContent = JSON.stringify(exportData, null, 2);
    downloadFile(jsonContent, 'inventory-export.json', 'application/json');
  };

  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const toggleField = (field: keyof ExportOptions['fields']) => {
    setOptions(prev => ({
      ...prev,
      fields: {
        ...prev.fields,
        [field]: !prev.fields[field]
      }
    }));
  };

  const selectAllFields = () => {
    setOptions(prev => ({
      ...prev,
      fields: Object.keys(prev.fields).reduce((acc, key) => ({
        ...acc,
        [key]: true
      }), {} as ExportOptions['fields'])
    }));
  };

  const deselectAllFields = () => {
    setOptions(prev => ({
      ...prev,
      fields: Object.keys(prev.fields).reduce((acc, key) => ({
        ...acc,
        [key]: false
      }), {} as ExportOptions['fields'])
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Export
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Export Inventory</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          {/* Format Selection */}
          <div className="space-y-2">
            <Label>Export Format</Label>
            <div className="flex gap-2">
              <Button
                variant={options.format === 'csv' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setOptions(prev => ({ ...prev, format: 'csv' }))}
                className="flex items-center gap-2"
              >
                <Table className="h-4 w-4" />
                CSV
              </Button>
              <Button
                variant={options.format === 'json' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setOptions(prev => ({ ...prev, format: 'json' }))}
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                JSON
              </Button>
            </div>
          </div>

          {/* Data Selection */}
          <div className="space-y-2">
            <Label>Data to Export</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="useFiltered"
                  checked={options.useFiltered}
                  onCheckedChange={(checked) => setOptions(prev => ({ ...prev, useFiltered: !!checked }))}
                />
                <Label htmlFor="useFiltered" className="text-sm">
                  Export filtered items only ({filteredItems.length} items)
                </Label>
              </div>
              {!options.useFiltered && (
                <p className="text-xs text-muted-foreground">
                  Will export all {items.length} items
                </p>
              )}
            </div>
          </div>

          {/* Field Selection */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Fields to Include</Label>
              <div className="flex gap-1">
                <Button variant="ghost" size="sm" onClick={selectAllFields} className="text-xs">
                  All
                </Button>
                <Button variant="ghost" size="sm" onClick={deselectAllFields} className="text-xs">
                  None
                </Button>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(options.fields).map(([field, selected]) => (
                <div key={field} className="flex items-center space-x-2">
                  <Checkbox
                    id={field}
                    checked={selected}
                    onCheckedChange={() => toggleField(field as keyof ExportOptions['fields'])}
                  />
                  <Label htmlFor={field} className="text-sm capitalize">
                    {field.replace(/([A-Z])/g, ' $1').trim()}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button onClick={exportData}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ExportInventory;
