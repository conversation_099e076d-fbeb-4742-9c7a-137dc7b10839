
import React, { useState, useEffect } from 'react';
import { Package, ShoppingCart, AlertTriangle, Trash2, Image as ImageIcon } from 'lucide-react';
import GlassCard from '../ui/GlassCard';
import { cn } from '@/lib/utils';
import { FormattedInventoryItem } from '@/types/inventory';
import { Button } from '@/components/ui/button';
import InventoryImage from './InventoryImage';

interface InventoryCardProps {
  item: FormattedInventoryItem;
  onClick: () => void;
  selected?: boolean;
  showDelete?: boolean;
  onDeleteClick?: (e: React.MouseEvent) => void;
}

const InventoryCard: React.FC<InventoryCardProps> = ({
  item,
  onClick,
  selected = false,
  showDelete = false,
  onDeleteClick
}) => {
  const lowStock = item.quantity < item.minQuantity;

  // Handle delete button click
  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click
    if (onDeleteClick) {
      onDeleteClick(e);
    }
  };

  return (
    <GlassCard
      onClick={onClick}
      className={cn(
        "relative transition-all transform cursor-pointer hover:shadow-md",
        selected && "ring-2 ring-primary",
        "h-full flex flex-col"
      )}
    >
      {/* Image section */}
      <div className="relative w-full aspect-square bg-slate-100 rounded-t-lg overflow-hidden">
        <InventoryImage
          imageUrl={item.imageUrl}
          amazonUrl={item.amazonUrl}
          itemName={item.name}
          className="w-full h-full object-contain p-2"
          size="lg"
        />

        {/* Low stock indicator */}
        {lowStock && (
          <div className="absolute bottom-2 right-2 bg-red-100 text-red-600 p-1 rounded-full">
            <AlertTriangle className="h-5 w-5" />
          </div>
        )}

        {/* Delete button */}
        {showDelete && (
          <Button
            size="icon"
            variant="destructive"
            className="absolute top-2 right-2 h-8 w-8 rounded-full opacity-90"
            onClick={handleDeleteClick}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Content section */}
      <div className="p-4 flex-grow flex flex-col">
        <h3 className="font-medium line-clamp-2">{item.name}</h3>

        <div className="mt-2 flex items-center justify-between">
          {/* Quantity display */}
          <div className="flex items-center">
            <div className={cn(
              "px-2 py-0.5 rounded text-sm",
              lowStock ? "bg-red-100 text-red-700" : "bg-green-100 text-green-700"
            )}>
              {item.quantity} {item.quantity === 1 ? 'item' : 'items'}
            </div>
          </div>

          {/* Price display */}
          {item.price !== undefined && item.price > 0 && (
            <div className="text-sm font-medium text-slate-700">
              ${item.price.toFixed(2)}
            </div>
          )}
        </div>

        {/* External links */}
        <div className="mt-3 flex items-center gap-2">
          {/* Amazon link */}
          {item.amazonUrl && (
            <a
              href={item.amazonUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-orange-500 hover:text-orange-700 transition-colors"
              onClick={(e) => e.stopPropagation()}
            >
              <img
                src="/amazon-icon.svg"
                alt="Amazon"
                className="h-4 w-auto"
              />
            </a>
          )}

          {/* Walmart link */}
          {item.walmartUrl && (
            <a
              href={item.walmartUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 hover:text-blue-700 transition-colors"
              onClick={(e) => e.stopPropagation()}
            >
              <img
                src="/walmart-icon.svg"
                alt="Walmart"
                className="h-4 w-auto"
              />
            </a>
          )}
        </div>

        {/* Collection tag */}
        {item.collection && (
          <div className="mt-3 pt-2 border-t border-slate-100">
            <span className="text-xs text-slate-500">
              {item.collection}
            </span>
          </div>
        )}
      </div>
    </GlassCard>
  );
};

export default InventoryCard;
