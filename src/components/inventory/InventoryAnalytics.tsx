import React from 'react';
import { FormattedInventoryItem } from '@/types/inventory';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  Package, 
  DollarSign,
  BarChart3,
  PieChart
} from 'lucide-react';

interface InventoryAnalyticsProps {
  items: FormattedInventoryItem[];
  isLoading?: boolean;
}

const InventoryAnalytics: React.FC<InventoryAnalyticsProps> = ({
  items,
  isLoading = false
}) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="h-5 bg-muted rounded animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="h-4 bg-muted rounded animate-pulse"></div>
                <div className="h-4 bg-muted rounded animate-pulse w-3/4"></div>
                <div className="h-4 bg-muted rounded animate-pulse w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Calculate analytics
  const totalItems = items.length;
  const lowStockItems = items.filter(item => item.quantity < item.minQuantity);
  const outOfStockItems = items.filter(item => item.quantity === 0);
  const totalValue = items.reduce((sum, item) => sum + (item.price || 0) * item.quantity, 0);
  
  // Property distribution
  const propertyDistribution = items.reduce((acc, item) => {
    const property = item.propertyName || 'Unknown';
    acc[property] = (acc[property] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Collection distribution
  const collectionDistribution = items.reduce((acc, item) => {
    const collection = item.collection || 'Other';
    acc[collection] = (acc[collection] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Stock status distribution
  const stockStatusDistribution = {
    'In Stock': items.filter(item => item.quantity >= item.minQuantity).length,
    'Low Stock': lowStockItems.length,
    'Out of Stock': outOfStockItems.length
  };

  // Top collections by value
  const collectionValues = items.reduce((acc, item) => {
    const collection = item.collection || 'Other';
    const value = (item.price || 0) * item.quantity;
    acc[collection] = (acc[collection] || 0) + value;
    return acc;
  }, {} as Record<string, number>);

  const topCollectionsByValue = Object.entries(collectionValues)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5);

  // Stock health percentage
  const stockHealthPercentage = totalItems > 0 
    ? Math.round(((totalItems - lowStockItems.length) / totalItems) * 100)
    : 100;

  return (
    <div className="space-y-6">
      {/* Stock Health Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Stock Health Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Stock Health</span>
              <span className="text-2xl font-bold">{stockHealthPercentage}%</span>
            </div>
            <Progress value={stockHealthPercentage} className="h-2" />
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {stockStatusDistribution['In Stock']}
                </div>
                <div className="text-xs text-muted-foreground">In Stock</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-yellow-600">
                  {stockStatusDistribution['Low Stock']}
                </div>
                <div className="text-xs text-muted-foreground">Low Stock</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-red-600">
                  {stockStatusDistribution['Out of Stock']}
                </div>
                <div className="text-xs text-muted-foreground">Out of Stock</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Property Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              Items by Property
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(propertyDistribution)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 5)
                .map(([property, count]) => {
                  const percentage = Math.round((count / totalItems) * 100);
                  return (
                    <div key={property} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="text-sm font-medium truncate max-w-32">
                          {property}
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {count}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <Progress value={percentage} className="w-16 h-2" />
                        <span className="text-xs text-muted-foreground w-8">
                          {percentage}%
                        </span>
                      </div>
                    </div>
                  );
                })}
            </div>
          </CardContent>
        </Card>

        {/* Collection Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Items by Collection
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(collectionDistribution)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 5)
                .map(([collection, count]) => {
                  const percentage = Math.round((count / totalItems) * 100);
                  return (
                    <div key={collection} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="text-sm font-medium truncate max-w-32">
                          {collection}
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {count}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <Progress value={percentage} className="w-16 h-2" />
                        <span className="text-xs text-muted-foreground w-8">
                          {percentage}%
                        </span>
                      </div>
                    </div>
                  );
                })}
            </div>
          </CardContent>
        </Card>

        {/* Top Collections by Value */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Top Collections by Value
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topCollectionsByValue.map(([collection, value]) => (
                <div key={collection} className="flex items-center justify-between">
                  <div className="text-sm font-medium truncate max-w-32">
                    {collection}
                  </div>
                  <div className="text-sm font-bold">
                    ${value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Low Stock Alerts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              Low Stock Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            {lowStockItems.length === 0 ? (
              <div className="text-center py-4">
                <div className="text-green-600 font-medium">All items are well stocked!</div>
                <div className="text-xs text-muted-foreground">No low stock alerts</div>
              </div>
            ) : (
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {lowStockItems.slice(0, 10).map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-2 bg-yellow-50 rounded">
                    <div>
                      <div className="text-sm font-medium truncate max-w-32">
                        {item.name}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {item.propertyName} • {item.collection}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-bold text-red-600">
                        {item.quantity}/{item.minQuantity}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Current/Min
                      </div>
                    </div>
                  </div>
                ))}
                {lowStockItems.length > 10 && (
                  <div className="text-xs text-muted-foreground text-center pt-2">
                    And {lowStockItems.length - 10} more items...
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default InventoryAnalytics;
