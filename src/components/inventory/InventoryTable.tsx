
import React, { useState, useEffect } from 'react';
import { FormattedInventoryItem } from '@/types/inventory';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Trash } from 'lucide-react';
import InventoryImage from './InventoryImage';

interface InventoryTableProps {
  items: FormattedInventoryItem[];
  onItemClick: (item: FormattedInventoryItem) => void;
  onDeleteItems: (itemIds: string[]) => void;
}

const InventoryTable: React.FC<InventoryTableProps> = ({
  items,
  onItemClick,
  onDeleteItems,
}) => {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  const toggleSelectAll = () => {
    if (selectedItems.length === items.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(items.map(item => item.id));
    }
  };

  const toggleSelectItem = (itemId: string) => {
    if (selectedItems.includes(itemId)) {
      setSelectedItems(selectedItems.filter(id => id !== itemId));
    } else {
      setSelectedItems([...selectedItems, itemId]);
    }
  };

  const handleDeleteSelected = () => {
    if (selectedItems.length > 0) {
      console.log(`[InventoryTable] Deleting ${selectedItems.length} selected items:`, selectedItems);
      onDeleteItems(selectedItems);
      setSelectedItems([]);
    }
  };

  return (
    <div>
      {selectedItems.length > 0 && (
        <div className="flex justify-between items-center mb-4 p-3 bg-muted/50 rounded-lg border">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">
              {selectedItems.length} item{selectedItems.length === 1 ? '' : 's'} selected
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedItems([])}
              className="text-xs"
            >
              Clear Selection
            </Button>
          </div>
          <Button variant="destructive" size="sm" onClick={handleDeleteSelected}>
            <Trash className="h-4 w-4 mr-2" />
            Delete Selected ({selectedItems.length})
          </Button>
        </div>
      )}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={items.length > 0 && selectedItems.length === items.length}
                  onCheckedChange={toggleSelectAll}
                  aria-label="Select all"
                />
              </TableHead>
              <TableHead>Image</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Property</TableHead>
              <TableHead>Collection</TableHead>
              <TableHead className="text-right">Quantity</TableHead>
              <TableHead className="text-right">Min Quantity</TableHead>
              <TableHead className="text-right">Price</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {items.map((item) => (
              <TableRow
                key={item.id}
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => onItemClick(item)}
              >
                <TableCell className="p-2" onClick={(e) => {
                  e.stopPropagation();
                }}>
                  <Checkbox
                    checked={selectedItems.includes(item.id)}
                    onCheckedChange={() => toggleSelectItem(item.id)}
                    aria-label={`Select ${item.name}`}
                  />
                </TableCell>
                <TableCell className="p-2">
                  <InventoryImage
                    imageUrl={item.imageUrl}
                    amazonUrl={item.amazonUrl}
                    itemName={item.name}
                    size="sm"
                  />
                </TableCell>
                <TableCell>{item.name}</TableCell>
                <TableCell>{item.propertyName}</TableCell>
                <TableCell>{item.collection}</TableCell>
                <TableCell className="text-right">
                  <span className={item.quantity < item.minQuantity ? "text-destructive font-bold" : ""}>
                    {item.quantity}
                  </span>
                </TableCell>
                <TableCell className="text-right">{item.minQuantity}</TableCell>
                <TableCell className="text-right">{item.price ? `$${item.price.toFixed(2)}` : '-'}</TableCell>
              </TableRow>
            ))}
            {items.length === 0 && (
              <TableRow>
                <TableCell colSpan={8} className="h-24 text-center">
                  No results found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default InventoryTable;
