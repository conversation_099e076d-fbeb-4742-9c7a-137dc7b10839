import React, { useEffect } from 'react';

interface ForceCloseDialogProps {
  isOpen: boolean;
  onClose: () => void;
  timeout?: number;
}

/**
 * A component that forces a dialog to close after a specified timeout
 * This is a failsafe mechanism to ensure dialogs don't get stuck open
 */
const ForceCloseDialog: React.FC<ForceCloseDialogProps> = ({ 
  isOpen, 
  onClose, 
  timeout = 5000 // Default 5 seconds
}) => {
  useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(() => {
        console.log('ForceCloseDialog: Forcing dialog to close after timeout');
        onClose();
      }, timeout);
      
      return () => clearTimeout(timer);
    }
  }, [isOpen, onClose, timeout]);
  
  return null; // This component doesn't render anything
};

export default ForceCloseDialog;
