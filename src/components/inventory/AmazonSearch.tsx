import React, { useState, useEffect, useMemo } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { supabase } from '@/integrations/supabase/client';
import { Search, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AmazonProduct } from './types';
// import AmazonProductCard from './AmazonProductCard';
// import { useInventoryOperations } from '@/hooks/useInventoryOperations';
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
// import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ExtensionImportProduct } from './types';
// import ChromeExtensionStatus from './ChromeExtensionStatus';

const AmazonSearch = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [rawProperties, setRawProperties] = useState<{id: string, name: string}[]>([]);
  const [collections, setCollections] = useState<string[]>([]);
  const [searchResults, setSearchResults] = useState<AmazonProduct[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);
  const [isExtensionInstalled, setIsExtensionInstalled] = useState(false);
  const [isExtensionConnected, setIsExtensionConnected] = useState(false);
  const [isCheckingExtension, setIsCheckingExtension] = useState(true);
  const [importStep, setImportStep] = useState<'search' | 'results' | 'import'>('search');
  const [selectedImportProperties, setSelectedImportProperties] = useState<{[asin: string]: string}>({});
  const [selectedImportCollections, setSelectedImportCollections] = useState<{[asin: string]: string}>({});
  const [selectedImportQuantities, setSelectedImportQuantities] = useState<{[asin: string]: number}>({});
  const [selectedImportMinQuantities, setSelectedImportMinQuantities] = useState<{[asin: string]: number}>({});

  // Deduplicate properties to prevent duplicates in the dropdown
  const properties = useMemo(() => {
    // Create a map to store unique properties by name
    const uniquePropertiesByName = new Map<string, { id: string; name: string }>();

    // Process properties to keep only one entry per property name
    // We'll clean the property name by removing user info in parentheses
    for (const property of rawProperties) {
      // Extract the base property name without the user info in parentheses
      const baseName = property.name.replace(/\s*\([^)]*\)\s*$/, '').trim();
      const key = baseName.toLowerCase();

      if (!uniquePropertiesByName.has(key)) {
        // Store with the original name but keyed by the base name
        uniquePropertiesByName.set(key, {
          id: property.id,
          name: baseName // Use the cleaned name without user info
        });
      }
    }

    // Convert the map values to an array
    const uniqueProperties = Array.from(uniquePropertiesByName.values());

    console.log(`[AmazonSearch] Deduplicated properties: ${uniqueProperties.length} (from ${rawProperties.length})`);
    return uniqueProperties;
  }, [rawProperties]);

  // Fetch properties and collections
  useEffect(() => {
    const fetchProperties = async () => {
      const { data, error } = await supabase
        .from('properties')
        .select('id, name')
        .order('name');

      if (error) {
        console.error('Error fetching properties:', error);
        return;
      }

      setRawProperties(data as {id: string, name: string}[] || []);
    };

    const fetchCollections = async () => {
      try {
        const { data, error } = await supabase
          .from('inventory_items')
          .select('collection')
          .order('collection', { ascending: true });

        if (error) {
          console.error('Error fetching collections:', error);
          return;
        }

        // Extract unique collections
        const uniqueCollections = new Set<string>();
        data.forEach((item: { collection: string }) => {
          if (item.collection) uniqueCollections.add(item.collection);
        });

        setCollections(Array.from(uniqueCollections));
      } catch (error) {
        console.error('Error in fetchCollections:', error);
      }
    };

    fetchProperties();
    fetchCollections();
  }, []);

  // Check for extension on component mount
  useEffect(() => {
    checkExtension();

    // Listen for search results from the extension
    const handleSearchResults = (event: MessageEvent) => {
      if (event.data.type === 'STAYFU_SEARCH_RESULTS') {
        console.log('Received search results from extension:', event.data);
        handleSearchResultsReceived(event.data.searchTerm, event.data.products);
      }
    };

    window.addEventListener('message', handleSearchResults);

    return () => {
      window.removeEventListener('message', handleSearchResults);
    };
  }, []);

  // Handle search results received from extension
  const handleSearchResultsReceived = (searchTerm: string, products: any[]) => {
    setIsLoading(false);

    if (!products || products.length === 0) {
      toast.error(`No products found for "${searchTerm}"`);
      return;
    }

    console.log(`AmazonSearch: Received ${products.length} products for "${searchTerm}"`);

    // Format products for display
    const formattedProducts: AmazonProduct[] = products.map(product => ({
      asin: product.asin,
      title: product.title,
      price: product.price,
      numericPrice: product.numericPrice,
      rating: product.rating,
      reviewCount: product.reviewCount,
      img: product.img,
      isPrime: product.isPrime,
      searchTerm: product.searchTerm,
      url: product.url
    }));

    setSearchResults(formattedProducts);
    setImportStep('results');
    toast.success(`Found ${formattedProducts.length} products for "${searchTerm}"`);
  };

  // Check if the extension is installed and connected
  const checkExtension = async () => {
    setIsCheckingExtension(true);

    try {
      // Check 1: Check if chrome.runtime is available
      if (!window.chrome || !chrome.runtime) {
        console.warn('AmazonSearch: Chrome runtime not available. Is this a Chrome browser?');
        setIsExtensionInstalled(false);
        setIsCheckingExtension(false);
        return;
      }

      // Check 2: Check if extension ID is configured
      const extensionId = import.meta.env.VITE_CHROME_EXTENSION_ID;
      if (!extensionId || typeof extensionId !== 'string' || extensionId === 'YOUR_EXTENSION_ID_HERE' || extensionId.trim() === '') {
        console.warn('AmazonSearch: Extension ID not configured in environment variables');
        setIsExtensionInstalled(false);
        setIsCheckingExtension(false);
        return;
      }

      // Check 3: Attempt to Ping the Extension
      console.log(`AmazonSearch: Attempting to ping extension with ID: ${extensionId}`);
      try {
        chrome.runtime.sendMessage(extensionId, { action: 'ping' }, (pingResponse) => {
          if (chrome.runtime.lastError) {
            console.warn(`AmazonSearch: Extension ping failed (ID: ${extensionId}). Is it installed and enabled? Error:`, chrome.runtime.lastError.message);
            setIsExtensionInstalled(false);
            setIsCheckingExtension(false);
          } else if (pingResponse?.success) {
            // Ping successful! Extension is installed.
            setIsExtensionInstalled(true);
            console.log("AmazonSearch: Extension ping successful. Checking connection...");

            // Check connection to app
            chrome.runtime.sendMessage(extensionId, { action: 'checkStayfuConnection' }, (connectionResponse) => {
              if (chrome.runtime.lastError) {
                console.warn("AmazonSearch: Extension connection check failed:", chrome.runtime.lastError.message);
                setIsExtensionConnected(false);
                setIsCheckingExtension(false);
              } else {
                console.log("AmazonSearch: Connection check response:", connectionResponse);
                if (connectionResponse?.connected === true) {
                  setIsExtensionConnected(true);
                } else {
                  setIsExtensionConnected(false);
                  console.warn("AmazonSearch: Extension not connected to app. Response:", connectionResponse);
                }
                setIsCheckingExtension(false);
              }
            });
          } else {
            console.warn("AmazonSearch: Extension ping returned unsuccessful response:", pingResponse);
            setIsExtensionInstalled(false);
            setIsCheckingExtension(false);
          }
        });
      } catch (error) {
        console.error("AmazonSearch: Error pinging extension:", error);
        setIsExtensionInstalled(false);
        setIsCheckingExtension(false);
      }
    } catch (error) {
      console.error("AmazonSearch: Error checking extension:", error);
      setIsExtensionInstalled(false);
      setIsCheckingExtension(false);
    }
  };

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) {
      toast.error('Please enter a search query');
      return;
    }

    if (!isExtensionInstalled) {
      toast.error('StayFu Chrome extension is not installed or active.');
      return;
    }

    setIsLoading(true);
    setSearchResults([]);

    const extensionId = import.meta.env.VITE_CHROME_EXTENSION_ID;
    if (!extensionId || typeof extensionId !== 'string' || extensionId === 'YOUR_EXTENSION_ID_HERE' || extensionId.trim() === '') {
       toast.error("Extension ID configuration missing. Cannot initiate search.");
       setIsLoading(false);
       return;
    }

    // Set a timeout to reset loading state if no results are received
    const searchTimeout = setTimeout(() => {
      if (isLoading) {
        console.warn('AmazonSearch: Search timeout reached, no results received');
        toast.error('Search timed out. Please try again or check the extension.');
        setIsLoading(false);
      }
    }, 30000); // 30 second timeout

    console.log(`AmazonSearch: Sending 'startSearch' to extension ID: ${extensionId}`);
    try {
      chrome.runtime.sendMessage(extensionId,
        {
          action: 'startSearch',
          searchTerm: searchQuery
        },
        (response: any) => {
          if (chrome.runtime.lastError) {
            console.error('Extension search error:', chrome.runtime.lastError);
            toast.error(`Search failed: ${chrome.runtime.lastError.message || 'Communication error with extension'}`);
            setIsLoading(false);
            clearTimeout(searchTimeout);
            return;
          }

          if (response?.error) {
            toast.error(`Search failed: ${response.error}`);
            setIsLoading(false);
            clearTimeout(searchTimeout);
          } else if (response?.success) {
            toast.success(`Search initiated for "${searchQuery}". Results will appear shortly.`);
            // Don't set isLoading to false here - we'll wait for results
            // The timeout will handle the case where no results are received
          } else {
             toast.warning('Received an unexpected response from the extension after search initiation.');
             setIsLoading(false);
             clearTimeout(searchTimeout);
          }
        }
      );
    } catch (error) {
      console.error('Error initiating Amazon search:', error);
      toast.error('Failed to initiate search. Please check the extension and browser console.');
      setIsLoading(false);
      clearTimeout(searchTimeout);
    }
  };

  // Handle product selection
  const toggleProductSelection = (asin: string) => {
    const newSelection = new Set(selectedProducts);
    if (newSelection.has(asin)) {
      newSelection.delete(asin);
    } else {
      newSelection.add(asin);
    }
    setSelectedProducts(newSelection);
  };

  // Handle import button click
  const handleImport = () => {
    if (selectedProducts.size === 0) {
      toast.error('Please select at least one product to import');
      return;
    }

    setImportStep('import');

    // Initialize import properties for selected products
    const selectedProductsArray = Array.from(selectedProducts);
    const initialProperties: {[asin: string]: string} = {};
    const initialCollections: {[asin: string]: string} = {};
    const initialQuantities: {[asin: string]: number} = {};
    const initialMinQuantities: {[asin: string]: number} = {};

    selectedProductsArray.forEach(asin => {
      initialProperties[asin] = '';
      initialCollections[asin] = 'none'; // Use 'none' instead of empty string
      initialQuantities[asin] = 1;
      initialMinQuantities[asin] = 0;
    });

    setSelectedImportProperties(initialProperties);
    setSelectedImportCollections(initialCollections);
    setSelectedImportQuantities(initialQuantities);
    setSelectedImportMinQuantities(initialMinQuantities);
  };

  // Handle final import submission
  const handleFinalImport = () => {
    // Validate that all selected products have a property assigned
    const selectedProductsArray = Array.from(selectedProducts);
    const missingProperties = selectedProductsArray.filter(asin => !selectedImportProperties[asin]);

    if (missingProperties.length > 0) {
      toast.error(`Please assign a property to all selected products (${missingProperties.length} missing)`);
      return;
    }

    // Prepare import data
    const importItems: ExtensionImportProduct[] = selectedProductsArray.map(asin => {
      const product = searchResults.find(p => p.asin === asin);
      if (!product) return null;

      const propertyId = selectedImportProperties[asin];
      const property = properties.find(p => p.id === propertyId);

      // Convert 'none' to empty string for collection
      const collection = selectedImportCollections[asin] === 'none' ? '' : selectedImportCollections[asin];

      // Ensure all values are of the correct type
      const amazonUrl = String(product.url || `https://www.amazon.com/dp/${product.asin}`);
      const imageUrl = String(product.img || `https://via.placeholder.com/300x300?text=${encodeURIComponent(product.title || 'Product')}`);

      console.log(`Preparing import for ${product.title}:`);
      console.log(`- Amazon URL: ${amazonUrl}`);
      console.log(`- Image URL: ${imageUrl}`);

      return {
        name: String(product.title || 'Unnamed Product'),
        propertyId: String(propertyId || ''),
        propertyName: String(property?.name || 'Unknown Property'),
        collection: String(collection || ''),
        quantity: Number(selectedImportQuantities[asin] || 1),
        minQuantity: Number(selectedImportMinQuantities[asin] || 0),
        price: Number(product.numericPrice || 0),
        // Ensure we have a valid Amazon URL
        amazonUrl: amazonUrl,
        // Skip image processing
        hasProcessedImage: true,
        // Ensure we have a valid image URL
        imageUrl: imageUrl
      };
    }).filter(Boolean) as ExtensionImportProduct[];

    // Import the items
    if (importItems.length > 0) {
      try {
        // Stringify and parse to ensure we're not passing any complex objects
        const safeImportItems = JSON.parse(JSON.stringify(importItems));

        // Pass the items to the Inventory component via window message
        window.postMessage({
          type: "STAYFU_IMPORT_FROM_EXTENSION",
          data: safeImportItems,
          isExtensionImport: true
        }, window.location.origin);

        // Show a more detailed success message
        toast.success(`${importItems.length} products sent for import. Please wait while they are processed.`);

        // Close the import dialog immediately to avoid hanging
        // Reset state
        setImportStep('search');
        setSearchResults([]);
        setSelectedProducts(new Set());
      } catch (error) {
        console.error('Error preparing import data:', error);
        toast.error('Error preparing import data. Please try again.');
      }
    } else {
      toast.error('No valid products to import');
    }
  };

  // Render search form
  const renderSearchForm = () => {
    return (
      <form onSubmit={handleSearch} className="space-y-4 p-6 bg-card rounded-lg border shadow-sm">
        <div className="space-y-2">
          <Label htmlFor="search">Search Query</Label>
          <Input
            id="search"
            placeholder="Enter product name or keywords..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            disabled={isLoading}
          />
        </div>

        <Button
          type="submit"
          className="w-full sm:w-auto flex items-center gap-2"
          disabled={isLoading || !searchQuery.trim()}
        >
          {isLoading ? (
            <>
              <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
              Searching...
            </>
          ) : (
            <>
              <Search className="h-4 w-4" />
              Search Amazon
            </>
          )}
        </Button>
      </form>
    );
  };

  // Render search results
  const renderSearchResults = () => {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">
            Search Results ({searchResults.length} products)
          </h3>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => {
                setImportStep('search');
                setSearchResults([]);
                setSelectedProducts(new Set());
              }}
            >
              New Search
            </Button>
            <Button
              onClick={handleImport}
              disabled={selectedProducts.size === 0}
            >
              Import Selected ({selectedProducts.size})
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {searchResults.map((product) => (
            <Card key={product.asin} className={`overflow-hidden ${selectedProducts.has(product.asin) ? 'ring-2 ring-primary' : ''}`}>
              <div className="absolute top-2 right-2 z-10">
                <Checkbox
                  checked={selectedProducts.has(product.asin)}
                  onCheckedChange={() => toggleProductSelection(product.asin)}
                />
              </div>
              <div className="aspect-video relative">
                <img
                  src={product.img}
                  alt={product.title}
                  className="w-full h-full object-contain"
                />
              </div>
              <CardContent className="p-4">
                <h3 className="font-medium line-clamp-2 mb-2">{product.title}</h3>
                <div className="flex justify-between items-center">
                  <span className="text-lg font-bold">{product.price}</span>
                  {product.isPrime && (
                    <Badge variant="outline">Prime</Badge>
                  )}
                </div>
                <div className="text-sm text-muted-foreground mt-1">
                  {product.rating} stars ({product.reviewCount} reviews)
                </div>
              </CardContent>
              <CardFooter className="p-4 pt-0 flex justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(product.url, '_blank')}
                >
                  View on Amazon
                </Button>
                <Button
                  size="sm"
                  onClick={() => toggleProductSelection(product.asin)}
                >
                  {selectedProducts.has(product.asin) ? 'Deselect' : 'Select'}
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    );
  };

  // Render import form
  const renderImportForm = () => {
    const selectedProductsArray = Array.from(selectedProducts);

    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">
            Import Products ({selectedProductsArray.length} selected)
          </h3>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setImportStep('results')}
            >
              Back to Results
            </Button>
            <Button
              onClick={handleFinalImport}
            >
              Complete Import
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          {selectedProductsArray.map(asin => {
            const product = searchResults.find(p => p.asin === asin);
            if (!product) return null;

            return (
              <Card key={asin} className="overflow-hidden">
                <div className="flex flex-col md:flex-row">
                  <div className="w-full md:w-1/4 p-4">
                    <img
                      src={product.img}
                      alt={product.title}
                      className="w-full h-auto object-contain"
                    />
                  </div>
                  <div className="w-full md:w-3/4 p-4">
                    <h3 className="font-medium mb-2">{product.title}</h3>
                    <p className="text-lg font-bold mb-4">{product.price}</p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor={`property-${asin}`}>Property *</Label>
                        <Select
                          value={selectedImportProperties[asin]}
                          onValueChange={(value) => {
                            setSelectedImportProperties({
                              ...selectedImportProperties,
                              [asin]: value
                            });
                          }}
                        >
                          <SelectTrigger id={`property-${asin}`}>
                            <SelectValue placeholder="Select a property" />
                          </SelectTrigger>
                          <SelectContent>
                            {properties.map(property => (
                              <SelectItem key={property.id} value={property.id}>
                                {property.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`collection-${asin}`}>Collection (Optional)</Label>
                        <Select
                          value={selectedImportCollections[asin]}
                          onValueChange={(value) => {
                            setSelectedImportCollections({
                              ...selectedImportCollections,
                              [asin]: value
                            });
                          }}
                        >
                          <SelectTrigger id={`collection-${asin}`}>
                            <SelectValue placeholder="Select a collection" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">None</SelectItem>
                            {collections.map(collection => (
                              <SelectItem key={collection} value={collection}>
                                {collection}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`quantity-${asin}`}>Quantity</Label>
                        <Input
                          id={`quantity-${asin}`}
                          type="number"
                          min="1"
                          value={selectedImportQuantities[asin] || 1}
                          onChange={(e) => {
                            setSelectedImportQuantities({
                              ...selectedImportQuantities,
                              [asin]: parseInt(e.target.value) || 1
                            });
                          }}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`min-quantity-${asin}`}>Min Quantity</Label>
                        <Input
                          id={`min-quantity-${asin}`}
                          type="number"
                          min="0"
                          value={selectedImportMinQuantities[asin] || 0}
                          onChange={(e) => {
                            setSelectedImportMinQuantities({
                              ...selectedImportMinQuantities,
                              [asin]: parseInt(e.target.value) || 0
                            });
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </div>
    );
  };

  // Render content based on extension status and current step
  const renderContent = () => {
    if (isCheckingExtension) {
      return (
        <Alert>
          <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full mr-2" />
          <AlertDescription>
            Checking for StayFu Chrome Extension...
          </AlertDescription>
        </Alert>
      );
    }

    if (!isExtensionInstalled) {
      // Check if the ID is missing/placeholder as a specific reason
      const extensionId = import.meta.env.VITE_CHROME_EXTENSION_ID;
      const idMissing = !extensionId || typeof extensionId !== 'string' || extensionId === 'YOUR_EXTENSION_ID_HERE' || extensionId.trim() === '';

      return (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Extension Not Found</AlertTitle>
          <AlertDescription className="space-y-2">
             <p>
               {idMissing
                 ? "Extension ID is not configured in the application environment. Please contact support."
                 : "The StayFu Chrome Extension was not detected. Please ensure it's installed and enabled in your browser."}
             </p>
             <div className="pt-2">
               <Button
                 variant="outline"
                 size="sm"
                 onClick={checkExtension}
                 className="mr-2"
               >
                 Check Again
               </Button>
               <Button
                 variant="outline"
                 size="sm"
                 onClick={() => window.open('https://docs.stayfu.com/extension', '_blank')}
               >
                 Installation Guide
               </Button>
             </div>
          </AlertDescription>
        </Alert>
      );
    }

    if (!isExtensionConnected) {
      return (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Extension Not Connected</AlertTitle>
          <AlertDescription className="space-y-2">
            <p>
              The StayFu Chrome Extension is installed but not properly connected to the application.
              Please open the extension settings and configure the correct URL.
            </p>
            <div className="pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={checkExtension}
                className="mr-2"
              >
                Check Again
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  if (chrome.runtime.openOptionsPage) {
                    chrome.runtime.openOptionsPage();
                  } else {
                    window.open(chrome.runtime.getURL('options.html'));
                  }
                }}
              >
                Open Extension Settings
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      );
    }

    // Extension is installed and connected, show the appropriate step
    if (importStep === 'search') {
      return renderSearchForm();
    } else if (importStep === 'results') {
      return renderSearchResults();
    } else if (importStep === 'import') {
      return renderImportForm();
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-medium">
          Search Amazon products to import into your inventory via the StayFu Chrome Extension
        </h2>
      </div>

      <div className="glass p-6 rounded-xl space-y-4">
        {renderContent()}
      </div>
    </div>
  );
};

export default AmazonSearch;
