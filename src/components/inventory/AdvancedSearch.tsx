import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Search, Filter, X } from 'lucide-react';
import { Property } from '@/types/inventory';

interface AdvancedSearchProps {
  onSearch: (criteria: SearchCriteria) => void;
  properties: Property[];
  collections: string[];
  currentCriteria?: SearchCriteria;
}

export interface SearchCriteria {
  name?: string;
  collection?: string;
  propertyId?: string;
  minQuantity?: number;
  maxQuantity?: number;
  minPrice?: number;
  maxPrice?: number;
  stockStatus?: 'all' | 'low' | 'ok';
  hasAmazonUrl?: boolean;
  hasWalmartUrl?: boolean;
}

const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  onSearch,
  properties,
  collections,
  currentCriteria
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [criteria, setCriteria] = useState<SearchCriteria>(currentCriteria || {});

  const handleSearch = () => {
    onSearch(criteria);
    setIsOpen(false);
  };

  const handleReset = () => {
    const resetCriteria: SearchCriteria = {};
    setCriteria(resetCriteria);
    onSearch(resetCriteria);
  };

  const hasActiveCriteria = Object.keys(criteria).some(key => {
    const value = criteria[key as keyof SearchCriteria];
    return value !== undefined && value !== '' && value !== 'all';
  });

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Search className="h-4 w-4" />
          Advanced Search
          {hasActiveCriteria && (
            <span className="bg-primary text-primary-foreground text-xs px-1.5 py-0.5 rounded-full">
              Active
            </span>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Advanced Search</DialogTitle>
        </DialogHeader>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
          {/* Name Search */}
          <div className="space-y-2">
            <Label htmlFor="name">Item Name</Label>
            <Input
              id="name"
              placeholder="Search by name..."
              value={criteria.name || ''}
              onChange={(e) => setCriteria({ ...criteria, name: e.target.value })}
            />
          </div>

          {/* Collection */}
          <div className="space-y-2">
            <Label htmlFor="collection">Collection</Label>
            <Select
              value={criteria.collection || 'all'}
              onValueChange={(value) => setCriteria({ ...criteria, collection: value === 'all' ? undefined : value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select collection" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Collections</SelectItem>
                {collections.map((collection) => (
                  <SelectItem key={collection} value={collection}>
                    {collection}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Property */}
          <div className="space-y-2">
            <Label htmlFor="property">Property</Label>
            <Select
              value={criteria.propertyId || 'all'}
              onValueChange={(value) => setCriteria({ ...criteria, propertyId: value === 'all' ? undefined : value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select property" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Properties</SelectItem>
                {properties.map((property) => (
                  <SelectItem key={property.id} value={property.id}>
                    {property.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Stock Status */}
          <div className="space-y-2">
            <Label htmlFor="stockStatus">Stock Status</Label>
            <Select
              value={criteria.stockStatus || 'all'}
              onValueChange={(value) => setCriteria({ ...criteria, stockStatus: value as any })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Items</SelectItem>
                <SelectItem value="ok">In Stock</SelectItem>
                <SelectItem value="low">Low Stock</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Quantity Range */}
          <div className="space-y-2">
            <Label>Quantity Range</Label>
            <div className="flex gap-2">
              <Input
                type="number"
                placeholder="Min"
                value={criteria.minQuantity || ''}
                onChange={(e) => setCriteria({ ...criteria, minQuantity: e.target.value ? parseInt(e.target.value) : undefined })}
              />
              <Input
                type="number"
                placeholder="Max"
                value={criteria.maxQuantity || ''}
                onChange={(e) => setCriteria({ ...criteria, maxQuantity: e.target.value ? parseInt(e.target.value) : undefined })}
              />
            </div>
          </div>

          {/* Price Range */}
          <div className="space-y-2">
            <Label>Price Range</Label>
            <div className="flex gap-2">
              <Input
                type="number"
                step="0.01"
                placeholder="Min $"
                value={criteria.minPrice || ''}
                onChange={(e) => setCriteria({ ...criteria, minPrice: e.target.value ? parseFloat(e.target.value) : undefined })}
              />
              <Input
                type="number"
                step="0.01"
                placeholder="Max $"
                value={criteria.maxPrice || ''}
                onChange={(e) => setCriteria({ ...criteria, maxPrice: e.target.value ? parseFloat(e.target.value) : undefined })}
              />
            </div>
          </div>
        </div>

        <div className="flex justify-between pt-4">
          <Button variant="outline" onClick={handleReset}>
            <X className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSearch}>
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AdvancedSearch;
