
import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import InventoryImageUploader from './InventoryImageUploader';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { FormattedInventoryItem } from '@/types/inventory';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";

interface InventoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (item: FormattedInventoryItem) => void;
  item: Partial<FormattedInventoryItem> | null;
  properties: { id: string; name: string }[];
}

const InventoryDialog: React.FC<InventoryDialogProps> = ({
  isOpen,
  onClose,
  onSave,
  item,
  properties
}) => {
  const [formData, setFormData] = useState<Partial<FormattedInventoryItem>>({
    name: '',
    propertyId: '',
    collection: '',
    quantity: 0,
    minQuantity: 1,
    price: undefined,
    amazonUrl: '',
    walmartUrl: '',
    imageUrl: '',
    hasProcessedImage: false
  });

  // Track if we've already processed an image to avoid repeated processing
  const [processedImageUrl, setProcessedImageUrl] = useState<string>('');

  const [collections, setCollections] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<string>('general');
  const [isLoading, setIsLoading] = useState(false);

  // Image upload is now handled by the InventoryImageUploader component

  // Reset form data when dialog opens with a new item
  useEffect(() => {
    if (isOpen && item) {
      console.log('[InventoryDialog] Opening dialog with item:', item);

      // Copy all fields from item to formData
      const newFormData = {
        ...item,
        // Ensure these fields are always present even if they're undefined in the item
        imageUrl: item.imageUrl || '',
        propertyId: item.propertyId || '',
        collection: item.collection || '',
        quantity: typeof item.quantity === 'number' ? item.quantity : 0,
        minQuantity: typeof item.minQuantity === 'number' ? item.minQuantity : 1,
        price: item.price,
        amazonUrl: item.amazonUrl || '',
        walmartUrl: item.walmartUrl || '',
        hasProcessedImage: item.hasProcessedImage || false,
        // Ensure any fields that were undefined in the item are set to empty strings
      };

      console.log('[InventoryDialog] Formatted form data:', newFormData);

      setFormData(newFormData);

      // If there's an image URL, track it as already processed
      if (newFormData.imageUrl) {
        setProcessedImageUrl(newFormData.imageUrl);
      } else {
        setProcessedImageUrl('');
      }

      // Set the active tab to 'general'
      setActiveTab('general');

      // Load collections for this property if a property is selected
      if (item.propertyId) {
        loadCollectionsForProperty(item.propertyId);
      }
    }
  }, [isOpen, item]);

  // Updated collections loading function
  const loadCollectionsForProperty = async (propertyId: string) => {
    try {
      setIsLoading(true);
      // Fetch collections for the selected property
      const response = await fetch(`/api/properties/${propertyId}/collections`);
      if (response.ok) {
        const data = await response.json();
        setCollections(data);
      } else {
        console.error('Failed to load collections:', await response.text());
        setCollections([]);
      }
    } catch (error) {
      console.error('Error loading collections:', error);
      setCollections([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    if (name === 'quantity' || name === 'minQuantity' || name === 'price') {
      // Convert to number and validate
      const numValue = parseFloat(value);
      setFormData({
        ...formData,
        [name]: isNaN(numValue) ? 0 : numValue
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Handle property change
  const handlePropertyChange = (value: string) => {
    setFormData({
      ...formData,
      propertyId: value,
      // Also clear the collection when property changes
      collection: ''
    });

    // Load collections for the selected property
    loadCollectionsForProperty(value);
  };

  // Handle collection change
  const handleCollectionChange = (value: string) => {
    setFormData({
      ...formData,
      collection: value
    });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.name || !formData.propertyId) {
      alert('Please fill out all required fields.');
      return;
    }

    // Prepare the item for saving, including the original ID if we're editing
    const itemToSave: FormattedInventoryItem = {
      id: item?.id || '', // Keep the ID if we're editing
      name: formData.name || '',
      propertyId: formData.propertyId || '',
      propertyName: properties.find(p => p.id === formData.propertyId)?.name || '',
      collection: formData.collection || '',
      quantity: typeof formData.quantity === 'number' ? formData.quantity : 0,
      minQuantity: typeof formData.minQuantity === 'number' ? formData.minQuantity : 1,
      price: formData.price,
      amazonUrl: formData.amazonUrl || undefined,
      walmartUrl: formData.walmartUrl || undefined,
      imageUrl: formData.imageUrl || undefined,
      hasProcessedImage: formData.hasProcessedImage || false,
      teamId: item?.teamId // Preserve the team ID if it exists
    };

    console.log('[InventoryDialog] Saving item:', itemToSave);

    // Call the onSave callback with the item
    onSave(itemToSave);
  };

  // Image upload is now handled by the InventoryImageUploader component

  // Image removal is now handled by the InventoryImageUploader component

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{item?.id ? 'Edit Inventory Item' : 'Add Inventory Item'}</DialogTitle>
          <DialogDescription>
            {item?.id
              ? 'Update the details of this inventory item.'
              : 'Enter the details to add a new inventory item.'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="links">Links</TabsTrigger>
              <TabsTrigger value="image">Image</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4 pt-4">
              {/* General tab content */}
              <div className="space-y-2">
                <Label htmlFor="name">Name*</Label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Item name"
                  value={formData.name || ''}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="property">Property*</Label>
                <Select
                  value={formData.propertyId}
                  onValueChange={handlePropertyChange}
                  required
                >
                  <SelectTrigger id="property">
                    <SelectValue placeholder="Select property" />
                  </SelectTrigger>
                  <SelectContent>
                    {properties.map(property => (
                      <SelectItem key={property.id} value={property.id}>
                        {property.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="collection">Collection</Label>
                <Select
                  value={formData.collection || ''}
                  onValueChange={handleCollectionChange}
                  disabled={!formData.propertyId || isLoading}
                >
                  <SelectTrigger id="collection">
                    <SelectValue placeholder="Select collection" />
                  </SelectTrigger>
                  <SelectContent>
                    {collections.length > 0 ? (
                      collections.map((collection, index) => (
                        <SelectItem key={index} value={collection}>{collection}</SelectItem>
                      ))
                    ) : (
                      <>
                        <SelectItem value="Bathroom">Bathroom</SelectItem>
                        <SelectItem value="Kitchen">Kitchen</SelectItem>
                        <SelectItem value="Bedroom">Bedroom</SelectItem>
                        <SelectItem value="Living Room">Living Room</SelectItem>
                        <SelectItem value="Outdoor">Outdoor</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="quantity">Current Quantity</Label>
                  <Input
                    id="quantity"
                    name="quantity"
                    type="number"
                    min={0}
                    value={formData.quantity}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="minQuantity">Minimum Quantity</Label>
                  <Input
                    id="minQuantity"
                    name="minQuantity"
                    type="number"
                    min={0}
                    value={formData.minQuantity}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="price">Price (Optional)</Label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2">$</span>
                  <Input
                    id="price"
                    name="price"
                    type="number"
                    step="0.01"
                    min={0}
                    className="pl-7"
                    value={formData.price || ''}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="links" className="space-y-4 pt-4">
              {/* Links tab content */}
              <div className="space-y-2">
                <Label htmlFor="amazonUrl">Amazon URL (Optional)</Label>
                <Input
                  id="amazonUrl"
                  name="amazonUrl"
                  placeholder="https://amazon.com/..."
                  value={formData.amazonUrl || ''}
                  onChange={handleInputChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="walmartUrl">Walmart URL (Optional)</Label>
                <Input
                  id="walmartUrl"
                  name="walmartUrl"
                  placeholder="https://walmart.com/..."
                  value={formData.walmartUrl || ''}
                  onChange={handleInputChange}
                />
              </div>
            </TabsContent>

            <TabsContent value="image" className="space-y-4 pt-4">
              {/* Image tab content */}
              <InventoryImageUploader
                imageUrl={formData.imageUrl || ''}
                onImageChange={(url) => {
                  // When the image uploader provides a URL, it's already processed
                  // or it's a Supabase storage URL
                  const isProcessed = url.includes('supabase.co/storage') ||
                                     !url.startsWith('data:image') ||
                                     processedImageUrl === url; // Already processed this URL

                  // Update the processed URL tracker if this is a new processed URL
                  if (isProcessed && url !== processedImageUrl) {
                    setProcessedImageUrl(url);
                  }

                  setFormData({
                    ...formData,
                    imageUrl: url,
                    hasProcessedImage: isProcessed
                  });
                }}
                inventoryItemId={item?.id}
              />

              <div className="space-y-2 mt-4">
                <Label htmlFor="imageUrl">Image URL (Optional)</Label>
                <Input
                  id="imageUrl"
                  name="imageUrl"
                  placeholder="https://example.com/image.jpg"
                  value={formData.imageUrl || ''}
                  onChange={(e) => {
                    const newUrl = e.target.value;
                    // Set hasProcessedImage to false when manually entering a URL
                    setFormData({
                      ...formData,
                      imageUrl: newUrl,
                      hasProcessedImage: false // Mark as needing processing
                    });
                  }}
                />
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} className="mr-2">
              Cancel
            </Button>
            <Button type="submit">
              {item?.id ? 'Update Item' : 'Add Item'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default InventoryDialog;
