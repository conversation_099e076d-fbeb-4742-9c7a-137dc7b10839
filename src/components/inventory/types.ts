export interface InventoryItem {
  id: string;
  name: string;
  propertyId: string;
  propertyName?: string;
  property_name?: string; // Added for compatibility
  collection: string;
  quantity: number;
  minQuantity: number;
  price?: number;
  amazonUrl?: string;
  walmartUrl?: string;
  targetUrl?: string;
  imageUrl?: string;
  lastOrdered?: string;
  hasProcessedImage?: boolean; // Flag indicating if the image is already processed by the extension
}

export interface InventoryFilter {
  property: string;
  collection: string;
  stockStatus: string;
}

export interface InventoryFormData {
  name: string;
  propertyId: string;
  collection: string;
  quantity: number;
  minQuantity: number;
  price?: number;
  amazonUrl?: string;
  walmartUrl?: string;
  targetUrl?: string;
  imageUrl?: string;
}

export interface PurchaseOrder {
  propertyId: string;
  propertyName: string;
  items: {
    inventory_item_id: string;
    item_name: string;
    quantity: number;
    price: number;
    amazon_url?: string;
    walmart_url?: string;
    target_url?: string;
  }[];
  totalPrice: number;
  notes: string;
}

export interface ScrapedProduct {
  name: string;
  price: number;
  imageUrl?: string;
  url: string;
  error?: string;
}

export interface ExtensionImportProduct {
  name: string;
  propertyId: string;
  propertyName: string;
  collection: string;
  quantity: number;
  minQuantity: number;
  price: number;
  amazonUrl?: string;
  imageUrl?: string;
  hasProcessedImage?: boolean; // Flag indicating if the image is already processed by the extension
}

export interface BulkImportItem {
  name: string;
  propertyId: string;
  collection: string;
  quantity: number;
  minQuantity: number;
  price?: number;
  amazonUrl?: string;
  walmartUrl?: string;
  targetUrl?: string;
  imageUrl?: string;
  hasProcessedImage?: boolean; // Flag indicating if the image is already processed by the extension
}

export interface AmazonProduct {
  asin: string;
  title: string;
  price: string;
  numericPrice: number;
  rating: number;
  reviewCount: number;
  img: string;
  isPrime: boolean;
  searchTerm: string;
  url?: string;
}
