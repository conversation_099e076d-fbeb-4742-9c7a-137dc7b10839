import React, { useState, useEffect, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { InventoryItem } from './types';
import { toast } from "sonner";
import { ShoppingCart, AlertTriangle, Plus, Minus, Search } from 'lucide-react';
// import { useNavigate } from 'react-router-dom';

interface CreatePurchaseOrderDialogProps {
  isOpen: boolean;
  onClose: () => void;
  items: InventoryItem[];
  properties: { id: string, name: string }[];
  onSave?: (purchaseOrder: any) => void;
}

interface OrderItem extends InventoryItem {
  selected: boolean;
  orderQuantity: number;
}

const CreatePurchaseOrderDialog: React.FC<CreatePurchaseOrderDialogProps> = ({
  isOpen,
  onClose,
  items,
  properties,
  onSave
}) => {
  // Navigation is not needed in this component
  const [selectedPropertyId, setSelectedPropertyId] = useState<string>('all');
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [showLowStockOnly, setShowLowStockOnly] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isInitialized, setIsInitialized] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (isOpen) {
      // Log the data to help with debugging
      console.log('[CreatePurchaseOrderDialog] Properties:', properties);
      console.log('[CreatePurchaseOrderDialog] Items loaded:', items.length);

      if (items.length > 0) {
        console.log('[CreatePurchaseOrderDialog] Sample item:', items[0]);
      }

      // Initialize the order items
      const initialItems = items.map(item => {
        // Ensure minQuantity is a number
        const minQuantity = typeof item.minQuantity === 'number' ? item.minQuantity : 1;
        const quantity = typeof item.quantity === 'number' ? item.quantity : 0;
        const isLowStock = quantity <= minQuantity;

        // Handle different property ID field names
        // First try propertyId, then property_id, then try to find it in the item
        let propertyId = '';
        if (item.propertyId) {
          propertyId = item.propertyId;
        } else if (item.property_id) {
          propertyId = item.property_id;
        } else {
          // Look for any field that might contain a property ID
          for (const key in item) {
            if ((key.toLowerCase().includes('property') && key.toLowerCase().includes('id')) && item[key]) {
              propertyId = item[key];
              console.log(`[CreatePurchaseOrderDialog] Found property ID in field ${key}:`, propertyId);
              break;
            }
          }
        }

        // If we still don't have a property ID and we have properties, use the first one
        if (!propertyId && properties.length > 0) {
          propertyId = properties[0].id;
          console.log(`[CreatePurchaseOrderDialog] Using first property as fallback for ${item.name}:`, propertyId);
        }

        // Try to get the property name from the properties array
        let propertyName = '';

        // First try item.propertyName - this is the formatted name from the inventory list
        if (item.propertyName) {
          propertyName = item.propertyName;
        } else if (item.property_name) {
          // Then try item.property_name
          propertyName = item.property_name;
        }

        // If we have a property ID, try to find the name in the properties array
        // But only use it if we don't already have a property name from the item
        if (propertyId && !propertyName) {
          const property = properties.find(p => p.id === propertyId);
          if (property) {
            propertyName = property.name;
          }
        }

        // If we still don't have a property name, use a default
        if (!propertyName) {
          propertyName = 'Unknown Property';
        }

        // Log the property name determination process
        console.log(`[CreatePurchaseOrderDialog] Property name for ${item.name}:`, {
          fromItem: item.propertyName || item.property_name || 'none',
          fromProperties: propertyId ? (properties.find(p => p.id === propertyId)?.name || 'not found') : 'no property ID',
          finalName: propertyName
        });

        // Log each item to debug property and quantity issues
        console.log(`[CreatePurchaseOrderDialog] Processing item ${item.name}:`, {
          propertyId,
          propertyName,
          quantity,
          minQuantity,
          isLowStock,
          originalPropertyId: item.propertyId || item.property_id || 'none'
        });

        return {
          ...item,
          propertyId,
          propertyName,
          quantity,
          minQuantity,
          selected: showLowStockOnly ? isLowStock : false,
          orderQuantity: Math.max(minQuantity - quantity, 1)
        };
      });

      setOrderItems(initialItems);

      // Always default to 'all' properties on first render
      if (!isInitialized) {
        setSelectedPropertyId('all');
        console.log('[CreatePurchaseOrderDialog] Default to All Properties');
        setIsInitialized(true);
      }
    } else {
      // Reset search when dialog closes
      setSearchQuery('');
      setIsSubmitting(false);
    }
  }, [isOpen, items, showLowStockOnly, properties, isInitialized]);

  const filteredItems = orderItems.filter(item => {
    // Check if item has valid minQuantity
    if (typeof item.minQuantity !== 'number') {
      console.warn(`[CreatePurchaseOrderDialog] Item ${item.name} has invalid minQuantity:`, item.minQuantity);
    }

    // Check if item has valid propertyId
    if (!item.propertyId) {
      console.warn(`[CreatePurchaseOrderDialog] Item ${item.name} has invalid propertyId:`, item.propertyId);
    }

    // Log filtering process to debug
    const matchesProperty = selectedPropertyId === 'all' || item.propertyId === selectedPropertyId;

    // For low stock filter, explicitly check if quantity is less than or equal to minQuantity
    const isLowStock = item.quantity <= item.minQuantity;
    const matchesLowStock = !showLowStockOnly || isLowStock;

    const matchesSearch = !searchQuery ||
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (item.collection && item.collection.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (item.propertyName && item.propertyName.toLowerCase().includes(searchQuery.toLowerCase()));

    // Debug filtering with more details
    if (!matchesProperty) {
      console.log(`[CreatePurchaseOrderDialog] Item ${item.name} filtered out - property mismatch:`, {
        itemPropertyId: item.propertyId,
        selectedPropertyId,
        itemPropertyName: item.propertyName
      });
    }

    if (showLowStockOnly && !isLowStock) {
      console.log(`[CreatePurchaseOrderDialog] Item ${item.name} filtered out - not low stock:`, {
        quantity: item.quantity,
        minQuantity: item.minQuantity
      });
    }

    if (!matchesSearch && searchQuery) {
      console.log(`[CreatePurchaseOrderDialog] Item ${item.name} filtered out - search mismatch:`, {
        searchQuery,
        itemName: item.name,
        itemCollection: item.collection,
        itemPropertyName: item.propertyName
      });
    }

    return matchesProperty && matchesLowStock && matchesSearch;
  });

  useEffect(() => {
    // Log the filtering results for debugging
    console.log('[CreatePurchaseOrderDialog] Filtered items:', filteredItems.length);
    console.log('[CreatePurchaseOrderDialog] Filter settings:', {
      property: selectedPropertyId,
      lowStockOnly: showLowStockOnly,
      searchQuery
    });
  }, [filteredItems.length, selectedPropertyId, showLowStockOnly, searchQuery]);

  const handleToggleItem = (id: string) => {
    setOrderItems(prev => prev.map(item =>
      item.id === id ? { ...item, selected: !item.selected } : item
    ));
  };

  const handleQuantityChange = (id: string, quantity: number) => {
    console.log(`[CreatePurchaseOrderDialog] Changing quantity for item ${id} to ${quantity}`);

    // Ensure quantity is a valid number and at least 1
    const validQuantity = Math.max(isNaN(quantity) ? 1 : quantity, 1);

    setOrderItems(prev => {
      const updated = prev.map(item =>
        item.id === id ? { ...item, orderQuantity: validQuantity } : item
      );

      // Log the updated item for debugging
      const updatedItem = updated.find(item => item.id === id);
      console.log(`[CreatePurchaseOrderDialog] Updated item:`, updatedItem);

      return updated;
    });
  };

  const handleCreateOrder = () => {
    try {
      console.log('[CreatePurchaseOrderDialog] Creating order...');
      setIsSubmitting(true);

      // Get selected items
      const selectedItems = orderItems.filter(item => item.selected);
      console.log('[CreatePurchaseOrderDialog] Selected items:', selectedItems);

      if (selectedItems.length === 0) {
        toast.error("Please select at least one item");
        setIsSubmitting(false);
        return;
      }

      // Set a local timeout to ensure we don't get stuck in submitting state
      const localTimeout = setTimeout(() => {
        console.log('[CreatePurchaseOrderDialog] Local timeout triggered in handleCreateOrder');
        // Only reset if we're still in submitting state after 5 seconds
        if (isSubmitting) {
          console.warn('[CreatePurchaseOrderDialog] Still in submitting state after 5 seconds, may be stuck');
          // We don't force setIsSubmitting(false) here to avoid conflicts with the parent component
          // The parent safety timeout will handle this if needed
        }
      }, 5000); // 5 seconds timeout

      // Debug the selected items' properties
      const selectedItemProperties = selectedItems.map(item => ({
        id: item.id,
        name: item.name,
        propertyId: item.propertyId,
        propertyName: item.propertyName
      }));
      console.log('[CreatePurchaseOrderDialog] Selected items properties:', selectedItemProperties);

      // Determine the property ID to use for the order
      let effectivePropertyId = selectedPropertyId;
      let propertyName = "";

      // Check if a property is selected (but not 'all')
      if (selectedPropertyId === 'all') {
        console.log('[CreatePurchaseOrderDialog] All Properties selected, analyzing selected items');

        // Get all valid property IDs from selected items
        const validPropertyIds = selectedItems
          .filter(item => item.propertyId && item.propertyId !== 'all')
          .map(item => item.propertyId);

        // Get unique property IDs
        const uniquePropertyIds = [...new Set(validPropertyIds)];
        console.log('[CreatePurchaseOrderDialog] Unique property IDs in selected items:', uniquePropertyIds);

        if (uniquePropertyIds.length > 1) {
          // If items from multiple properties are selected, we'll create a separate order for each property
          // But for now, we'll use the first property and inform the user about the behavior

          // Group items by property
          const itemsByProperty = selectedItems.reduce((acc, item) => {
            const propId = item.propertyId || 'unknown';
            if (!acc[propId]) {
              acc[propId] = [];
            }
            acc[propId].push(item);
            return acc;
          }, {} as Record<string, typeof selectedItems>);

          // Log the grouping for debugging
          console.log('[CreatePurchaseOrderDialog] Items grouped by property:',
            Object.keys(itemsByProperty).map(propId => ({
              propertyId: propId,
              itemCount: itemsByProperty[propId].length,
              propertyName: itemsByProperty[propId][0]?.propertyName || 'Unknown'
            }))
          );

          // For now, use the first property but keep the original property IDs for each item
          // This will allow the parent component to handle multiple properties correctly
          const firstPropertyId = uniquePropertyIds[0];
          const firstPropertyItems = itemsByProperty[firstPropertyId] || [];

          if (firstPropertyItems.length > 0) {
            effectivePropertyId = firstPropertyId;
            propertyName = firstPropertyItems[0]?.propertyName || "Unknown Property";

            console.log(`[CreatePurchaseOrderDialog] Multiple properties found, using first property: ${effectivePropertyId} (${propertyName})`);

            // Show a toast to inform the user that we're preserving property assignments
            toast.info(`Creating order with items from multiple properties. Each item will maintain its original property assignment.`);
          } else {
            // Fallback if something went wrong with the grouping
            effectivePropertyId = firstPropertyId;
            const itemWithProperty = selectedItems.find(item => item.propertyId === effectivePropertyId);
            propertyName = itemWithProperty?.propertyName || "Unknown Property";

            console.log(`[CreatePurchaseOrderDialog] Fallback to first property: ${effectivePropertyId} (${propertyName})`);
            toast.info(`Using property: ${propertyName}`);
          }
        } else if (uniquePropertyIds.length === 1) {
          // If all selected items are from the same property, use that property
          effectivePropertyId = uniquePropertyIds[0];

          // Get the property name from the first selected item with this property ID
          const itemWithProperty = selectedItems.find(item => item.propertyId === effectivePropertyId);

          // Use the property name from the item, which should already have the correct format
          propertyName = itemWithProperty?.propertyName || "Unknown Property";

          console.log(`[CreatePurchaseOrderDialog] Auto-selecting property ${effectivePropertyId} (${propertyName}) since all items are from this property`);
        } else {
          // No valid property IDs found in selected items
          console.error('[CreatePurchaseOrderDialog] No valid property IDs found in selected items');

          // If we have properties available, use the first one
          if (properties.length > 0) {
            effectivePropertyId = properties[0].id;
            propertyName = properties[0].name;
            console.log(`[CreatePurchaseOrderDialog] Using first available property: ${effectivePropertyId} (${propertyName})`);
            toast.info(`Using property: ${properties[0].name}`);
          } else {
            // If we still don't have a property ID, show an error
            toast.error("No valid property found. Please select a specific property.");
            setIsSubmitting(false);
            return;
          }
        }
      } else {
        // A specific property was selected, first try to find it in the selected items
        // This is more likely to have the correct display format with user info
        const itemWithSelectedProperty = selectedItems.find(item => item.propertyId === selectedPropertyId);
        if (itemWithSelectedProperty && itemWithSelectedProperty.propertyName) {
          propertyName = itemWithSelectedProperty.propertyName;
          console.log(`[CreatePurchaseOrderDialog] Using property name from selected item: ${selectedPropertyId} (${propertyName})`);
        } else {
          // If not found in items, try to find it in the properties array
          const selectedProperty = properties.find(p => p.id === selectedPropertyId);
          if (selectedProperty) {
            propertyName = selectedProperty.name;
            console.log(`[CreatePurchaseOrderDialog] Using selected property from properties array: ${selectedPropertyId} (${propertyName})`);
          } else {
            console.error('[CreatePurchaseOrderDialog] Selected property not found in properties array:', selectedPropertyId);
            console.log('[CreatePurchaseOrderDialog] Available properties:', properties);

            // Try to find the property name in any item (not just selected ones)
            const anyItemWithProperty = orderItems.find(item => item.propertyId === selectedPropertyId);
            if (anyItemWithProperty && anyItemWithProperty.propertyName) {
              propertyName = anyItemWithProperty.propertyName;
              console.log(`[CreatePurchaseOrderDialog] Found property name in all items: ${propertyName}`);
            } else if (properties.length > 0) {
              // If we can't find the property name, use the first available property
              effectivePropertyId = properties[0].id;
              propertyName = properties[0].name;
              console.log(`[CreatePurchaseOrderDialog] Using first available property as fallback: ${effectivePropertyId} (${propertyName})`);
              toast.info(`Using property: ${propertyName}`);
            } else {
              propertyName = "Unknown Property";
            }
          }
        }
      }

      // Validate that we have a valid property ID
      if (!effectivePropertyId || effectivePropertyId === 'all') {
        console.error('[CreatePurchaseOrderDialog] No valid property ID determined');

        // Last resort: try to get a property ID from any selected item
        const itemWithProperty = selectedItems.find(item => item.propertyId && item.propertyId !== 'all');
        if (itemWithProperty && itemWithProperty.propertyId) {
          effectivePropertyId = itemWithProperty.propertyId;
          // Use the property name directly from the item, which should have the correct format
          propertyName = itemWithProperty.propertyName || "Unknown Property";
          console.log(`[CreatePurchaseOrderDialog] Last resort: using item's property: ${effectivePropertyId} (${propertyName})`);
        } else if (properties.length > 0) {
          // If we still don't have a property ID, use the first available property
          effectivePropertyId = properties[0].id;
          propertyName = properties[0].name;
          console.log(`[CreatePurchaseOrderDialog] Last resort: using first available property: ${effectivePropertyId} (${propertyName})`);
          toast.info(`Using property: ${propertyName}`);
        } else {
          // If we still don't have a property ID, show an error
          toast.error("No valid property found. Please select a specific property.");
          setIsSubmitting(false);
          return;
        }
      }

      // Final validation to ensure we have a valid property ID
      if (!effectivePropertyId || effectivePropertyId === 'all') {
        console.error('[CreatePurchaseOrderDialog] Still no valid property ID after all attempts');
        toast.error("Could not determine a valid property. Please select a specific property.");
        setIsSubmitting(false);
        return;
      }

      // If we don't have a property name yet, try to find it in the items first
      if (!propertyName) {
        // First try to find the property name in any item (selected or not)
        // This is more likely to have the correct display format with user info
        const itemWithProperty = orderItems.find(item => item.propertyId === effectivePropertyId);
        if (itemWithProperty && itemWithProperty.propertyName) {
          propertyName = itemWithProperty.propertyName;
          console.log(`[CreatePurchaseOrderDialog] Found property name in all items: ${propertyName}`);
        } else {
          // If not found in items, try to find it in the properties array
          const property = properties.find(p => p.id === effectivePropertyId);
          if (property) {
            propertyName = property.name;
            console.log(`[CreatePurchaseOrderDialog] Found property name in properties array: ${propertyName}`);
          } else {
            // If we still can't find the property, use a default name
            console.error('[CreatePurchaseOrderDialog] Property not found in properties array:', effectivePropertyId);
            console.log('[CreatePurchaseOrderDialog] Available properties:', properties);

            propertyName = "Unknown Property";
            console.log(`[CreatePurchaseOrderDialog] Using default property name: ${propertyName}`);
          }
        }
      }

      // Debug the properties data
      console.log('[CreatePurchaseOrderDialog] Final property ID:', effectivePropertyId);
      console.log('[CreatePurchaseOrderDialog] Final property name:', propertyName);
      console.log('[CreatePurchaseOrderDialog] Available properties:', properties);

      // Get all selected items (regardless of property)
      const propertyItems = selectedItems.map(item => ({
        id: item.id,  // Keep the original ID for reference
        inventory_item_id: item.id,
        item_name: item.name,
        quantity: item.orderQuantity,
        price: item.price || 0,
        amazon_url: item.amazonUrl || '',
        walmart_url: item.walmartUrl || '',
        propertyId: item.propertyId,  // Include property ID for reference
        propertyName: item.propertyName  // Include property name for reference
      }));

      if (propertyItems.length === 0) {
        toast.error("No items selected");
        setIsSubmitting(false);
        return;
      }

      const totalPrice = propertyItems.reduce((sum, item) =>
        sum + (item.price || 0) * item.quantity, 0);

      const orderData = {
        propertyId: effectivePropertyId,
        propertyName,
        items: propertyItems,
        totalPrice,
        notes: ''
      };

      console.log('[CreatePurchaseOrderDialog] Final order data:', orderData);

      if (onSave) {
        try {
          onSave(orderData);
          console.log('[CreatePurchaseOrderDialog] onSave called with order data');
          // Note: We don't clear isSubmitting here because the parent component will handle that
          // when it closes the dialog after successful creation
        } catch (saveError) {
          console.error('[CreatePurchaseOrderDialog] Error in onSave callback:', saveError);
          toast.error("Failed to create purchase order: " + (saveError.message || "Unknown error"));
          setIsSubmitting(false);
          clearTimeout(localTimeout);
        }
      } else {
        console.error('[CreatePurchaseOrderDialog] onSave callback is not provided!');
        toast.error("Failed to create purchase order: Internal error");
        setIsSubmitting(false);
        clearTimeout(localTimeout);
      }
    } catch (error) {
      console.error('[CreatePurchaseOrderDialog] Error creating order:', error);
      toast.error("An error occurred while creating the purchase order");
      setIsSubmitting(false);
      // No need to clear timeout here as it's not defined in this scope
    }
  };

  // Log properties for debugging
  useEffect(() => {
    console.log(`[CreatePurchaseOrderDialog] Properties received: ${properties.length}`);

    // Check for any duplicate IDs
    const propertyIds = new Set<string>();
    const duplicates: string[] = [];

    for (const property of properties) {
      if (propertyIds.has(property.id)) {
        duplicates.push(`${property.id} (${property.name})`);
      } else {
        propertyIds.add(property.id);
      }
    }

    if (duplicates.length > 0) {
      console.warn(`[CreatePurchaseOrderDialog] Found ${duplicates.length} duplicate property IDs:`, duplicates);
    }
  }, [properties]);

  // Add a safety timeout to ensure dialog closes even if there are issues
  useEffect(() => {
    let safetyTimeout: NodeJS.Timeout | null = null;

    if (isSubmitting) {
      safetyTimeout = setTimeout(() => {
        console.log('[CreatePurchaseOrderDialog] Safety timeout triggered - forcing dialog close');
        // Only close if we're still in submitting state after 15 seconds
        if (isSubmitting) {
          setIsSubmitting(false);
          onClose();
        }
      }, 15000); // 15 seconds timeout
    }

    return () => {
      if (safetyTimeout) {
        clearTimeout(safetyTimeout);
      }
    };
  }, [isSubmitting, onClose]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        setIsSubmitting(false);
        onClose();
      }
    }}>
      <DialogContent className="sm:max-w-[650px] max-h-[calc(100vh-2rem)] overflow-y-auto mx-4">
        <DialogHeader>
          <DialogTitle>Create Purchase Order</DialogTitle>
          <DialogDescription>
            Select items to include in your purchase order
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-3">
          <div className="flex flex-col space-y-2">
            <Select
              value={selectedPropertyId}
              onValueChange={setSelectedPropertyId}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select property" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Properties</SelectItem>
                {properties.map(property => (
                  <SelectItem key={property.id} value={property.id}>{property.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="relative w-full">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={16} className="text-muted-foreground" />
              </div>
              <input
                type="text"
                placeholder="Search items..."
                className="w-full pl-10 pr-4 py-2 rounded-lg border border-border focus:outline-none focus:ring-2 focus:ring-primary/30"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="flex items-center gap-2">
              <Checkbox
                id="lowStockOnly"
                checked={showLowStockOnly}
                onCheckedChange={(checked) => {
                  console.log(`[CreatePurchaseOrderDialog] Low stock filter changed to: ${checked}`);
                  setShowLowStockOnly(checked as boolean);
                }}
              />
              <Label htmlFor="lowStockOnly" className="cursor-pointer text-sm">
                Low stock only
              </Label>
            </div>
          </div>

          <div className="max-h-[400px] overflow-y-auto space-y-2">
            {filteredItems.length > 0 ? (
              filteredItems.map(item => (
                <div
                  key={item.id}
                  className="border border-border rounded-lg p-3 flex items-center gap-2"
                >
                  <Checkbox
                    id={`item-${item.id}`}
                    checked={item.selected}
                    onCheckedChange={() => handleToggleItem(item.id)}
                  />

                  <div className="flex-1">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="font-medium text-sm flex items-center gap-2">
                          {item.name}
                          {item.quantity <= item.minQuantity && (
                            <span className="text-xs px-2 py-0.5 bg-amber-100 text-amber-800 rounded-full flex items-center">
                              <AlertTriangle size={10} className="mr-1" />
                              Low Stock
                            </span>
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {item.propertyName || 'Unknown Property'} • {item.collection || 'General'}
                        </div>
                        <div className="text-xs mt-1">
                          <span className={item.quantity <= item.minQuantity ? "text-red-500" : ""}>
                            {item.quantity} in stock
                          </span>
                          <span className="text-muted-foreground ml-1">
                            (min: {item.minQuantity})
                          </span>
                        </div>
                      </div>

                      {item.price && (
                        <div className="text-sm font-medium">${item.price.toFixed(2)}</div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center">
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-8 w-8 rounded-r-none"
                      onClick={() => {
                        console.log(`[CreatePurchaseOrderDialog] Decrease button clicked for ${item.id}`);
                        handleQuantityChange(item.id, item.orderQuantity - 1);
                      }}
                      disabled={item.orderQuantity <= 1}
                      type="button"
                    >
                      <Minus size={14} />
                    </Button>
                    <Input
                      type="number"
                      min="1"
                      value={item.orderQuantity}
                      onChange={(e) => {
                        const val = parseInt(e.target.value);
                        console.log(`[CreatePurchaseOrderDialog] Input changed to ${val} for ${item.id}`);
                        handleQuantityChange(item.id, val);
                      }}
                      className="h-8 rounded-none w-12 text-center p-0 m-0 border-l-0 border-r-0"
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-8 w-8 rounded-l-none"
                      onClick={() => {
                        console.log(`[CreatePurchaseOrderDialog] Increase button clicked for ${item.id}`);
                        handleQuantityChange(item.id, item.orderQuantity + 1);
                      }}
                      type="button"
                    >
                      <Plus size={14} />
                    </Button>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-3">
                  <ShoppingCart size={20} className="text-muted-foreground" />
                </div>
                <p className="text-muted-foreground">No items match your criteria</p>
              </div>
            )}
          </div>

          <div className="pt-3 flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button
              onClick={handleCreateOrder}
              className="gap-2"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-1" />
                  Creating...
                </>
              ) : (
                <>
                  <ShoppingCart size={16} />
                  Create Purchase Order
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreatePurchaseOrderDialog;
