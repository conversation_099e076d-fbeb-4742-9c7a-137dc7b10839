
import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import InventoryContent from './InventoryContent';
import { FilterOptions } from '@/hooks/useInventoryFilters';

test('renders loading state when isLoading is true', () => {
  const mockOnItemClick = vi.fn();
  const mockOnDeleteItems = vi.fn();
  const mockRetryFetch = vi.fn();
  const mockOnViewModeChange = vi.fn();
  
  render(
    <InventoryContent
      isLoading={true}
      filteredItems={[]}
      onItemClick={mockOnItemClick}
      onDeleteItems={mockOnDeleteItems}
      searchQuery=""
      filters={{ property: '', collection: '', stockStatus: 'all' }}
      retryFetch={mockRetryFetch}
      error={null}
      viewMode="grid"
      onViewModeChange={mockOnViewModeChange}
    />
  );
  
  expect(screen.getByText('Loading inventory items...')).toBeInTheDocument();
});

test('renders empty state when no items match filters', () => {
  const mockOnItemClick = vi.fn();
  const mockOnDeleteItems = vi.fn();
  const mockRetryFetch = vi.fn();
  const mockOnViewModeChange = vi.fn();
  
  render(
    <InventoryContent
      isLoading={false}
      filteredItems={[]}
      onItemClick={mockOnItemClick}
      onDeleteItems={mockOnDeleteItems}
      searchQuery="test query"
      filters={{ property: '', collection: '', stockStatus: 'all' }}
      retryFetch={mockRetryFetch}
      error={null}
      viewMode="grid"
      onViewModeChange={mockOnViewModeChange}
    />
  );
  
  expect(screen.getByText('No matching items found')).toBeInTheDocument();
  expect(screen.getByText('Try adjusting your search or filters')).toBeInTheDocument();
});

test('renders grid view when viewMode is grid', () => {
  const mockOnItemClick = vi.fn();
  const mockOnDeleteItems = vi.fn();
  const mockRetryFetch = vi.fn();
  const mockOnViewModeChange = vi.fn();
  
  const mockItems = [
    {
      id: '1',
      name: 'Item 1',
      propertyId: 'prop1',
      propertyName: 'Property 1',
      collection: 'Collection 1',
      quantity: 5,
      minQuantity: 3,
      price: 19.99,
      imageUrl: 'https://example.com/image1.jpg',
      amazonUrl: 'https://amazon.com/item1',
      walmartUrl: 'https://walmart.com/item1'
    }
  ];
  
  render(
    <InventoryContent
      isLoading={false}
      filteredItems={mockItems}
      onItemClick={mockOnItemClick}
      onDeleteItems={mockOnDeleteItems}
      searchQuery=""
      filters={{ property: '', collection: '', stockStatus: 'all' }}
      retryFetch={mockRetryFetch}
      error={null}
      viewMode="grid"
      onViewModeChange={mockOnViewModeChange}
    />
  );
  
  // This depends on how your grid component is structured
  expect(screen.getByText('Item 1')).toBeInTheDocument();
});

test('renders list view when viewMode is list', () => {
  const mockOnItemClick = vi.fn();
  const mockOnDeleteItems = vi.fn();
  const mockRetryFetch = vi.fn();
  const mockOnViewModeChange = vi.fn();
  
  const mockItems = [
    {
      id: '1',
      name: 'Item 1',
      propertyId: 'prop1',
      propertyName: 'Property 1',
      collection: 'Collection 1',
      quantity: 5,
      minQuantity: 3,
      price: 19.99,
      imageUrl: 'https://example.com/image1.jpg',
      amazonUrl: 'https://amazon.com/item1',
      walmartUrl: 'https://walmart.com/item1'
    }
  ];
  
  render(
    <InventoryContent
      isLoading={false}
      filteredItems={mockItems}
      onItemClick={mockOnItemClick}
      onDeleteItems={mockOnDeleteItems}
      searchQuery=""
      filters={{ property: '', collection: '', stockStatus: 'all' }}
      retryFetch={mockRetryFetch}
      error={null}
      viewMode="list"
      onViewModeChange={mockOnViewModeChange}
    />
  );
  
  // This depends on how your list component is structured
  expect(screen.getByText('Item 1')).toBeInTheDocument();
});
