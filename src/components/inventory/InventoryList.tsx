
import React from 'react';
import { InventoryItem } from './types';
import { Button } from '@/components/ui/button';
import { ExternalLink, Trash2, Edit, ShoppingCart } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface InventoryListProps {
  items: InventoryItem[];
  onItemClick: (item: InventoryItem) => void;
  onDeleteItems?: (itemIds: string[]) => void;
}

const InventoryList: React.FC<InventoryListProps> = ({
  items,
  onItemClick,
  onDeleteItems
}) => {
  return (
    <div className="w-full overflow-auto">
      <table className="w-full border-collapse">
        <thead className="bg-muted/50">
          <tr>
            <th className="text-left p-3 text-sm font-medium">Item</th>
            <th className="text-left p-3 text-sm font-medium">Property</th>
            <th className="text-left p-3 text-sm font-medium">Collection</th>
            <th className="text-left p-3 text-sm font-medium">Quantity</th>
            <th className="text-left p-3 text-sm font-medium">Min Qty</th>
            <th className="text-left p-3 text-sm font-medium">Price</th>
            <th className="text-left p-3 text-sm font-medium">Actions</th>
          </tr>
        </thead>
        <tbody>
          {items.map((item) => (
            <tr 
              key={item.id} 
              className="border-b hover:bg-muted/30 cursor-pointer"
              onClick={() => onItemClick(item)}
            >
              <td className="p-3">
                <div className="flex items-center">
                  {item.imageUrl && (
                    <img
                      src={item.imageUrl}
                      alt={item.name} 
                      className="w-10 h-10 rounded object-cover mr-3"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/placeholder.svg';
                      }}
                    />
                  )}
                  <div>
                    <div className="font-medium">{item.name}</div>
                    {(item.amazonUrl || item.walmartUrl) && (
                      <div className="flex gap-2 mt-1">
                        {item.amazonUrl && (
                          <Button variant="ghost" size="icon" className="h-6 w-6" 
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(item.amazonUrl, '_blank');
                            }}
                          >
                            <ExternalLink size={14} />
                          </Button>
                        )}
                        {item.walmartUrl && (
                          <Button variant="ghost" size="icon" className="h-6 w-6"
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(item.walmartUrl, '_blank');
                            }}
                          >
                            <ExternalLink size={14} />
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </td>
              <td className="p-3">{item.propertyName}</td>
              <td className="p-3">{item.collection}</td>
              <td className="p-3">
                <div className="flex items-center">
                  {item.quantity}
                  {item.quantity <= item.minQuantity && (
                    <Badge variant="destructive" className="ml-2 text-xs">Low</Badge>
                  )}
                </div>
              </td>
              <td className="p-3">{item.minQuantity}</td>
              <td className="p-3">{item.price ? `$${item.price}` : '-'}</td>
              <td className="p-3">
                <div className="flex gap-1">
                  <Button variant="ghost" size="icon" className="h-8 w-8" 
                    onClick={(e) => {
                      e.stopPropagation();
                      onItemClick(item);
                    }}
                  >
                    <Edit size={16} />
                  </Button>
                  {onDeleteItems && (
                    <Button variant="ghost" size="icon" className="h-8 w-8"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteItems([item.id]);
                      }}
                    >
                      <Trash2 size={16} />
                    </Button>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default InventoryList;
