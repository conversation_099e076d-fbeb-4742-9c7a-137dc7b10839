
import { render, screen, fireEvent, waitFor } from '@/tests/utils/test-utils';
import '@testing-library/jest-dom';
import InventoryDialog from './InventoryDialog';
import { InventoryItem } from './types';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Mock supabase
jest.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    maybeSingle: jest.fn().mockResolvedValue({
      data: { collections: [{ name: 'Test Collection' }] },
      error: null
    }),
    storage: {
      from: jest.fn().mockReturnThis(),
      upload: jest.fn().mockResolvedValue({ data: { path: 'test/path.jpg' }, error: null }),
      getPublicUrl: jest.fn().mockReturnValue({ data: { publicUrl: 'https://example.com/test.jpg' } })
    }
  }
}));

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}));

describe('InventoryDialog Component', () => {
  const mockProperties = [
    { id: 'prop1', name: 'Property One' },
    { id: 'prop2', name: 'Property Two' }
  ];
  
  const mockItem: InventoryItem = {
    id: 'test-item-1',
    name: 'Test Item',
    propertyId: 'prop1',
    propertyName: 'Property One',
    collection: 'Kitchen',
    quantity: 5,
    minQuantity: 2,
    price: 19.99
  };
  
  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    onSave: jest.fn(),
    item: null,
    properties: mockProperties
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('renders the add item form correctly', () => {
    render(<InventoryDialog {...defaultProps} />);
    
    expect(screen.getByText('Add New Item')).toBeInTheDocument();
    expect(screen.getByLabelText(/Item Name/i)).toBeInTheDocument();
    expect(screen.getByRole('combobox', { name: /Property/i })).toBeInTheDocument();
    expect(screen.getByRole('combobox', { name: /Collection/i })).toBeInTheDocument();
    expect(screen.getByLabelText('Quantity')).toBeInTheDocument();
    expect(screen.getByLabelText('Minimum Quantity')).toBeInTheDocument();
    expect(screen.getByLabelText(/Price/i)).toBeInTheDocument();
    
    // Buttons
    expect(screen.getByRole('button', { name: /Cancel/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Add Item/i })).toBeInTheDocument();
  });
  
  it('renders the edit item form with populated data', async () => {
    render(<InventoryDialog {...defaultProps} item={mockItem} />);
    
    await waitFor(() => {
      expect(screen.getByText('Edit Item')).toBeInTheDocument();
    });
    
    expect(screen.getByDisplayValue('Test Item')).toBeInTheDocument();
    expect(screen.getByDisplayValue('5')).toBeInTheDocument();
    expect(screen.getByDisplayValue('2')).toBeInTheDocument();
    expect(screen.getByDisplayValue('19.99')).toBeInTheDocument();
  });
});
