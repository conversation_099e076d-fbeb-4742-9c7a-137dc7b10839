
import React, { useState, useEffect, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { FormattedInventoryItem, ScrapedProduct, ExtensionImportProduct } from '@/types/inventory';
import { supabase } from '@/integrations/supabase/client';
import { forceCloseDialog } from '@/utils/forceCloseDialog';

interface BulkImportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (items: Partial<FormattedInventoryItem>[]) => void;
  properties: { id: string, name: string }[];
  items?: ExtensionImportProduct[];
  isExtensionImport?: boolean;
}

type ProductSource = 'amazon' | 'walmart';

const BulkImportDialog: React.FC<BulkImportDialogProps> = (props) => {
  const {
    isOpen,
    onClose,
    onImport,
    properties: rawProperties,
    items: extensionItems = [],
    isExtensionImport = false
  } = props;

  // Deduplicate properties to prevent duplicates in the dropdown
  const properties = useMemo(() => {
    // Create a map to store unique properties by name
    const uniquePropertiesByName = new Map<string, { id: string; name: string }>();

    // Process properties to keep only one entry per property name
    // We'll clean the property name by removing user info in parentheses
    for (const property of rawProperties) {
      // Extract the base property name without the user info in parentheses
      const baseName = property.name.replace(/\s*\([^)]*\)\s*$/, '').trim();
      const key = baseName.toLowerCase();

      if (!uniquePropertiesByName.has(key)) {
        // Store with the original name but keyed by the base name
        uniquePropertiesByName.set(key, {
          id: property.id,
          name: baseName // Use the cleaned name without user info
        });
      }
    }

    // Convert the map values to an array
    const uniqueProperties = Array.from(uniquePropertiesByName.values());

    console.log(`[BulkImportDialog] Deduplicated properties: ${uniqueProperties.length} (from ${rawProperties.length})`);
    return uniqueProperties;
  }, [rawProperties]);

  const [isLoading, setIsLoading] = useState(false);
  const [productIds, setProductIds] = useState('');
  const [productSource, setProductSource] = useState<ProductSource>('amazon');
  // Initialize propertyId with the first property if available
  const [propertyId, setPropertyId] = useState(() => {
    if (properties && properties.length > 0) {
      console.log('[BulkImportDialog] Initializing propertyId with first property:', properties[0].id);
      return properties[0].id;
    }
    return '';
  });
  const [propertyCollections, setPropertyCollections] = useState<string[]>([]);
  const [collection, setCollection] = useState('');
  const [scrapedProducts, setScrapedProducts] = useState<ScrapedProduct[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [scraperError, setScraperError] = useState<string | null>(null);
  // Used to track processed images during import
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [processedImages, setProcessedImages] = useState<Record<string, boolean>>({});

  // Reset form when dialog is opened/closed
  useEffect(() => {
    if (isOpen) {
      if (isExtensionImport && extensionItems && extensionItems.length > 0) {
        try {
          console.log('[BulkImportDialog] Processing extension items');

          // Reset processed images state
          setProcessedImages({});

          // Handle extension import case
          const safeProducts = extensionItems.map(item => ({
            name: item.name || 'Unnamed Product',
            price: typeof item.price === 'number' ? item.price : 0,
            imageUrl: item.imageUrl || '',
            url: item.amazonUrl || '',
            error: undefined
          }));

          setScrapedProducts(safeProducts);

          // Set property and collection if available
          if (extensionItems[0]?.propertyId) {
            setPropertyId(extensionItems[0].propertyId);
            fetchCollectionsForProperty(extensionItems[0].propertyId);
          } else if (properties && properties.length > 0) {
            setPropertyId(properties[0].id);
            fetchCollectionsForProperty(properties[0].id);
          }

          if (extensionItems[0]?.collection) {
            setCollection(extensionItems[0].collection);
          } else {
            setCollection('Other');
          }

          setShowPreview(true);
        } catch (error) {
          console.error('Error processing extension items:', error);
          toast.error('Error processing import data');
          setShowPreview(false);
        }
      } else {
        // Regular manual import case
        setProductIds('');

        // Ensure we have properties before setting a default
        if (properties && properties.length > 0) {
          setPropertyId(properties[0].id);
          fetchCollectionsForProperty(properties[0].id);
        }

        setCollection('Other');
        setScrapedProducts([]);
        setShowPreview(false);
        setScraperError(null);
        setProcessedImages({});
      }
    }
  }, [isOpen, isExtensionImport, extensionItems, properties]);

  // Set initial propertyId only once when properties are first loaded
  useEffect(() => {
    if (properties && properties.length > 0 && !propertyId) {
      // Only set if propertyId is empty to avoid loops
      console.log('[BulkImportDialog] Setting initial propertyId:', properties[0].id);
      setPropertyId(properties[0].id);
    }
  }, [properties, propertyId]);

  // Fetch collections for the selected property - only on initial load
  useEffect(() => {
    if (propertyId && isOpen) {
      // Only fetch on initial load to avoid loops
      console.log('[BulkImportDialog] Initial fetch of collections for property:', propertyId);

      // Use our helper function to fetch collections
      fetchCollectionsForProperty(propertyId);
    } else if (!propertyId) {
      setPropertyCollections([]);
      setCollection('Other');
    }
  }, [isOpen, propertyId]);

  // Helper function to fetch collections for a property
  const fetchCollectionsForProperty = (propId: string) => {
    if (!propId) return;

    console.log('[BulkImportDialog] Fetching collections for property:', propId);

    return supabase
      .from('properties')
      .select('collections, name')
      .eq('id', propId)
      .single()
      .then(({ data, error }) => {
        if (error) {
          console.error('Error fetching property collections:', error);
          setPropertyCollections([]);
          setCollection('Other');
          return;
        }

        if (data?.collections?.length > 0) {
          console.log('[BulkImportDialog] Setting collections for property:', data.name, data.collections);
          setPropertyCollections(data.collections);
          setCollection(data.collections[0]);
        } else {
          console.log('[BulkImportDialog] No collections found, setting to Other');
          setPropertyCollections([]);
          setCollection('Other');
        }
      });
  };

  const handleScrapeProducts = async () => {
    console.log('[BulkImportDialog] handleScrapeProducts called with propertyId:', propertyId);
    console.log('[BulkImportDialog] Available properties:', properties);

    if (!propertyId) {
      console.warn('[BulkImportDialog] No property selected!');

      // Try to auto-select the first property if available
      if (properties && properties.length > 0) {
        const firstPropertyId = properties[0].id;
        console.log('[BulkImportDialog] Auto-selecting first property:', firstPropertyId);
        setPropertyId(firstPropertyId);
        fetchCollectionsForProperty(firstPropertyId);
        toast.error('Auto-selected the first property. Please try again.');
      } else {
        toast.error('Please select a property');
      }
      return;
    }

    if (!productIds.trim()) {
      toast.error('Please enter at least one product ID');
      return;
    }

    setIsLoading(true);
    setScraperError(null);

    try {
      // Parse product IDs from textarea (comma or newline separated)
      const ids = productIds
        .split(/[\n,]/)
        .map(id => id.trim())
        .filter(id => id.length > 0);

      if (ids.length === 0) {
        toast.error('No valid product IDs found');
        setIsLoading(false);
        return;
      }

      // Call the scraper API
      const response = await fetch('/api/scrape-products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ids,
          source: productSource
        }),
      });

      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}: ${await response.text()}`);
      }

      const data = await response.json();

      if (data.results && data.results.length > 0) {
        const validProducts = data.results.filter((result: any) => !result.error);
        const errorProducts = data.results.filter((result: any) => result.error);

        setScrapedProducts(data.results);
        setShowPreview(true);

        if (validProducts.length > 0) {
          toast.success(`Successfully scraped ${validProducts.length} products`);
        }

        if (errorProducts.length > 0) {
          toast.warning(`Failed to scrape ${errorProducts.length} products. See details below.`);
          console.log('Scrape errors:', errorProducts.map((p: any) => p.error));
        }
      } else {
        setScraperError("No products were successfully scraped. The scraper might be down or the product IDs might be invalid.");
        toast.error("No products found. Please check the product IDs and try again.");
      }
    } catch (err: any) {
      console.error('Error in bulk import:', err);
      let errorMessage = "An unexpected error occurred during scraping.";
      if (err.message && typeof err.message === 'string') {
        if (err.message.includes('Failed to fetch')) {
          errorMessage = "Failed to connect to the product source. Please check your internet connection and try again.";
        } else if (err.message.includes('404')) {
          errorMessage = "One or more product IDs were not found. Please verify the IDs and try again.";
        }
      }
      setScraperError(err instanceof Error ? err.message : String(err));
      toast.error(`Error importing products: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleImport = () => {
    console.log('[BulkImportDialog] handleImport triggered. isExtensionImport:', isExtensionImport);
    let inventoryItems: Partial<FormattedInventoryItem>[] = [];

    try {
      // If no property is selected yet, select the first available property
      const selectedPropertyId = propertyId || (properties.length > 0 ? properties[0].id : '');
      const selectedCollection = collection || 'Other';

      if (isExtensionImport && extensionItems && extensionItems.length > 0) {
        // If importing from extension, map directly from extensionItems
        // to preserve individual propertyId, collection, quantity, etc.
        inventoryItems = extensionItems.map(extItem => {
          const property = properties.find(p => p.id === (extItem.propertyId || selectedPropertyId));
          return {
            name: extItem.name || 'Unnamed Product',
            propertyId: extItem.propertyId || selectedPropertyId,
            propertyName: property ? property.name : (extItem.propertyName || ''),
            collection: extItem.collection || selectedCollection,
            quantity: typeof extItem.quantity === 'number' ? extItem.quantity : 1,
            minQuantity: typeof extItem.minQuantity === 'number' ? extItem.minQuantity : 0,
            price: typeof extItem.price === 'number' ? extItem.price : 0,
            // Just use the amazonUrl as is since we've already fixed it in AmazonSearch
            amazonUrl: extItem.amazonUrl || '',
            imageUrl: extItem.imageUrl || '',
            // Pass through the flag indicating if the image is already processed
            hasProcessedImage: extItem.hasProcessedImage || false
          };
        });
      } else {
        // If using the scraper, map from scraped products
        inventoryItems = scrapedProducts
          .filter(product => !product.error)
          .map(product => {
            const property = properties.find(p => p.id === selectedPropertyId);
            return {
              name: product.name || 'Unnamed Product',
              propertyId: selectedPropertyId,
              propertyName: property ? property.name : '',
              collection: selectedCollection,
              quantity: 1,
              minQuantity: 0,
              price: typeof product.price === 'number' ? product.price : 0,
              amazonUrl: productSource === 'amazon' ? product.url : '',
              walmartUrl: productSource === 'walmart' ? product.url : '',
              imageUrl: product.imageUrl || '',
              // We're setting hasProcessedImage to false so the image processor will handle it
              hasProcessedImage: false
            };
          });
      }

      if (inventoryItems.length === 0) {
        toast.error('No valid products to import');
        return;
      }

      console.log('[BulkImportDialog] Importing items:', inventoryItems.length);

      // Set importing state to true
      setIsImporting(true);

      // Show a loading toast with a custom ID so we can update it later
      const importToastId = toast.loading(`Importing ${inventoryItems.length} items...`);

      // Actually import the items
      onImport(inventoryItems);

      // Close the dialog after a short delay to allow the import to start
      setTimeout(() => {
        if (isImporting) {
          console.log('[BulkImportDialog] Regular closing after timeout');
          toast.dismiss(importToastId);
          toast.success(`Successfully imported ${inventoryItems.length} items`);
          setIsImporting(false);
          onClose();
        }
      }, 2000); // 2 second delay is enough since the actual import is async

      // Force close the dialog after 5 seconds as a backup
      forceCloseDialog(() => {
        console.log('[BulkImportDialog] Force closing dialog after timeout');
        toast.dismiss(importToastId);
        setIsImporting(false);
        onClose();
      }, 5000, '[BulkImportDialog] Force closing dialog after timeout');
    } catch (error) {
      console.error('Error in handleImport:', error);
      toast.error('Error importing products. Please try again.');
      setIsImporting(false);
    }
  };

  const retryWithSameProduct = () => {
    setShowPreview(false);
    handleScrapeProducts();
  };

  // Render the dialog with proper error handling
  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        onClose();
      }
    }}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Bulk Import Products</DialogTitle>
          <DialogDescription>
            {isExtensionImport
              ? "Review and import products from the browser extension"
              : "Enter product IDs from Amazon or Walmart to automatically import product details"}
          </DialogDescription>
        </DialogHeader>

        {!showPreview ? (
          <form className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="productSource">Product Source</Label>
              <Select
                value={productSource}
                onValueChange={(value: ProductSource) => setProductSource(value)}
              >
                <SelectTrigger className="w-full bg-background">
                  <SelectValue placeholder="Select product source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="amazon">Amazon</SelectItem>
                  <SelectItem value="walmart">Walmart</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="productIds">
                {productSource === 'amazon' ? 'Amazon ASINs' : 'Walmart Product IDs'}
                <span className="text-xs ml-2 text-muted-foreground">
                  (One per line or comma-separated)
                </span>
              </Label>
              <Textarea
                id="productIds"
                value={productIds}
                onChange={(e) => setProductIds(e.target.value)}
                placeholder={productSource === 'amazon'
                  ? "B07JW9H4J1\nB07X6C9RMF"
                  : "123456789\n987654321"}
                className="min-h-[120px]"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="property">Property</Label>
              <div className="flex flex-col gap-2">
                {/* Simplified Select component */}
                {properties && properties.length > 0 && (
                  <Select
                    key={`property-select-${properties[0]?.id || 'none'}`}
                    value={propertyId || ''}
                    onValueChange={(value) => {
                      if (!value) return;

                      console.log('[BulkImportDialog] Property dropdown selection changed to:', value);

                      // Set the property ID directly
                      setPropertyId(value);

                      // Fetch collections for this property
                      fetchCollectionsForProperty(value);
                    }}
                    required
                  >

                    <SelectTrigger id="property-select" className="w-full bg-background">
                      <SelectValue placeholder="Select property" />
                    </SelectTrigger>
                    <SelectContent>
                      {properties.map(property => (
                        <SelectItem key={property.id} value={property.id}>
                          {property.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}

                <div className="w-full mb-1 text-sm font-medium mt-4">Quick select:</div>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 mt-1">
                  {properties && properties.length > 0 ? (
                    properties.map(property => (
                      <Button
                        key={property.id}
                        type="button"
                        size="sm"
                        variant={propertyId === property.id ? "default" : "outline"}
                        className="justify-start truncate w-full"
                        onClick={() => {
                          // Use a direct approach to avoid state update issues
                          const newPropertyId = property.id;
                          console.log('[BulkImportDialog] Property button clicked:', newPropertyId, property.name);

                          // Set the property ID directly
                          setPropertyId(newPropertyId);

                          // Fetch collections for this property
                          fetchCollectionsForProperty(newPropertyId);
                        }}
                      >
                        {property.name}
                      </Button>
                    ))
                  ) : (
                    <div className="text-sm text-muted-foreground italic col-span-full">
                      No properties available
                      <Button
                        variant="link"
                        className="p-0 h-auto text-sm text-primary"
                        onClick={() => {
                          // Close this dialog
                          onClose();
                          // Navigate to properties page
                          window.location.href = '/properties';
                        }}
                      >
                        Add a property
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="collection">Collection</Label>
              <Select
                key={`collection-select-${propertyId || 'none'}`}
                value={collection}
                onValueChange={(value) => {
                  console.log('[BulkImportDialog] Collection changed to:', value);
                  setCollection(value);
                }}
                required
              >
                <SelectTrigger className="w-full bg-background">
                  <SelectValue placeholder="Select collection" />
                </SelectTrigger>
                <SelectContent>
                  {propertyCollections.length > 0 ? (
                    propertyCollections.map((col, index) => (
                      <SelectItem key={index} value={col}>{col}</SelectItem>
                    ))
                  ) : (
                    <>
                      <SelectItem value="Bathroom">Bathroom</SelectItem>
                      <SelectItem value="Kitchen">Kitchen</SelectItem>
                      <SelectItem value="Bedroom">Bedroom</SelectItem>
                      <SelectItem value="Living Room">Living Room</SelectItem>
                      <SelectItem value="Outdoor">Outdoor</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </>
                  )}
                </SelectContent>
              </Select>

              {/* Collection quick select buttons */}
              <div className="flex flex-wrap gap-2 mt-2">
                {['Bathroom', 'Kitchen', 'Bedroom', 'Living Room', 'Other'].map(col => (
                  <Button
                    key={col}
                    type="button"
                    size="sm"
                    variant={collection === col ? "default" : "outline"}
                    className="text-xs"
                    onClick={() => {
                      console.log('[BulkImportDialog] Collection button clicked:', col);
                      setCollection(col);
                    }}
                  >
                    {col}
                  </Button>
                ))}
              </div>
            </div>

            {scraperError && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3 text-sm text-red-800">
                <p className="font-medium">Error occurred during scraping:</p>
                <p>{scraperError}</p>
              </div>
            )}

            <div className="pt-4 flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleScrapeProducts}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Scraping...
                  </>
                ) : "Scrape Products"}
              </Button>
            </div>
          </form>
        ) : (
          <div className="space-y-4">
            <div className="text-sm">
              <p className="font-medium">Found {scrapedProducts.length} products:</p>
              <div className="max-h-[300px] overflow-y-auto mt-2 border rounded-md">
                {scrapedProducts.map((product, index) => (
                  <div key={index} className="p-3 border-b last:border-b-0 flex gap-3">
                    {product.error ? (
                      <div className="text-destructive">
                        Error: {product.error}
                      </div>
                    ) : (
                      <>
                        <div className="w-12 h-12 flex-shrink-0 bg-slate-100 rounded overflow-hidden">
                          {product.imageUrl ? (
                            <div className="relative w-full h-full">
                              {/* Add a placeholder spinner while image loads */}
                              <div className="absolute inset-0 flex items-center justify-center">
                                <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                              </div>
                              <img
                                src={product.imageUrl}
                                alt={product.name || 'Product image'}
                                className="w-full h-full object-contain"
                                onLoad={(e) => {
                                  // Hide spinner when image loads
                                  const parent = e.currentTarget.parentElement;
                                  if (parent) {
                                    const spinner = parent.querySelector('div.absolute');
                                    if (spinner) spinner.classList.add('hidden');
                                  }
                                  setProcessedImages(prev => ({ ...prev, [product.imageUrl || '']: true }));
                                }}
                                onError={(e) => {
                                  console.error('Failed to load image:', product.imageUrl);
                                  e.currentTarget.src = 'https://placehold.co/200?text=No+Image';
                                  // Hide spinner on error too
                                  const parent = e.currentTarget.parentElement;
                                  if (parent) {
                                    const spinner = parent.querySelector('div.absolute');
                                    if (spinner) spinner.classList.add('hidden');
                                  }
                                }}
                              />
                            </div>
                          ) : (
                            <div className="w-full h-full flex items-center justify-center text-slate-400">
                              No image
                            </div>
                          )}
                        </div>
                        <div className="flex-1">
                          <p className="font-medium">{product.name || 'Unnamed Product'}</p>
                          <p className="text-sm text-muted-foreground">
                            {typeof product.price === 'number' ? `$${product.price.toFixed(2)}` : 'Price not available'}
                          </p>
                          {product.url && (
                            <p className="text-xs text-blue-600 truncate">
                              {product.url}
                            </p>
                          )}
                        </div>
                      </>
                    )}
                  </div>
                ))}
              </div>
            </div>

            <div className="pt-4 flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => setShowPreview(false)}>
                Back
              </Button>
              {!isExtensionImport && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={retryWithSameProduct}
                >
                  Retry Scraping
                </Button>
              )}
              <Button
                type="button"
                onClick={handleImport}
                disabled={isImporting}
              >
                {isImporting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Importing...
                  </>
                ) : (
                  'Import Products'
                )}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default BulkImportDialog;
