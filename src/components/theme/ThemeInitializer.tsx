import { useEffect, useRef } from 'react';
import { fetchUserSettings, applyDarkMode, applyCompactMode } from '@/services/settingsService';
import { useAuth } from '@/contexts/AuthContext';

const ThemeInitializer = () => {
  const { authState } = useAuth();
  const hasInitialized = useRef(false);

  useEffect(() => {
    const initializeTheme = async () => {
      // Only run initialization once per session
      if (hasInitialized.current) return;

      try {
        console.log('Initializing theme...');
        // First check localStorage for a theme preference (faster than waiting for DB)
        const savedTheme = localStorage.getItem('theme-mode');

        // Always default to light mode unless explicitly set to dark
        const initialDarkMode = savedTheme === 'dark';

        // Apply without triggering unnecessary DOM mutations
        if (initialDarkMode) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
          // Ensure light mode is set in localStorage
          if (!savedTheme) {
            localStorage.setItem('theme-mode', 'light');
          }
        }

        // Mark as initialized to prevent multiple initializations
        hasInitialized.current = true;

        // Only fetch from DB if user is authenticated and not in loading state
        if (authState?.user?.id && !authState?.isLoading) {
          const settings = await fetchUserSettings(authState.user.id);

          if (settings) {
            // If the current theme doesn't match the DB setting, update it
            if (initialDarkMode !== settings.dark_mode) {
              console.log('ThemeInitializer: Applying theme from DB:', settings.dark_mode);
              applyDarkMode(settings.dark_mode);

              // Also update compact mode
              applyCompactMode(settings.compact_mode);

              // Update localStorage with DB value
              localStorage.setItem('theme-mode', settings.dark_mode ? 'dark' : 'light');
            }
          }
        }
      } catch (error) {
        console.error('Error initializing theme:', error);
      }
    };

    initializeTheme();
  }, [authState?.user?.id, authState?.isLoading]);

  return null; // This is a utility component with no UI
};

export default ThemeInitializer;
