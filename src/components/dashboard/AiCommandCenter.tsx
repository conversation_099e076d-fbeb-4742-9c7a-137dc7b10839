
import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Loader2, Send, Sparkles, AlertCircle, Info } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

interface CommandResult {
  success: boolean;
  message: string;
  action?: string;
  entityType?: string;
  entityId?: string;
}

const AiCommandCenter: React.FC = () => {
  const [command, setCommand] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [result, setResult] = useState<CommandResult | null>(null);
  const { authState } = useAuth();
  const navigate = useNavigate();
  const userId = authState.user?.id;

  const handleCommandChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCommand(e.target.value);
    // Clear previous results when input changes
    if (result) setResult(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!command.trim()) return;
    if (!userId) {
      toast.error("You must be logged in to use the AI assistant");
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      console.log("Sending command to AI:", command);

      // Call Supabase edge function with proper authentication
      const { data, error } = await supabase.functions.invoke('ai-command-processor', {
        body: {
          command,
          userId
        }
      });

      if (error) {
        throw new Error(error.message || 'Failed to process command');
      }

      console.log("Response from AI command processor:", data);

      // Add additional check for empty data
      if (!data) {
        throw new Error('No response received from AI command processor');
      }

      setResult(data);

      if (data.success) {
        toast.success(data.message);

        // If the action created an entity that has a dedicated page, navigate to it or refresh data
        if (data.action && data.entityType && data.entityId) {
          if (data.entityType === 'purchase_order') {
            setTimeout(() => {
              navigate('/purchase-orders');
            }, 1500);
          } else if (data.entityType === 'maintenance_task') {
            // Trigger a refresh of maintenance tasks
            console.log('[AiCommandCenter] Maintenance task created, triggering refresh');
            setTimeout(() => {
              // Dispatch a custom event to refresh maintenance tasks
              window.dispatchEvent(new CustomEvent('force-refresh-maintenance'));

              // Also force a URL update to trigger a re-render
              const timestamp = Math.floor(Date.now() / 1000);
              window.history.replaceState(
                {},
                document.title,
                `${window.location.pathname}?refresh=${timestamp}`
              );
            }, 500);
          }
        }
      } else {
        toast.error(data.message);
      }

    } catch (error) {
      console.error("Error processing AI command:", error);
      setResult({
        success: false,
        message: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
      toast.error("Failed to process command");
    } finally {
      setIsLoading(false);
    }
  };

  const getPlaceholderText = () => {
    const suggestions = [
      "Add a property named Ocean View at 123 Beach Rd with 3 bedrooms",
      "Create a new kitchen collection with a budget of $500",
      "Add maintenance task to fix the broken sink at Mountain Cabin",
      "Create a purchase order for all low stock items",
      "We're down to only one Bath towel, we need a minimum of 12"
    ];

    return suggestions[Math.floor(Math.random() * suggestions.length)];
  };

  return (
    <div className="mb-6">
      <div className="flex items-center gap-1.5 mb-2">
        <div className="w-3 h-3 bg-primary rounded-full"></div>
        <h2 className="font-medium">AI Assistant</h2>
        <button className="ml-auto text-muted-foreground">
          <Info className="h-4 w-4" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="flex gap-2 mb-1">
        <Input
          placeholder={`Try: "${getPlaceholderText()}"`}
          value={command}
          onChange={handleCommandChange}
          className="flex-1"
          disabled={isLoading}
        />
        <Button type="submit" size="icon" className="aspect-square" disabled={!command.trim() || isLoading}>
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Send className="h-4 w-4" />
          )}
        </Button>
      </form>

      <p className="text-xs text-muted-foreground">
        Use natural language to create properties, collections, maintenance tasks, purchase orders, and more.
      </p>

      {result && (
        <div className={`mt-3 p-3 rounded-md text-sm ${
          result.success ? 'bg-green-50 border border-green-100' : 'bg-red-50 border border-red-100'
        }`}>
          <div className="flex items-start gap-2">
            {result.success ? (
              <Sparkles className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
            )}
            <p className={`${result.success ? 'text-green-700' : 'text-red-700'} text-sm`}>
              {result.message}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default AiCommandCenter;
