
import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Provider } from './types';
import ProviderForm from './ProviderForm';

interface ProviderDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (provider: Omit<Provider, 'id'>) => void;
  provider?: Provider;
  mode: 'add' | 'edit';
}

const ProviderDialog: React.FC<ProviderDialogProps> = ({
  open,
  onOpenChange,
  onSave,
  provider,
  mode
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{mode === 'add' ? 'Add New Provider' : 'Edit Provider'}</DialogTitle>
          <DialogDescription>
            Enter the maintenance provider's details below
          </DialogDescription>
        </DialogHeader>
        
        <ProviderForm
          onSave={onSave}
          onCancel={() => onOpenChange(false)}
          provider={provider}
          mode={mode}
          open={open}
        />
      </DialogContent>
    </Dialog>
  );
};

export default ProviderDialog;
