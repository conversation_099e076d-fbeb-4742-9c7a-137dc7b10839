
/**
 * Validation utilities for provider forms
 */

// Validates email format
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Sanitizes text input (removes excessive whitespace, etc)
export const sanitizeText = (text: string): string => {
  return text.trim().replace(/\s+/g, ' ');
};

// Validates provider form data
export const validateProviderForm = (
  name: string, 
  address: string, 
  email: string, 
  phone: string
): Record<string, string> => {
  const errors: Record<string, string> = {};
  
  if (!name) {
    errors.name = 'Business name is required';
  } else if (sanitizeText(name).length < 2) {
    errors.name = 'Business name must be at least 2 characters';
  }
  
  if (!address) {
    errors.address = 'Business address is required';
  }
  
  if (email && !isValidEmail(email)) {
    errors.email = 'Please enter a valid email address';
  }
  
  if (phone) {
    const cleanedPhone = phone.replace(/\D/g, '');
    if (cleanedPhone.length < 10 || cleanedPhone.length > 15) {
      errors.phone = 'Please enter a valid phone number';
    }
  }
  
  return errors;
};
