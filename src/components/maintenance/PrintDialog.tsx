import React, { useRef, useEffect, useState, useMemo } from 'react';
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, DialogHeader, <PERSON><PERSON>Title, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Printer, Download, X, Loader2, Filter, FilterX } from 'lucide-react';
import PrintableMaintenanceList from './PrintableMaintenanceList';
import { MaintenanceTask } from './types';
import { useReactToPrint } from 'react-to-print';
import { toast } from 'sonner';
import html2pdf from 'html2pdf.js';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

interface PrintDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  tasks: MaintenanceTask[];
  filters: {
    status?: string;
    severity?: string;
    provider?: string;
    property?: string;
    showCompleted: boolean;
    sortField: string;
    sortDirection: string;
  };
  providers: Array<{ id: string; name: string }>;
  properties: Array<{ id: string; name: string }>;
}

const PrintDialog: React.FC<PrintDialogProps> = ({
  open,
  onOpenChange,
  tasks,
  filters,
  providers,
  properties
}) => {
  const printRef = useRef<HTMLDivElement>(null);
  const [isExporting, setIsExporting] = useState(false);
  const [useFilters, setUseFilters] = useState(true);

  // Deduplicate tasks by ID
  const uniqueTasks = useMemo(() => {
    const uniqueTasksMap = new Map();

    // First, sort tasks by ID to ensure consistent order
    const sortedTasks = [...tasks].sort((a, b) => {
      // If IDs are the same, use another property to determine order
      if (a.id === b.id) {
        // Use creation date or any other property that makes sense
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      }
      return a.id.localeCompare(b.id);
    });

    // Then add to map to deduplicate
    sortedTasks.forEach(task => {
      if (!uniqueTasksMap.has(task.id)) {
        uniqueTasksMap.set(task.id, task);
      }
    });

    return Array.from(uniqueTasksMap.values());
  }, [tasks]);

  // Ensure the content is ready when the dialog opens
  useEffect(() => {
    if (open && printRef.current) {
      console.log('Print content ready:', printRef.current);
    }
  }, [open, tasks]);

  // Add print-specific styles when dialog opens
  useEffect(() => {
    if (open) {
      const style = document.createElement('style');
      style.id = 'print-styles';
      style.innerHTML = `
        @media print {
          body * {
            visibility: hidden;
          }
          .print-content, .print-content * {
            visibility: visible !important;
          }
          .print-container, .print-container * {
            visibility: visible !important;
          }
          .print-content {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: auto;
            overflow: visible !important;
          }
          .no-print {
            display: none !important;
          }
          @page {
            size: portrait;
            margin: 0.5in;
          }
          table {
            page-break-inside: auto;
          }
          tr {
            page-break-inside: avoid;
            page-break-after: auto;
          }
          thead {
            display: table-header-group;
          }
          tfoot {
            display: table-footer-group;
          }
        }
      `;
      document.head.appendChild(style);

      return () => {
        const styleElement = document.getElementById('print-styles');
        if (styleElement) {
          document.head.removeChild(styleElement);
        }
      };
    }
  }, [open]);

  // Handle print functionality
  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: 'Maintenance Tasks Report',
    onBeforeGetContent: () => {
      return new Promise<void>((resolve) => {
        if (!printRef.current) {
          toast.error('Print content not ready');
          return;
        }
        toast.info('Preparing document...');
        setTimeout(resolve, 500);
      });
    },
    onAfterPrint: () => {
      toast.success('Document printed successfully!');
    },
    onPrintError: (error) => {
      console.error('Print error:', error);
      toast.error('Failed to print document');
    },
    removeAfterPrint: true,
  });

  // Handle PDF export using html2pdf.js
  const handleExportPDF = async () => {
    if (!printRef.current) {
      toast.error('Print content not ready');
      return;
    }

    try {
      setIsExporting(true);
      toast.info('Preparing PDF document...');

      const element = printRef.current;

      // Add a class to the element for better PDF rendering
      element.classList.add('pdf-export-mode');

      // Configure html2pdf options
      const options = {
        margin: [15, 15, 15, 15], // [top, right, bottom, left] in mm
        filename: 'Maintenance Tasks Report.pdf',
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
          scale: 2, // Higher scale for better quality
          useCORS: true,
          logging: false,
          allowTaint: true,
          backgroundColor: '#ffffff',
          windowWidth: 1200,
        },
        jsPDF: {
          unit: 'mm',
          format: 'a4',
          orientation: 'portrait'
        },
        pagebreak: {
          mode: ['avoid-all', 'css', 'legacy'],
          before: '.page-break-before',
          after: '.page-break-after',
          avoid: ['tr', 'td', '.task-row']
        }
      };

      // Apply additional styling for better PDF rendering
      const rows = element.querySelectorAll('tr');
      rows.forEach(row => {
        row.classList.add('task-row');
        row.style.pageBreakInside = 'avoid';
        row.style.breakInside = 'avoid';
      });

      // Ensure table headers appear on each page
      const thead = element.querySelector('thead');
      if (thead) {
        thead.style.display = 'table-header-group';
      }

      // Ensure table footers appear on each page
      const tfoot = element.querySelector('tfoot');
      if (tfoot) {
        tfoot.style.display = 'table-footer-group';
      }

      // Generate the PDF
      await html2pdf()
        .from(element)
        .set(options)
        .save();

      // Remove the classes after rendering
      element.classList.remove('pdf-export-mode');

      toast.success('PDF exported successfully!');
    } catch (error) {
      console.error('Error exporting PDF:', error);
      toast.error('Failed to export PDF');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Print Maintenance Tasks</span>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onOpenChange(false)}
              className="no-print"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
          <DialogDescription>
            Preview and print the current filtered list of maintenance tasks
          </DialogDescription>
        </DialogHeader>

        <div className="my-4 no-print">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="use-filters"
                checked={useFilters}
                onCheckedChange={setUseFilters}
              />
              <Label htmlFor="use-filters" className="flex items-center cursor-pointer">
                {useFilters ? (
                  <>
                    <Filter className="h-4 w-4 mr-2 text-primary" />
                    Apply current filters
                  </>
                ) : (
                  <>
                    <FilterX className="h-4 w-4 mr-2 text-muted-foreground" />
                    Show all tasks
                  </>
                )}
              </Label>
            </div>
            <div className="text-sm text-muted-foreground">
              {useFilters
                ? `Showing ${uniqueTasks.filter(t =>
                    (!filters.status || t.status === filters.status) &&
                    (!filters.severity || t.severity === filters.severity) &&
                    (!filters.provider || t.providerId === filters.provider) &&
                    (!filters.property || t.propertyId === filters.property) &&
                    (filters.showCompleted || filters.status === 'completed' ||
                      (t.status !== 'completed' && t.status !== 'cancelled'))
                  ).length} filtered tasks`
                : `Showing all ${uniqueTasks.length} tasks`}
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={handlePrint}
              className="flex items-center gap-2"
            >
              <Printer className="h-4 w-4" />
              Print
            </Button>
            <Button
              onClick={handleExportPDF}
              className="flex items-center gap-2"
              disabled={isExporting}
            >
              {isExporting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4" />
                  Export as PDF
                </>
              )}
            </Button>
          </div>
        </div>

        <div id="print-container" className="print-content" ref={printRef} style={{ minHeight: '500px' }}>
          {uniqueTasks.length > 0 ? (
            <PrintableMaintenanceList
              tasks={uniqueTasks}
              filters={{
                ...filters,
                // If useFilters is false, set all filter values to undefined to show all tasks
                status: useFilters ? filters.status : undefined,
                severity: useFilters ? filters.severity : undefined,
                provider: useFilters ? filters.provider : undefined,
                property: useFilters ? filters.property : undefined,
                showCompleted: useFilters ? filters.showCompleted : true
              }}
              providers={providers}
              properties={properties}
            />
          ) : (
            <div className="text-center py-8">
              <p className="text-lg text-gray-500">No tasks match the current filters</p>
              <p className="text-sm text-gray-400 mt-2">Try changing your filters to see more tasks</p>
            </div>
          )}
        </div>

        <DialogFooter className="no-print mt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PrintDialog;
