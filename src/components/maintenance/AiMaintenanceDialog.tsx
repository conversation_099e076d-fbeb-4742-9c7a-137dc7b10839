
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Loader2, AlertCircle, CheckCircle2 } from 'lucide-react';
import { Provider } from './types';
import { supabase } from '@/integrations/supabase/client';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

interface AiMaintenanceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTasksCreated: () => void;
  providers: Provider[];
}

const AiMaintenanceDialog: React.FC<AiMaintenanceDialogProps> = ({
  open,
  onOpenChange,
  onTasksCreated,
  providers
}) => {
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { authState } = useAuth();
  const userId = authState?.user?.id;

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputText(e.target.value);
    // Clear previous error when input changes
    if (error) {
      setError(null);
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    const pastedText = e.clipboardData.getData('text');
    setInputText(pastedText);
  };

  const handleAnalyze = async () => {
    if (!inputText.trim()) {
      setError('Please enter some text to analyze');
      return;
    }

    if (!userId) {
      setError('You must be logged in to use the AI assistant');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log("Sending command to AI:", inputText);

      // Use the same working ai-command-processor that the main dashboard uses
      const { data, error: invokeError } = await supabase.functions.invoke('ai-command-processor', {
        body: {
          command: inputText,
          userId: userId
        },
      });

      console.log("Response from function:", data, "Error:", invokeError);

      if (invokeError) {
        console.error("Invoke error:", invokeError);
        throw new Error(`Error calling AI service: ${invokeError.message}`);
      }

      if (!data) {
        throw new Error('No data received from AI service');
      }

      // Handle the ai-command-processor response format
      if (data.success) {
        console.log('AI command processor response:', data);

        // The ai-command-processor creates tasks directly and returns success
        toast.success(data.message || 'Successfully created maintenance task');
        onTasksCreated();
        onOpenChange(false);
        // Reset form
        setInputText('');
      } else {
        // If not successful, show the error message
        const errorMessage = data.message || 'Failed to process AI command';
        setError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (err: any) {
      console.error('Error analyzing text:', err);
      setError(err.message || 'Failed to analyze text. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };



  const handleClear = () => {
    setInputText('');
    setError(null);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Generate Maintenance Tasks with AI</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="maintenance-text">
              Paste text describing maintenance needs
            </Label>
            <Textarea
              id="maintenance-text"
              value={inputText}
              onChange={handleInputChange}
              onPaste={handlePaste}
              placeholder="Paste text here... e.g., 'The kitchen sink at Ocean View has a leak due by next Friday, and the hallway light bulb needs to be replaced as soon as possible.'"
              className="min-h-[150px]"
            />
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleAnalyze}
              disabled={isLoading || !inputText.trim()}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Analyzing...
                </>
              ) : 'Generate Tasks'}
            </Button>
            <Button
              variant="outline"
              onClick={handleClear}
              disabled={isLoading || !inputText}
            >
              Clear
            </Button>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}


        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isLoading}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AiMaintenanceDialog;
