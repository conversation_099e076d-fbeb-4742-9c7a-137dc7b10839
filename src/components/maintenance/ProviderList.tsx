
import React, { useState } from 'react';
import { Provider } from './types';
import { Button } from '@/components/ui/button';
import { Edit, Trash2, Plus, Building } from 'lucide-react';
import ProviderDialog from './ProviderDialog';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

interface ProviderListProps {
  providers: Provider[];
  onAddProvider: (provider: Omit<Provider, 'id'>) => void;
  onUpdateProvider: (id: string, provider: Omit<Provider, 'id'>) => void;
  onDeleteProvider: (id: string) => void;
}

const ProviderList: React.FC<ProviderListProps> = ({
  providers,
  onAddProvider,
  onUpdateProvider,
  onDeleteProvider
}) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<'add' | 'edit'>('add');
  const [selectedProvider, setSelectedProvider] = useState<Provider | undefined>(undefined);
  const { authState } = useAuth();

  console.log("ProviderList rendering with providers:", providers);

  const handleAddClick = () => {
    setDialogMode('add');
    setSelectedProvider(undefined);
    setDialogOpen(true);
  };

  const handleEditClick = (provider: Provider) => {
    setDialogMode('edit');
    setSelectedProvider(provider);
    setDialogOpen(true);
  };

  const handleDeleteClick = (id: string) => {
    if (confirm('Are you sure you want to delete this provider?')) {
      onDeleteProvider(id);
    }
  };

  const handleSaveProvider = (providerData: Omit<Provider, 'id'>) => {
    if (dialogMode === 'add') {
      onAddProvider(providerData);
    } else {
      if (selectedProvider) {
        onUpdateProvider(selectedProvider.id, providerData);
      }
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Maintenance Providers</h2>
        <Button onClick={handleAddClick} className="flex items-center gap-2">
          <Plus size={16} />
          Add Provider
        </Button>
      </div>

      {providers.length === 0 ? (
        <div className="text-center py-8 border border-dashed rounded-lg">
          <Building className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-2 text-lg font-medium">No providers added yet</h3>
          <p className="text-sm text-muted-foreground mt-1">
            Add maintenance providers to assign tasks to them.
          </p>
          <Button onClick={handleAddClick} variant="outline" className="mt-4">
            Add First Provider
          </Button>
        </div>
      ) : (
        <div className="grid gap-4">
          {providers.map((provider) => (
            <div
              key={provider.id}
              className="p-4 border rounded-lg hover:border-primary/50 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div>
                  <div className="flex flex-wrap items-center gap-2">
                    <h3 className="font-medium">{provider.name}</h3>
                    {provider.user_id && provider.user_id !== authState.user?.id && (
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">Team Provider</span>
                    )}
                    {/* {provider.notes && provider.notes.includes('Added from team') && ( */}
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full ml-1">Team Member</span>
                    {/* )} */}
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">{provider.address}</p>
                  <div className="mt-1 grid gap-1">
                    <p className="text-sm">{provider.phone}</p>
                    <p className="text-sm">{provider.email}</p>
                  </div>
                </div>
                <div className="flex gap-2">
                  {(!provider.user_id || provider.user_id === authState.user?.id) && (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditClick(provider)}
                      >
                        <Edit size={16} />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteClick(provider.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 size={16} />
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <ProviderDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onSave={handleSaveProvider}
        provider={selectedProvider}
        mode={dialogMode}
      />
    </div>
  );
};

export default ProviderList;
