// Add these types if they're not already defined

export type MaintenanceStatus =
  | 'new'
  | 'assigned'
  | 'in_progress'
  | 'completed'
  | 'cancelled'
  | 'accepted'
  | 'rejected'
  | 'open'
  | 'pending';

export type MaintenanceSeverity = 'low' | 'medium' | 'high' | 'critical';

export interface MaintenanceTask {
  id: string;
  title: string;
  description: string;
  propertyId?: string;
  propertyName: string;
  status: MaintenanceStatus;
  severity: MaintenanceSeverity;
  dueDate: string;
  assignedTo?: string;
  createdAt: string;
  providerId?: string;
  providerEmail?: string;
  userId?: string; // The user who created this task
}

export interface Provider {
  id: string;
  name: string;
  email: string;
  phone: string;
  specialty: string;
  address: string;
  user_id?: string; // The user who created this provider
}

// Define and export MaintenanceTaskFilters
export interface MaintenanceTaskFilters {
  status?: MaintenanceStatus;
  severity?: MaintenanceSeverity;
  propertyId?: string;
  providerId?: string;
  showCompleted?: boolean;
  // Add other potential filter fields as needed
}
