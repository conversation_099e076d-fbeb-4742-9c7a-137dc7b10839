
import React from 'react';
import { Button } from '@/components/ui/button';
import Provider<PERSON><PERSON><PERSON>ield from './ProviderFormField';
import { DialogFooter } from '@/components/ui/dialog';
import { useProviderForm } from './hooks/useProviderForm';
import { Provider } from './types';

interface ProviderFormProps {
  onSave: (provider: Omit<Provider, 'id'>) => void;
  onCancel: () => void;
  provider?: Provider;
  mode: 'add' | 'edit';
  open: boolean;
}

const ProviderForm: React.FC<ProviderFormProps> = ({
  onSave,
  onCancel,
  provider,
  mode,
  open
}) => {
  const {
    name,
    setName,
    address,
    setAddress,
    phone,
    setPhone,
    email,
    setEmail,
    specialty,
    setSpecialty,
    formErrors,
    resetForm,
    validateForm,
    prepareProviderData,
    hasError
  } = useProviderForm({ provider, mode, open });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Submitting provider form with data:', { name, address, phone, email, specialty });
    
    if (!validateForm()) {
      console.log('Form validation failed with errors:', formErrors);
      return;
    }
    
    const newProvider = prepareProviderData();
    console.log('Sending sanitized provider data:', newProvider);
    onSave(newProvider);
    resetForm();
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="grid gap-4 py-4">
        <ProviderFormField
          id="name"
          label="Business Name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          error={formErrors.name}
          required
        />
        
        <ProviderFormField
          id="address"
          label="Business Address"
          value={address}
          onChange={(e) => setAddress(e.target.value)}
          error={formErrors.address}
          required
        />
        
        <ProviderFormField
          id="phone"
          label="Phone Number"
          value={phone}
          onChange={(e) => setPhone(e.target.value)}
          error={formErrors.phone}
        />
        
        <ProviderFormField
          id="email"
          label="Email Address"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          error={formErrors.email}
          type="email"
        />
        
        <ProviderFormField
          id="specialty"
          label="Specialty"
          value={specialty}
          onChange={(e) => setSpecialty(e.target.value)}
        />
      </div>
      
      <DialogFooter>
        <Button type="button" variant="outline" onClick={() => {
          resetForm();
          onCancel();
        }}>
          Cancel
        </Button>
        <Button type="submit">{mode === 'add' ? 'Add Provider' : 'Save Changes'}</Button>
      </DialogFooter>
    </form>
  );
};

export default ProviderForm;
