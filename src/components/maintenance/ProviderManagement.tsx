
import React, { useEffect } from 'react';
import ProviderList from './ProviderList';
import { Provider } from './types';
import { useAuth } from '@/contexts/AuthContext';

interface ProviderManagementProps {
  providers: Provider[];
  onAddProvider: (provider: Omit<Provider, 'id'>) => void;
  onUpdateProvider: (id: string, provider: Omit<Provider, 'id'>) => void;
  onDeleteProvider: (id: string) => void;
}

const ProviderManagement: React.FC<ProviderManagementProps> = ({
  providers,
  onAddProvider,
  onUpdateProvider,
  onDeleteProvider
}) => {
  const { authState } = useAuth();
  
  // Debugging: Log provider information and auth state when the component renders or providers change
  useEffect(() => {
    console.log("[ProviderManagement] Auth state:", authState);
    console.log("[ProviderManagement] Providers updated:", providers);
  }, [providers, authState]);
  
  const handleAddProvider = (provider: Omit<Provider, 'id'>) => {
    console.log("[ProviderManagement] Adding provider:", provider);
    if (!authState.user?.id) {
      console.error("[ProviderManagement] Cannot add provider: User not logged in");
      return;
    }
    onAddProvider(provider);
  };
  
  const handleUpdateProvider = (id: string, provider: Omit<Provider, 'id'>) => {
    console.log("[ProviderManagement] Updating provider:", id, provider);
    if (!authState.user?.id) {
      console.error("[ProviderManagement] Cannot update provider: User not logged in");
      return;
    }
    onUpdateProvider(id, provider);
  };
  
  const handleDeleteProvider = (id: string) => {
    console.log("[ProviderManagement] Deleting provider:", id);
    if (!authState.user?.id) {
      console.error("[ProviderManagement] Cannot delete provider: User not logged in");
      return;
    }
    onDeleteProvider(id);
  };
  
  return (
    <div className="container mx-auto px-4 py-2 max-w-6xl">
      <ProviderList 
        providers={providers}
        onAddProvider={handleAddProvider}
        onUpdateProvider={handleUpdateProvider}
        onDeleteProvider={handleDeleteProvider}
      />
    </div>
  );
};

export default ProviderManagement;
