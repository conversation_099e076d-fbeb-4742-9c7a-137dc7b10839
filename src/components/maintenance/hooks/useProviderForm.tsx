
import { useState, useEffect } from 'react';
import { Provider } from '../types';
import { validateProviderForm, sanitizeText } from '../utils/providerValidation';

interface UseProviderFormProps {
  provider?: Provider;
  mode: 'add' | 'edit';
  open: boolean;
}

export const useProviderForm = ({ provider, mode, open }: UseProviderFormProps) => {
  const [name, setName] = useState('');
  const [address, setAddress] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [specialty, setSpecialty] = useState('');
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Initialize form with provider data when editing
  useEffect(() => {
    if (provider && mode === 'edit') {
      console.log('Editing provider with data:', provider);
      setName(provider.name || '');
      setAddress(provider.address || '');
      setPhone(provider.phone || '');
      setEmail(provider.email || '');
      setSpecialty(provider.specialty || '');
    } else {
      resetForm();
    }
    // Clear errors when dialog opens/closes
    setFormErrors({});
  }, [provider, mode, open]);

  const resetForm = () => {
    setName('');
    setAddress('');
    setPhone('');
    setEmail('');
    setSpecialty('');
    setFormErrors({});
  };

  const validateForm = (): boolean => {
    const errors = validateProviderForm(name, address, email, phone);
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const prepareProviderData = (): Omit<Provider, 'id'> => {
    return {
      name: sanitizeText(name),
      address: sanitizeText(address),
      phone: phone.trim(),
      email: email.trim().toLowerCase(),
      specialty: sanitizeText(specialty)
    };
  };

  return {
    name,
    setName,
    address,
    setAddress,
    phone, 
    setPhone,
    email,
    setEmail,
    specialty,
    setSpecialty,
    formErrors,
    resetForm,
    validateForm,
    prepareProviderData,
    hasError: (field: string) => !!formErrors[field]
  };
};
