
import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { AlertCircle } from 'lucide-react';

interface ProviderFormFieldProps {
  id: string;
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  error?: string;
  required?: boolean;
  type?: string;
}

const ProviderFormField: React.FC<ProviderFormFieldProps> = ({
  id,
  label,
  value,
  onChange,
  error,
  required = false,
  type = 'text'
}) => {
  const hasError = !!error;
  
  return (
    <div className="grid grid-cols-4 items-start gap-4">
      <Label htmlFor={id} className="text-right pt-2">{label}</Label>
      <div className="col-span-3 space-y-1">
        <Input
          id={id}
          type={type}
          value={value}
          onChange={onChange}
          className={hasError ? 'border-red-500' : ''}
          required={required}
        />
        {hasError && (
          <div className="text-sm text-red-500 flex items-center gap-1">
            <AlertCircle className="h-3 w-3" /> {error}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProviderFormField;
