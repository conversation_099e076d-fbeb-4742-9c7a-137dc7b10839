
import React, { useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { Provider, MaintenanceTask } from './types';
import MaintenanceTaskForm from './forms/MaintenanceTaskForm';
import { useMaintenanceTaskForm } from './hooks/useMaintenanceTaskForm';
import { toast } from 'sonner';
import Tooltip from '@/components/ui/Tooltip';
import { HelpCircle } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { syncPropertyCalendarIfNeeded } from '@/utils/calendarUtils';

interface AddMaintenanceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTaskCreated: () => void;
  providers?: Provider[];
  initialTask?: MaintenanceTask; // For edit mode
}

const AddMaintenanceDialog: React.FC<AddMaintenanceDialogProps> = ({
  open,
  onOpenChange,
  onTaskCreated,
  providers = [],
  initialTask
}) => {
  const { authState } = useAuth();
  const userId = authState.user?.id;
  const isEditMode = !!initialTask;

  // Sync property calendar when dialog opens and property is selected
  useEffect(() => {
    if (open && initialTask?.propertyId && userId) {
      // Fetch the property details first
      const fetchPropertyAndSync = async () => {
        try {
          const { data: property, error } = await supabase
            .from('properties')
            .select('*')
            .eq('id', initialTask.propertyId)
            .single();

          if (error) {
            console.error('[AddMaintenanceDialog] Error fetching property:', error);
            return;
          }

          if (property) {
            console.log('[AddMaintenanceDialog] Syncing calendar for property:', property.name);
            await syncPropertyCalendarIfNeeded(property, userId);
          }
        } catch (error) {
          console.error('[AddMaintenanceDialog] Error syncing property calendar:', error);
        }
      };

      fetchPropertyAndSync();
    }
  }, [open, initialTask, userId]);

  const {
    formState,
    handleSubmit,
    resetForm,
    syncCalendarData
  } = useMaintenanceTaskForm(onTaskCreated, onOpenChange, providers, initialTask);

  const handleCancel = () => {
    resetForm();
    onOpenChange(false);
  };

  // Generate a simple token for the response links
  const generateResponseToken = async (taskId: string) => {
    if (!taskId) {
      console.error('Cannot generate token: taskId is undefined');
      return '';
    }

    try {
      const encoder = new TextEncoder();
      const data = encoder.encode(taskId + "maintenance-response-secret");
      const hashBuffer = await crypto.subtle.digest("SHA-256", data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      return hashArray.map(b => b.toString(16).padStart(2, "0")).join("").substring(0, 32);
    } catch (error) {
      console.error('Error generating response token:', error);
      return '';
    }
  };

  const sendProviderEmail = async (task: MaintenanceTask) => {
    if (!task.providerId || !task.providerEmail || !task.id) {
      console.error('Missing required provider information or task ID');
      return false;
    }

    try {
      console.log('Preparing to send provider email for task:', task.id);
      const provider = providers.find(p => p.id === task.providerId);
      const userEmail = authState.user?.email;

      const responseToken = await generateResponseToken(task.id);
      if (!responseToken) {
        throw new Error('Failed to generate response token');
      }

      // Use the current app URL for the response handler with proper hash routing
      const baseUrl = window.location.origin;
      const functionUrl = `${baseUrl}/#/maintenance/response`;

      const acceptUrl = `${functionUrl}?taskId=${task.id}&action=accept&token=${responseToken}`;
      const rejectUrl = `${functionUrl}?taskId=${task.id}&action=decline&token=${responseToken}`;

      console.log('Sending provider email with response URLs:', {
        acceptUrl,
        rejectUrl
      });

      const { data, error } = await supabase.functions.invoke('send-email', {
        body: {
          from: `Property Manager <<EMAIL>>`,
          to: task.providerEmail,
          subject: isEditMode ?
            `Updated Maintenance Task: ${task.title}` :
            `New Maintenance Task: ${task.title}`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2>${isEditMode ? 'Updated Maintenance Task' : 'New Maintenance Task'}: ${task.title}</h2>
              <p><strong>Property:</strong> ${task.propertyName || 'Not specified'}</p>
              <p><strong>Severity:</strong> ${task.severity}</p>
              <p><strong>Due Date:</strong> ${task.dueDate || 'No due date'}</p>
              <p><strong>Description:</strong> ${task.description}</p>
              <p>Please contact ${userEmail || 'the property manager'} for more information.</p>

              <div style="margin: 30px 0;">
                <p><strong>Are you available to handle this task?</strong></p>
                <table cellpadding="0" cellspacing="0" border="0">
                  <tr>
                    <td style="padding-right: 15px;">
                      <a href="${acceptUrl}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
                        Accept Task
                      </a>
                    </td>
                    <td>
                      <a href="${rejectUrl}" style="display: inline-block; background-color: #e53935; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
                        Decline Task
                      </a>
                    </td>
                  </tr>
                </table>
              </div>

              <p style="font-size: 0.8em; color: #666;">
                By accepting, you agree to take responsibility for this maintenance task.
                If you decline, the property manager will be notified to find another service provider.
              </p>

              <div style="margin-top: 30px;">
                <p><strong>Add to your calendar:</strong></p>
                <a href="${generateGoogleCalendarUrl(task)}" target="_blank" style="display: inline-block; background-color: #1a73e8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold; margin-right: 10px;">
                  Google Calendar
                </a>
                <a href="${generateIcsCalendarUrl(task)}" download="maintenance-task.ics" style="display: inline-block; background-color: #1a73e8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
                  Apple Calendar (.ics)
                </a>
              </div>
            </div>
          `,
          reply_to: userEmail
        }
      });

      if (error) {
        console.error('Error invoking send-email function:', error);
        throw error;
      }

      console.log('Provider notification email sent successfully:', data);
      return true;
    } catch (error) {
      console.error('Error sending provider notification:', error);
      // Don't show toast to user since this is a background operation
      return false;
    }
  };

  // Generate a Google Calendar URL
  const generateGoogleCalendarUrl = (task: MaintenanceTask) => {
    const title = `Maintenance: ${task.title}`;
    const description = `Maintenance task: ${task.title}\nProperty: ${task.propertyName || 'Not specified'}\nSeverity: ${task.severity}\nDescription: ${task.description || ''}`;
    const location = task.propertyName || '';

    // Parse the due date or use a default (2 weeks from now)
    let dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 14);

    if (task.dueDate && task.dueDate !== 'No due date') {
      try {
        const parsedDate = new Date(task.dueDate);
        if (!isNaN(parsedDate.getTime())) {
          dueDate = parsedDate;
        }
      } catch (e) {
        console.error("Error parsing due date:", e);
      }
    }

    // Format dates for Google Calendar
    const startDate = dueDate.toISOString().replace(/-|:|\.\d\d\d/g, '');
    const endDate = new Date(dueDate.getTime() + 2 * 60 * 60 * 1000).toISOString().replace(/-|:|\.\d\d\d/g, '');

    return `https://www.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(title)}&dates=${startDate}/${endDate}&details=${encodeURIComponent(description)}&location=${encodeURIComponent(location)}&sf=true&output=xml`;
  };

  // Generate an ICS calendar URL for Apple Calendar
  const generateIcsCalendarUrl = (task: MaintenanceTask) => {
    const title = `Maintenance: ${task.title}`;
    const description = `Maintenance task: ${task.title}\nProperty: ${task.propertyName || 'Not specified'}\nSeverity: ${task.severity}\nDescription: ${task.description || ''}`;
    const location = task.propertyName || '';

    // Parse the due date or use a default (2 weeks from now)
    let dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 14);

    if (task.dueDate && task.dueDate !== 'No due date') {
      try {
        const parsedDate = new Date(task.dueDate);
        if (!isNaN(parsedDate.getTime())) {
          dueDate = parsedDate;
        }
      } catch (e) {
        console.error("Error parsing due date:", e);
      }
    }

    // Format dates for ICS
    const formatDate = (date: Date) => {
      return date.toISOString().replace(/-|:|\.\d\d\d/g, '');
    };

    const startDate = formatDate(dueDate);
    const endDate = formatDate(new Date(dueDate.getTime() + 2 * 60 * 60 * 1000)); // 2 hours later
    const createdDate = formatDate(new Date());

    const icsContent = `BEGIN:VCALENDAR
VERSION:2.0
CALSCALE:GREGORIAN
PRODID:-//Maintenance Task//EN
METHOD:PUBLISH
BEGIN:VEVENT
UID:${new Date().getTime()}-maintenance-task
SUMMARY:${title}
DESCRIPTION:${description.replace(/\n/g, '\\n')}
LOCATION:${location}
DTSTART:${startDate}
DTEND:${endDate}
DTSTAMP:${createdDate}
STATUS:CONFIRMED
SEQUENCE:0
END:VEVENT
END:VCALENDAR`;

    return `data:text/calendar;charset=utf-8,${encodeURIComponent(icsContent)}`;
  };

  const handleFormSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    try {
      const taskData = await handleSubmit(e);

      // If task was successfully created/updated and has a provider assigned, send email
      if (taskData && taskData.providerId && taskData.providerEmail && taskData.id) {
        const emailSent = await sendProviderEmail(taskData);
        if (emailSent) {
          console.log('Email notification sent to provider successfully');
        } else {
          console.warn('Failed to send email notification to provider');
        }
      } else {
        console.log('No provider email sent - missing required fields:', {
          taskId: taskData?.id,
          providerId: taskData?.providerId,
          providerEmail: taskData?.providerEmail
        });
      }

      toast.success(isEditMode ? 'Task updated successfully!' : 'New task created successfully!');
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error('There was a problem saving the task');
    }
  };

  // Debug providers
  console.log('[AddMaintenanceDialog] Providers:', providers);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader className="flex flex-row items-center justify-between">
          <div>
            <DialogTitle>{isEditMode ? 'Edit Maintenance Task' : 'Add New Maintenance Task'}</DialogTitle>
            <p className="text-sm text-muted-foreground mt-1">
              {isEditMode ? 'Update the details of this maintenance task' : 'Create a new maintenance task for a property'}
            </p>
          </div>
          <Tooltip
            text={
              isEditMode
              ? "Update the details of this maintenance task"
              : "Create a new maintenance task for a property"
            }
            position="left"
          >
            <HelpCircle size={16} className="text-muted-foreground" />
          </Tooltip>
        </DialogHeader>

        <form onSubmit={handleFormSubmit}>
          <MaintenanceTaskForm
            title={formState.title}
            setTitle={formState.setTitle}
            description={formState.description}
            setDescription={formState.setDescription}
            selectedPropertyId={formState.selectedPropertyId}
            setSelectedPropertyId={formState.setSelectedPropertyId}
            severity={formState.severity}
            setSeverity={formState.setSeverity}
            status={formState.status}
            setStatus={formState.setStatus}
            dueDate={formState.dueDate}
            setDueDate={formState.setDueDate}
            selectedProviderId={formState.selectedProviderId}
            setSelectedProviderId={formState.setSelectedProviderId}
            assignedTo={formState.assignedTo || ''}
            setAssignedTo={formState.setAssignedTo}
            properties={formState.properties}
            providers={providers}
            editMode={isEditMode}
            nextCheckInDate={formState.nextCheckInDate}
            checkOutDate={formState.checkOutDate}
            isOccupied={formState.isOccupied}
            isSyncingCalendar={formState.isSyncingCalendar}
            onSyncCalendar={syncCalendarData}
          />

          <DialogFooter className="flex-col sm:flex-row sm:justify-between mt-6">
            <Tooltip text="Cancel changes and close this dialog" position="top">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                className="w-full sm:w-auto mb-2 sm:mb-0"
              >
                Cancel
              </Button>
            </Tooltip>
            <Tooltip
              text={isEditMode ? "Save changes to this task" : "Create this new maintenance task"}
              position="top"
            >
              <Button
                type="submit"
                disabled={formState.isLoading}
                className="w-full sm:w-auto"
              >
                {formState.isLoading ? (isEditMode ? 'Updating...' : 'Creating...') : (isEditMode ? 'Update Task' : 'Create Task')}
              </Button>
            </Tooltip>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddMaintenanceDialog;
