import React from 'react';
import { usePermissions } from '@/hooks/usePermissions';
import { PermissionType } from '@/types/auth';
import { useAuth } from '@/contexts/AuthContext';

interface PermissionGuardProps {
  children: React.ReactNode;
  permission: PermissionType;
  teamId?: string;
  fallback?: React.ReactNode;
}

/**
 * A component that conditionally renders its children based on user permissions.
 * 
 * @param children - The content to render if the user has the required permission
 * @param permission - The permission required to view the content
 * @param teamId - Optional team ID to check permission against
 * @param fallback - Optional content to render if the user doesn't have permission
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  teamId,
  fallback = null
}) => {
  const { hasPermission, isAdmin } = usePermissions();
  const { authState } = useAuth();

  // Admins and super admins can see everything
  if (isAdmin()) {
    return <>{children}</>;
  }

  // Property managers can see everything
  if (authState?.profile?.role === 'property_manager') {
    return <>{children}</>;
  }

  // Check if the user has the specific permission
  if (hasPermission(permission, teamId)) {
    return <>{children}</>;
  }

  // User doesn't have permission, render fallback or nothing
  return <>{fallback}</>;
};

export default PermissionGuard;
