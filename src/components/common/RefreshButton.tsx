import React from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, RefreshCw } from 'lucide-react';
import { useGlobalDataRefresh } from '@/contexts/GlobalDataRefreshContext';
import { useLocation } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';

interface RefreshButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  refreshAll?: boolean;
  className?: string;
  label?: string;
  queryKeys?: string[]; // Optional specific query keys to refresh
}

/**
 * A simplified RefreshButton component that uses React Query's built-in functionality
 * to refresh data. This component can refresh all data or specific query keys.
 */
const RefreshButton: React.FC<RefreshButtonProps> = ({
  variant = 'outline',
  size = 'sm',
  refreshAll = false,
  className = '',
  label = 'Refresh',
  queryKeys
}) => {
  const { refreshAllData, refreshRouteData, isRefreshing } = useGlobalDataRefresh();
  const location = useLocation();
  const queryClient = useQueryClient();

  const handleRefresh = async () => {
    if (isRefreshing) return;

    // If specific query keys are provided, refresh only those
    if (queryKeys && queryKeys.length > 0) {
      console.log(`[RefreshButton] Refreshing specific query keys: ${queryKeys.join(', ')}`);

      // Invalidate and refetch the specific query keys
      for (const key of queryKeys) {
        await queryClient.invalidateQueries({ queryKey: [key], exact: false });
        await queryClient.refetchQueries({ queryKey: [key], exact: false, type: 'all' });
      }
    }
    // Otherwise use the global refresh functions
    else if (refreshAll) {
      await refreshAllData();
    } else {
      await refreshRouteData(location.pathname);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleRefresh}
      disabled={isRefreshing}
      className={className}
    >
      {isRefreshing ? (
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <RefreshCw className="mr-2 h-4 w-4" />
      )}
      {label}
    </Button>
  );
};

export default RefreshButton;
