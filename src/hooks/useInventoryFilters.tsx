
import { useState, useMemo } from 'react';
import { FormattedInventoryItem } from '@/types/inventory';

export interface FilterOptions {
  property: string;
  collection: string;
  lowStock: boolean;
}

export function useInventoryFilters(inventoryItems: FormattedInventoryItem[]) {
  // State for search and filters
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<FilterOptions>({
    property: '',
    collection: '',
    lowStock: false
  });
  const [showFilters, setShowFilters] = useState(false);

  // Extract unique collections for the filter dropdown
  const collections = useMemo(() => {
    const uniqueCollections = new Set<string>();
    inventoryItems.forEach(item => {
      if (item.collection) {
        uniqueCollections.add(item.collection);
      }
    });
    return Array.from(uniqueCollections).sort();
  }, [inventoryItems]);

  // Apply filters and search query to items
  const filteredItems = useMemo(() => {
    return inventoryItems.filter(item => {
      // Apply search filter
      const matchesSearch = !searchQuery || 
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.collection?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.propertyName?.toLowerCase().includes(searchQuery.toLowerCase());
      
      // Apply property filter
      const matchesProperty = !filters.property || item.propertyId === filters.property;
      
      // Apply collection filter
      const matchesCollection = !filters.collection || item.collection === filters.collection;
      
      // Apply low stock filter
      const matchesLowStock = !filters.lowStock || (item.quantity < item.minQuantity);
      
      return matchesSearch && matchesProperty && matchesCollection && matchesLowStock;
    });
  }, [inventoryItems, searchQuery, filters]);

  // Toggle filters visibility
  const toggleFilters = () => {
    setShowFilters(prev => !prev);
  };

  return {
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    showFilters,
    toggleFilters,
    collections,
    filteredItems
  };
}
