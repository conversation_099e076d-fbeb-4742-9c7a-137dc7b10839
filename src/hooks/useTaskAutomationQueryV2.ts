import { useState, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { format } from 'date-fns';

export interface AutomationRule {
  id: string;
  name: string;
  trigger_type: 'check_in' | 'check_out';
  task_type: 'cleaning' | 'inspection' | 'maintenance';
  time_offset: number; // Hours before/after trigger
  property_ids: string[] | null; // null means all properties
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  assigned_to: string | null;
  created_at: string;
  updated_at: string;
}

export interface TaskAutomationData {
  rules: AutomationRule[];
  loading: boolean;
  error: string | null;
  isError: boolean;
  fetchRules: () => Promise<void>;
  createRule: (rule: Omit<AutomationRule, 'id' | 'created_at' | 'updated_at'>) => Promise<boolean>;
  updateRule: (id: string, rule: Partial<AutomationRule>) => Promise<boolean>;
  deleteRule: (id: string) => Promise<boolean>;
  processAllBookings: () => Promise<boolean>;
}

/**
 * A standardized hook for fetching task automation rules using React Query
 * This follows the same pattern as useOperationsDataQuery for consistency
 */
export const useTaskAutomationQueryV2 = (): TaskAutomationData => {
  const { authState } = useAuth();
  const userId = authState?.user?.id;
  const queryClient = useQueryClient();
  const [retryCount, setRetryCount] = useState(0);

  // Function to retry data fetching manually
  const retryFetch = useCallback(async () => {
    console.log('[useTaskAutomationQueryV2] Manual refresh triggered');
    setRetryCount(prev => prev + 1);
    await queryClient.invalidateQueries({ queryKey: ['automationRulesV2'] });
    await queryClient.refetchQueries({ queryKey: ['automationRulesV2'] });
  }, [queryClient]);

  // Fetch automation rules
  const {
    data: rules = [],
    isLoading,
    error,
    isError
  } = useQuery({
    queryKey: ['automationRulesV2'],
    queryFn: async () => {
      try {
        if (!userId) {
          throw new Error('User not authenticated');
        }

        console.log(`[useTaskAutomationQueryV2] Fetching automation rules (attempt ${retryCount + 1})`);

        const { data, error } = await supabase
          .from('automation_rules')
          .select('*')
          .eq('user_id', userId);

        if (error) {
          console.error('[useTaskAutomationQueryV2] Error fetching automation rules:', error);
          throw error;
        }

        console.log(`[useTaskAutomationQueryV2] Successfully loaded ${data?.length || 0} automation rules`);
        return data || [];
      } catch (err: any) {
        console.error('[useTaskAutomationQueryV2] Error fetching automation rules:', err);
        throw err;
      }
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: true,
    refetchOnReconnect: true,
    keepPreviousData: true, // Added to preserve data during refetching
    enabled: !!userId,
    networkMode: 'always'
  });

  // Create a new automation rule
  const createRule = async (rule: Omit<AutomationRule, 'id' | 'created_at' | 'updated_at'>): Promise<boolean> => {
    if (!userId) return false;

    try {
      console.log('[useTaskAutomationQueryV2] Creating new automation rule:', rule);

      const { error } = await supabase
        .from('automation_rules')
        .insert({
          ...rule,
          user_id: userId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) {
        console.error('[useTaskAutomationQueryV2] Error creating automation rule:', error);
        throw error;
      }

      // Invalidate and refetch rules
      await queryClient.invalidateQueries({ queryKey: ['automationRulesV2'] });
      await retryFetch();

      return true;
    } catch (error: any) {
      console.error('[useTaskAutomationQueryV2] Error creating automation rule:', error);
      return false;
    }
  };

  // Update an existing automation rule
  const updateRule = async (id: string, rule: Partial<AutomationRule>): Promise<boolean> => {
    if (!userId) return false;

    try {
      console.log(`[useTaskAutomationQueryV2] Updating automation rule ${id}:`, rule);

      const { error } = await supabase
        .from('automation_rules')
        .update({
          ...rule,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('user_id', userId);

      if (error) {
        console.error('[useTaskAutomationQueryV2] Error updating automation rule:', error);
        throw error;
      }

      // Invalidate and refetch rules
      await queryClient.invalidateQueries({ queryKey: ['automationRulesV2'] });
      await retryFetch();

      return true;
    } catch (error: any) {
      console.error('[useTaskAutomationQueryV2] Error updating automation rule:', error);
      return false;
    }
  };

  // Delete an automation rule
  const deleteRule = async (id: string): Promise<boolean> => {
    if (!userId) return false;

    try {
      console.log(`[useTaskAutomationQueryV2] Deleting automation rule ${id}`);

      const { error } = await supabase
        .from('automation_rules')
        .delete()
        .eq('id', id)
        .eq('user_id', userId);

      if (error) {
        console.error('[useTaskAutomationQueryV2] Error deleting automation rule:', error);
        throw error;
      }

      // Invalidate and refetch rules
      await queryClient.invalidateQueries({ queryKey: ['automationRulesV2'] });
      await retryFetch();

      return true;
    } catch (error: any) {
      console.error('[useTaskAutomationQueryV2] Error deleting automation rule:', error);
      return false;
    }
  };

  // Process all bookings for automation
  const processAllBookings = async (): Promise<boolean> => {
    if (!userId) return false;

    try {
      console.log('[useTaskAutomationQueryV2] Processing all bookings for automation');

      // Call the Supabase Edge Function to process all bookings
      const { data, error } = await supabase.functions.invoke('process-automation-queue', {
        body: { userId }
      });

      if (error) {
        console.error('[useTaskAutomationQueryV2] Error processing bookings:', error);
        throw error;
      }

      console.log('[useTaskAutomationQueryV2] Successfully triggered automation processing:', data);

      // Invalidate maintenance tasks query to show newly created tasks
      await queryClient.invalidateQueries({ queryKey: ['maintenanceTasksV2'] });

      return true;
    } catch (error: any) {
      console.error('[useTaskAutomationQueryV2] Error processing bookings:', error);
      return false;
    }
  };

  return {
    rules,
    loading: isLoading,
    error: error ? String(error) : null,
    isError,
    fetchRules: retryFetch,
    createRule,
    updateRule,
    deleteRule,
    processAllBookings
  };
};
