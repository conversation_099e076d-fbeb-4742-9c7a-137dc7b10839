import { useAuth } from '@/contexts/AuthContext';
import { PermissionType, type UserPermission } from '@/types/auth';
import { useEffect, useState, useRef, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';

// Create a type for the permission cache
type PermissionCacheKey = string;
type PermissionCacheValue = boolean;

export const usePermissions = () => {
  const { authState } = useAuth();
  const [permissions, setPermissions] = useState<UserPermission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userTeams, setUserTeams] = useState<string[]>([]);
  
  // Use a ref for the permission cache to persist across renders
  const permissionCacheRef = useRef<Map<PermissionCacheKey, PermissionCacheValue>>(new Map());
  
  // Track if we've already logged permissions for this render cycle
  const loggedPermissionsRef = useRef<Set<string>>(new Set());
  
  // Track if we've already fetched permissions
  const fetchedPermissionsRef = useRef(false);
  
  // Clear the logged permissions set on each render
  useEffect(() => {
    return () => {
      loggedPermissionsRef.current.clear();
    };
  });

  useEffect(() => {
    const fetchPermissions = async () => {
      // Skip if we've already fetched or if there's no user
      if (fetchedPermissionsRef.current || !authState.user?.id) {
        if (!authState.user?.id) {
          setPermissions([]);
          setUserTeams([]);
          setLoading(false);
        }
        return;
      }

      setLoading(true);
      setError(null);
      
      // Clear the permission cache when user or admin status changes
      permissionCacheRef.current.clear();

      try {
        if (authState?.profile?.is_super_admin) {
          fetchedPermissionsRef.current = true;
          setLoading(false);
          return;
        }

        console.log('[usePermissions] Fetching team memberships for user:', authState.user.id);
        const { data: teamMemberships, error: teamError } = await supabase
          .from('team_members')
          .select('team_id')
          .eq('user_id', authState.user.id)
          .eq('status', 'active');

        if (teamError) {
          console.error('[usePermissions] Error fetching team memberships:', teamError);
          setUserTeams([]);
        } else if (teamMemberships && teamMemberships.length > 0) {
          const teams = teamMemberships.map((tm: { team_id: string }) => tm.team_id);
          console.log('[usePermissions] User is a member of teams:', teams);
          setUserTeams(teams);
        } else {
          console.log('[usePermissions] User is not a member of any teams');
          setUserTeams([]);
        }

        let { data, error } = await supabase
          .from('user_permissions')
          .select('*')
          .eq('user_id', authState.user.id)
          .eq('enabled', true);

        if (error) {
          console.warn('Error fetching user permissions (non-critical):', error);
          // Don't set error state for network issues, just use empty permissions
          setPermissions([]);
        } else {
          setPermissions(data || []);
        }
        
        // Mark as fetched to prevent repeated fetches
        fetchedPermissionsRef.current = true;
      } catch (error: any) {
        setError('Unexpected error: ' + error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchPermissions();
  }, [authState.user?.id, authState?.profile?.is_super_admin]);

  // Create a memoized hasPermission function that uses the cache
  const hasPermission = useCallback((permission: PermissionType, teamId?: string): boolean => {
    // Create a cache key based on permission and teamId
    const cacheKey = `${permission}:${teamId || 'global'}`;
    
    // Check if we already have a cached result
    if (permissionCacheRef.current.has(cacheKey)) {
      return permissionCacheRef.current.get(cacheKey)!;
    }
    
    // Create a log key to prevent duplicate logs
    const logKey = `${permission}:${teamId || 'global'}`;
    const shouldLog = !loggedPermissionsRef.current.has(logKey);
    
    // Helper function to log only once per permission check
    const logOnce = (message: string) => {
      if (shouldLog) {
        console.log(message);
        loggedPermissionsRef.current.add(logKey);
      }
    };
    
    // Helper function to cache and return result
    const returnResult = (result: boolean, logMessage?: string) => {
      permissionCacheRef.current.set(cacheKey, result);
      if (logMessage && shouldLog) {
        logOnce(logMessage);
      }
      return result;
    };
    
    // Super admins have all permissions
    if (authState?.profile?.is_super_admin) {
      return returnResult(true, `[usePermissions] Super admin has permission: ${permission}`);
    }

    // Admins have all permissions
    if (authState?.profile?.role === 'admin') {
      return returnResult(true, `[usePermissions] Admin has permission: ${permission}`);
    }

    // Property managers have full permissions for their properties
    if (authState?.profile?.role === 'property_manager') {
      // Property managers should always have MANAGE_PROPERTIES permission
      if (permission === PermissionType.MANAGE_PROPERTIES) {
        return returnResult(true, '[usePermissions] Property manager has MANAGE_PROPERTIES permission');
      }

      // If we're checking MANAGE_STAFF for a specific team
      if (permission === PermissionType.MANAGE_STAFF && teamId) {
        // Allow if they own the team
        const isOwnedTeam = userTeams.includes(teamId);
        if (isOwnedTeam) {
          return returnResult(true, `[usePermissions] Property manager owns team ${teamId}, granting MANAGE_STAFF permission`);
        }
      }

      // If no specific team is provided, check global permissions
      if (!teamId) {
        const globalPropertyManagerPermissions = [
          PermissionType.MANAGE_PROPERTIES,
          PermissionType.MANAGE_MAINTENANCE,
          PermissionType.SUBMIT_DAMAGE_REPORTS,
          PermissionType.VIEW_MAINTENANCE,
          PermissionType.VIEW_DAMAGE_REPORTS,
          PermissionType.MANAGE_DAMAGE_REPORTS
        ];

        if (globalPropertyManagerPermissions.includes(permission)) {
          return returnResult(true, `[usePermissions] Property manager has global permission: ${permission}`);
        }

        const result = permissions.some(p => p.permission === permission && p.enabled && !p.team_id);
        return returnResult(result);
      }

      const isOwnedTeam = userTeams.includes(teamId);
      if (isOwnedTeam) {
        const teamOwnerPermissions = [
          PermissionType.MANAGE_PROPERTIES,
          PermissionType.MANAGE_MAINTENANCE,
          PermissionType.SUBMIT_DAMAGE_REPORTS,
          PermissionType.VIEW_MAINTENANCE,
          PermissionType.VIEW_DAMAGE_REPORTS,
          PermissionType.MANAGE_DAMAGE_REPORTS,
          PermissionType.MANAGE_STAFF,
          PermissionType.MANAGE_SERVICE_PROVIDERS,
          PermissionType.MANAGE_INVENTORY,
          PermissionType.VIEW_PURCHASE_ORDERS,
          PermissionType.MANAGE_PURCHASE_ORDERS,
          PermissionType.VIEW_TEAM,
          PermissionType.MANAGE_TEAM
        ];

        if (teamOwnerPermissions.includes(permission)) {
          return returnResult(true, `[usePermissions] Property manager owns team ${teamId}, granting ${permission} permission`);
        }
      }
    }

    // For service providers and staff, check team membership and default permissions
    if (authState?.profile?.role === 'service_provider' || authState?.profile?.role === 'staff') {
      // Default permissions for team members
      const teamMemberDefaultPermissions = [
        PermissionType.VIEW_MAINTENANCE,
        PermissionType.SUBMIT_DAMAGE_REPORTS,
        PermissionType.VIEW_DAMAGE_REPORTS,
        PermissionType.MANAGE_INVENTORY,
        PermissionType.VIEW_PURCHASE_ORDERS,
        PermissionType.MANAGE_PURCHASE_ORDERS,
        PermissionType.VIEW_TEAM,
        PermissionType.MANAGE_TEAM
      ];

      // Only allow default permissions if they're a member of the specified team
      const isTeamMember = teamId && userTeams.includes(teamId);

      if (isTeamMember && teamMemberDefaultPermissions.includes(permission)) {
        return returnResult(true, `[usePermissions] Team member has permission: ${permission} for team: ${teamId}`);
      }

      // If no specific team is provided, but it's a basic permission, allow it
      if (!teamId) {
        const basicPermissions = [
          PermissionType.MANAGE_MAINTENANCE,
          PermissionType.SUBMIT_DAMAGE_REPORTS,
          PermissionType.VIEW_DAMAGE_REPORTS,
          PermissionType.VIEW_MAINTENANCE
        ];

        if (basicPermissions.includes(permission)) {
          return returnResult(true, `[usePermissions] Team member has basic permission: ${permission}`);
        }
      }

      // Special case for VIEW_DAMAGE_REPORTS - if user is a team member, they should be able to view damage reports
      // for any team they're a member of, even if no specific teamId is provided
      if (permission === PermissionType.VIEW_DAMAGE_REPORTS && userTeams.length > 0) {
        return returnResult(true, `[usePermissions] Team member has VIEW_DAMAGE_REPORTS permission for teams: ${userTeams.join(', ')}`);
      }

      // Special case for VIEW_MAINTENANCE - if user is a team member, they should be able to view maintenance tasks
      // for any team they're a member of, even if no specific teamId is provided
      if (permission === PermissionType.VIEW_MAINTENANCE && userTeams.length > 0) {
        return returnResult(true, `[usePermissions] Team member has VIEW_MAINTENANCE permission for teams: ${userTeams.join(', ')}`);
      }

      // Special case for VIEW_TEAM - if user is a team member, they should be able to view team data
      // for any team they're a member of, even if no specific teamId is provided
      if (permission === PermissionType.VIEW_TEAM && userTeams.length > 0) {
        return returnResult(true, `[usePermissions] Team member has VIEW_TEAM permission for teams: ${userTeams.join(', ')}`);
      }
    }

    // Check explicitly assigned permissions for all roles
    // Check explicitly assigned permissions for the specific team
    if (teamId) {
      const hasExplicitPermission = permissions.some(
        p => p.permission === permission &&
             p.team_id === teamId &&
             p.enabled
      );
      return returnResult(hasExplicitPermission);
    }

    // If no team specified, check if they have the permission for any team
    const result = permissions.some(
      p => p.permission === permission && p.enabled
    );
    return returnResult(result);
  }, [authState?.profile?.is_super_admin, authState?.profile?.role, permissions, userTeams]);

  const getAccessibleTeams = useCallback((): string[] => {
    // Get the unique team IDs this user has permissions for
    if (authState?.profile?.is_super_admin || authState?.profile?.role === 'admin') {
      // Admins can access all teams (this will be handled separately in queries)
      console.log('[usePermissions] Admin/Super admin has access to all teams');
      return ['*'];
    }

    // If they're a property manager, only return teams they own or are members of
    if (authState?.profile?.role === 'property_manager') {
      console.log('[usePermissions] Property manager has access to teams:', userTeams);
      return [...userTeams];
    }

    // For other roles (staff, service providers), they only have access to teams
    // they're explicitly members of
    console.log('[usePermissions] User has access to teams as member:', userTeams);
    return userTeams;
  }, [authState?.profile?.is_super_admin, authState?.profile?.role, userTeams]);

  // Helper functions for role checks
  const isAdmin = useCallback(() => {
    return authState?.profile?.is_super_admin || authState?.profile?.role === 'admin';
  }, [authState?.profile?.is_super_admin, authState?.profile?.role]);

  const isOwner = useCallback(() => {
    return authState?.profile?.role === 'property_manager';
  }, [authState?.profile?.role]);

  // Helper function to get accessible property IDs
  const getAccessiblePropertyIds = useCallback(async () => {
    if (isAdmin()) {
      // Admins can access all properties
      console.log('[usePermissions] Admin can access all properties');
      return [];
    }

    // Get properties for teams the user is a member of
    if (userTeams.length > 0) {
      console.log('[usePermissions] Getting properties for teams:', userTeams);
      const { data } = await supabase
        .from('properties')
        .select('id')
        .in('team_id', userTeams);

      return data ? data.map((p: { id: string }) => p.id) : [];
    }

    return [];
  }, [isAdmin, userTeams]);

  return {
    permissions,
    hasPermission,
    getAccessibleTeams,
    userTeams,
    loading,
    error,
    isAdmin,
    isOwner,
    getAccessiblePropertyIds
  };
};
