import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Hook to ensure queries are properly initialized when navigating to pages
 * This fixes the issue where data doesn't load if you start on a page without data (like Debug)
 * and then navigate to pages that should have data
 */
export const useQueryInitialization = () => {
  const queryClient = useQueryClient();
  const location = useLocation();
  const { authState } = useAuth();
  const userId = authState?.user?.id;
  const isAuthenticated = authState?.isAuthenticated;
  const previousLocationRef = useRef<string>('');
  const initializedPagesRef = useRef<Set<string>>(new Set());

  useEffect(() => {
    // Only proceed if user is authenticated
    if (!isAuthenticated || !userId) {
      return;
    }

    const currentPath = location.pathname;
    const previousPath = previousLocationRef.current;
    
    // Skip if we're on the same page
    if (currentPath === previousPath) {
      return;
    }

    // Update the previous location
    previousLocationRef.current = currentPath;

    // Check if this page has been initialized before
    const pageKey = currentPath.split('/')[1] || 'root'; // Get the main route segment
    const hasBeenInitialized = initializedPagesRef.current.has(pageKey);

    console.log(`[useQueryInitialization] Navigation detected: ${previousPath} -> ${currentPath}`);
    console.log(`[useQueryInitialization] Page ${pageKey} initialized before: ${hasBeenInitialized}`);

    // Define query keys that should be initialized for each page
    const pageQueryMap: Record<string, string[]> = {
      'properties': ['propertiesV2', 'properties'],
      'dashboard': ['dashboardProperties', 'dashboardMaintenanceTasks', 'dashboardInventoryItems', 'dashboardDamages', 'dashboardPurchaseOrders'],
      'maintenance': ['maintenanceTasksV2', 'maintenanceTasks'],
      'teams': ['teamsV2', 'teams'],
      'damages': ['damageReportsV2', 'damageReports'],
      'inventory': ['inventoryV2', 'inventory'],
      'operations': ['operationsData', 'operationsProperties'],
      'settings': ['appearanceSettings', 'userSettings'],
      'purchase-orders': ['purchaseOrders']
    };

    // Get the queries that should be active for this page
    const relevantQueries = pageQueryMap[pageKey] || [];

    if (relevantQueries.length > 0) {
      console.log(`[useQueryInitialization] Initializing queries for ${pageKey}:`, relevantQueries);

      // Force invalidate and refetch queries for this page
      // Use Promise.all to handle async operations properly
      const initializeQueries = async () => {
        try {
          await Promise.all(relevantQueries.map(async (queryKey) => {
            try {
              // Check if query exists in cache
              const existingQuery = queryClient.getQueryData([queryKey]);
              const queryState = queryClient.getQueryState([queryKey]);

              console.log(`[useQueryInitialization] Query ${queryKey} state:`, {
                hasData: !!existingQuery,
                status: queryState?.status,
                dataUpdatedAt: queryState?.dataUpdatedAt,
                isStale: queryState?.isStale
              });

              // If query doesn't exist or has never been fetched, invalidate to trigger fetch
              if (!existingQuery || !queryState || queryState.dataUpdatedAt === 0) {
                console.log(`[useQueryInitialization] Invalidating query ${queryKey} to trigger initial fetch`);
                await queryClient.invalidateQueries({
                  queryKey: [queryKey],
                  exact: false // This will match queries with additional parameters
                });
              }
              // If query exists but is stale, refetch it
              else if (queryState.isStale) {
                console.log(`[useQueryInitialization] Refetching stale query ${queryKey}`);
                await queryClient.refetchQueries({
                  queryKey: [queryKey],
                  exact: false
                });
              }
            } catch (error) {
              console.error(`[useQueryInitialization] Error initializing query ${queryKey}:`, error);
            }
          }));
        } catch (error) {
          console.error(`[useQueryInitialization] Error in initializeQueries:`, error);
        }
      };

      // Call the async function without awaiting to avoid blocking render
      initializeQueries();

      // Mark this page as initialized
      initializedPagesRef.current.add(pageKey);
    }

    // Also invalidate any queries that might be stuck in pending state
    const allQueries = queryClient.getQueryCache().getAll();
    const stuckQueries = allQueries.filter(query =>
      query.state.status === 'pending' &&
      query.state.dataUpdatedAt === 0 &&
      Date.now() - (query.state.dataUpdatedAt || Date.now()) > 5000 // Stuck for more than 5 seconds
    );

    if (stuckQueries.length > 0) {
      console.log(`[useQueryInitialization] Found ${stuckQueries.length} stuck queries, invalidating...`);

      // Handle stuck queries properly with Promise.all
      const handleStuckQueries = async () => {
        try {
          await Promise.all(stuckQueries.map(async (query) => {
            try {
              await queryClient.invalidateQueries({ queryKey: query.queryKey });
            } catch (error) {
              console.error(`[useQueryInitialization] Error invalidating stuck query:`, error);
            }
          }));
        } catch (error) {
          console.error(`[useQueryInitialization] Error handling stuck queries:`, error);
        }
      };

      // Call without awaiting to avoid blocking render
      handleStuckQueries();
    }

  }, [location.pathname, isAuthenticated, userId, queryClient]);

  // Reset initialization tracking when user logs out
  useEffect(() => {
    if (!isAuthenticated) {
      initializedPagesRef.current.clear();
      previousLocationRef.current = '';
    }
  }, [isAuthenticated]);

  return {
    initializePage: (pageKey: string, queryKeys: string[]) => {
      if (!isAuthenticated || !userId) return;
      
      console.log(`[useQueryInitialization] Manually initializing page ${pageKey} with queries:`, queryKeys);
      
      // Handle manual initialization properly
      const manualInitialize = async () => {
        try {
          await Promise.all(queryKeys.map(async (queryKey) => {
            try {
              await queryClient.invalidateQueries({
                queryKey: [queryKey],
                exact: false
              });
            } catch (error) {
              console.error(`[useQueryInitialization] Error manually initializing query ${queryKey}:`, error);
            }
          }));
        } catch (error) {
          console.error(`[useQueryInitialization] Error in manual initialization:`, error);
        }
      };

      // Call without awaiting
      manualInitialize();
      
      initializedPagesRef.current.add(pageKey);
    }
  };
};
