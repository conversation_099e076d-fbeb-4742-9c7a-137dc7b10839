import { useState, useCallback, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useImpersonation } from '@/contexts/ImpersonationContext';
import { MaintenanceTask, MaintenanceTaskFilters } from '@/components/maintenance/types';
import { toast } from 'sonner';

export interface MaintenanceTasksData {
  tasks: MaintenanceTask[];
  loading: boolean;
  error: string | null;
  isError: boolean;
  fetchTasks: () => Promise<void>;
  updateTaskStatus: (taskId: string, status: string) => Promise<boolean>;
  deleteTask: (taskId: string) => Promise<boolean>;
  addTask: (task: Omit<MaintenanceTask, 'id' | 'status' | 'createdAt'>) => Promise<any>;
  updateTask: (taskId: string, task: Omit<MaintenanceTask, 'id' | 'createdAt'>) => Promise<any>;
}

/**
 * A standardized hook for fetching maintenance tasks using React Query
 * This follows the same pattern as useOperationsDataQuery for consistency
 */
export const useMaintenanceTasksQueryV2 = (
  initialFilters: Partial<MaintenanceTaskFilters> = {}
): MaintenanceTasksData => {
  const { authState } = useAuth();
  const userId = authState?.user?.id;
  const queryClient = useQueryClient();
  const [retryCount, setRetryCount] = useState(0);
  const { isImpersonating } = useImpersonation();

  // Function to retry data fetching manually
  const retryFetch = useCallback(async () => {
    console.log('[useMaintenanceTasksQueryV2] Manual refresh triggered');
    setRetryCount(prev => prev + 1);
    await queryClient.invalidateQueries({ queryKey: ['maintenanceTasksV2'] });
    await queryClient.refetchQueries({ queryKey: ['maintenanceTasksV2'] });
  }, [queryClient]);

  // Keep track of previous tasks to prevent data disappearing during loading
  const [previousTasks, setPreviousTasks] = useState<MaintenanceTask[]>([]);

  // Fetch maintenance tasks
  const {
    data: tasks = [],
    isLoading,
    error,
    isError
  } = useQuery({
    queryKey: ['maintenanceTasksV2'],
    queryFn: async () => {
      try {
        if (!userId) {
          throw new Error('User not authenticated');
        }

        console.log(`[useMaintenanceTasksQueryV2] Fetching tasks (attempt ${retryCount + 1})`);

        // Use our RPC function to get tasks
        const { data: rpcTasks, error: rpcError } = await supabase.rpc('get_maintenance_tasks_for_user', {
          p_user_id: userId
        });

        if (rpcError) {
          console.error('[useMaintenanceTasksQueryV2] RPC function error:', rpcError);
          throw rpcError;
        }

        if (!rpcTasks || rpcTasks.length === 0) {
          console.log('[useMaintenanceTasksQueryV2] No tasks found');
          return previousTasks.length > 0 ? previousTasks : []; // Return previous tasks if available
        }

        console.log(`[useMaintenanceTasksQueryV2] Successfully loaded ${rpcTasks.length} tasks`);

        // Apply impersonation filter if needed
        let filteredTasks = rpcTasks;
        if (isImpersonating) {
          console.log('[useMaintenanceTasksQueryV2] Impersonating user, filtering tasks');
          filteredTasks = rpcTasks.filter(task => task.user_id === userId);
        }

        // Format tasks
        const formattedTasks = filteredTasks.map((task: any) => ({
          id: task.id,
          title: task.title || '',
          description: task.description || '',
          status: task.status || 'open',
          severity: task.severity || 'medium',
          dueDate: task.due_date || '',
          propertyId: task.property_id || '',
          propertyName: task.property_name || '',
          assignedTo: task.assigned_to || '',
          providerId: task.provider_id || '',
          providerName: task.provider_name || '',
          createdAt: task.created_at || '',
          updatedAt: task.updated_at || '',
          userId: task.user_id || '',
        }));

        // Update previous tasks for future use
        setPreviousTasks(formattedTasks);

        return formattedTasks;
      } catch (err: any) {
        console.error('[useMaintenanceTasksQueryV2] Error fetching tasks:', err);
        // Return previous tasks on error to prevent data disappearing
        if (previousTasks.length > 0) {
          console.log('[useMaintenanceTasksQueryV2] Returning previous tasks due to error');
          return previousTasks;
        }
        throw err;
      }
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: true,
    refetchOnReconnect: true,
    enabled: !!userId,
    keepPreviousData: true, // Keep previous data while fetching new data
    placeholderData: previousTasks.length > 0 ? previousTasks : undefined, // Use previous tasks as placeholder data
    networkMode: 'always'
  });

  // Update task status
  const updateTaskStatus = async (taskId: string, status: string): Promise<boolean> => {
    if (!userId) {
      console.error('[useMaintenanceTasksQueryV2] No authenticated user for status update');
      toast.error('User not authenticated');
      return false;
    }

    try {
      console.log(`[useMaintenanceTasksQueryV2] Updating task ${taskId} status to ${status}`);

      const { error } = await supabase
        .from('maintenance_tasks')
        .update({ status, updated_at: new Date().toISOString() })
        .eq('id', taskId);

      if (error) {
        console.error('[useMaintenanceTasksQueryV2] Supabase error updating task status:', error);
        toast.error(`Failed to update task status: ${error.message}`);
        throw error;
      }

      console.log(`[useMaintenanceTasksQueryV2] Successfully updated task ${taskId} status to ${status}`);

      // Invalidate and refetch tasks
      await queryClient.invalidateQueries({ queryKey: ['maintenanceTasksV2'] });
      await retryFetch();

      return true;
    } catch (error: any) {
      console.error('[useMaintenanceTasksQueryV2] Exception updating task status:', error);
      if (!error.message?.includes('Failed to update task status:')) {
        toast.error(`Failed to update task status: ${error.message || 'Unknown error'}`);
      }
      return false;
    }
  };

  // Delete task
  const deleteTask = async (taskId: string): Promise<boolean> => {
    if (!userId) {
      console.error('[useMaintenanceTasksQueryV2] No authenticated user for task deletion');
      toast.error('User not authenticated');
      return false;
    }

    try {
      console.log(`[useMaintenanceTasksQueryV2] Deleting task ${taskId}`);

      const { error } = await supabase
        .from('maintenance_tasks')
        .delete()
        .eq('id', taskId);

      if (error) {
        console.error('[useMaintenanceTasksQueryV2] Supabase error deleting task:', error);
        toast.error(`Failed to delete task: ${error.message}`);
        throw error;
      }

      console.log(`[useMaintenanceTasksQueryV2] Successfully deleted task ${taskId}`);

      // Invalidate and refetch tasks
      await queryClient.invalidateQueries({ queryKey: ['maintenanceTasksV2'] });
      await retryFetch();

      return true;
    } catch (error: any) {
      console.error('[useMaintenanceTasksQueryV2] Exception deleting task:', error);
      if (!error.message?.includes('Failed to delete task:')) {
        toast.error(`Failed to delete task: ${error.message || 'Unknown error'}`);
      }
      return false;
    }
  };

  // Add task
  const addTask = async (task: Omit<MaintenanceTask, 'id' | 'status' | 'createdAt'>): Promise<any> => {
    if (!userId) {
      console.error('[useMaintenanceTasksQueryV2] No authenticated user');
      toast.error('User not authenticated');
      return false;
    }

    try {
      console.log('[useMaintenanceTasksQueryV2] Adding task:', task);
      console.log('[useMaintenanceTasksQueryV2] User ID:', userId);

      // Validate and determine property access and team_id
      let validPropertyId = null;
      let teamId = null;

      if (task.propertyId) {
        // First check if the property exists and user has access to it
        const { data: propertyData, error: propertyError } = await supabase
          .from('properties')
          .select('id, user_id')
          .eq('id', task.propertyId)
          .limit(1);

        if (propertyError) {
          console.warn(`[useMaintenanceTasksQueryV2] Error checking property access: ${propertyError.message}`);
        } else if (propertyData && propertyData.length > 0) {
          const property = propertyData[0];

          // Check if user owns the property or has team access
          if (property.user_id === userId) {
            validPropertyId = task.propertyId;
            console.log(`[useMaintenanceTasksQueryV2] User owns property ${task.propertyId}`);
          } else {
            // Check if user has team access to this property
            const { data: teamPropertyData, error: teamPropertyError } = await supabase
              .from('team_properties')
              .select('team_id, team_members!inner(user_id, status)')
              .eq('property_id', task.propertyId)
              .eq('team_members.user_id', userId)
              .eq('team_members.status', 'active')
              .limit(1);

            if (!teamPropertyError && teamPropertyData && teamPropertyData.length > 0) {
              validPropertyId = task.propertyId;
              teamId = teamPropertyData[0].team_id;
              console.log(`[useMaintenanceTasksQueryV2] User has team access to property ${task.propertyId} via team ${teamId}`);
            } else {
              console.warn(`[useMaintenanceTasksQueryV2] User does not have access to property ${task.propertyId}, creating task without property association`);
            }
          }
        } else {
          console.warn(`[useMaintenanceTasksQueryV2] Property ${task.propertyId} not found, creating task without property association`);
        }
      }

      const dbTask = {
        title: task.title,
        description: task.description,
        property_id: validPropertyId,
        property_name: task.propertyName,
        severity: task.severity,
        status: 'open',
        due_date: task.dueDate === 'No due date' ? null : task.dueDate,
        user_id: userId,
        provider_id: task.providerId || null,
        provider_email: task.providerEmail || null,
        assigned_to: task.assignedTo || null,
        team_id: teamId
      };

      console.log('[useMaintenanceTasksQueryV2] Sending to Supabase:', dbTask);

      const { data, error } = await supabase
        .from('maintenance_tasks')
        .insert(dbTask)
        .select();

      if (error) {
        console.error('[useMaintenanceTasksQueryV2] Supabase error:', error);

        // Provide more specific error messages
        if (error.code === '42501') {
          throw new Error('Permission denied: You do not have access to create maintenance tasks for this property');
        } else if (error.code === '23503') {
          throw new Error('Invalid property or team reference');
        } else if (error.code === '23502') {
          throw new Error('Missing required field');
        } else {
          throw new Error(`Database error: ${error.message}`);
        }
      }

      console.log('[useMaintenanceTasksQueryV2] Task added successfully:', data);

      // Invalidate and refetch tasks
      await queryClient.invalidateQueries({ queryKey: ['maintenanceTasksV2'] });
      await retryFetch();

      // Return the created task data for email notifications
      if (data && data.length > 0) {
        const createdTask = {
          id: data[0].id,
          title: data[0].title,
          description: data[0].description,
          propertyId: data[0].property_id,
          propertyName: data[0].property_name,
          severity: data[0].severity,
          status: data[0].status,
          dueDate: data[0].due_date || 'No due date',
          assignedTo: data[0].assigned_to,
          providerId: data[0].provider_id,
          providerEmail: data[0].provider_email,
          createdAt: data[0].created_at
        };
        return createdTask;
      }

      return true;
    } catch (error: any) {
      console.error('[useMaintenanceTasksQueryV2] Error adding task:', error);
      toast.error(`Failed to add task: ${error.message || 'Unknown error'}`);
      return false;
    }
  };

  // Update task
  const updateTask = async (taskId: string, task: Omit<MaintenanceTask, 'id' | 'createdAt'>): Promise<any> => {
    if (!userId) return false;

    try {
      console.log('[useMaintenanceTasksQueryV2] Updating task:', taskId, task);

      const dbTask = {
        title: task.title,
        description: task.description,
        property_id: task.propertyId || null,
        property_name: task.propertyName,
        severity: task.severity,
        status: task.status,
        due_date: task.dueDate === 'No due date' ? null : task.dueDate,
        provider_id: task.providerId || null,
        provider_email: task.providerEmail || null,
        assigned_to: task.assignedTo || null,
        updated_at: new Date().toISOString()
      };

      console.log('[useMaintenanceTasksQueryV2] Sending update to Supabase:', dbTask);

      const { data, error } = await supabase
        .from('maintenance_tasks')
        .update(dbTask)
        .eq('id', taskId)
        .select();

      if (error) {
        console.error('[useMaintenanceTasksQueryV2] Supabase error:', error);
        throw error;
      }

      console.log('[useMaintenanceTasksQueryV2] Task updated successfully:', data);

      // Invalidate and refetch tasks
      await queryClient.invalidateQueries({ queryKey: ['maintenanceTasksV2'] });
      await retryFetch();

      // Return the updated task data for email notifications
      if (data && data.length > 0) {
        const updatedTask = {
          id: data[0].id,
          title: data[0].title,
          description: data[0].description,
          propertyId: data[0].property_id,
          propertyName: data[0].property_name,
          severity: data[0].severity,
          status: data[0].status,
          dueDate: data[0].due_date || 'No due date',
          assignedTo: data[0].assigned_to,
          providerId: data[0].provider_id,
          providerEmail: data[0].provider_email,
          createdAt: data[0].created_at
        };
        return updatedTask;
      }

      return true;
    } catch (error: any) {
      console.error('[useMaintenanceTasksQueryV2] Error updating task:', error);
      toast.error(`Failed to update task: ${error.message || 'Unknown error'}`);
      return false;
    }
  };

  // Add error retry effect similar to usePurchaseOrders
  useEffect(() => {
    // Only retry on actual errors, not on empty data
    if (isError) {
      const timer = setTimeout(() => {
        if (retryCount < 3) {
          console.log('[useMaintenanceTasksQueryV2] Auto-retrying data fetch due to error');
          retryFetch();
        } else {
          console.error('[useMaintenanceTasksQueryV2] Failed to load tasks after multiple attempts');
        }
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [isError, retryCount, retryFetch]);

  return {
    tasks,
    loading: isLoading,
    error: error ? String(error) : null,
    isError,
    fetchTasks: retryFetch,
    updateTaskStatus,
    deleteTask,
    addTask,
    updateTask
  };
};
