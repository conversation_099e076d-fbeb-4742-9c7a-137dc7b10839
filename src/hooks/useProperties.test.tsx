
import { renderHook, waitFor } from '@/tests/utils/test-utils';
import { useProperties } from '@/hooks/useProperties';
import { testProperties } from '@/tests/data/properties';

// Mock supabase client
jest.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    then: jest.fn().mockImplementation((callback) => {
      return Promise.resolve(callback({ data: testProperties, error: null }));
    })
  }
}));

describe('useProperties hook', () => {
  it('returns properties and loading state', async () => {
    const { result } = renderHook(() => useProperties());
    
    // Initially, it should be loading with empty properties
    expect(result.current.loading).toBe(true);
    expect(result.current.properties).toEqual([]);
    
    // Wait for the query to resolve
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    // Now properties should be populated
    expect(result.current.properties.length).toBeGreaterThan(0);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
  });
  
  it('provides methods to fetch properties', async () => {
    const { result } = renderHook(() => useProperties());
    
    // Wait for the query to resolve
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    // Verify that methods are provided
    expect(typeof result.current.fetchProperties).toBe('function');
    expect(typeof result.current.setProperties).toBe('function');
  });
});
