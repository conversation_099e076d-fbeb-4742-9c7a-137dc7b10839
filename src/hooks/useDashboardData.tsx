import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { usePropertiesQueryV2 } from '@/hooks/usePropertiesQueryV2';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { format } from 'date-fns';
import { mapPropertyToCardProperty } from '@/utils/propertyUtils';
// Use the RPC version of the hook for better performance
import { useMaintenanceTasksRPC } from './useMaintenanceTasksRPC';
import { usePurchaseOrders } from './usePurchaseOrders';
import { useInventoryQueryV2 } from './useInventoryQueryV2';
import { usePermissions } from '@/hooks/usePermissionsFixed';
import { toast } from 'sonner';
// Removed unused imports

interface DamageReport {
  id: string;
  title: string;
  property_name?: string;
  status: string;
  created_at: string;
  properties?: {
    name?: string;
  };
}

interface DashboardDamage {
  id: string;
  title: string;
  propertyName: string;
  status: 'new' | 'pending' | 'completed';
  reportedAt: string;
}

// Debounce function to prevent excessive state updates
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

export const useDashboardData = () => {
  // Prevent excessive logging
  const DEBUG = false;
  const debugLog = (...args: any[]) => {
    if (DEBUG) console.log(...args);
  };

  // Track if this is the first load
  const isFirstLoad = useRef(true);
  const loadingTimerRef = useRef<NodeJS.Timeout | null>(null);
  const stableDataRef = useRef<any>(null);

  const { properties, loading: propertiesLoading } = usePropertiesQueryV2();
  // Get the full result object from useMaintenanceTasksRPC
  const maintenanceTaskResult = useMaintenanceTasksRPC();
  const { purchaseOrders, isLoading: purchaseOrdersLoading } = usePurchaseOrders();
  const { inventoryItems: allInventoryItems, loading: inventoryLoading } = useInventoryQueryV2();

  const [damages, setDamages] = useState<DashboardDamage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { authState } = useAuth();
  const userId = authState?.user?.id;

  // Get user's role information
  const isAdmin = authState?.profile?.is_super_admin || authState?.profile?.role === 'admin';

  // Initialize team state
  const [userTeams, setUserTeams] = useState<string[]>([]);
  const [accessibleTeams, setAccessibleTeams] = useState<string[]>([]);

  // Get team information in an effect
  useEffect(() => {
    if (userId) {
      // Fetch team memberships directly
      const fetchTeamMemberships = async () => {
        try {
          console.log('[useDashboardData] Fetching team memberships for user:', userId);
          const { data: teamMemberships, error: teamError } = await supabase
            .from('team_members')
            .select('team_id')
            .eq('user_id', userId)
            .eq('status', 'active');

          if (teamError) {
            console.error('[useDashboardData] Error fetching team memberships:', teamError);
            setUserTeams([]);
          } else if (teamMemberships && teamMemberships.length > 0) {
            const teams = teamMemberships.map((tm: { team_id: string }) => tm.team_id);
            console.log('[useDashboardData] User is a member of teams:', teams);
            setUserTeams(teams);

            // Set accessible teams based on role
            if (isAdmin) {
              setAccessibleTeams(['*']); // Admins can access all teams
            } else {
              setAccessibleTeams(teams);
            }
          } else {
            console.log('[useDashboardData] User is not a member of any teams');
            setUserTeams([]);
            setAccessibleTeams([]);
          }
        } catch (error) {
          console.error('[useDashboardData] Error fetching team data:', error);
          setUserTeams([]);
          setAccessibleTeams([]);
        }
      };

      fetchTeamMemberships();
    }
  }, [userId, isAdmin]);

  // Format dates for display
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (error) {
      return dateString || 'N/A';
    }
  };

  // Get low stock inventory items for dashboard display - memoized to prevent recalculation
  const inventoryItems = useMemo(() => {
    if (loading) return [];

    debugLog('[useDashboardData] Processing inventory items:', allInventoryItems?.length);

    if (!allInventoryItems || allInventoryItems.length === 0) {
      debugLog('[useDashboardData] No inventory items available');
      return [];
    }

    try {
      // Normalize the data structure to ensure consistent property access
      const normalizedItems = allInventoryItems.map((item: any) => {
        // Ensure we have valid objects
        if (!item) return null;

        // Handle potential type issues with quantity and minQuantity
        const quantity = typeof item.quantity === 'number' ? item.quantity : parseInt(String(item.quantity || '0')) || 0;
        const minQuantity = typeof item.minQuantity === 'number' ? item.minQuantity :
                          item.min_quantity ? parseInt(String(item.min_quantity)) : 1;

        return {
          id: item.id,
          name: item.name || 'Unnamed Item',
          propertyId: item.propertyId || item.property_id || '',
          propertyName: item.propertyName || item.property_name || 'Unknown Property',
          collection: item.collection || '',
          quantity: quantity,
          minQuantity: minQuantity,
          price: item.price || 0,
          amazonUrl: item.amazonUrl || item.amazon_url || '',
          walmartUrl: item.walmartUrl || item.walmart_url || '',
          imageUrl: item.imageUrl || item.image_url || '',
          lastOrdered: item.lastOrdered || item.last_ordered || ''
        };
      }).filter(Boolean); // Remove any null items

      // Filter inventory items to show only those with low stock
      const lowStockItems = normalizedItems.filter((item: any) => {
        if (!item) return false;
        const isLowStock = item.quantity <= item.minQuantity;
        return isLowStock;
      }).slice(0, 5);

      debugLog('[useDashboardData] Low stock items for dashboard:', lowStockItems.length);
      return lowStockItems;
    } catch (error) {
      console.error('[useDashboardData] Error processing inventory items:', error);
      return [];
    }
  }, [allInventoryItems, loading]);

  // Fetch damage reports
  // Get user's role and team information once at the component level (moved to top of component)

  useEffect(() => {
    const fetchDamageReports = async () => {
      if (!userId) return;

      try {

        console.log('[useDashboardData] Fetching damage reports');
        console.log('[useDashboardData] User is admin:', isAdmin);
        console.log('[useDashboardData] User teams:', userTeams);
        console.log('[useDashboardData] Accessible teams:', accessibleTeams);

        let query = supabase
          .from('damage_reports')
          .select('*, properties(name)')
          .order('created_at', { ascending: false })
          .limit(5);

        // Apply different filters based on user role
        if (isAdmin) {
          // Admins see all damage reports
          console.log('[useDashboardData] Admin fetching all damage reports');
        } else if (accessibleTeams.includes('*')) {
          // Special case for wildcard access
          console.log('[useDashboardData] User has wildcard access to teams');
        } else if (accessibleTeams.length > 0) {
          // For team members, get team property IDs
          console.log('[useDashboardData] Fetching team property damage reports');

          // First get all property IDs from the user's teams
          const { data: teamPropertiesData } = await supabase
            .from('team_properties')
            .select('property_id')
            .in('team_id', accessibleTeams);

          if (teamPropertiesData && teamPropertiesData.length > 0) {
            // Get the property IDs from the team properties
            const propertyIds = teamPropertiesData.map((tp: { property_id: string }) => tp.property_id);
            console.log('[useDashboardData] Team property IDs:', propertyIds);

            // Filter damage reports by these property IDs
            query = query.in('property_id', propertyIds);
          } else {
            // Fallback to user's own reports if no team properties
            console.log('[useDashboardData] No team properties found, showing own reports');
            query = query.eq('user_id', userId);
          }
        } else {
          // Default case - just show user's own reports
          console.log('[useDashboardData] Showing only user\'s own reports');
          query = query.eq('user_id', userId);
        }

        const { data, error } = await query;

        if (error) throw error;

        if (data) {
          console.log(`[useDashboardData] Successfully loaded ${data.length} damage reports`);
          const formattedDamages: DashboardDamage[] = data.map((damage: DamageReport) => ({
            id: damage.id,
            title: damage.title,
            propertyName: damage.properties?.name || 'Unknown Property',
            status: damage.status === 'open' ? 'new' :
                  damage.status === 'in_progress' ? 'pending' : 'completed',
            reportedAt: formatDate(damage.created_at)
          }));

          setDamages(formattedDamages);
        }
      } catch (error) {
        console.error('[useDashboardData] Error fetching damage reports:', error);
        setError('Failed to fetch damage reports');
      }
    };

    fetchDamageReports();
  }, [userId, isAdmin, userTeams, accessibleTeams]);

  // Create a debounced loading state to prevent rapid changes
  // Adjust loading state: include maintenanceTaskResult.loading
  const rawLoadingState = propertiesLoading || maintenanceTaskResult.loading || inventoryLoading || purchaseOrdersLoading;
  const debouncedLoadingState = useDebounce(rawLoadingState, 300);

  // Update loading state based on all data fetches with a minimum loading time
  useEffect(() => {
    // Clear any existing timer
    if (loadingTimerRef.current) {
      clearTimeout(loadingTimerRef.current);
      loadingTimerRef.current = null;
    }

    // If we're transitioning to a loading state, update immediately
    if (debouncedLoadingState && !loading) {
      debugLog('[useDashboardData] Some data still loading, setting loading to true');
      setLoading(true);
      return;
    }

    // If we're transitioning from loading to not loading, enforce a minimum loading time
    if (!debouncedLoadingState && loading) {
      // Ensure we show loading for at least 500ms to prevent flashing
      const minimumLoadingTime = isFirstLoad.current ? 800 : 500;

      loadingTimerRef.current = setTimeout(() => {
        debugLog('[useDashboardData] All data loaded, setting loading to false');
        setLoading(false);
        isFirstLoad.current = false;
      }, minimumLoadingTime);
    }

    return () => {
      if (loadingTimerRef.current) {
        clearTimeout(loadingTimerRef.current);
      }
    };
  }, [debouncedLoadingState, loading]);

  // Map properties to the correct format for the dashboard - memoized to prevent recalculation
  const dashboardProperties = useMemo(() => {
    console.log('[useDashboardData] Mapping properties for dashboard:', properties.length);

    // Log a sample property for debugging
    if (properties.length > 0) {
      console.log('[useDashboardData] Sample property before mapping:', {
        name: properties[0].name,
        bedrooms: properties[0].bedrooms,
        bathrooms: properties[0].bathrooms,
        image_url: properties[0].image_url
      });
    }

    const mapped = properties.map(property => {
      const cardProperty = mapPropertyToCardProperty(property);

      // Log the first mapped property for debugging
      if (property === properties[0]) {
        console.log('[useDashboardData] Sample property after mapping:', {
          name: cardProperty.name,
          bedrooms: cardProperty.bedrooms,
          bathrooms: cardProperty.bathrooms,
          imageUrl: cardProperty.imageUrl
        });
      }

      return cardProperty;
    });

    return mapped;
  }, [properties]);

  // Helper function to refetch damage reports
  function fetchDamageReports(): Promise<void> {
    if (!userId) return Promise.resolve();

    return new Promise((resolve, reject) => {
      console.log('[useDashboardData] Refreshing damage reports');
      console.log('[useDashboardData] User is admin:', isAdmin);
      console.log('[useDashboardData] User teams:', userTeams);
      console.log('[useDashboardData] Accessible teams:', accessibleTeams);

      let query = supabase
        .from('damage_reports')
        .select('*, properties(name)')
        .order('created_at', { ascending: false })
        .limit(5);

      // Apply different filters based on user role
      if (isAdmin) {
        // Admins see all damage reports
        console.log('[useDashboardData] Admin fetching all damage reports');
      } else if (accessibleTeams.includes('*')) {
        // Special case for wildcard access
        console.log('[useDashboardData] User has wildcard access to teams');
      } else if (accessibleTeams.length > 0) {
        // For team members, we need to get team property IDs first
        console.log('[useDashboardData] Fetching team property damage reports');

        // We'll handle this in the async chain below
        // For now, just set a flag to indicate we need to do this
        query = query.eq('user_id', userId); // Default filter, will be replaced
      } else {
        // Default case - just show user's own reports
        console.log('[useDashboardData] Showing only user\'s own reports');
        query = query.eq('user_id', userId);
      }

      // Special handling for team members
      if (!isAdmin && accessibleTeams.length > 0 && !accessibleTeams.includes('*')) {
        // First get team properties
        supabase
          .from('team_properties')
          .select('property_id')
          .in('team_id', accessibleTeams)
          .then(({ data: teamPropertiesData, error: teamPropsError }) => {
            if (teamPropsError) {
              console.error('[useDashboardData] Error fetching team properties:', teamPropsError);
              // Fall back to user's own reports
              return supabase
                .from('damage_reports')
                .select('*, properties(name)')
                .eq('user_id', userId)
                .order('created_at', { ascending: false })
                .limit(5);
            }

            if (teamPropertiesData && teamPropertiesData.length > 0) {
              // Get the property IDs from the team properties
              const propertyIds = teamPropertiesData.map((tp: { property_id: string }) => tp.property_id);
              console.log('[useDashboardData] Team property IDs:', propertyIds);

              // Now get damage reports for these properties
              return supabase
                .from('damage_reports')
                .select('*, properties(name)')
                .in('property_id', propertyIds)
                .order('created_at', { ascending: false })
                .limit(5);
            } else {
              // No team properties, fall back to user's own reports
              return supabase
                .from('damage_reports')
                .select('*, properties(name)')
                .eq('user_id', userId)
                .order('created_at', { ascending: false })
                .limit(5);
            }
          })
          .then(({ data, error }) => {
            if (error) {
              console.error('[useDashboardData] Error fetching damage reports:', error);
              reject(error);
              return;
            }

            if (data) {
              console.log(`[useDashboardData] Successfully loaded ${data.length} damage reports`);
              const formattedDamages: DashboardDamage[] = data.map((damage: DamageReport) => ({
                id: damage.id,
                title: damage.title,
                propertyName: damage.properties?.name || damage.property_name || 'Unknown Property',
                status: damage.status === 'open' ? 'new' :
                      damage.status === 'in_progress' ? 'pending' : 'completed',
                reportedAt: formatDate(damage.created_at)
              }));

              setDamages(formattedDamages);
              resolve();
            } else {
              setDamages([]);
              resolve();
            }
          })
          .catch((error: any) => {
            console.error('[useDashboardData] Error in damage reports chain:', error);
            reject(error);
          });
      } else {
        // For admins and users without teams, use the original query
        query
          .then(({ data, error }) => {
            if (error) {
              console.error('[useDashboardData] Error fetching damage reports:', error);
              reject(error);
              return;
            }

            if (data) {
              console.log(`[useDashboardData] Successfully loaded ${data.length} damage reports`);
              const formattedDamages: DashboardDamage[] = data.map((damage: DamageReport) => ({
                id: damage.id,
                title: damage.title,
                propertyName: damage.properties?.name || damage.property_name || 'Unknown Property',
                status: damage.status === 'open' ? 'new' :
                      damage.status === 'in_progress' ? 'pending' : 'completed',
                reportedAt: formatDate(damage.created_at)
              }));

              setDamages(formattedDamages);
              resolve();
            } else {
              setDamages([]);
              resolve();
            }
          })
          .catch(reject);
      }
    });
  }

  // Track if we're currently refreshing to prevent multiple simultaneous refreshes
  const isRefreshingRef = useRef(false);

  // Fetch data function for refreshing dashboard
  const refreshData = useCallback(async () => {
    // Prevent multiple simultaneous refreshes
    if (isRefreshingRef.current) {
      console.log('[Dashboard] Already refreshing, skipping duplicate refresh');
      return Promise.resolve();
    }

    console.log('[Dashboard] Refreshing dashboard data');
    isRefreshingRef.current = true;

    // Store refresh info for debugging
    try {
      const refreshes = JSON.parse(localStorage.getItem('stayfu_dashboard_refreshes') || '[]');
      refreshes.push({
        time: new Date().toISOString(),
        trigger: 'manual'
      });

      // Keep only the last 20 refreshes
      if (refreshes.length > 20) {
        refreshes.shift();
      }

      localStorage.setItem('stayfu_dashboard_refreshes', JSON.stringify(refreshes));
    } catch (e) {
      console.error('Error saving refresh info to localStorage:', e);
    }

    // Manually trigger refetches for all data sources
    try {
      // First, ensure properties are loaded
      console.log('[Dashboard] Ensuring properties are loaded first');
      if (properties.length === 0) {
        // Force a refresh of properties
        console.log('[Dashboard] No properties loaded, fetching properties first');
        try {
          const { data: propertiesData, error: propertiesError } = await supabase
            .from('properties')
            .select('*');

          if (propertiesError) {
            console.error('[Dashboard] Error fetching properties:', propertiesError);
          } else {
            console.log(`[Dashboard] Fetched ${propertiesData?.length || 0} properties directly`);
          }
        } catch (propError) {
          console.error('[Dashboard] Exception fetching properties:', propError);
        }

        // Add a small delay to allow properties to be processed
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Then fetch damage reports
      await fetchDamageReports();

      // Refresh maintenance tasks using the result object
      console.log('[Dashboard] Refreshing maintenance tasks');
      if (maintenanceTaskResult.refreshTasks) {
        maintenanceTaskResult.refreshTasks();

        // Also try a direct query as a backup
        console.log('[Dashboard] Also trying direct query for maintenance tasks...');
        supabase.from('maintenance_tasks')
          .select('*')
          .then(({ data, error }) => {
            console.log('[Dashboard] Direct query result:', { count: data?.length || 0, error });
            // Use maintenanceTaskResult.tasks here
            if (data && data.length > 0 && maintenanceTaskResult.tasks.length === 0) {
              console.log('[Dashboard] Got tasks from direct query in useDashboardData, triggering refreshTasks again');
              // Optionally trigger another refresh if direct query found tasks when the hook didn't
              maintenanceTaskResult.refreshTasks();
            }
          })
          .catch((err: Error) => {
            console.error('[Dashboard] Error with direct query in useDashboardData:', err);
          });
      } else {
        console.error('[Dashboard] maintenanceTaskResult.refreshTasks function is not available!');
        // Try a direct query as a fallback
        console.log('[Dashboard] Trying direct query as fallback...');
        const { data, error } = await supabase.from('maintenance_tasks').select('*');
        if (error) {
          console.error('[Dashboard] Error with fallback query:', error);
        } else {
          console.log('[Dashboard] Got tasks from fallback query:', data?.length || 0);
        }
      }

      // Return a resolved promise to indicate completion
      return Promise.resolve();
    } catch (error) {
      console.error('[Dashboard] Error refreshing data:', error);
      return Promise.reject(error);
    } finally {
      // Reset the refreshing flag
      setTimeout(() => {
        isRefreshingRef.current = false;
        console.log('[Dashboard] Reset refreshing flag');
      }, 5000); // Add a 5-second cooldown period to prevent rapid refreshes
    }
  }, [userId, isAdmin, userTeams, accessibleTeams, maintenanceTaskResult.refreshTasks, properties.length, maintenanceTaskResult.tasks.length]);

  // REMOVED: Visibility change handler that refreshes data
  // This was causing data to disappear when the app comes back into focus

  // Removed visibility change handler to rely on React Query's built-in functionality

  // Team information already defined at the top of the component

  // Calculate derived data with memoization to prevent recalculation
  const pendingOrders = useMemo(() => {
    if (loading) return [];
    return purchaseOrders.filter(order => order.status === 'pending');
  }, [purchaseOrders, loading]);

  // Create stable data reference to prevent unnecessary re-renders
  const currentData = {
    properties: loading ? [] : dashboardProperties,
    // Pass the entire maintenanceTaskResult object
    maintenanceTaskResult: maintenanceTaskResult,
    inventoryItems: loading ? [] : inventoryItems,
    damages: loading ? [] : damages,
    purchaseOrders: loading ? [] : purchaseOrders,
    pendingOrders: loading ? [] : pendingOrders,
    loading, // Overall loading state
    error,
    refreshData,
  };

  // Log the current data object - but only when loading completes
  const hasLoggedDataRef = useRef(false);

  useEffect(() => {
    // Only log once after loading completes and we have data
    // Use maintenanceTaskResult.tasks.length
    if (!loading && !hasLoggedDataRef.current && (
      properties.length > 0 ||
      maintenanceTaskResult.tasks.length > 0 ||
      inventoryItems.length > 0 ||
      damages.length > 0 ||
      purchaseOrders.length > 0
    )) {
      hasLoggedDataRef.current = true;
      console.log('[useDashboardData] Data loaded successfully:', {
        propertiesCount: properties.length,
        // Use maintenanceTaskResult.tasks.length
        maintenanceTasksCount: maintenanceTaskResult.tasks.length,
        inventoryItemsCount: inventoryItems.length,
        damagesCount: damages.length,
        purchaseOrdersCount: purchaseOrders.length
      });
    }
  }, [loading, properties.length, maintenanceTaskResult.tasks.length, inventoryItems.length, damages.length, purchaseOrders.length]);

  // Track if we've already tried a direct query
  const hasTriedDirectQueryRef = useRef(false);

  // Only update stable data reference when loading completes and we have data
  useEffect(() => {
    // Use maintenanceTaskResult.tasks.length
    if (!loading && (
      properties.length > 0 ||
      maintenanceTaskResult.tasks.length > 0 ||
      inventoryItems.length > 0 ||
      damages.length > 0 ||
      purchaseOrders.length > 0
    )) {
      // Update stableDataRef with the latest data
      stableDataRef.current = currentData;
    } else if (!loading && process.env.NODE_ENV === 'production' && maintenanceTaskResult.tasks.length === 0 && !hasTriedDirectQueryRef.current) {
      // Production retry logic (remains the same, but uses maintenanceTaskResult.tasks.length)
      console.log('[useDashboardData] Production environment with no maintenance tasks, trying direct query');
      hasTriedDirectQueryRef.current = true;

      // Try a direct query for maintenance tasks
      supabase.from('maintenance_tasks')
        .select('*')
        .then(({ data, error }) => {
          if (error) {
            console.error('[useDashboardData] Production direct query error:', error);
          } else if (data && data.length > 0) {
            console.log('[useDashboardData] Production direct query found tasks:', data.length);
          } else {
            console.log('[useDashboardData] Production direct query found no tasks');
          }
        })
        .catch((err: Error) => {
          console.error('[useDashboardData] Production direct query exception:', err);
        });
    }
  }, [loading, currentData, properties.length, maintenanceTaskResult.tasks.length, inventoryItems.length, damages.length, purchaseOrders.length]);

  // Return the stable data reference if available, otherwise the current data
  return stableDataRef.current || currentData;
};
