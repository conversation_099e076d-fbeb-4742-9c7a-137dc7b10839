import { useState, useEffect } from 'react'
import { supabaseSimple } from '@/integrations/supabase/client-simple'
import { useAuthSimple } from '@/contexts/AuthContextSimple'

interface Property {
  id: string
  name: string
  address: string
  city: string
  state: string
  zip: string
  bedrooms: number
  bathrooms: number
  image_url?: string
  created_at: string
  updated_at: string
}

export const usePropertiesSimple = () => {
  const [properties, setProperties] = useState<Property[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { authState } = useAuthSimple()

  const fetchProperties = async () => {
    if (!authState.user?.id) {
      setProperties([])
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      console.log('🏠 Fetching properties for user:', authState.user.id)

      const { data, error: fetchError } = await supabaseSimple
        .from('properties')
        .select('*')
        .eq('user_id', authState.user.id)
        .order('created_at', { ascending: false })

      if (fetchError) {
        console.error('❌ Error fetching properties:', fetchError)
        setError(fetchError.message)
        setProperties([])
      } else {
        console.log('✅ Properties fetched successfully:', data?.length || 0)
        setProperties(data || [])
      }
    } catch (err) {
      console.error('❌ Unexpected error fetching properties:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
      setProperties([])
    } finally {
      setLoading(false)
    }
  }

  // Fetch properties when user changes
  useEffect(() => {
    if (authState.isLoading) return
    
    fetchProperties()
  }, [authState.user?.id, authState.isLoading])

  return {
    properties,
    loading,
    error,
    refetch: fetchProperties
  }
}
