import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useImpersonation } from '@/contexts/ImpersonationContext';
import { usePermissions } from '@/hooks/usePermissions';
import { PermissionType } from '@/types/auth';
import { CollectionWithBudget } from '@/components/properties/PropertyCard';
import { toast } from 'sonner';

// Helper function to process properties data
const processPropertiesData = (data: any[]): Property[] => {
  return (data || []).map((property: any) => {
    let collections: CollectionWithBudget[] = [];

    // Process collections from the database
    try {
      if (property.collections) {
        if (Array.isArray(property.collections)) {
          collections = property.collections
            .filter((col: any): col is string | object => col !== null && col !== '')
            .map((col: any): CollectionWithBudget | null => {
              if (typeof col === 'string' && col.trim()) {
                return { name: col.trim() };
              } else if (col && typeof col === 'object' && 'name' in col && col.name) {
                return col as CollectionWithBudget;
              }
              return null;
            })
            .filter((item): item is CollectionWithBudget => item !== null);
        } else if (typeof property.collections === 'string') {
          // Handle case where collections is a string (possibly JSON)
          try {
            const parsed = JSON.parse(property.collections);
            if (Array.isArray(parsed)) {
              collections = parsed
                .filter((col: any) => col !== null && col !== '')
                .map((col: any): CollectionWithBudget | null => {
                  if (typeof col === 'string' && col.trim()) {
                    return { name: col.trim() };
                  } else if (col && typeof col === 'object' && 'name' in col && col.name) {
                    return col as CollectionWithBudget;
                  }
                  return null;
                })
                .filter((item): item is CollectionWithBudget => item !== null);
            }
          } catch (e) {
            // If parsing fails, create a single collection from the string
            if (property.collections.trim()) {
              collections = [{ name: property.collections.trim() }];
            }
          }
        } else if (typeof property.collections === 'object' && property.collections !== null) {
          // Handle object collections
          Object.entries(property.collections).forEach(([key, value]) => {
            if (!key || key === '') return;

            if (typeof value === 'object' && value !== null) {
              collections.push({
                name: key,
                ...(value as any)
              });
            } else {
              collections.push({ name: key });
            }
          });
        }
      }
    } catch (e) {
      console.error('Error processing collections:', e);
      collections = [];
    }

    // Ensure numeric values for bedrooms and bathrooms
    const bedrooms = typeof property.bedrooms === 'number' ? property.bedrooms :
                    parseInt(String(property.bedrooms || '1')) || 1;

    const bathrooms = typeof property.bathrooms === 'number' ? property.bathrooms :
                     parseInt(String(property.bathrooms || '1')) || 1;

    // Ensure budget is a number
    const budget = typeof property.budget === 'number' ? property.budget :
                 parseFloat(String(property.budget || '0')) || 0;

    // Log the property data for debugging
    console.log(`[useProperties] Processing property ${property.name}:`, {
      bedrooms: property.bedrooms,
      parsedBedrooms: bedrooms,
      bathrooms: property.bathrooms,
      parsedBathrooms: bathrooms,
      budget: property.budget,
      parsedBudget: budget
    });

    return {
      id: property.id,
      name: property.name,
      address: property.address,
      city: property.city,
      state: property.state,
      zip: property.zip,
      image_url: property.image_url || '',
      bedrooms: bedrooms,
      bathrooms: bathrooms,
      budget: budget,
      ical_url: property.ical_url || '',
      next_booking: property.next_booking || '',
      next_checkin_date: property.next_checkin_date || '',
      next_checkin_formatted: property.next_checkin_formatted || '',
      collections: collections,
      is_occupied: property.is_occupied,
      current_checkout: property.current_checkout,
      last_ical_sync: property.last_ical_sync || '',
      timezone: property.timezone || 'America/Los_Angeles',
      check_in_time: property.check_in_time || '15:00:00',
      check_out_time: property.check_out_time || '11:00:00'
    };
  });
};

export interface Property {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  image_url: string;
  bedrooms: number;
  bathrooms: number;
  budget: number;
  ical_url?: string;
  next_booking?: string;
  next_checkin_date?: string; // ISO format date
  next_checkin_formatted?: string; // Human-readable format
  collections: CollectionWithBudget[];
  is_occupied?: boolean;
  current_checkout?: string;
  last_ical_sync?: string;
  timezone?: string; // Property timezone
  check_in_time?: string; // Check-in time in HH:MM format
  check_out_time?: string; // Check-out time in HH:MM format
}

export const useProperties = () => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { authState } = useAuth();
  const { userTeams, hasPermission, getAccessibleTeams } = usePermissions();

  // Use the impersonation context
  const { isImpersonating, applyImpersonationFilter } = useImpersonation();

  const fetchProperties = async (retryCount = 0, maxRetries = 3) => {
    try {
      setLoading(true);
      setError(null);

      if (!authState.user?.id) {
        throw new Error('User not authenticated');
      }

      console.log(`[useProperties] Fetching properties (attempt ${retryCount + 1}/${maxRetries + 1})`);

      // Use the unified get_user_role_properties function that handles all user roles
      try {
        console.log('[useProperties] Using get_user_role_properties RPC function');
        const { data: rpcData, error: rpcError } = await supabase.rpc(
          'get_user_role_properties',
          { p_user_id: authState.user.id }
        );

        if (!rpcError && rpcData) {
          console.log(`[useProperties] Successfully loaded ${rpcData.length} properties via RPC function`);

          // Debug log the first property to check if all fields are present
          if (rpcData.length > 0) {
            console.log('[useProperties] Sample property from RPC:', {
              id: rpcData[0].id,
              name: rpcData[0].name,
              bedrooms: rpcData[0].bedrooms,
              bathrooms: rpcData[0].bathrooms,
              image_url: rpcData[0].image_url
            });
          }

          // Apply impersonation filter if needed
          let filteredData = rpcData;
          if (isImpersonating) {
            console.log('[useProperties] Impersonating user, filtering properties');
            // Simple filter for impersonation - can be enhanced if needed
            filteredData = rpcData.filter(p => p.user_id === authState.user.id);
          }

          // Process the properties and set them
          const formattedProperties = processPropertiesData(filteredData);

          // Debug log the first processed property
          if (formattedProperties.length > 0) {
            console.log('[useProperties] Sample property after processing:', {
              id: formattedProperties[0].id,
              name: formattedProperties[0].name,
              bedrooms: formattedProperties[0].bedrooms,
              bathrooms: formattedProperties[0].bathrooms,
              image_url: formattedProperties[0].image_url
            });
          }

          setProperties(formattedProperties);
          setLoading(false);
          return;
        } else {
          console.error('[useProperties] RPC function error:', rpcError);
          // Fall back to the old method
        }
      } catch (rpcErr) {
        console.error('[useProperties] Error using RPC function:', rpcErr);
        // Fall back to the old method
      }

      // Fallback method: Get user's own properties
      console.log('[useProperties] Falling back to direct query method');
      let query = supabase
        .from('properties')
        .select('*')
        .eq('user_id', authState.user.id);

      // Apply impersonation filter if needed
      if (isImpersonating) {
        console.log('[useProperties] Impersonating user, showing only their properties');
        query = applyImpersonationFilter(query);
      }

      // Sort by created date
      query = query.order('created_at', { ascending: false });

      const { data: ownProperties, error: ownPropertiesError } = await query;

      if (ownPropertiesError) {
        console.error('[useProperties] Error fetching own properties:', ownPropertiesError);
        throw ownPropertiesError;
      }

      // Debug log the first property to check if all fields are present
      if (ownProperties && ownProperties.length > 0) {
        console.log('[useProperties] Sample property from direct query:', {
          id: ownProperties[0].id,
          name: ownProperties[0].name,
          bedrooms: ownProperties[0].bedrooms,
          bathrooms: ownProperties[0].bathrooms,
          image_url: ownProperties[0].image_url
        });
      }

      console.log(`[useProperties] Successfully loaded ${ownProperties?.length || 0} own properties`);

      // Step 2: Get team properties if user is in any teams
      let teamProperties: any[] = [];
      if (userTeams && userTeams.length > 0) {
        console.log('[useProperties] User is in teams, fetching team properties');

        // Get all team properties for the user's teams
        const teamPropertiesIds: string[] = [];

        try {
          // Directly query the team_properties table instead of using RPC
          const { data: teamPropertyData, error: teamPropertyError } = await supabase
            .from('team_properties')
            .select('property_id')
            .in('team_id', userTeams);

          if (teamPropertyError) {
            console.error(`[useProperties] Error fetching properties for teams:`, teamPropertyError);
          } else if (teamPropertyData && teamPropertyData.length > 0) {
            // Extract property IDs from the result
            const propertyIds = teamPropertyData.map(item => item.property_id);
            teamPropertiesIds.push(...propertyIds);
            console.log(`[useProperties] Found ${propertyIds.length} team property IDs`);
          }
        } catch (err) {
          console.error(`[useProperties] Exception fetching team properties:`, err);
        }

        // Remove duplicates from team property IDs
        const uniqueTeamPropertyIds = [...new Set(teamPropertiesIds)];
        console.log(`[useProperties] Found ${uniqueTeamPropertyIds.length} unique team property IDs`);

        if (uniqueTeamPropertyIds.length > 0) {
          // Fetch the actual property details for these IDs
          const { data: teamPropsData, error: teamPropsError } = await supabase
            .from('properties')
            .select('*')
            .in('id', uniqueTeamPropertyIds);

          if (teamPropsError) {
            console.error('[useProperties] Error fetching team properties details:', teamPropsError);
          } else {
            teamProperties = teamPropsData || [];
            console.log(`[useProperties] Successfully loaded ${teamProperties.length} team properties`);

            // Debug log the first team property to check if all fields are present
            if (teamProperties.length > 0) {
              console.log('[useProperties] Sample team property:', {
                id: teamProperties[0].id,
                name: teamProperties[0].name,
                bedrooms: teamProperties[0].bedrooms,
                bathrooms: teamProperties[0].bathrooms,
                image_url: teamProperties[0].image_url
              });
            }
          }
        }
      }

      // Step 3: Combine own properties and team properties
      const allProperties = [...(ownProperties || []), ...teamProperties];
      console.log(`[useProperties] Combined ${ownProperties?.length || 0} own properties with ${teamProperties.length} team properties`);

      // If we got an empty array but we know we should have properties, retry
      if (allProperties.length === 0 && properties.length > 0) {
        console.warn('[useProperties] Received empty properties array when we had properties before');
        if (retryCount < maxRetries) {
          console.log(`[useProperties] Retrying fetch (${retryCount + 1}/${maxRetries})`);
          // Wait with exponential backoff before retrying
          const delay = Math.min(1000 * 2 ** retryCount, 10000);
          setTimeout(() => fetchProperties(retryCount + 1, maxRetries), delay);
          return;
        }
      }

      const formattedProperties = processPropertiesData(allProperties);

      // Debug log the first property after processing
      if (formattedProperties.length > 0) {
        console.log('[useProperties] Sample property after final processing:', {
          id: formattedProperties[0].id,
          name: formattedProperties[0].name,
          bedrooms: formattedProperties[0].bedrooms,
          bathrooms: formattedProperties[0].bathrooms,
          image_url: formattedProperties[0].image_url
        });
      }

      // Filter out duplicate properties by ID
      const uniqueProperties = formattedProperties.reduce((acc, property) => {
        // If we haven't seen this property ID before, add it to the accumulator
        if (!acc.some(p => p.id === property.id)) {
          acc.push(property);
        }
        return acc;
      }, [] as Property[]);

      console.log(`[useProperties] Filtered ${formattedProperties.length - uniqueProperties.length} duplicate properties`);

      // Only update properties if we have new data or no existing data
      if (uniqueProperties.length > 0 || properties.length === 0) {
        console.log(`[useProperties] Updating properties: ${uniqueProperties.length} new properties`);
        setProperties(uniqueProperties);
      } else {
        console.log('[useProperties] Not updating properties - would replace with empty array');
      }

      // Ensure loading is set to false on success
      setLoading(false);
    } catch (err: any) {
      console.error('[useProperties] Error fetching properties:', err);
      setError(err.message);

      // Only show toast on first attempt to avoid spamming
      if (retryCount === 0) {
        toast.error('Failed to load properties. Retrying...', {
          description: 'Please wait while we try to reconnect.',
          action: {
            label: 'Retry Now',
            onClick: () => fetchProperties(0, maxRetries)
          }
        });
      }

      // Retry with exponential backoff if we haven't exceeded max retries
      if (retryCount < maxRetries) {
        const delay = Math.min(1000 * 2 ** retryCount, 10000);
        console.log(`[useProperties] Will retry in ${delay}ms (attempt ${retryCount + 1}/${maxRetries})`);
        setTimeout(() => fetchProperties(retryCount + 1, maxRetries), delay);
      } else {
        // If we've exhausted retries, show a final error with manual retry option
        toast.error('Could not load properties after multiple attempts', {
          description: 'There might be a connection issue.',
          action: {
            label: 'Try Again',
            onClick: () => fetchProperties(0, maxRetries)
          },
          duration: 10000 // Show for longer
        });
        setLoading(false);
      }
    } finally {
      // We now set loading to false in the success case and in the error case after max retries
      // This block is kept for safety, but should be redundant
      if (retryCount >= maxRetries) {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    if (authState.user?.id) {
      fetchProperties();
    }
  }, [authState.user?.id, userTeams, isImpersonating]);

  return { properties, loading, error, fetchProperties, setProperties };
};
