
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Property } from './useProperties';
import { Property as CardProperty } from '@/components/properties/PropertyCard';

const usePropertyUpdate = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleUpdateProperty = async (
    propertyId: string | undefined,
    originalProperty: Property,
    updatedProperty: Partial<Property | CardProperty>
  ) => {
    if (!propertyId) {
      setError('Property ID is missing');
      toast.error('Unable to update property: Missing ID');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      // Map from UI property format to database format
      const dataToUpdate: any = {
        name: updatedProperty.name,
        address: updatedProperty.address,
        city: updatedProperty.city,
        state: updatedProperty.state,
        zip: updatedProperty.zip,
        // Handle imageUrl vs image_url differences between CardProperty and Property
        image_url: 'imageUrl' in updatedProperty
          ? updatedProperty.imageUrl
          : ('image_url' in updatedProperty ? updatedProperty.image_url : undefined),
        bedrooms: updatedProperty.bedrooms,
        bathrooms: updatedProperty.bathrooms,
        budget: updatedProperty.budget,
        // Handle iCalUrl vs ical_url differences between CardProperty and Property
        ical_url: 'iCalUrl' in updatedProperty
          ? updatedProperty.iCalUrl
          : ('ical_url' in updatedProperty ? updatedProperty.ical_url : undefined),
        collections: updatedProperty.collections,
        // Add timezone and check-in/check-out times
        timezone: updatedProperty.timezone,
        check_in_time: updatedProperty.check_in_time,
        check_out_time: updatedProperty.check_out_time
      };

      // Remove undefined fields to avoid overwriting with null
      Object.keys(dataToUpdate).forEach(key => {
        if (dataToUpdate[key] === undefined) {
          delete dataToUpdate[key];
        }
      });

      console.log('Updating property with:', dataToUpdate);

      const { data, error } = await supabase
        .from('properties')
        .update(dataToUpdate)
        .eq('id', propertyId)
        .select()
        .single();

      if (error) {
        console.error('Error updating property:', error);
        setError(error.message);
        toast.error(`Failed to update property: ${error.message}`);
        return null;
      }

      // Process collections from the database for UI
      if (data) {
        toast.success('Property updated successfully');

        // Map from database format back to UI format if needed
        return {
          ...data,
          imageUrl: data.image_url,
          iCalUrl: data.ical_url
        };
      }

      return null;
    } catch (err) {
      console.error('Error updating property:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);
      toast.error(`Failed to update property: ${errorMessage}`);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    handleUpdateProperty,
    loading,
    error
  };
};

export default usePropertyUpdate;
