import { renderHook, act } from '@testing-library/react-hooks';
import { useAdmin } from './useAdmin';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Mock dependencies
jest.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: { session: { access_token: 'test-token' } },
        error: null
      })
    },
    functions: {
      invoke: jest.fn()
    }
  }
}));

jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}));

jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    authState: {
      profile: { is_super_admin: true }
    }
  })
}));

describe('useAdmin', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should check if user is super admin', () => {
    const { result } = renderHook(() => useAdmin());
    
    expect(result.current.isSuperAdmin()).toBe(true);
  });

  it('should fetch users successfully', async () => {
    const mockUsers = [{ id: '1', email: '<EMAIL>' }];
    (supabase.functions.invoke as jest.Mock).mockResolvedValueOnce({
      data: { users: mockUsers },
      error: null
    });

    const { result } = renderHook(() => useAdmin());
    
    let users;
    await act(async () => {
      users = await result.current.fetchUsers();
    });
    
    expect(supabase.functions.invoke).toHaveBeenCalledWith('admin-get-users', {
      headers: { Authorization: 'Bearer test-token' }
    });
    expect(users).toEqual(mockUsers);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it('should handle fetch users error', async () => {
    (supabase.functions.invoke as jest.Mock).mockResolvedValueOnce({
      data: null,
      error: { message: 'Failed to fetch users' }
    });

    const { result } = renderHook(() => useAdmin());
    
    let users;
    await act(async () => {
      users = await result.current.fetchUsers();
    });
    
    expect(users).toEqual([]);
    expect(result.current.error).toBe('Failed to fetch users');
  });

  it('should update user successfully', async () => {
    (supabase.functions.invoke as jest.Mock).mockResolvedValueOnce({
      data: { success: true },
      error: null
    });

    const { result } = renderHook(() => useAdmin());
    
    let success;
    await act(async () => {
      success = await result.current.updateUser('user-id', { first_name: 'John' });
    });
    
    expect(supabase.functions.invoke).toHaveBeenCalledWith('admin-update-user', {
      headers: { Authorization: 'Bearer test-token' },
      body: { 
        userId: 'user-id',
        userData: { first_name: 'John' }
      }
    });
    expect(success).toBe(true);
    expect(toast.success).toHaveBeenCalledWith('User updated successfully');
  });

  it('should handle update user error', async () => {
    (supabase.functions.invoke as jest.Mock).mockResolvedValueOnce({
      data: null,
      error: { message: 'Failed to update user' }
    });

    const { result } = renderHook(() => useAdmin());
    
    let success;
    await act(async () => {
      success = await result.current.updateUser('user-id', { first_name: 'John' });
    });
    
    expect(success).toBe(false);
    expect(toast.error).toHaveBeenCalledWith('Failed to update user');
  });

  it('should delete user successfully', async () => {
    (supabase.functions.invoke as jest.Mock).mockResolvedValueOnce({
      data: { success: true },
      error: null
    });

    const { result } = renderHook(() => useAdmin());
    
    let success;
    await act(async () => {
      success = await result.current.deleteUser('user-id');
    });
    
    expect(supabase.functions.invoke).toHaveBeenCalledWith('admin-delete-user', {
      headers: { Authorization: 'Bearer test-token' },
      body: { userId: 'user-id' }
    });
    expect(success).toBe(true);
    expect(toast.success).toHaveBeenCalledWith('User deleted successfully');
  });
});
