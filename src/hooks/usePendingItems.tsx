
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface PendingItem {
  id: string;
  type: 'maintenance' | 'damage' | 'inventory';
  title: string;
  status: string;
  priority?: string;
  created_at: string;
}

export const usePendingItems = (propertyId?: string, userId?: string) => {
  const [pendingItems, setPendingItems] = useState<PendingItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  const fetchPendingItems = async () => {
    if (!propertyId || !userId) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      
      // Fetch maintenance tasks
      const { data: maintenanceTasks, error: maintenanceError } = await supabase
        .from('maintenance_tasks')
        .select('id, title, status, severity, created_at')
        .eq('property_id', propertyId)
        .eq('user_id', userId)
        .neq('status', 'completed')
        .order('created_at', { ascending: false });
      
      if (maintenanceError) throw maintenanceError;
      
      // Fetch damage reports
      const { data: damageReports, error: damageError } = await supabase
        .from('damage_reports')
        .select('id, title, status, created_at')
        .eq('property_id', propertyId)
        .eq('user_id', userId)
        .neq('status', 'closed')
        .order('created_at', { ascending: false });
      
      if (damageError) throw damageError;
      
      // Fetch inventory items that are low on stock
      const { data: inventoryItems, error: inventoryError } = await supabase
        .from('inventory_items')
        .select('id, name, quantity, min_quantity, created_at')
        .eq('property_id', propertyId)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });
      
      if (inventoryError) throw inventoryError;
      
      // Process maintenance tasks
      const formattedMaintenanceTasks: PendingItem[] = (maintenanceTasks || []).map(task => ({
        id: task.id,
        type: 'maintenance',
        title: task.title,
        status: task.status,
        priority: task.severity,
        created_at: task.created_at
      }));
      
      // Process damage reports
      const formattedDamageReports: PendingItem[] = (damageReports || []).map(report => ({
        id: report.id,
        type: 'damage',
        title: report.title,
        status: report.status,
        created_at: report.created_at
      }));
      
      // Process low inventory items
      const formattedInventoryItems: PendingItem[] = (inventoryItems || [])
        .filter(item => item.quantity <= item.min_quantity)
        .map(item => ({
          id: item.id,
          type: 'inventory',
          title: `Low on stock: ${item.name}`,
          status: 'low_stock',
          created_at: item.created_at
        }));
      
      // Combine all pending items
      const combined = [
        ...formattedMaintenanceTasks,
        ...formattedDamageReports,
        ...formattedInventoryItems
      ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
      
      setPendingItems(combined);
    } catch (err: any) {
      console.error('Error fetching pending items:', err);
      setError(err.message || 'Failed to fetch pending items');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPendingItems();
  }, [propertyId, userId]);

  return {
    pendingItems,
    loading,
    error,
    fetchPendingItems
  };
};
