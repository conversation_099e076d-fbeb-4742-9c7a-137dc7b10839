import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { fetchUserSettings, updateUserSetting, applyDarkMode, applyCompactMode } from '@/services/settingsService';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { UserSettingKey } from '@/types/settings';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface UserSetting {
  id: string;
  key: string;
  label: string;
  description: string;
  value: boolean;
}

export const useThemeSettings = () => {
  const { authState } = useAuth();
  const { resetTutorials } = useOnboarding();
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isCompactMode, setIsCompactMode] = useState(false);
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState<UserSetting[]>([]);

  useEffect(() => {
    const loadSettings = async () => {
      const isDarkActive = document.documentElement.classList.contains('dark');
      setIsDarkMode(isDarkActive);

      const storedTheme = localStorage.getItem('theme-mode');
      if (storedTheme === 'dark') {
        setIsDarkMode(true);
        applyDarkMode(true);
      } else {
        // Default to light mode
        setIsDarkMode(false);
        applyDarkMode(false);

        // Ensure light mode is set in localStorage if not already set
        if (!storedTheme) {
          localStorage.setItem('theme-mode', 'light');
        }
      }

      if (authState.user?.id) {
        setLoading(true);
        try {
          const userSettings = await fetchUserSettings(authState.user.id);
          if (userSettings) {
            console.log('useThemeSettings: Got settings from DB:', userSettings);
            setIsDarkMode(userSettings.dark_mode);
            setIsCompactMode(userSettings.compact_mode || false);

            applyDarkMode(userSettings.dark_mode);
            applyCompactMode(userSettings.compact_mode || false);

            setSettings([
              {
                id: 'dark_mode',
                key: 'dark_mode',
                label: 'Dark Mode',
                description: 'Enable dark theme for the application',
                value: userSettings.dark_mode
              },
              {
                id: 'compact_mode',
                key: 'compact_mode',
                label: 'Compact Mode',
                description: 'Use a more compact UI layout',
                value: userSettings.compact_mode || false
              },
              {
                id: 'email_notifications',
                key: 'email_notifications',
                label: 'Email Notifications',
                description: 'Receive notifications via email',
                value: userSettings.email_notifications || false
              },
              {
                id: 'push_notifications',
                key: 'push_notifications',
                label: 'Push Notifications',
                description: 'Receive notifications in the browser',
                value: userSettings.push_notifications || false
              },
              {
                id: 'weekly_summary',
                key: 'weekly_summary',
                label: 'Weekly Summary',
                description: 'Receive a weekly summary of activities',
                value: userSettings.weekly_summary || false
              },
              {
                id: 'inventory_alerts',
                key: 'inventory_alerts',
                label: 'Inventory Alerts',
                description: 'Get alerts when inventory is low',
                value: userSettings.inventory_alerts || false
              },
              {
                id: 'animations',
                key: 'animations',
                label: 'Animations',
                description: 'Enable UI animations',
                value: userSettings.animations || true
              }
            ]);
          } else {
            console.log('No user settings found, using defaults');

            // Default to light mode
            const defaultDarkMode = storedTheme === 'dark';
            applyDarkMode(defaultDarkMode);
            applyCompactMode(false);

            if (authState.user?.id) {
              await supabase.from('user_settings').upsert({
                user_id: authState.user.id,
                dark_mode: defaultDarkMode,
                compact_mode: false,
                animations: true,
                email_notifications: true,
                push_notifications: false,
                weekly_summary: true,
                inventory_alerts: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              }).then(({ error }) => {
                if (error) {
                  console.error('Error creating default settings:', error);
                }
              });
            }

            setSettings([
              {
                id: 'dark_mode',
                key: 'dark_mode',
                label: 'Dark Mode',
                description: 'Enable dark theme for the application',
                value: defaultDarkMode
              },
              {
                id: 'compact_mode',
                key: 'compact_mode',
                label: 'Compact Mode',
                description: 'Use a more compact UI layout',
                value: false
              },
              {
                id: 'email_notifications',
                key: 'email_notifications',
                label: 'Email Notifications',
                description: 'Receive notifications via email',
                value: true
              },
              {
                id: 'push_notifications',
                key: 'push_notifications',
                label: 'Push Notifications',
                description: 'Receive notifications in the browser',
                value: false
              },
              {
                id: 'weekly_summary',
                key: 'weekly_summary',
                label: 'Weekly Summary',
                description: 'Receive a weekly summary of activities',
                value: true
              },
              {
                id: 'inventory_alerts',
                key: 'inventory_alerts',
                label: 'Inventory Alerts',
                description: 'Get alerts when inventory is low',
                value: true
              },
              {
                id: 'animations',
                key: 'animations',
                label: 'Animations',
                description: 'Enable UI animations',
                value: true
              }
            ]);
          }
        } catch (error) {
          console.error('Failed to load theme settings:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    loadSettings();
  }, [authState.user?.id, prefersDarkMode]);

  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (
          mutation.attributeName === 'class' &&
          mutation.target === document.documentElement
        ) {
          const isDarkActive = document.documentElement.classList.contains('dark');
          if (isDarkMode !== isDarkActive) {
            console.log('Dark mode class changed in DOM:', isDarkActive);
            setIsDarkMode(isDarkActive);
          }
        }
      });
    });

    observer.observe(document.documentElement, { attributes: true });

    return () => observer.disconnect();
  }, [isDarkMode]);

  const toggleDarkMode = async () => {
    if (!authState.user?.id) return;

    const newValue = !isDarkMode;
    console.log(`useThemeSettings: Toggling dark mode to ${newValue}`);

    setIsDarkMode(newValue);
    applyDarkMode(newValue);

    localStorage.setItem('theme-mode', newValue ? 'dark' : 'light');

    await updateUserSetting(authState.user.id, 'dark_mode', newValue);

    setSettings(prev =>
      prev.map(setting =>
        setting.key === 'dark_mode'
          ? { ...setting, value: newValue }
          : setting
      )
    );
  };

  const toggleCompactMode = async () => {
    if (!authState.user?.id) return;

    const newValue = !isCompactMode;
    setIsCompactMode(newValue);
    applyCompactMode(newValue);

    await updateUserSetting(authState.user.id, 'compact_mode', newValue);

    setSettings(prev =>
      prev.map(setting =>
        setting.key === 'compact_mode'
          ? { ...setting, value: newValue }
          : setting
      )
    );
  };

  const updateSetting = async (key: UserSettingKey, value: boolean) => {
    if (!authState.user?.id) return;

    if (key === 'dark_mode') {
      setIsDarkMode(value);
      applyDarkMode(value);
    } else if (key === 'compact_mode') {
      setIsCompactMode(value);
      applyCompactMode(value);
    }

    await updateUserSetting(authState.user.id, key, value);

    setSettings(prev =>
      prev.map(setting =>
        setting.key === key
          ? { ...setting, value }
          : setting
      )
    );
  };

  const handleResetTutorials = async () => {
    if (!authState.user?.id) return;

    try {
      resetTutorials();

      await supabase
        .from('user_preferences')
        .upsert({
          user_id: authState.user.id,
          onboarding_state: {
            hasSeenDashboardTutorial: false,
            hasSeenPropertiesTutorial: false,
            hasSeenInventoryTutorial: false,
            hasSeenMaintenanceTutorial: false,
            hasSeenDamagesTutorial: false,
          }
        });

      toast.success('Tutorials have been reset');
    } catch (error) {
      console.error('Failed to reset tutorials:', error);
      toast.error('Failed to reset tutorials');
    }
  };

  return {
    isDarkMode,
    isCompactMode,
    settings,
    loading,
    toggleDarkMode,
    toggleCompactMode,
    updateSetting,
    resetTutorials: handleResetTutorials
  };
};
