import { useState, useEffect, useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export interface PropertyStatistics {
  propertyId: string;
  maintenanceTasks: {
    total: number;
    critical: number;
    high: number;
    medium: number;
    low: number;
    new: number;
    inProgress: number;
    completed: number;
  };
  damageReports: {
    total: number;
    open: number;
    inProgress: number;
    resolved: number;
  };
  inventoryItems: {
    total: number;
    lowStock: number;
    outOfStock: number;
  };
}

export interface PropertyStatisticsMap {
  [propertyId: string]: PropertyStatistics;
}

/**
 * Hook to fetch statistics for all properties (maintenance tasks, damage reports, inventory)
 * This provides data for property cards to show visual indicators
 */
export const usePropertyStatistics = (propertyIds: string[]) => {
  const { authState } = useAuth();
  const userId = authState?.user?.id;

  const {
    data: statistics = {},
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['propertyStatistics', propertyIds],
    queryFn: async (): Promise<PropertyStatisticsMap> => {
      if (!userId || propertyIds.length === 0) {
        return {};
      }

      console.log('[usePropertyStatistics] Fetching statistics for properties:', propertyIds);

      const statisticsMap: PropertyStatisticsMap = {};

      // Initialize statistics for all properties
      propertyIds.forEach(propertyId => {
        statisticsMap[propertyId] = {
          propertyId,
          maintenanceTasks: {
            total: 0,
            critical: 0,
            high: 0,
            medium: 0,
            low: 0,
            new: 0,
            inProgress: 0,
            completed: 0
          },
          damageReports: {
            total: 0,
            open: 0,
            inProgress: 0,
            resolved: 0
          },
          inventoryItems: {
            total: 0,
            lowStock: 0,
            outOfStock: 0
          }
        };
      });

      try {
        // Fetch maintenance tasks for all properties
        const { data: maintenanceTasks, error: maintenanceError } = await supabase
          .from('maintenance_tasks')
          .select('property_id, status, severity')
          .in('property_id', propertyIds);

        if (maintenanceError) {
          console.error('[usePropertyStatistics] Error fetching maintenance tasks:', maintenanceError);
        } else if (maintenanceTasks) {
          maintenanceTasks.forEach(task => {
            const stats = statisticsMap[task.property_id];
            if (stats) {
              stats.maintenanceTasks.total++;
              
              // Count by severity
              switch (task.severity) {
                case 'critical':
                  stats.maintenanceTasks.critical++;
                  break;
                case 'high':
                  stats.maintenanceTasks.high++;
                  break;
                case 'medium':
                  stats.maintenanceTasks.medium++;
                  break;
                case 'low':
                  stats.maintenanceTasks.low++;
                  break;
              }

              // Count by status
              switch (task.status) {
                case 'new':
                case 'open':
                  stats.maintenanceTasks.new++;
                  break;
                case 'assigned':
                case 'in_progress':
                case 'accepted':
                  stats.maintenanceTasks.inProgress++;
                  break;
                case 'completed':
                  stats.maintenanceTasks.completed++;
                  break;
              }
            }
          });
        }

        // Fetch damage reports for all properties
        const { data: damageReports, error: damageError } = await supabase
          .from('damage_reports')
          .select('property_id, status')
          .in('property_id', propertyIds);

        if (damageError) {
          console.error('[usePropertyStatistics] Error fetching damage reports:', damageError);
        } else if (damageReports) {
          damageReports.forEach(report => {
            const stats = statisticsMap[report.property_id];
            if (stats) {
              stats.damageReports.total++;
              
              switch (report.status) {
                case 'open':
                  stats.damageReports.open++;
                  break;
                case 'in_progress':
                  stats.damageReports.inProgress++;
                  break;
                case 'resolved':
                  stats.damageReports.resolved++;
                  break;
              }
            }
          });
        }

        // Fetch inventory items for all properties
        const { data: inventoryItems, error: inventoryError } = await supabase
          .from('inventory_items')
          .select('property_id, quantity, min_quantity')
          .in('property_id', propertyIds);

        if (inventoryError) {
          console.error('[usePropertyStatistics] Error fetching inventory items:', inventoryError);
        } else if (inventoryItems) {
          inventoryItems.forEach(item => {
            const stats = statisticsMap[item.property_id];
            if (stats) {
              stats.inventoryItems.total++;
              
              if (item.quantity === 0) {
                stats.inventoryItems.outOfStock++;
              } else if (item.min_quantity && item.quantity <= item.min_quantity) {
                stats.inventoryItems.lowStock++;
              }
            }
          });
        }

        console.log('[usePropertyStatistics] Statistics calculated:', statisticsMap);
        return statisticsMap;

      } catch (err) {
        console.error('[usePropertyStatistics] Error fetching property statistics:', err);
        throw err;
      }
    },
    enabled: !!userId && propertyIds.length > 0,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
    retry: 2,
    retryDelay: 1000
  });

  const getPropertyStatistics = useCallback((propertyId: string): PropertyStatistics | null => {
    return statistics[propertyId] || null;
  }, [statistics]);

  const refreshStatistics = useCallback(() => {
    refetch();
  }, [refetch]);

  return {
    statistics,
    getPropertyStatistics,
    loading: isLoading,
    error: error ? String(error) : null,
    refreshStatistics
  };
};
