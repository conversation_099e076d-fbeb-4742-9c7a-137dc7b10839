import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

/**
 * A custom hook for standardized data loading with automatic retries and error handling
 * @param fetchFunction - The function to fetch the data
 * @param dependencies - Dependencies to trigger refetch
 * @param options - Additional options for the hook
 * @returns Object with data, loading state, error, and refetch function
 */
export function useDataLoader<T>(
  fetchFunction: () => Promise<T>,
  dependencies: any[] = [],
  options: {
    initialData?: T;
    retryCount?: number;
    retryDelay?: number;
    onSuccess?: (data: T) => void;
    onError?: (error: Error) => void;
    loadOnMount?: boolean;
    loadingToast?: boolean;
    errorToast?: boolean;
    successToast?: boolean;
    successMessage?: string;
    errorMessage?: string;
  } = {}
) {
  const {
    initialData,
    retryCount = 3,
    retryDelay = 1000,
    onSuccess,
    onError,
    loadOnMount = true,
    loadingToast = false,
    errorToast = true,
    successToast = false,
    successMessage = 'Data loaded successfully',
    errorMessage = 'Failed to load data'
  } = options;

  const [data, setData] = useState<T | undefined>(initialData);
  const [loading, setLoading] = useState(loadOnMount);
  const [error, setError] = useState<Error | null>(null);
  const [retryAttempt, setRetryAttempt] = useState(0);
  const { authState } = useAuth();
  const isMountedRef = useRef(true);
  const isLoadingRef = useRef(false);
  const toastIdRef = useRef<string | number | undefined>(undefined);

  // Function to fetch data with retry logic
  const fetchData = useCallback(async (showToast = false) => {
    // Prevent concurrent fetches
    if (isLoadingRef.current) {
      console.log('[useDataLoader] Already loading, skipping duplicate fetch');
      return;
    }

    // Skip if not authenticated
    if (!authState?.isAuthenticated) {
      console.log('[useDataLoader] Not authenticated, skipping fetch');
      setLoading(false);
      return;
    }

    isLoadingRef.current = true;
    setLoading(true);
    setError(null);

    if (showToast && loadingToast) {
      toastIdRef.current = toast.loading('Loading data...');
    }

    try {
      console.log('[useDataLoader] Fetching data...');
      const result = await fetchFunction();

      if (isMountedRef.current) {
        setData(result);
        setRetryAttempt(0);

        if (onSuccess) {
          onSuccess(result);
        }

        if (showToast && successToast) {
          if (toastIdRef.current) {
            toast.dismiss(toastIdRef.current);
          }
          toast.success(successMessage);
        }
      }
    } catch (err: any) {
      console.error('[useDataLoader] Error fetching data:', err);

      if (isMountedRef.current) {
        setError(err instanceof Error ? err : new Error(err.message || 'Unknown error'));

        // Handle retry logic
        if (retryAttempt < retryCount) {
          const nextRetryDelay = retryDelay * Math.pow(2, retryAttempt);
          console.log(`[useDataLoader] Retrying in ${nextRetryDelay}ms (attempt ${retryAttempt + 1}/${retryCount})`);

          setTimeout(() => {
            if (isMountedRef.current) {
              setRetryAttempt(prev => prev + 1);
              fetchData(false); // Don't show toast on retry
            }
          }, nextRetryDelay);
        } else {
          if (onError) {
            onError(err);
          }

          if (errorToast) {
            if (toastIdRef.current) {
              toast.dismiss(toastIdRef.current);
            }
            toast.error(errorMessage, {
              description: err.message,
              action: {
                label: 'Retry',
                onClick: () => {
                  setRetryAttempt(0);
                  fetchData(true);
                }
              }
            });
          }
        }
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
      isLoadingRef.current = false;

      // Dismiss loading toast if it's still showing
      if (toastIdRef.current && !successToast && !errorToast) {
        toast.dismiss(toastIdRef.current);
        toastIdRef.current = undefined;
      }
    }
  }, [
    authState?.isAuthenticated,
    fetchFunction,
    loadingToast,
    successToast,
    errorToast,
    successMessage,
    errorMessage,
    retryAttempt,
    retryCount,
    retryDelay,
    onSuccess,
    onError
  ]);

  // Refetch function that can be called manually
  const refetch = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);

  // Fetch data on mount and when dependencies change
  useEffect(() => {
    if (loadOnMount || dependencies.length > 0) {
      fetchData(false);
    }

    return () => {
      isMountedRef.current = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchData, ...dependencies]);

  return { data, loading, error, refetch };
}
