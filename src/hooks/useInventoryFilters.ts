
import { useState, useCallback } from 'react';
import { FormattedInventoryItem } from '@/types/inventory';

export interface FilterOptions {
  property: string;
  collection: string;
  stockStatus: string;
}

export const useInventoryFilters = (items: FormattedInventoryItem[]) => {
  const [filters, setFilters] = useState<FilterOptions>({
    property: '',
    collection: '',
    stockStatus: 'all'
  });
  const [searchQuery, setSearchQuery] = useState('');

  // Get unique property names and collections for filter dropdowns
  const properties = [...new Set(items.map(item => item.propertyName).filter(Boolean))].sort();
  const collections = [...new Set(items.map(item => item.collection).filter(Boolean))].sort();

  const filteredItems = useCallback(() => {
    return items.filter(item => {
      // Search filter
      const matchesSearch = !searchQuery ||
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.collection.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (item.propertyName?.toLowerCase() || '').includes(searchQuery.toLowerCase());

      // Property filter - check both propertyId and propertyName
      const matchesProperty = !filters.property ||
        filters.property === 'all' ||
        item.propertyId === filters.property ||
        item.propertyName === filters.property;

      // Collection filter
      const matchesCollection = !filters.collection ||
        filters.collection === 'all' ||
        item.collection === filters.collection;

      // Stock status filter
      const matchesStockStatus = filters.stockStatus === 'all' ||
        (filters.stockStatus === 'low' && item.quantity < item.minQuantity) ||
        (filters.stockStatus === 'ok' && item.quantity >= item.minQuantity);

      return matchesSearch && matchesProperty && matchesCollection && matchesStockStatus;
    });
  }, [items, searchQuery, filters]);

  return {
    filters,
    setFilters,
    searchQuery,
    setSearchQuery,
    filteredItems: filteredItems(),
    properties,
    collections
  };
};
