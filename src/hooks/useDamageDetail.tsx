import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { DamageReport, Property, Provider, DamagePhoto, DamageNote, Invoice } from '@/types/damages';
import { usePermissions } from '@/hooks/usePermissions';
import { useAuth } from '@/contexts/AuthContext';
import { PermissionType } from '@/types/auth';

export const useDamageDetail = (id: string | undefined) => {
  const [report, setReport] = useState<DamageReport | null>(null);
  const [properties, setProperties] = useState<Property[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [property, setProperty] = useState<Property | null>(null);
  const [photos, setPhotos] = useState<DamagePhoto[]>([]);
  const [notes, setNotes] = useState<DamageNote[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [permissionError, setPermissionError] = useState<boolean>(false);
  const { hasPermission, getAccessibleTeams } = usePermissions();
  const { authState } = useAuth();

  useEffect(() => {
    const fetchDamageReport = async () => {
      if (!id) return;

      try {
        setIsLoading(true);
        setPermissionError(false);

        // Get teams the user has access to
        const accessibleTeams = getAccessibleTeams();
        const isSuperAdmin = authState?.profile?.is_super_admin || authState?.profile?.role === 'admin';

        // Query for the damage report
        let query = supabase
          .from('damage_reports')
          .select(`
            *,
            properties:property_id (id, name, address, city, state, team_id),
            maintenance_providers:provider_id (id, name, email, phone, specialty)
          `)
          .eq('id', id);

        // Apply team filtering if user is not a super admin or admin
        if (!isSuperAdmin && !accessibleTeams.includes('*')) {
          query = query.in('properties.team_id', accessibleTeams);
        }

        const { data, error } = await query.single();

        if (error) {
          if (error.code === 'PGRST116') {
            // No rows returned, could be permission issue or non-existent record
            setPermissionError(true);
            toast.error('You do not have permission to view this damage report');
            return;
          }
          throw error;
        }

        if (data) {
          // Additional permission check based on property's team
          if (!isSuperAdmin && data.properties?.team_id) {
            const hasTeamAccess = accessibleTeams.includes(data.properties.team_id);
            const canViewDamages = hasPermission(PermissionType.VIEW_DAMAGE_REPORTS, data.properties.team_id);

            if (!hasTeamAccess || !canViewDamages) {
              setPermissionError(true);
              toast.error('You do not have permission to view this damage report');
              return;
            }
          }

          const reportData: DamageReport = {
            id: data.id,
            title: data.title,
            description: data.description,
            status: data.status,
            property_id: data.property_id,
            property_name: data.properties?.name,
            created_at: data.created_at,
            updated_at: data.updated_at,
            provider_id: data.provider_id,
            provider_name: data.maintenance_providers?.name,
            platform: data.platform
          };

          setReport(reportData);
          setProperty(data.properties || null);
        }

        // Only fetch related data if we have permission
        if (!permissionError) {
          await Promise.all([
            fetchPhotos(id),
            fetchNotes(id),
            fetchInvoices(id)
          ]);
        }
      } catch (error) {
        console.error('Error fetching damage report:', error);
        toast.error('Failed to load damage report details');
      } finally {
        setIsLoading(false);
      }
    };

    const fetchProperties = async () => {
      try {
        // Get teams the user has access to
        const accessibleTeams = getAccessibleTeams();
        const isSuperAdmin = authState?.profile?.is_super_admin || authState?.profile?.role === 'admin';

        let query = supabase
          .from('properties')
          .select('id, name, address, city, state, team_id')
          .order('name');

        // Filter properties by accessible teams if not admin
        if (!isSuperAdmin && !accessibleTeams.includes('*')) {
          query = query.in('team_id', accessibleTeams);
        }

        const { data, error } = await query;

        if (error) throw error;

        setProperties(data || []);
      } catch (error) {
        console.error('Error fetching properties:', error);
      }
    };

    const fetchProviders = async () => {
      try {
        // Get teams the user has access to
        const accessibleTeams = getAccessibleTeams();
        const isSuperAdmin = authState?.profile?.is_super_admin || authState?.profile?.role === 'admin';

        // Use the get_providers RPC function instead of direct query
        // This function handles team access properly
        const { data, error } = await supabase.rpc('get_providers');

        if (error) {
          console.error('Error fetching providers with RPC:', error);
          // Fallback to direct query without team filtering
          const { data: fallbackData, error: fallbackError } = await supabase
            .from('maintenance_providers')
            .select('id, name, email, phone, specialty')
            .order('name');

          if (fallbackError) throw fallbackError;

          setProviders(fallbackData || []);
          return;
        }

        // Format the providers from the RPC function
        const formattedProviders = data.map((provider: any) => ({
          id: provider.id,
          name: provider.name || '',
          email: provider.email || '',
          phone: provider.phone || '',
          specialty: provider.specialty || ''
        }));

        setProviders(formattedProviders || []);
      } catch (error) {
        console.error('Error fetching providers:', error);
      }
    };

    fetchDamageReport();
    fetchProperties();
    fetchProviders();
  }, [id, authState?.profile, hasPermission, getAccessibleTeams]);

  const fetchPhotos = async (reportId: string) => {
    try {
      console.log('Fetching photos for report:', reportId);

      // Use the improved RPC function
      const { data: rpcData, error: rpcError } = await supabase.rpc(
        'get_damage_photos',
        { p_damage_report_id: reportId }
      );

      if (rpcError) {
        console.error('Error fetching photos with RPC:', rpcError);
        // Fall back to direct query with public URLs
        const { data, error } = await supabase
          .from('damage_photos')
          .select('*')
          .eq('damage_report_id', reportId)
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching photos:', error);
          return;
        }

        console.log(`Fetched ${data?.length || 0} raw photos`);

        // Create public URLs for all photos
        const photosWithUrls = data.map((photo) => {
          if (photo.file_path) {
            return {
              ...photo,
              url: `https://pwaeknalhosfwuxkpaet.supabase.co/storage/v1/object/public/damage-photos/${photo.file_path}`
            };
          }
          return {
            ...photo,
            url: null
          };
        });

        setPhotos(photosWithUrls);
        console.log(`Processed ${photosWithUrls.length} photos with URLs`);
      } else {
        // Format the photos with URLs from RPC
        const photosWithUrls = rpcData.map((photo: any) => ({
          ...photo,
          url: photo.public_url
        }));

        setPhotos(photosWithUrls);
        console.log(`Processed ${photosWithUrls.length} photos with URLs from RPC`);
      }
    } catch (error) {
      console.error('Error processing photos:', error);
      toast.error('Failed to load photos');
    }
  };

  const fetchNotes = async (reportId: string) => {
    try {
      console.log('Fetching notes for report:', reportId);

      // Use the new RPC function to get notes, including non-private ones
      const { data: rpcData, error: rpcError } = await supabase.rpc(
        'get_damage_notes',
        {
          p_damage_report_id: reportId,
          p_include_private: false // Only include non-private notes and user's own private notes
        }
      );

      if (rpcError) {
        console.error('Error fetching notes with RPC:', rpcError);
        // Fall back to direct query
        const { data, error } = await supabase
          .from('damage_notes')
          .select('*')
          .eq('damage_report_id', reportId)
          .or(`private.eq.false,user_id.eq.${authState.user?.id}`)
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching notes:', error);
          return;
        }

        setNotes(data || []);
        console.log(`Fetched ${data?.length || 0} notes with direct query`);
      } else {
        setNotes(rpcData || []);
        console.log(`Fetched ${rpcData?.length || 0} notes with RPC`);
      }
    } catch (error) {
      console.error('Error fetching notes:', error);
    }
  };

  const fetchInvoices = async (reportId: string) => {
    try {
      const { data, error } = await supabase
        .from('damage_invoices')
        .select('*, maintenance_providers(*)')
        .eq('damage_report_id', reportId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching invoices:', error);
        return;
      }

      const invoicesWithItems = await Promise.all(
        (data || []).map(async (invoice) => {
          const { data: itemsData, error: itemsError } = await supabase
            .from('invoice_items')
            .select('*')
            .eq('invoice_id', invoice.id);

          if (itemsError) {
            console.error(`Error fetching items for invoice ${invoice.id}:`, itemsError);
            return { ...invoice, items: [] };
          }

          return { ...invoice, items: itemsData || [] };
        })
      );

      setInvoices(invoicesWithItems);
      console.log(`Fetched ${invoicesWithItems.length} invoices with items`);
    } catch (error) {
      console.error('Error fetching invoices:', error);
    }
  };

  const handleUpdateReport = async (updatedData: Partial<DamageReport>) => {
    if (!id) return;

    try {
      console.log("DamageDetail - Updating report with data:", updatedData);

      const dataToUpdate = {
        title: updatedData.title,
        description: updatedData.description,
        status: updatedData.status,
        property_id: updatedData.property_id,
        provider_id: updatedData.provider_id === undefined ? null : updatedData.provider_id,
        platform: updatedData.platform === undefined ? null : updatedData.platform
      };

      const { error } = await supabase
        .from('damage_reports')
        .update(dataToUpdate)
        .eq('id', id);

      if (error) throw error;

      const propertyName = properties.find(p => p.id === updatedData.property_id)?.name;
      const providerName = updatedData.provider_id
        ? providers.find(p => p.id === updatedData.provider_id)?.name
        : undefined;

      setReport(prev => prev ? {
        ...prev,
        ...updatedData,
        property_name: propertyName || prev.property_name,
        provider_name: providerName
      } : null);

      toast.success('Damage report updated successfully');
    } catch (error) {
      console.error('Error updating damage report:', error);
      toast.error('Failed to update damage report');
      throw error;
    }
  };

  const refreshPhotos = async () => {
    if (!id) return;
    console.log("Refreshing photos for damage report:", id);
    await fetchPhotos(id);
    return photos;
  };

  const refreshNotes = () => fetchNotes(id || '');
  const refreshInvoices = () => fetchInvoices(id || '');

  return {
    report,
    properties,
    providers,
    isLoading,
    property,
    photos,
    notes,
    invoices,
    handleUpdateReport,
    refreshPhotos,
    refreshNotes,
    refreshInvoices
  };
};
