import { useAuth } from '@/contexts/AuthContext';
import { PermissionType, type UserPermission } from '@/types/auth';
import { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

export const usePermissions = () => {
  const { authState } = useAuth();
  const [permissions, setPermissions] = useState<UserPermission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userTeams, setUserTeams] = useState<string[]>([]);

  useEffect(() => {
    const fetchPermissions = async () => {
      if (!authState.user?.id) {
        setPermissions([]);
        setUserTeams([]);
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        // Check if user is super admin first
        if (authState?.profile?.is_super_admin || authState?.user?.user_metadata?.is_super_admin) {
          console.log('[usePermissions] Super admin detected, skipping permission fetch');
          setPermissions([]);
          setUserTeams([]);
          setLoading(false);
          return;
        }

        console.log('[usePermissions] Fetching team memberships for user:', authState.user.id);

        // Add retry logic for team memberships fetch
        let teamMemberships: any[] | null = null;
        let teamError: any = null;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            const result = await supabase
              .from('team_members')
              .select('team_id')
              .eq('user_id', authState.user.id)
              .eq('status', 'active');

            teamMemberships = result.data;
            teamError = result.error;
            break;
          } catch (fetchError: any) {
            retryCount++;
            console.warn(`[usePermissions] Team memberships fetch attempt ${retryCount} failed:`, fetchError);

            if (retryCount >= maxRetries) {
              teamError = fetchError;
              break;
            }

            // Wait before retry (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
          }
        }

        if (teamError) {
          console.error('[usePermissions] Error fetching team memberships:', teamError);

          // Check if this is an authentication error
          if (teamError.message?.includes('JWT') || teamError.message?.includes('auth') || teamError.code === 'PGRST301') {
            console.warn('[usePermissions] Authentication error detected, user may need to re-login');
            setError('Authentication session expired. Please refresh the page.');
          } else {
            setError(`Failed to fetch team memberships: ${teamError.message || 'Network error'}`);
          }
          setUserTeams([]);
        } else if (teamMemberships && teamMemberships.length > 0) {
          const teams = teamMemberships.map((tm: { team_id: string }) => tm.team_id);
          console.log('[usePermissions] User is a member of teams:', teams);
          setUserTeams(teams);
        } else {
          console.log('[usePermissions] User is not a member of any teams');
          setUserTeams([]);
        }

        // Fetch user permissions with retry logic
        let permissionsData: any[] | null = null;
        let permissionsError: any = null;
        retryCount = 0;

        while (retryCount < maxRetries) {
          try {
            const result = await supabase
              .from('user_permissions')
              .select('*')
              .eq('user_id', authState.user.id)
              .eq('enabled', true);

            permissionsData = result.data;
            permissionsError = result.error;
            break;
          } catch (fetchError: any) {
            retryCount++;
            console.warn(`[usePermissions] User permissions fetch attempt ${retryCount} failed:`, fetchError);

            if (retryCount >= maxRetries) {
              permissionsError = fetchError;
              break;
            }

            // Wait before retry (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));
          }
        }

        if (permissionsError) {
          console.error('[usePermissions] Error fetching user permissions:', permissionsError);

          // Check if this is an authentication error
          if (permissionsError.message?.includes('JWT') || permissionsError.message?.includes('auth') || permissionsError.code === 'PGRST301') {
            console.warn('[usePermissions] Authentication error detected, user may need to re-login');
            setError('Authentication session expired. Please refresh the page.');
          } else {
            setError(`Failed to fetch permissions: ${permissionsError.message || 'Network error'}`);
          }
          setPermissions([]);
        } else {
          setPermissions(permissionsData || []);
        }
      } catch (error: any) {
        console.error('[usePermissions] Unexpected error in fetchPermissions:', error);
        setError(`Unexpected error: ${error.message || 'Unknown error'}`);
        setPermissions([]);
        setUserTeams([]);
      } finally {
        setLoading(false);
      }
    };

    fetchPermissions();
  }, [authState.user?.id, authState?.profile?.is_super_admin, authState?.user?.user_metadata?.is_super_admin]);


  const hasPermission = (permission: PermissionType, teamId?: string): boolean => {
    // REBUILT: Use user metadata instead of profile
    const userRole = authState?.user?.user_metadata?.role || 'property_manager';
    const isSuper = authState?.user?.user_metadata?.is_super_admin === true;

    // Super admins have all permissions
    if (isSuper) {
      console.log(`[usePermissions] Super admin has permission: ${permission}`);
      return true;
    }

    // Admins have all permissions
    if (userRole === 'admin') {
      console.log(`[usePermissions] Admin has permission: ${permission}`);
      return true;
    }

    // Property managers have full permissions for their properties
    if (authState?.profile?.role === 'property_manager') {
      console.log(`[usePermissions] Checking property manager permission: ${permission}`);

      // Property managers should always have MANAGE_PROPERTIES permission
      if (permission === PermissionType.MANAGE_PROPERTIES) {
        console.log('[usePermissions] Property manager has MANAGE_PROPERTIES permission');
        return true;
      }

      // If we're checking MANAGE_STAFF for a specific team
      if (permission === PermissionType.MANAGE_STAFF && teamId) {
        // Allow if they own the team
        const managerHasTeamAccess = userTeams.includes(teamId);
        if (managerHasTeamAccess) {
          console.log(`[usePermissions] Property manager has team access for ${teamId} and permission: ${permission}`);
          return true;
        }
      }

      // Existing logic for other permissions remains the same
      if (!teamId) {
        const globalPropertyManagerPermissions = [
          PermissionType.MANAGE_PROPERTIES,
          PermissionType.MANAGE_MAINTENANCE,
          PermissionType.SUBMIT_DAMAGE_REPORTS,
          PermissionType.VIEW_MAINTENANCE,
          PermissionType.VIEW_DAMAGE_REPORTS,  // Add VIEW_DAMAGE_REPORTS to global permissions
          PermissionType.MANAGE_DAMAGE_REPORTS  // Add MANAGE_DAMAGE_REPORTS to global permissions
        ];

        if (globalPropertyManagerPermissions.includes(permission)) {
          console.log(`[usePermissions] Property manager has global permission: ${permission}`);
          return true;
        }

        return permissions.some(p => p.permission === permission && p.enabled && !p.team_id);
      }

      const isOwnedTeam = userTeams.includes(teamId);

      if (isOwnedTeam) {
        const defaultPropertyManagerPermissions = [
          PermissionType.MANAGE_PROPERTIES,
          PermissionType.MANAGE_SERVICE_PROVIDERS,
          PermissionType.MANAGE_MAINTENANCE,
          PermissionType.VIEW_MAINTENANCE,
          PermissionType.MANAGE_DAMAGE_REPORTS,
          PermissionType.VIEW_DAMAGE_REPORTS
        ];

        if (defaultPropertyManagerPermissions.includes(permission)) {
          console.log(`[usePermissions] Property manager has team permission: ${permission} for team: ${teamId}`);
          return true;
        }
      }
    }

    // For staff and service providers (team members) - restricted permissions
    if (authState?.profile?.role === 'staff' || authState?.profile?.role === 'service_provider') {
      console.log(`[usePermissions] Checking ${authState?.profile?.role} permission: ${permission}`);
      console.log(`[usePermissions] User teams:`, userTeams);
      console.log(`[usePermissions] User permissions:`, permissions);

      // Team member default permissions - restrict to only what's specified in requirements
      const teamMemberDefaultPermissions = [
        // Use the permissions that exist in the database
        PermissionType.VIEW_MAINTENANCE,            // View maintenance items
        PermissionType.MANAGE_MAINTENANCE,          // Manage maintenance items
        PermissionType.SUBMIT_DAMAGE_REPORTS,       // Submit damage reports
        PermissionType.VIEW_DAMAGE_REPORTS,         // View damage reports
        PermissionType.MANAGE_DAMAGE_REPORTS,       // Manage damage reports
        PermissionType.VIEW_INVENTORY,              // View inventory
        PermissionType.MANAGE_INVENTORY,            // Manage inventory
        PermissionType.VIEW_PURCHASE_ORDERS,        // View purchase orders
        PermissionType.MANAGE_PURCHASE_ORDERS,      // Manage purchase orders
        PermissionType.VIEW_TEAM,                   // View team
        PermissionType.MANAGE_TEAM,                 // Manage team
        PermissionType.MANAGE_PROPERTIES            // Add MANAGE_PROPERTIES for service providers
      ];

      // Only allow default permissions if they're a member of the specified team
      const isTeamMember = teamId && userTeams.includes(teamId);

      if (isTeamMember && teamMemberDefaultPermissions.includes(permission)) {
        console.log(`[usePermissions] Team member has permission: ${permission} for team: ${teamId}`);
        return true;
      }

      // If no specific team is provided, but it's a basic permission, allow it
      if (!teamId) {
        const basicPermissions = [
          PermissionType.MANAGE_MAINTENANCE,
          PermissionType.SUBMIT_DAMAGE_REPORTS,
          PermissionType.VIEW_DAMAGE_REPORTS,  // Add VIEW_DAMAGE_REPORTS to basic permissions
          PermissionType.VIEW_MAINTENANCE      // Add VIEW_MAINTENANCE to basic permissions
        ];

        if (basicPermissions.includes(permission)) {
          console.log(`[usePermissions] Team member has basic permission: ${permission}`);
          return true;
        }
      }

      // Special case for VIEW_DAMAGE_REPORTS - if user is a team member, they should be able to view damage reports
      // for any team they're a member of, even if no specific teamId is provided
      if (permission === PermissionType.VIEW_DAMAGE_REPORTS && userTeams.length > 0) {
        console.log(`[usePermissions] Team member has VIEW_DAMAGE_REPORTS permission for teams: ${userTeams.join(', ')}`);
        return true;
      }

      // Special case for VIEW_MAINTENANCE - if user is a team member, they should be able to view maintenance tasks
      // for any team they're a member of, even if no specific teamId is provided
      if (permission === PermissionType.VIEW_MAINTENANCE && userTeams.length > 0) {
        console.log(`[usePermissions] Team member has VIEW_MAINTENANCE permission for teams: ${userTeams.join(', ')}`);
        return true;
      }

      // Special case for MANAGE_PROPERTIES - if user is a service provider, they should be able to see properties
      if (permission === PermissionType.MANAGE_PROPERTIES && userTeams.length > 0) {
        console.log(`[usePermissions] Service provider has MANAGE_PROPERTIES permission for teams: ${userTeams.join(', ')}`);
        return true;
      }

      // Special case for VIEW_INVENTORY - if user is a service provider, they should be able to see inventory
      if (permission === PermissionType.VIEW_INVENTORY && userTeams.length > 0) {
        console.log(`[usePermissions] Service provider has VIEW_INVENTORY permission for teams: ${userTeams.join(', ')}`);
        return true;
      }

      // Special case for VIEW_PURCHASE_ORDERS - if user is a service provider, they should be able to see purchase orders
      if (permission === PermissionType.VIEW_PURCHASE_ORDERS && userTeams.length > 0) {
        console.log(`[usePermissions] Service provider has VIEW_PURCHASE_ORDERS permission for teams: ${userTeams.join(', ')}`);
        return true;
      }
    }

    // Check explicitly assigned permissions for all roles
    // Check explicitly assigned permissions for the specific team
    if (teamId) {
      const hasExplicitPermission = permissions.some(
        p => p.permission === permission &&
             p.team_id === teamId &&
             p.enabled
      );
      return hasExplicitPermission;
    }


    // If no team specified, check if they have the permission for any team
    return permissions.some(
      p => p.permission === permission && p.enabled
    );
  };

  const getAccessibleTeams = (): string[] => {
    // REBUILT: Use user metadata instead of profile
    const userRole = authState?.user?.user_metadata?.role || 'property_manager';
    const isSuper = authState?.user?.user_metadata?.is_super_admin === true;

    // Get the unique team IDs this user has permissions for
    if (isSuper || userRole === 'admin') {
      // Admins can access all teams (this will be handled separately in queries)
      console.log('[usePermissions] Admin/Super admin has access to all teams');
      return ['*'];
    }

    // If they're a property manager, only return teams they own or are members of
    if (userRole === 'property_manager') {
      console.log('[usePermissions] Property manager has access to teams:', userTeams);
      return [...userTeams];
    }

    // For other roles (staff, service providers), they only have access to teams
    // they're explicitly members of
    console.log('[usePermissions] User has access to teams as member:', userTeams);
    return userTeams;
  };

  // Helper functions for role checks
  const isAdmin = () => {
    // REBUILT: Use user metadata instead of profile
    const userRole = authState?.user?.user_metadata?.role || 'property_manager';
    const isSuper = authState?.user?.user_metadata?.is_super_admin === true;
    return isSuper || userRole === 'admin';
  };

  const isOwner = () => {
    // REBUILT: Use user metadata instead of profile
    const userRole = authState?.user?.user_metadata?.role || 'property_manager';
    return userRole === 'property_manager';
  };

  // Helper function to get accessible property IDs
  const getAccessiblePropertyIds = async () => {
    if (isAdmin()) {
      // Admins can access all properties
      console.log('[usePermissions] Admin can access all properties');
      return [];
    }

    // Get properties for teams the user is a member of
    if (userTeams.length > 0) {
      console.log('[usePermissions] Getting properties for teams:', userTeams);
      const { data } = await supabase
        .from('properties')
        .select('id')
        .in('team_id', userTeams);

      return data ? data.map((p: { id: string }) => p.id) : [];
    }

    return [];
  };

  return {
    permissions,
    hasPermission,
    getAccessibleTeams,
    userTeams,
    loading,
    error,
    isAdmin,
    isOwner,
    getAccessiblePropertyIds
  };
};
