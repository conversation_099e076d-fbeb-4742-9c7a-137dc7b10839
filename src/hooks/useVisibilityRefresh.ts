import { useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * This hook no longer uses visibility change events as they cause data to disappear.
 * Instead, it provides a function to manually refresh data when needed.
 */
export const useVisibilityRefresh = () => {
  const queryClient = useQueryClient();

  const refreshData = useCallback(() => {
    // Invalidate queries for relevant data
    const queryKeys = [
      'properties',
      'maintenanceTasks',
      'inventoryItems',
      'damageReports',
      'purchaseOrders',
      'teamsV2',
      'teamMembersV2',
      'teamPropertiesV2',
      'teamInvitationsV2',
      'automationRulesV2',
      'maintenanceTasksV2',
    ];

    console.log('[useVisibilityRefresh] Manually refreshing data for keys:', queryKeys);

    queryKeys.forEach((key) => {
      queryClient.invalidateQueries({
        queryKey: [key],
        refetchType: 'active',
        exact: false,
      });
    });
  }, [queryClient]);

  // Return the refresh function for manual use
  return { refreshData };
};