
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

export function useImageUpload() {
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const { authState } = useAuth();

  // Function to handle image upload with timeout control
  const handleImageUpload = async (file: File, timeoutMs = 15000): Promise<string | null> => {
    if (!authState.user) {
      toast.error('You must be logged in to upload images');
      return null;
    }

    // Create an AbortController for the fetch operation
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
      setIsUploading(false);
      setUploadProgress(0);
      toast.error('Image upload timed out. Please try again with a smaller image.');
    }, timeoutMs);

    try {
      setIsUploading(true);
      setUploadProgress(10); // Start progress indication

      // Get bucket and folder information first
      const bucketName = window.INVENTORY_BUCKET_NAME || 'inventory';
      const folderPath = window.INVENTORY_FOLDER_PATH || 'inventory-images';

      // Generate a unique file path
      const timestamp = new Date().getTime();
      const randomString = Math.random().toString(36).substring(2, 10);
      const fileName = `${timestamp}-${randomString}-${file.name.replace(/\s+/g, '_')}`;
      const filePath = `${folderPath}/${fileName}`;

      // Show intermediate progress
      setUploadProgress(30);

      console.log(`Using storage bucket: ${bucketName}, folder: ${folderPath}`);

      // Try to list the bucket to check if we have access
      const { error: listError } = await supabase.storage
        .from(bucketName)
        .list();

      if (listError) {
        console.error('Error accessing storage bucket:', listError);
        toast.error('Storage access error. Using original image URL instead.');

        // Create an object URL as a fallback
        const objectUrl = URL.createObjectURL(file);
        console.log('Using object URL as fallback:', objectUrl);
        return objectUrl;
      }

      console.log('Uploading file to path:', filePath);

      // Upload the file to Supabase Storage
      const { error } = await supabase.storage
        .from(bucketName)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true,
          contentType: file.type
        });

      // Clear the timeout since upload completed or errored
      clearTimeout(timeoutId);

      if (error) {
        console.error('Error uploading file:', error);

        // Check for specific error types
        if (error.message.includes('folderPath')) {
          console.error('FolderPath error detected. This is likely a storage bucket configuration issue.');
          toast.error('Storage configuration error. Using local image instead.');
        } else {
          toast.error(`Upload failed: ${error.message}`);
        }

        // Return the original file URL as a fallback
        try {
          const objectUrl = URL.createObjectURL(file);
          console.log('Using object URL as fallback:', objectUrl);
          return objectUrl;
        } catch (urlError) {
          console.error('Error creating object URL:', urlError);
          // Last resort fallback - return a placeholder
          return 'https://placehold.co/400?text=Image+Unavailable';
        }
      }

      setUploadProgress(70);

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filePath);

      console.log('Image uploaded successfully:', publicUrl);
      setUploadProgress(100);
      toast.success('Image uploaded successfully');

      return publicUrl;
    } catch (error: any) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        console.error('Image upload timed out');
        toast.error('Image upload timed out. Please try again with a smaller image.');
      } else if (error.message && error.message.includes('folderPath')) {
        console.error('FolderPath error detected:', error);
        toast.error('Storage configuration error. Using local image instead.');
      } else {
        console.error('Error in image upload:', error);
        toast.error(`Upload error: ${error.message || 'Unknown error'}`);
      }

      // If we have a file, create an object URL as a fallback
      if (file) {
        try {
          const objectUrl = URL.createObjectURL(file);
          console.log('Using object URL as fallback after error:', objectUrl);
          return objectUrl;
        } catch (urlError) {
          console.error('Error creating object URL:', urlError);
          // Last resort fallback
          return 'https://placehold.co/400?text=Image+Unavailable';
        }
      }

      // Last resort fallback
      return 'https://placehold.co/400?text=Image+Unavailable';
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  return {
    handleImageUpload,
    uploadProgress,
    isUploading
  };
}
