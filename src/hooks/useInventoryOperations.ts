
import { useState } from 'react';
import { useInventoryQueryV2 } from './useInventoryQueryV2';
import { FormattedInventoryItem } from '@/types/inventory';
import { useAuth } from '@/contexts/AuthContext';
import { useDialog } from './useDialog';
import { toast } from 'sonner';

export const useInventoryOperations = () => {
  const [selectedItem, setSelectedItem] = useState<Partial<FormattedInventoryItem> | null>(null);
  const { addInventoryItem, updateInventoryItem, deleteInventoryItem, bulkAddInventoryItems } = useInventoryQueryV2();
  const { authState } = useAuth();

  // Dialog controls
  const itemDialog = useDialog();
  const purchaseOrderDialog = useDialog();
  const bulkImportDialog = useDialog();

  // Handle clicking an item to edit
  const handleItemClick = (item: FormattedInventoryItem) => {
    // Map from database format to form format if needed
    const formattedItem: FormattedInventoryItem = {
      ...item,
      propertyId: item.propertyId,
      minQuantity: item.minQuantity,
      amazonUrl: item.amazonUrl,
      walmartUrl: item.walmartUrl,
      imageUrl: item.imageUrl
    };

    setSelectedItem(formattedItem);
    itemDialog.onOpen();
  };

  // Handle adding a new item
  const handleAddItem = () => {
    setSelectedItem({
      name: '',
      propertyId: '',
      collection: '',
      quantity: 0,
      minQuantity: 1,
      price: undefined,
      amazonUrl: '',
      walmartUrl: '',
      imageUrl: ''
    });
    itemDialog.onOpen();
  };

  // Handle creating a purchase order
  const handleCreatePurchaseOrder = () => {
    purchaseOrderDialog.onOpen();
  };

  // Handle bulk import
  const handleBulkImport = () => {
    bulkImportDialog.onOpen();
  };

  // Handle saving an item (new or edit)
  const handleSaveItem = async (item: FormattedInventoryItem) => {
    if (!authState.user) {
      toast.error('You must be logged in to save inventory items');
      return;
    }

    const saveItem = {
      id: item.id,
      name: item.name,
      propertyId: item.propertyId,
      collection: item.collection,
      quantity: item.quantity,
      minQuantity: item.minQuantity,
      price: item.price,
      amazonUrl: item.amazonUrl,
      walmartUrl: item.walmartUrl,
      imageUrl: item.imageUrl,
      hasProcessedImage: item.hasProcessedImage // Flag to track if image is already processed
    };

    try {
      if (item.id) {
        // Update existing item
        const success = await updateInventoryItem(item.id, saveItem);
        if (success) {
          toast.success('Item updated successfully');
          itemDialog.onClose();
          setSelectedItem(null);
        } else {
          toast.error('Failed to update item');
        }
      } else {
        // Check if the image URL is an object URL (blob:)
        const isObjectUrl = saveItem.imageUrl?.startsWith('blob:');

        // If it's an object URL, we need to handle it differently
        // For now, we'll just log it and use it as is
        if (isObjectUrl) {
          console.log('Using object URL for image:', saveItem.imageUrl);
          toast.info('Using temporary image URL. The image may not persist after page refresh.');
        }

        // Add new item
        const newItem = {
          name: saveItem.name,
          propertyId: saveItem.propertyId,
          collection: saveItem.collection,
          quantity: saveItem.quantity,
          minQuantity: saveItem.minQuantity,
          price: saveItem.price,
          amazonUrl: saveItem.amazonUrl,
          walmartUrl: saveItem.walmartUrl,
          imageUrl: saveItem.imageUrl,
          hasProcessedImage: !isObjectUrl && saveItem.hasProcessedImage
        };

        const success = await addInventoryItem(newItem);
        if (success) {
          toast.success('Item added successfully');
          itemDialog.onClose();
          setSelectedItem(null);
        } else {
          toast.error('Failed to add item');
        }
      }
    } catch (error) {
      console.error('Error saving item:', error);
      toast.error('Failed to save item');
    }
  };

  // Handle deleting one or more items
  const handleDeleteItems = async (itemIds: string[]) => {
    if (!itemIds || itemIds.length === 0) {
      toast.error('No items selected for deletion');
      return;
    }

    if (itemIds.length === 1) {
      // Show confirmation dialog for single item
      const confirmed = window.confirm('Are you sure you want to delete this item? This action cannot be undone.');
      if (!confirmed) return;

      try {
        console.log(`[handleDeleteItems] Deleting single item: ${itemIds[0]}`);
        const success = await deleteInventoryItem(itemIds[0]);
        if (success) {
          toast.success('Item deleted successfully');
        } else {
          toast.error('Failed to delete item. Please try again.');
        }
      } catch (error) {
        console.error('Error deleting item:', error);
        toast.error(`Failed to delete item: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    } else if (itemIds.length > 1) {
      // Show confirmation dialog for multiple items
      const confirmed = window.confirm(`Are you sure you want to delete ${itemIds.length} items? This action cannot be undone.`);
      if (!confirmed) return;

      // Delete multiple items sequentially
      let deletedCount = 0;
      let failedCount = 0;
      const totalCount = itemIds.length;
      const failedItems: string[] = [];

      // Create a loading toast that will be updated
      const loadingToast = toast.loading(`Deleting ${totalCount} items...`);

      console.log(`[handleDeleteItems] Starting bulk delete of ${totalCount} items`);

      // Process deletions sequentially to avoid overwhelming the database
      const deleteNext = async (index: number) => {
        if (index >= itemIds.length) {
          toast.dismiss(loadingToast);

          if (deletedCount === totalCount) {
            toast.success(`Successfully deleted all ${totalCount} items`);
          } else if (deletedCount > 0) {
            toast.success(`Successfully deleted ${deletedCount} of ${totalCount} items`);
            if (failedCount > 0) {
              toast.error(`Failed to delete ${failedCount} items`);
            }
          } else {
            toast.error('Failed to delete any items');
          }

          console.log(`[handleDeleteItems] Bulk delete completed: ${deletedCount} succeeded, ${failedCount} failed`);
          return;
        }

        try {
          console.log(`[handleDeleteItems] Deleting item ${index + 1}/${totalCount}: ${itemIds[index]}`);
          const success = await deleteInventoryItem(itemIds[index]);
          if (success) {
            deletedCount++;
          } else {
            failedCount++;
            failedItems.push(itemIds[index]);
            console.error(`Failed to delete item ${itemIds[index]}`);
          }
        } catch (error) {
          failedCount++;
          failedItems.push(itemIds[index]);
          console.error(`Failed to delete item ${itemIds[index]}:`, error);
        }

        // Update loading toast with progress
        toast.loading(`Deleting items... ${index + 1}/${totalCount}`, { id: loadingToast });

        // Continue with next item after a small delay to prevent overwhelming the server
        setTimeout(() => deleteNext(index + 1), 100);
      };

      deleteNext(0);
    }
  };

  // Handle bulk saving items
  const handleBulkSaveItems = async (items: Partial<FormattedInventoryItem>[]) => {
    if (!authState.user) {
      toast.error('You must be logged in to import items');
      return;
    }

    // Format items for bulk import
    const formattedItems = items.map(item => ({
      name: item.name || 'Unnamed Product',
      propertyId: item.propertyId || '',
      collection: item.collection || 'Other',
      quantity: item.quantity || 1,
      minQuantity: item.minQuantity || 0,
      price: item.price,
      amazonUrl: item.amazonUrl || '',
      walmartUrl: item.walmartUrl || '',
      imageUrl: item.imageUrl || '',
      hasProcessedImage: item.hasProcessedImage || false
    }));

    try {
      const success = await bulkAddInventoryItems(formattedItems as FormattedInventoryItem[]);
      if (success) {
        toast.success('Items imported successfully');
        bulkImportDialog.onClose();
      } else {
        toast.error('Failed to import items');
      }
    } catch (error) {
      console.error('Error importing items:', error);
      toast.error('Failed to import items');
    }
  };

  return {
    selectedItem,
    itemDialog,
    purchaseOrderDialog,
    bulkImportDialog,
    handleItemClick,
    handleAddItem,
    handleCreatePurchaseOrder,
    handleBulkImport,
    handleSaveItem,
    handleDeleteItems,
    handleBulkSaveItems,
  };
};
