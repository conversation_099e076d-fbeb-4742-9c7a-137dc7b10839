import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import {
  MaintenanceTask,
  MaintenanceStatus,
  MaintenanceTaskFilters, // Corrected import path
} from '@/components/maintenance/types';
import { useAuth } from '@/contexts/AuthContext';
import { useImpersonation } from '@/contexts/ImpersonationContext';
import { toast } from 'sonner';
import { clearCacheAndRefresh, retrySupabaseFetch } from '@/utils/cacheUtils';

export const useMaintenanceTasksRPC = (initialFilters: Partial<MaintenanceTaskFilters> = {}) => {
  const [tasks, setTasks] = useState<MaintenanceTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { authState } = useAuth();
  const userId = authState?.user?.id;

  // Use the impersonation context
  const { isImpersonating, applyImpersonationFilter } = useImpersonation();

  // Track if we've already fetched tasks to prevent duplicate fetches
  const hasFetchedRef = useRef(false);

  const mapDbStatusToMaintenanceStatus = (dbStatus: string): MaintenanceStatus => {
    switch (dbStatus) {
      case 'open': return 'new';
      case 'in_progress': return 'in_progress';
      case 'completed': return 'completed';
      case 'assigned': return 'assigned';
      case 'cancelled': return 'cancelled';
      case 'accepted': return 'accepted';
      case 'rejected': return 'rejected';
      default: return 'new'; // Default fallback
    }
  };

  const mapMaintenanceStatusToDbStatus = (status: MaintenanceStatus): string => {
    switch (status) {
      case 'new': return 'open';
      case 'in_progress': return 'in_progress';
      case 'completed': return 'completed';
      case 'assigned': return 'assigned';
      case 'cancelled': return 'cancelled';
      case 'accepted': return 'accepted';
      case 'rejected': return 'rejected';
      default: return 'open';
    }
  };

  const fetchTasks = useCallback(async () => {
    console.log('[useMaintenanceTasksRPC] fetchTasks called');

    // Skip if no user ID
    if (!userId) {
      console.log('[useMaintenanceTasksRPC] No user ID, skipping fetch');
      return;
    }

    console.log('[useMaintenanceTasksRPC] Proceeding with fetch');

    try {
      // Store current tasks to prevent flash/disappear
      const currentTasks = [...tasks];
      console.log('[useMaintenanceTasksRPC] Current tasks before loading:', currentTasks.length);

      setLoading(true);
      setError(null);

      // If we have existing tasks, don't clear them during loading
      if (currentTasks.length > 0) {
        console.log('[useMaintenanceTasksRPC] Keeping existing tasks during loading');
      }

      // Handle impersonation first
      if (isImpersonating) {
        console.log('[useMaintenanceTasksRPC] Impersonating user, showing only their tasks');
        const { data, error } = await applyImpersonationFilter(supabase.from('maintenance_tasks').select('*'));
        if (error) throw error;

        // Format tasks for the UI
        const formattedTasks: MaintenanceTask[] = (data || []).map((task: any) => ({
          id: task.id,
          title: task.title,
          description: task.description || '',
          propertyId: task.property_id || '',
          propertyName: task.property_name || '',
          status: mapDbStatusToMaintenanceStatus(task.status),
          severity: task.severity as MaintenanceTask['severity'],
          dueDate: task.due_date || 'No due date',
          assignedTo: task.assigned_to || undefined,
          createdAt: task.created_at,
          providerId: task.provider_id || undefined,
          providerEmail: task.provider_email || undefined
        }));

        setTasks(formattedTasks);
        setLoading(false);
        hasFetchedRef.current = true;
        return;
      }

      // Try multiple approaches to get tasks, starting with the most reliable
      let tasksData: any[] | null = null;
      let fetchError: Error | null = null;

      // Approach 1: Use our RPC function to get tasks
      try {
        console.log('[useMaintenanceTasksRPC] Using get_maintenance_tasks_for_user RPC function with retry');

        // Use our retry mechanism for Supabase fetch operations
        const { data: rpcTasks, error: rpcError } = await retrySupabaseFetch(async () => {
          return await supabase.rpc('get_maintenance_tasks_for_user', {
            p_user_id: userId
          });
        });

        if (rpcError) {
          console.error('[useMaintenanceTasksRPC] Error with get_maintenance_tasks_for_user:', rpcError);
          fetchError = rpcError;
        } else if (rpcTasks && rpcTasks.length > 0) {
          console.log(`[useMaintenanceTasksRPC] Found ${rpcTasks.length} tasks with get_maintenance_tasks_for_user`);
          tasksData = rpcTasks;
        } else {
          console.log('[useMaintenanceTasksRPC] RPC function returned no tasks, trying next approach');
        }
      } catch (rpcErr) {
        console.error('[useMaintenanceTasksRPC] Exception with RPC function:', rpcErr);
        fetchError = rpcErr instanceof Error ? rpcErr : new Error(String(rpcErr));
      }

      // Approach 2: If RPC failed, try direct query with proper filters
      if (!tasksData) {
        try {
          console.log('[useMaintenanceTasksRPC] Trying direct query approach');

          // Get user role to determine the appropriate query
          const { data: profileData } = await supabase
            .from('profiles')
            .select('role, is_super_admin')
            .eq('id', userId)
            .single();

          let queryBuilder = supabase.from('maintenance_tasks').select('*');

          // Apply different filters based on user role
          if (profileData?.is_super_admin || profileData?.role === 'super_admin' || profileData?.role === 'admin') {
            // Admins can see all tasks, no filter needed
            console.log('[useMaintenanceTasksRPC] User is admin, fetching all tasks');
          } else if (profileData?.role === 'property_manager') {
            console.log('[useMaintenanceTasksRPC] User is property manager, fetching owned and team tasks');
            // Get user's team IDs
            const { data: teamData } = await supabase
              .from('teams')
              .select('id')
              .eq('owner_id', userId);

            const teamIds = teamData ? teamData.map((t: { id: string }) => t.id) : [];

            if (teamIds.length > 0) {
              queryBuilder = queryBuilder.or(`user_id.eq.${userId},team_id.in.(${teamIds.join(',')})`);
            } else {
              queryBuilder = queryBuilder.eq('user_id', userId);
            }
          } else if (profileData?.role === 'staff') {
            console.log('[useMaintenanceTasksRPC] User is staff, fetching team tasks');
            // Get user's team memberships
            const { data: teamMemberData } = await supabase
              .from('team_members')
              .select('team_id')
              .eq('user_id', userId)
              .eq('status', 'active');

            const teamIds = teamMemberData ? teamMemberData.map((tm: { team_id: string }) => tm.team_id) : [];

            if (teamIds.length > 0) {
              queryBuilder = queryBuilder.or(`user_id.eq.${userId},team_id.in.(${teamIds.join(',')})`);
            } else {
              queryBuilder = queryBuilder.eq('user_id', userId);
            }
          } else if (profileData?.role === 'service_provider') {
            console.log('[useMaintenanceTasksRPC] User is service provider, fetching assigned tasks');
            queryBuilder = queryBuilder.or(`user_id.eq.${userId},provider_id.eq.${userId},assigned_to.eq.${userId}`);
          } else {
            // Default fallback - just get user's own tasks
            queryBuilder = queryBuilder.eq('user_id', userId);
          }

          const { data: directData, error: directError } = await queryBuilder;

          if (directError) {
            console.error('[useMaintenanceTasksRPC] Direct query error:', directError);
            fetchError = directError;
          } else if (directData && directData.length > 0) {
            console.log(`[useMaintenanceTasksRPC] Direct query found ${directData.length} tasks`);
            tasksData = directData;
          } else {
            console.log('[useMaintenanceTasksRPC] Direct query returned no tasks, trying next approach');
          }
        } catch (directErr) {
          console.error('[useMaintenanceTasksRPC] Exception with direct query:', directErr);
          fetchError = directErr instanceof Error ? directErr : new Error(String(directErr));
        }
      }

      // Approach 3: Last resort - try Edge Function if available
      if (!tasksData && process.env.NODE_ENV === 'production') {
        try {
          console.log('[useMaintenanceTasksRPC] Trying Edge Function approach');
          const { data: edgeData, error: edgeError } = await supabase.functions.invoke('get-maintenance-tasks', {
            body: { userId }
          });

          if (edgeError) {
            console.error('[useMaintenanceTasksRPC] Edge Function error:', edgeError);
            fetchError = edgeError;
          } else if (edgeData && edgeData.length > 0) {
            console.log(`[useMaintenanceTasksRPC] Edge Function found ${edgeData.length} tasks`);
            tasksData = edgeData;
          } else {
            console.log('[useMaintenanceTasksRPC] Edge Function returned no tasks');
          }
        } catch (edgeErr) {
          console.error('[useMaintenanceTasksRPC] Exception with Edge Function:', edgeErr);
          fetchError = edgeErr instanceof Error ? edgeErr : new Error(String(edgeErr));
        }
      }

      // Process the tasks data if we found any
      if (tasksData && tasksData.length > 0) {
        // Sort tasks by creation date, most recent first
        const sortedTasks = [...tasksData].sort((a, b) => {
          const dateA = new Date(a.created_at).getTime();
          const dateB = new Date(b.created_at).getTime();
          return dateB - dateA; // Descending order
        });

        // Format tasks for the UI
        const formattedTasks: MaintenanceTask[] = sortedTasks.map((task: any) => ({
          id: task.id,
          title: task.title,
          description: task.description || '',
          propertyId: task.property_id || '',
          propertyName: task.property_name || '',
          status: mapDbStatusToMaintenanceStatus(task.status),
          severity: task.severity as MaintenanceTask['severity'],
          dueDate: task.due_date || 'No due date',
          assignedTo: task.assigned_to || undefined,
          createdAt: task.created_at,
          providerId: task.provider_id || undefined,
          providerEmail: task.provider_email || undefined
        }));

        // Only update tasks if we have new data or no existing data
        if (formattedTasks.length > 0 || tasks.length === 0) {
          console.log(`[useMaintenanceTasksRPC] Updating tasks: ${formattedTasks.length} new tasks`);
          setTasks(formattedTasks);
        } else {
          console.log('[useMaintenanceTasksRPC] Not updating tasks - would replace with empty array');
        }

        // Mark that we've successfully fetched tasks
        hasFetchedRef.current = true;
      } else if (fetchError) {
        // If all approaches failed and we have an error, throw it to be caught below
        throw fetchError;
      } else {
        // If we have no data but also no error, just log it
        console.log('[useMaintenanceTasksRPC] No tasks found with any approach');
        // Only clear tasks if we don't have any existing tasks
        if (tasks.length === 0) {
          setTasks([]);
        }
        hasFetchedRef.current = true;
      }
    } catch (error: any) {
      console.error('[useMaintenanceTasksRPC] Error fetching maintenance tasks:', error);
      setError(`Failed to load tasks: ${error.message}`);
      toast.error('Failed to load maintenance tasks');

      // Don't clear existing tasks on error
      if (tasks.length === 0) {
        // Only try a last-ditch effort if we have no tasks at all
        try {
          console.log('[useMaintenanceTasksRPC] Trying last-ditch fallback approach');
          const { data: fallbackData, error: fallbackError } = await supabase
            .from('maintenance_tasks')
            .select('*');

          if (fallbackError) {
            console.error('[useMaintenanceTasksRPC] Last-ditch fallback failed:', fallbackError);
          } else if (fallbackData && fallbackData.length > 0) {
            console.log(`[useMaintenanceTasksRPC] Last-ditch fallback found ${fallbackData.length} tasks`);

            // Format tasks for the UI
            const formattedTasks: MaintenanceTask[] = fallbackData.map((task: any) => ({
              id: task.id,
              title: task.title,
              description: task.description || '',
              propertyId: task.property_id || '',
              propertyName: task.property_name || '',
              status: mapDbStatusToMaintenanceStatus(task.status),
              severity: task.severity as MaintenanceTask['severity'],
              dueDate: task.due_date || 'No due date',
              assignedTo: task.assigned_to || undefined,
              createdAt: task.created_at,
              providerId: task.provider_id || undefined,
              providerEmail: task.provider_email || undefined
            }));

            setTasks(formattedTasks);
          }
        } catch (fallbackErr) {
          console.error('[useMaintenanceTasksRPC] Last-ditch fallback exception:', fallbackErr);
        }
      }
    } finally {
      setLoading(false);
    }
  }, [userId, isImpersonating, tasks.length]);

  // Fetch tasks when the component mounts and userId is available
  useEffect(() => {
    if (userId && !hasFetchedRef.current) {
      console.log('[useMaintenanceTasksRPC] User ID available, fetching tasks...');
      fetchTasks();
    }
  }, [userId, fetchTasks]);

  const addTask = async (task: Omit<MaintenanceTask, 'id' | 'status' | 'createdAt'>) => {
    if (!userId) return false;

    try {
      console.log('Adding task in useMaintenanceTasksRPC:', task);
      console.log('User ID:', userId);

      // Determine team_id based on property_id if available
      let teamId = null;
      if (task.propertyId) {
        const { data: teamPropertyData, error: teamPropertyError } = await supabase
          .from('team_properties')
          .select('team_id')
          .eq('property_id', task.propertyId)
          .limit(1);

        if (!teamPropertyError && teamPropertyData && teamPropertyData.length > 0) {
          teamId = teamPropertyData[0].team_id;
          console.log(`[useMaintenanceTasksRPC] Found team_id ${teamId} for property ${task.propertyId}`);
        }
      }

      const dbTask = {
        title: task.title,
        description: task.description,
        property_id: task.propertyId || null,
        property_name: task.propertyName,
        severity: task.severity,
        status: 'open',
        due_date: task.dueDate === 'No due date' ? null : task.dueDate,
        user_id: userId,
        provider_id: task.providerId || null,
        provider_email: task.providerEmail || null,
        assigned_to: task.assignedTo || null,
        team_id: teamId
      };

      console.log('Sending to Supabase:', dbTask);

      const { data, error } = await supabase
        .from('maintenance_tasks')
        .insert(dbTask)
        .select();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      console.log('Supabase response:', data);

      if (data && data.length > 0) {
        const newTask: MaintenanceTask = {
          id: data[0].id,
          title: data[0].title,
          description: data[0].description || '',
          propertyId: data[0].property_id || '',
          propertyName: data[0].property_name,
          status: mapDbStatusToMaintenanceStatus(data[0].status),
          severity: data[0].severity as MaintenanceTask['severity'],
          dueDate: data[0].due_date || 'No due date',
          assignedTo: data[0].assigned_to || undefined,
          createdAt: data[0].created_at,
          providerId: data[0].provider_id || undefined,
          providerEmail: data[0].provider_email || undefined
        };

        setTasks(prev => [newTask, ...prev]);
        return true;
      }
      return false;
    } catch (error: any) {
      console.error('Error adding task:', error);
      toast.error(`Failed to add task: ${error.message}`);
      return false;
    }
  };

  const updateTask = async (taskId: string, task: Omit<MaintenanceTask, 'id' | 'createdAt'>) => {
    if (!userId) return false;

    try {
      console.log('Updating task in useMaintenanceTasksRPC:', task);

      const dbTask = {
        title: task.title,
        description: task.description,
        property_id: task.propertyId || null,
        property_name: task.propertyName,
        severity: task.severity,
        status: mapMaintenanceStatusToDbStatus(task.status),
        due_date: task.dueDate === 'No due date' ? null : task.dueDate,
        provider_id: task.providerId || null,
        provider_email: task.providerEmail || null,
        assigned_to: task.assignedTo || null,
        updated_at: new Date().toISOString()
      };

      console.log('Sending update to Supabase:', dbTask);

      const { data, error } = await supabase
        .from('maintenance_tasks')
        .update(dbTask)
        .eq('id', taskId)
        .select();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      console.log('Supabase update response:', data);

      if (data && data.length > 0) {
        const updatedTask: MaintenanceTask = {
          id: data[0].id,
          title: data[0].title,
          description: data[0].description || '',
          propertyId: data[0].property_id || '',
          propertyName: data[0].property_name,
          status: mapDbStatusToMaintenanceStatus(data[0].status),
          severity: data[0].severity as MaintenanceTask['severity'],
          dueDate: data[0].due_date || 'No due date',
          assignedTo: data[0].assigned_to || undefined,
          createdAt: data[0].created_at,
          providerId: data[0].provider_id || undefined,
          providerEmail: data[0].provider_email || undefined
        };

        setTasks(prev =>
          prev.map(t => t.id === taskId ? updatedTask : t)
        );

        return true;
      }
      return false;
    } catch (error: any) {
      console.error('Error updating task:', error);
      toast.error(`Failed to update task: ${error.message}`);
      return false;
    }
  };

  const updateTaskStatus = async (taskId: string, newStatus: MaintenanceStatus) => {
    if (!userId) return false;

    try {
      const dbStatus = mapMaintenanceStatusToDbStatus(newStatus);
      console.log(`Updating task ${taskId} status to ${newStatus} (DB: ${dbStatus})`);

      const { error } = await supabase
        .from('maintenance_tasks')
        .update({ status: dbStatus, updated_at: new Date().toISOString() })
        .eq('id', taskId);

      if (error) throw error;

      console.log('Status update successful, refreshing task list');

      setTasks(prev =>
        prev.map(task =>
          task.id === taskId ? { ...task, status: newStatus } : task
        )
      );

      return true;
    } catch (error: any) {
      console.error('Error updating task status:', error);
      toast.error(`Failed to update task: ${error.message}`);
      return false;
    }
  };

  const deleteTask = async (taskId: string) => {
    if (!userId) return false;

    try {
      const { error } = await supabase
        .from('maintenance_tasks')
        .delete()
        .eq('id', taskId);

      if (error) throw error;

      setTasks(prev => prev.filter(task => task.id !== taskId));

      toast.success('Task deleted successfully');
      return true;
    } catch (error: any) {
      console.error('Error deleting task:', error);
      toast.error(`Failed to delete task: ${error.message}`);
      return false;
    }
  };

  // Track last refresh time to prevent too many refreshes
  const lastRefreshTimeRef = useRef<number>(0);
  const REFRESH_COOLDOWN_MS = 5000; // 5 seconds cooldown - Will be ignored for now

  const refreshTasks = useCallback(() => {
    const now = Date.now();
    const timeSinceLastRefresh = now - lastRefreshTimeRef.current;

    // Only refresh if it's been more than the cooldown period - REMOVED COOLDOWN CHECK
    // if (timeSinceLastRefresh > REFRESH_COOLDOWN_MS) {
      console.log('[useMaintenanceTasksRPC] Refreshing tasks...');
      lastRefreshTimeRef.current = now;
      hasFetchedRef.current = false; // Ensure we actually fetch
      fetchTasks();
    // } else {
    //   console.log(`[useMaintenanceTasksRPC] Refresh skipped - cooldown period (${Math.round(timeSinceLastRefresh/1000)}/${REFRESH_COOLDOWN_MS/1000}s)`);
    // }
  }, [fetchTasks]); // Keep dependencies minimal

  // REMOVED: Visibility change listener that refreshes data
  // This was causing data to disappear when the app comes back into focus

  // Removed visibility change handler to avoid conflicts with React Query's built-in functionality

  // Log the tasks before returning them
  useEffect(() => {
    console.log('[useMaintenanceTasksRPC] Current tasks:', tasks);
  }, [tasks]);

  return {
    tasks,
    loading,
    error,
    fetchTasks,
    addTask,
    updateTask,
    updateTaskStatus,
    deleteTask,
    refreshTasks
  };
};
