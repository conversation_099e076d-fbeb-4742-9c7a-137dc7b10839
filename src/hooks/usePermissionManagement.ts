import { useState, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { PermissionType, UserPermission } from '@/types/auth';

export const usePermissionManagement = () => {
  const [loading, setLoading] = useState(false);
  const [permissions, setPermissions] = useState<UserPermission[]>([]);
  const permissionsBackupRef = useRef<UserPermission[]>([]);
  const lastFetchRef = useRef<{userId?: string, teamId?: string, timestamp: number}>({timestamp: 0});
  const FETCH_COOLDOWN = 1000; // 1 second cooldown between fetches

  // Cache for permissions to prevent duplicate fetches
  const permissionsCacheRef = useRef<{[key: string]: {data: UserPermission[], timestamp: number}}>({});
  const CACHE_TTL = 5000; // 5 seconds cache TTL - shorter to ensure permissions load properly

  // Fetch permissions for a user
  const fetchUserPermissions = async (userId: string, teamId?: string) => {
    // Create a cache key for this user+team combination
    const cacheKey = `${userId}:${teamId || 'global'}`;

    // Check if we have a valid cache entry
    const now = Date.now();
    const cacheEntry = permissionsCacheRef.current[cacheKey];
    const isCacheValid = cacheEntry && (now - cacheEntry.timestamp < CACHE_TTL);

    // Prevent excessive fetching using both the ref and the cache
    // But only if we're not forcing a refresh
    const lastFetch = lastFetchRef.current;
    if (
      lastFetch.userId === userId &&
      lastFetch.teamId === teamId &&
      now - lastFetch.timestamp < FETCH_COOLDOWN &&
      permissions.length > 0
    ) {
      console.log('Skipping permission fetch - too soon after last fetch');
      return permissions;
    }

    // If we have a valid cache entry, use it
    // But only if we have data in the cache
    if (isCacheValid && cacheEntry?.data?.length > 0) {
      console.log(`Using cached permissions for user ${userId}${teamId ? ` and team ${teamId}` : ''}`);

      // Only update state if it's different from current state
      const currentPermissionsJson = JSON.stringify(permissions);
      const cachedPermissionsJson = JSON.stringify(cacheEntry.data);

      if (currentPermissionsJson !== cachedPermissionsJson) {
        console.log('Cached permissions different from current state, updating');
        setPermissions(cacheEntry.data);
      }

      return cacheEntry.data;
    }

    // Update last fetch timestamp
    lastFetchRef.current = { userId, teamId, timestamp: now };

    // Only set loading to true if we don't have any permissions data yet
    // This prevents the UI from flashing when refreshing existing data
    if (permissions.length === 0) {
      setLoading(true);
    }

    try {
      console.log(`Fetching permissions for user ${userId}${teamId ? ` and team ${teamId}` : ''}`);

      let query = supabase
        .from('user_permissions')
        .select('*')
        .eq('user_id', userId);

      if (teamId) {
        query = query.eq('team_id', teamId);
      }

      const { data, error } = await query;

      if (error) throw error;

      // Update the cache
      permissionsCacheRef.current[cacheKey] = {
        data: data || [],
        timestamp: now
      };

      // Backup the data before setting it
      permissionsBackupRef.current = data || [];

      // Only update the state if the data has actually changed
      // This prevents unnecessary re-renders
      const currentPermissionsJson = JSON.stringify(permissions);
      const newPermissionsJson = JSON.stringify(data || []);

      if (currentPermissionsJson !== newPermissionsJson) {
        console.log('Permissions data changed, updating state');
        setPermissions(data || []);
      } else {
        console.log('Permissions data unchanged, skipping state update');
      }

      console.log(`Fetched ${data?.length || 0} permissions successfully`);
      return data || [];
    } catch (error: any) {
      console.error('Error fetching user permissions:', error);

      // If we have a backup, use it instead of showing an error
      if (permissionsBackupRef.current.length > 0) {
        console.log('Using backup permissions data');

        // Only update if the current permissions are different from the backup
        const currentPermissionsJson = JSON.stringify(permissions);
        const backupPermissionsJson = JSON.stringify(permissionsBackupRef.current);

        if (currentPermissionsJson !== backupPermissionsJson) {
          setPermissions(permissionsBackupRef.current);
        }

        return permissionsBackupRef.current;
      }

      toast({
        title: "Error",
        description: "Failed to load permissions",
        variant: "destructive"
      });
      return [];
    } finally {
      setLoading(false);
    }
  };

  // Add a permission for a user
  const addUserPermission = async (userId: string, permission: PermissionType, teamId?: string) => {
    // Only set loading to true if we don't have any permissions data yet
    // This prevents the UI from flashing when adding a new permission
    if (permissions.length === 0) {
      setLoading(true);
    }

    try {
      console.log(`Adding permission ${permission} for user ${userId}${teamId ? ` and team ${teamId}` : ''}`);

      // Check if permission already exists
      const existingPermissions = permissions.length > 0 ? permissions : await fetchUserPermissions(userId, teamId);
      const permissionExists = existingPermissions.some(p =>
        p.permission === permission &&
        p.team_id === teamId
      );

      if (permissionExists) {
        console.log('Permission already exists, skipping add');
        toast({
          title: "Info",
          description: "Permission already exists",
          duration: 3000
        });
        return true;
      }

      const permissionData = {
        user_id: userId,
        permission,
        team_id: teamId,
        enabled: true
      };

      // Create a temporary permission with a temporary ID
      const tempPermission = {
        ...permissionData,
        id: `temp-${Date.now()}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      } as UserPermission;

      // Update the UI immediately for a responsive experience
      setPermissions(prev => [...prev, tempPermission]);

      // Then update the database
      const { data, error } = await supabase
        .from('user_permissions')
        .insert(permissionData)
        .select();

      if (error) throw error;

      toast({
        title: "Success",
        description: "Permission added successfully",
        duration: 3000
      });

      // Update the local state with the real permission from the database
      const newPermission = data?.[0];
      if (newPermission) {
        // Update state
        setPermissions(prev => prev.map(p =>
          p.id === tempPermission.id ? newPermission : p
        ));

        // Update backup
        permissionsBackupRef.current = [...permissionsBackupRef.current.filter(p => p.id !== tempPermission.id), newPermission];

        // Update cache
        const cacheKey = `${userId}:${teamId || 'global'}`;
        const cacheEntry = permissionsCacheRef.current[cacheKey];
        if (cacheEntry) {
          permissionsCacheRef.current[cacheKey] = {
            data: [...cacheEntry.data.filter(p => p.id !== tempPermission.id), newPermission],
            timestamp: Date.now()
          };
        }
      }

      return true;
    } catch (error: any) {
      console.error('Error adding user permission:', error);

      // Remove the temporary permission if it failed to add
      setPermissions(prev => prev.filter(p => !p.id.toString().startsWith('temp-')));

      toast({
        title: "Error",
        description: "Failed to add permission",
        variant: "destructive"
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Remove a permission from a user
  const removeUserPermission = async (permissionId: string, userId: string, teamId?: string) => {
    // Store the permission before removing it in case we need to restore it
    const permissionToRemove = permissions.find(p => p.id === permissionId);

    // Don't set loading to true here to prevent UI flashing
    // Instead, we'll disable the UI elements while the removal is in progress
    try {
      console.log(`Removing permission with ID ${permissionId} for user ${userId}`);

      // Update local state immediately for a responsive UI
      setPermissions(prev => prev.filter(p => p.id !== permissionId));

      // Keep a backup of the current state before removing
      const currentBackup = [...permissionsBackupRef.current];
      permissionsBackupRef.current = permissionsBackupRef.current.filter(p => p.id !== permissionId);

      // Update cache
      const cacheKey = `${userId}:${teamId || 'global'}`;
      const cacheEntry = permissionsCacheRef.current[cacheKey];
      if (cacheEntry) {
        permissionsCacheRef.current[cacheKey] = {
          data: cacheEntry.data.filter(p => p.id !== permissionId),
          timestamp: Date.now()
        };
      }

      // Then update the database
      const { error } = await supabase
        .from('user_permissions')
        .delete()
        .eq('id', permissionId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Permission removed successfully",
        duration: 3000
      });

      return true;
    } catch (error: any) {
      console.error('Error removing user permission:', error);

      // Restore the permission if it was removed from the UI but failed to remove from the database
      if (permissionToRemove) {
        // Restore state
        setPermissions(prev => [...prev, permissionToRemove]);

        // Restore backup
        permissionsBackupRef.current = [...permissionsBackupRef.current, permissionToRemove];

        // Restore cache
        const cacheKey = `${userId}:${teamId || 'global'}`;
        const cacheEntry = permissionsCacheRef.current[cacheKey];
        if (cacheEntry) {
          permissionsCacheRef.current[cacheKey] = {
            data: [...cacheEntry.data, permissionToRemove],
            timestamp: Date.now()
          };
        }
      }

      toast({
        title: "Error",
        description: "Failed to remove permission",
        variant: "destructive"
      });
      return false;
    }
  };

  // Update a permission for a user
  const updateUserPermission = async (permissionId: string, enabled: boolean, userId: string, teamId?: string) => {
    // Don't set loading to true here to prevent UI flashing
    // Instead, we'll disable the UI elements while the update is in progress
    try {
      console.log(`Updating permission with ID ${permissionId} to ${enabled ? 'enabled' : 'disabled'}`);

      // Update local state immediately for a responsive UI
      setPermissions(prev => prev.map(p =>
        p.id === permissionId ? { ...p, enabled } : p
      ));

      // Update backup
      permissionsBackupRef.current = permissionsBackupRef.current.map(p =>
        p.id === permissionId ? { ...p, enabled } : p
      );

      // Update cache
      const cacheKey = `${userId}:${teamId || 'global'}`;
      const cacheEntry = permissionsCacheRef.current[cacheKey];
      if (cacheEntry) {
        permissionsCacheRef.current[cacheKey] = {
          data: cacheEntry.data.map(p =>
            p.id === permissionId ? { ...p, enabled } : p
          ),
          timestamp: Date.now()
        };
      }

      // Then update the database
      const { error } = await supabase
        .from('user_permissions')
        .update({ enabled })
        .eq('id', permissionId);

      if (error) throw error;

      toast({
        title: "Success",
        description: `Permission ${enabled ? 'enabled' : 'disabled'} successfully`,
        duration: 3000
      });

      return true;
    } catch (error: any) {
      console.error('Error updating user permission:', error);

      // Revert the local state change
      const originalPermission = permissionsBackupRef.current.find(p => p.id === permissionId);
      if (originalPermission) {
        // Revert state
        setPermissions(prev => prev.map(p =>
          p.id === permissionId ? originalPermission : p
        ));

        // Revert cache
        const cacheKey = `${userId}:${teamId || 'global'}`;
        const cacheEntry = permissionsCacheRef.current[cacheKey];
        if (cacheEntry) {
          permissionsCacheRef.current[cacheKey] = {
            data: cacheEntry.data.map(p =>
              p.id === permissionId ? originalPermission : p
            ),
            timestamp: Date.now()
          };
        }
      }

      toast({
        title: "Error",
        description: "Failed to update permission",
        variant: "destructive"
      });
      return false;
    }
  };

  return {
    loading,
    permissions,
    fetchUserPermissions,
    addUserPermission,
    removeUserPermission,
    updateUserPermission
  };
};
