
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Provider } from '@/components/maintenance/types';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/hooks/usePermissionsFixed';
import { toast } from 'sonner';

export const useProviders = () => {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { authState } = useAuth();
  const { isAdmin } = usePermissions();
  const userId = authState.user?.id;

  const fetchProviders = async () => {
    if (!userId) return;

    try {
      setLoading(true);
      setError(null);

      console.log('[useProviders] Fetching providers for user:', userId);

      // Check if user is admin or super admin
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('is_super_admin, role')
        .eq('id', userId)
        .single();

      if (profileError) throw profileError;

      const isAdmin = profileData?.is_super_admin || profileData?.role === 'admin';
      console.log('[useProviders] User is admin:', isAdmin);

      let data: any[] = [];

      // First, fetch maintenance providers
      let maintenanceProviders: any[] = [];

      // Try our RPC function first
      try {
        console.log('[useProviders] Using get_providers RPC function');
        const { data: rpcProviders, error: rpcError } = await supabase.rpc(
          'get_providers'
        );

        if (rpcError) {
          console.error('[useProviders] Error with get_providers:', rpcError);
          // Continue to fallback methods
        } else if (rpcProviders && rpcProviders.length > 0) {
          console.log(`[useProviders] Found ${rpcProviders.length} providers with get_providers`);
          // Format the providers
          maintenanceProviders = rpcProviders.map((provider: any) => ({
            id: provider.id,
            name: provider.name || '',
            email: provider.email || '',
            phone: provider.phone || '',
            specialty: provider.specialty || '',
            notes: provider.notes || '',
            user_id: provider.user_id
          }));
          return maintenanceProviders;
        }
      } catch (rpcError) {
        console.error('[useProviders] Exception with get_providers:', rpcError);
        // Continue to fallback methods
      }

      // Fallback to original approach
      if (isAdmin) {
        // Admins can see all providers
        const { data: allProviders, error: allError } = await supabase
          .from('maintenance_providers')
          .select('*')
          .order('name');

        if (allError) throw allError;
        maintenanceProviders = allProviders || [];
        console.log('[useProviders] Admin fetched all maintenance providers:', maintenanceProviders.length);
      } else {
        // First, fetch the user's teams
        const { data: teamData, error: teamsError } = await supabase
          .from('team_members')
          .select('team_id')
          .eq('user_id', userId)
          .eq('status', 'active');

        if (teamsError) throw teamsError;

        const teamIds = teamData?.map((t: { team_id: string }) => t.team_id) || [];
        console.log('[useProviders] User teams:', teamIds);

        // Fetch the user's own providers
        const { data: ownProviders, error: ownError } = await supabase
          .from('maintenance_providers')
          .select('*')
          .eq('user_id', userId);

        if (ownError) throw ownError;
        console.log('[useProviders] Own providers:', ownProviders?.length);

        // Let RLS handle the filtering of team providers
        // This will only return providers the user has access to
        const { data: teamProviders, error: providersError } = await supabase
          .from('maintenance_providers')
          .select('*')
          .neq('user_id', userId);

        console.log('[useProviders] Team providers query result:', teamProviders?.length, providersError);

        if (providersError) throw providersError;
        console.log('[useProviders] Team providers:', teamProviders?.length);

        // Combine the results
        maintenanceProviders = [...(ownProviders || []), ...(teamProviders || [])];
      }

      // Now, fetch service provider accounts
      let serviceProviderAccounts: any[] = [];

      if (isAdmin) {
        // Admins can see all service providers
        const { data: allServiceProviders, error: spError } = await supabase
          .from('service_providers')
          .select('id, email, first_name, last_name, status')
          .eq('status', 'active');

        if (spError) {
          console.error('[useProviders] Error fetching all service providers:', spError);
        } else {
          serviceProviderAccounts = allServiceProviders || [];
        }
      } else {
        // First, get the user's teams
        const { data: userTeams, error: userTeamsError } = await supabase
          .from('team_members')
          .select('team_id')
          .eq('user_id', userId)
          .eq('status', 'active');

        if (userTeamsError) {
          console.error('[useProviders] Error fetching user teams:', userTeamsError);
        } else if (userTeams && userTeams.length > 0) {
          const teamIds = userTeams.map((t: { team_id: string }) => t.team_id);
          console.log('[useProviders] User teams for service providers:', teamIds);

          // Get service providers who are members of the same teams
          const { data: teamServiceProviders, error: teamSpError } = await supabase
            .from('team_members')
            .select('user_id')
            .in('team_id', teamIds)
            .eq('status', 'active');

          if (teamSpError) {
            console.error('[useProviders] Error fetching team service providers:', teamSpError);
          } else if (teamServiceProviders && teamServiceProviders.length > 0) {
            const spUserIds = teamServiceProviders.map((sp: { user_id: string }) => sp.user_id);
            console.log('[useProviders] Service provider user IDs:', spUserIds);

            // Get the service provider profiles
            const { data: spProfiles, error: spProfilesError } = await supabase
              .from('profiles')
              .select('id, first_name, last_name, email, role')
              .in('id', spUserIds)
              .eq('role', 'service_provider');

            if (spProfilesError) {
              console.error('[useProviders] Error fetching service provider profiles:', spProfilesError);
            } else {
              console.log('[useProviders] Service provider profiles:', spProfiles);

              // Get the actual service provider records
              const { data: spRecords, error: spRecordsError } = await supabase
                .from('service_providers')
                .select('id, email, first_name, last_name, status')
                .in('id', spUserIds)
                .eq('status', 'active');

              if (spRecordsError) {
                console.error('[useProviders] Error fetching service provider records:', spRecordsError);
              } else {
                serviceProviderAccounts = spRecords || [];
              }
            }
          }
        }
      }

      // Error handling is done in the individual sections above

      console.log('[useProviders] Service provider accounts:', serviceProviderAccounts?.length);

      // Convert service provider accounts to the same format as maintenance providers
      const serviceProviders = (serviceProviderAccounts || []).map((sp: { id: string; email: string; first_name: string; last_name: string }) => ({
        id: sp.id,
        name: `${sp.first_name} ${sp.last_name}`,
        email: sp.email,
        phone: '',
        specialty: 'Service Provider Account',
        notes: '',
        user_id: sp.id // Use the service provider's ID as the user_id
      }));

      // Combine maintenance providers and service provider accounts
      data = [...maintenanceProviders, ...serviceProviders];

      console.log('[useProviders] Combined providers:', data?.length);

      if (data) {
        const formattedProviders: Provider[] = data.map(provider => ({
          id: provider.id,
          name: provider.name,
          email: provider.email || '',
          phone: provider.phone || '',
          specialty: provider.specialty || '',
          address: provider.notes || '', // address is stored in the notes field
          user_id: provider.user_id // Keep track of who created this provider
        }));

        console.log('[useProviders] Fetched providers:', formattedProviders);
        setProviders(formattedProviders);
      }
    } catch (error: any) {
      console.error('[useProviders] Error fetching providers:', error);
      setError(`Failed to load providers: ${error.message}`);
      toast.error('Failed to load service providers');
    } finally {
      setLoading(false);
    }
  };

  // Fetch providers on component mount
  useEffect(() => {
    if (userId) {
      fetchProviders();
    }
  }, [userId]);

  // Function to add a provider
  const addProvider = async (provider: Omit<Provider, 'id'>) => {
    try {
      if (!userId) {
        console.error('[useProviders] Cannot add provider: No authenticated user');
        toast.error('You must be logged in to add providers');
        return false;
      }

      console.log('[useProviders] Adding provider:', provider);
      console.log('[useProviders] User ID:', userId);

      const { data, error } = await supabase
        .from('maintenance_providers')
        .insert({
          name: provider.name,
          email: provider.email,
          phone: provider.phone,
          specialty: provider.specialty,
          notes: provider.address, // Store address in the notes field
          user_id: userId
        })
        .select()
        .single();

      if (error) {
        console.error('[useProviders] Supabase error:', error);
        throw error;
      }

      console.log('[useProviders] Provider added successfully:', data);

      if (data) {
        const newProvider: Provider = {
          id: data.id,
          name: data.name,
          email: data.email || '',
          phone: data.phone || '',
          specialty: data.specialty || '',
          address: data.notes || ''
        };

        setProviders(prev => [...prev, newProvider]);
        toast.success('Provider added successfully');
        return true;
      }
      return false;
    } catch (error: any) {
      console.error('[useProviders] Error adding provider:', error);
      toast.error(`Failed to add provider: ${error.message}`);
      return false;
    }
  };

  // Function to update a provider
  const updateProvider = async (id: string, provider: Omit<Provider, 'id'>) => {
    try {
      if (!userId) {
        console.error('[useProviders] Cannot update provider: No authenticated user');
        toast.error('You must be logged in to update providers');
        return false;
      }

      console.log('[useProviders] Updating provider:', id, provider);

      const { error } = await supabase
        .from('maintenance_providers')
        .update({
          name: provider.name,
          email: provider.email,
          phone: provider.phone,
          specialty: provider.specialty,
          notes: provider.address // Store address in the notes field
        })
        .eq('id', id)
        .eq('user_id', userId);

      if (error) {
        console.error('[useProviders] Supabase update error:', error);
        throw error;
      }

      console.log('[useProviders] Provider updated successfully');

      // Update the local state with the updated provider
      setProviders(prev =>
        prev.map(p => p.id === id ? { ...p, ...provider, id } : p)
      );

      toast.success('Provider updated successfully');
      return true;
    } catch (error: any) {
      console.error('[useProviders] Error updating provider:', error);
      toast.error(`Failed to update provider: ${error.message}`);
      return false;
    }
  };

  // Function to delete a provider
  const deleteProvider = async (id: string) => {
    try {
      if (!userId) {
        console.error('[useProviders] Cannot delete provider: No authenticated user');
        toast.error('You must be logged in to delete providers');
        return false;
      }

      console.log('[useProviders] Deleting provider:', id);

      const { error } = await supabase
        .from('maintenance_providers')
        .delete()
        .eq('id', id)
        .eq('user_id', userId);

      if (error) {
        console.error('[useProviders] Supabase delete error:', error);
        throw error;
      }

      console.log('[useProviders] Provider deleted successfully');

      setProviders(prev => prev.filter(p => p.id !== id));
      toast.success('Provider deleted successfully');
      return true;
    } catch (error: any) {
      console.error('[useProviders] Error deleting provider:', error);
      toast.error(`Failed to delete provider: ${error.message}`);
      return false;
    }
  };

  return {
    providers,
    loading,
    error,
    fetchProviders,
    addProvider,
    updateProvider,
    deleteProvider
  };
};
