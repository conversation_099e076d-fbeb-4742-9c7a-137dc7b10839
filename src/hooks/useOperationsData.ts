import { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { format, subMonths, startOfMonth, endOfMonth, parseISO, differenceInDays, addDays } from 'date-fns';

// Types for operations data
export interface OperationsMetrics {
  avgTimeToComplete: number;
  completedOnDueDate: number;
  propertiesReadyBeforeCheckIn: number;
  overallStayRating: number;
  // Comparison with previous period
  avgTimeComparison: number;
  completedOnDueDateComparison: number;
  propertiesReadyComparison: number;
  overallStayRatingComparison: number;
}

export interface TasksCompletedData {
  month: string;
  total: number;
  reported: number;
  benchmark: number;
}

export interface TasksByDayData {
  day: string;
  cleaning: number;
  inspection: number;
  maintenance: number;
  benchmark: number;
}

export interface ReportedIssueData {
  name: string;
  value: number;
  color: string;
}

export interface TimeReadyData {
  day: string;
  hours: number;
}

export interface OperationsData {
  metrics: OperationsMetrics;
  tasksCompletedData: TasksCompletedData[];
  tasksByDayData: TasksByDayData[];
  reportedIssuesData: ReportedIssueData[];
  timeReadyData: TimeReadyData[];
  properties: { id: string; name: string }[];
  isLoading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
}

// Date range options
export type DateRangeOption = 'This month' | 'Last month' | 'Last 3 months' | 'This year';

// Department options
export type DepartmentOption = 'All departments' | 'Cleaning' | 'Maintenance' | 'Inspection';

// Hook for operations data
export const useOperationsData = (
  dateRange: DateRangeOption = 'This month',
  department: DepartmentOption = 'All departments',
  propertyId: string = 'All properties'
): OperationsData => {
  const { authState } = useAuth();
  const userId = authState.user?.id;
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // State for raw data
  const [maintenanceTasks, setMaintenanceTasks] = useState<any[]>([]);
  const [damageReports, setDamageReports] = useState<any[]>([]);
  const [bookings, setBookings] = useState<any[]>([]);
  const [properties, setProperties] = useState<{ id: string; name: string }[]>([]);

  // Calculate date range based on selection
  const dateRangeFilter = useMemo(() => {
    const now = new Date();
    let startDate, endDate;

    switch (dateRange) {
      case 'This month':
        startDate = startOfMonth(now);
        endDate = endOfMonth(now);
        break;
      case 'Last month':
        startDate = startOfMonth(subMonths(now, 1));
        endDate = endOfMonth(subMonths(now, 1));
        break;
      case 'Last 3 months':
        startDate = startOfMonth(subMonths(now, 3));
        endDate = endOfMonth(now);
        break;
      case 'This year':
        startDate = new Date(now.getFullYear(), 0, 1);
        endDate = new Date(now.getFullYear(), 11, 31);
        break;
      default:
        startDate = startOfMonth(now);
        endDate = endOfMonth(now);
    }

    return { startDate, endDate };
  }, [dateRange]);

  // Fetch properties
  useEffect(() => {
    const fetchProperties = async () => {
      try {
        const { data, error } = await supabase
          .from('properties')
          .select('id, name')
          .order('name');

        if (error) throw error;

        setProperties(data || []);
      } catch (err: any) {
        console.error('Error fetching properties:', err);
        setError(err.message);
      }
    };

    fetchProperties();
  }, []);

  // Fetch data based on filters
  useEffect(() => {
    const fetchData = async () => {
      if (!userId) return;

      setIsLoading(true);
      setError(null);

      try {
        // Fetch maintenance tasks
        let tasksQuery = supabase
          .from('maintenance_tasks')
          .select('*')
          .gte('created_at', dateRangeFilter.startDate.toISOString())
          .lte('created_at', dateRangeFilter.endDate.toISOString());

        // Apply department filter
        if (department !== 'All departments') {
          // Map department to task type or category
          const taskType = department.toLowerCase();
          tasksQuery = tasksQuery.ilike('title', `%${taskType}%`);
        }

        // Apply property filter
        if (propertyId !== 'All properties') {
          tasksQuery = tasksQuery.eq('property_id', propertyId);
        }

        const { data: tasksData, error: tasksError } = await tasksQuery;

        if (tasksError) throw tasksError;

        setMaintenanceTasks(tasksData || []);

        // Fetch damage reports
        let damageQuery = supabase
          .from('damage_reports')
          .select('*, properties(name)')
          .gte('created_at', dateRangeFilter.startDate.toISOString())
          .lte('created_at', dateRangeFilter.endDate.toISOString());

        // Apply property filter
        if (propertyId !== 'All properties') {
          damageQuery = damageQuery.eq('property_id', propertyId);
        }

        const { data: damageData, error: damageError } = await damageQuery;

        if (damageError) throw damageError;

        setDamageReports(damageData || []);

        // Fetch bookings
        let bookingsQuery = supabase
          .from('bookings')
          .select('*, properties(name)')
          .gte('check_in_date', dateRangeFilter.startDate.toISOString().split('T')[0])
          .lte('check_out_date', dateRangeFilter.endDate.toISOString().split('T')[0]);

        // Apply property filter
        if (propertyId !== 'All properties') {
          bookingsQuery = bookingsQuery.eq('property_id', propertyId);
        }

        const { data: bookingsData, error: bookingsError } = await bookingsQuery;

        if (bookingsError) throw bookingsError;

        setBookings(bookingsData || []);
      } catch (err: any) {
        console.error('Error fetching operations data:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [userId, dateRange, department, propertyId, dateRangeFilter]);

  // Calculate metrics
  const metrics = useMemo((): OperationsMetrics => {
    // Default values if no data
    if (maintenanceTasks.length === 0) {
      return {
        avgTimeToComplete: 0,
        completedOnDueDate: 0,
        propertiesReadyBeforeCheckIn: 0,
        overallStayRating: 0,
        avgTimeComparison: 0,
        completedOnDueDateComparison: 0,
        propertiesReadyComparison: 0,
        overallStayRatingComparison: 0
      };
    }

    // Calculate average time to complete (in hours)
    // For this example, we'll use a random value between 1-5 hours
    // In a real implementation, you would calculate this from created_at and updated_at timestamps
    const avgTimeToComplete = maintenanceTasks.length > 0
      ? maintenanceTasks.reduce((sum, task) => {
          // If task has both created_at and updated_at and status is 'completed'
          if (task.created_at && task.updated_at && task.status === 'completed') {
            const createdAt = parseISO(task.created_at);
            const updatedAt = parseISO(task.updated_at);
            const diffInHours = (updatedAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
            return sum + diffInHours;
          }
          return sum + 2.5; // Default value if we can't calculate
        }, 0) / maintenanceTasks.length
      : 2.5;

    // Calculate percentage of tasks completed on due date
    const completedTasks = maintenanceTasks.filter(task => task.status === 'completed');
    const completedOnDueDate = completedTasks.length > 0
      ? (completedTasks.filter(task => {
          if (!task.due_date) return false;
          const dueDate = new Date(task.due_date);
          const completedDate = parseISO(task.updated_at);
          return completedDate <= dueDate;
        }).length / completedTasks.length) * 100
      : 85;

    // Calculate percentage of properties ready before check-in
    const propertiesReadyBeforeCheckIn = bookings.length > 0 ? 90 : 90; // Placeholder

    // Calculate overall stay rating
    const overallStayRating = 4.8; // Placeholder

    // Comparison with previous period (placeholder values)
    const avgTimeComparison = 0.25;
    const completedOnDueDateComparison = -5;
    const propertiesReadyComparison = 7;
    const overallStayRatingComparison = -0.2;

    return {
      avgTimeToComplete,
      completedOnDueDate,
      propertiesReadyBeforeCheckIn,
      overallStayRating,
      avgTimeComparison,
      completedOnDueDateComparison,
      propertiesReadyComparison,
      overallStayRatingComparison
    };
  }, [maintenanceTasks, bookings]);

  // Generate tasks completed data
  const tasksCompletedData = useMemo((): TasksCompletedData[] => {
    // If we have real data, process it
    if (maintenanceTasks.length > 0) {
      // Group tasks by month
      const tasksByMonth = maintenanceTasks.reduce((acc: Record<string, any>, task) => {
        const month = format(parseISO(task.created_at), 'MMM');
        if (!acc[month]) {
          acc[month] = { total: 0, reported: 0 };
        }
        acc[month].total += 1;
        // Count reported tasks (those with a description)
        if (task.description && task.description.trim() !== '') {
          acc[month].reported += 1;
        }
        return acc;
      }, {});

      // Convert to array format
      return Object.entries(tasksByMonth).map(([month, data]: [string, any]) => ({
        month,
        total: data.total,
        reported: data.reported,
        benchmark: 280 // Placeholder benchmark value
      }));
    }

    // If no real data, return sample data
    return [
      { month: 'Jan', total: 120, reported: 80, benchmark: 280 },
      { month: 'Feb', total: 150, reported: 100, benchmark: 280 },
      { month: 'Mar', total: 180, reported: 120, benchmark: 280 },
      { month: 'Apr', total: 220, reported: 150, benchmark: 280 },
      { month: 'May', total: 250, reported: 180, benchmark: 280 },
      { month: 'Jun', total: 280, reported: 200, benchmark: 280 },
      { month: 'Jul', total: 320, reported: 220, benchmark: 280 },
      { month: 'Aug', total: 350, reported: 250, benchmark: 280 },
      { month: 'Sep', total: 370, reported: 270, benchmark: 280 },
      { month: 'Oct', total: 390, reported: 290, benchmark: 280 },
    ];
  }, [maintenanceTasks]);

  // Generate tasks by day data
  const tasksByDayData = useMemo((): TasksByDayData[] => {
    // If we have real data, process it
    if (maintenanceTasks.length > 0) {
      // Group tasks by day of week
      const tasksByDay = maintenanceTasks.reduce((acc: Record<string, any>, task) => {
        const day = format(parseISO(task.created_at), 'EEE');
        if (!acc[day]) {
          acc[day] = { cleaning: 0, inspection: 0, maintenance: 0 };
        }

        // Categorize task by type (based on title or description)
        const title = task.title.toLowerCase();
        if (title.includes('clean') || title.includes('housekeep')) {
          acc[day].cleaning += 1;
        } else if (title.includes('inspect') || title.includes('check')) {
          acc[day].inspection += 1;
        } else {
          acc[day].maintenance += 1;
        }

        return acc;
      }, {});

      // Define days of week in order
      const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

      // Convert to array format, ensuring all days are included
      return daysOfWeek.map(day => ({
        day,
        cleaning: tasksByDay[day]?.cleaning || 0,
        inspection: tasksByDay[day]?.inspection || 0,
        maintenance: tasksByDay[day]?.maintenance || 0,
        benchmark: 280 // Placeholder benchmark value
      }));
    }

    // If no real data, return sample data
    return [
      { day: 'Sun', cleaning: 120, inspection: 80, maintenance: 40, benchmark: 280 },
      { day: 'Mon', cleaning: 150, inspection: 100, maintenance: 60, benchmark: 280 },
      { day: 'Tue', cleaning: 180, inspection: 120, maintenance: 80, benchmark: 280 },
      { day: 'Wed', cleaning: 220, inspection: 150, maintenance: 100, benchmark: 280 },
      { day: 'Thu', cleaning: 250, inspection: 180, maintenance: 120, benchmark: 280 },
      { day: 'Fri', cleaning: 280, inspection: 200, maintenance: 140, benchmark: 280 },
      { day: 'Sat', cleaning: 320, inspection: 220, maintenance: 160, benchmark: 280 },
    ];
  }, [maintenanceTasks]);

  // Generate reported issues data
  const reportedIssuesData = useMemo((): ReportedIssueData[] => {
    // If we have real data, process it
    if (damageReports.length > 0) {
      // Group by reporter type (placeholder logic)
      const reporterCounts: Record<string, number> = {
        'Owner': 0,
        'Maintenance Tech': 0,
        'Guest': 0,
        'Inspector': 0,
        'Property Manager': 0,
        'Housekeeper': 0
      };

      // Assign each report to a category (placeholder logic)
      damageReports.forEach((report, index) => {
        const categories = Object.keys(reporterCounts);
        const category = categories[index % categories.length];
        reporterCounts[category] += 1;
      });

      // Define colors for each category
      const colors = {
        'Owner': '#8884d8',
        'Maintenance Tech': '#82ca9d',
        'Guest': '#ffc658',
        'Inspector': '#ff8042',
        'Property Manager': '#0088fe',
        'Housekeeper': '#00C49F'
      };

      // Convert to array format
      return Object.entries(reporterCounts)
        .filter(([_, count]) => count > 0) // Only include categories with data
        .map(([name, value]) => ({
          name,
          value,
          color: colors[name as keyof typeof colors]
        }));
    }

    // If no real data, return sample data
    return [
      { name: 'Owner', value: 25, color: '#8884d8' },
      { name: 'Maintenance Tech', value: 20, color: '#82ca9d' },
      { name: 'Guest', value: 15, color: '#ffc658' },
      { name: 'Inspector', value: 15, color: '#ff8042' },
      { name: 'Property Manager', value: 15, color: '#0088fe' },
      { name: 'Housekeeper', value: 10, color: '#00C49F' },
    ];
  }, [damageReports]);

  // Generate time ready data
  const timeReadyData = useMemo((): TimeReadyData[] => {
    // If we have real data, process it
    if (bookings.length > 0) {
      // Get the last 7 days
      const today = new Date();
      const last7Days = Array.from({ length: 7 }, (_, i) => {
        const date = addDays(today, -i);
        return format(date, 'MMM d');
      }).reverse();

      // Create data for each day (placeholder logic)
      return last7Days.map(day => ({
        day,
        hours: Math.random() * 0.8 + 0.2 // Random value between 0.2 and 1.0
      }));
    }

    // If no real data, return sample data
    return [
      { day: 'Jun 9', hours: 0.3 },
      { day: 'Jun 10', hours: 0.7 },
      { day: 'Jun 11', hours: 0.5 },
      { day: 'Jun 12', hours: 0.8 },
      { day: 'Jun 13', hours: 0.4 },
      { day: 'Jun 14', hours: 0.9 },
      { day: 'Jun 15', hours: 0.2 },
    ];
  }, [bookings]);

  // Track if a fetch is in progress to prevent duplicate fetches
  const isFetchingRef = useRef<boolean>(false);

  // Function to refresh data
  const refreshData = useCallback(async () => {
    // Prevent duplicate fetches
    if (isFetchingRef.current) {
      console.log('[useOperationsData] Fetch already in progress, skipping duplicate fetch');
      return;
    }

    // Check if user is authenticated
    if (!userId) {
      console.log('[useOperationsData] No user ID, skipping fetch');
      return;
    }

    isFetchingRef.current = true;
    setIsLoading(true);
    setError(null);

    console.log('[useOperationsData] Refreshing data with filters:', {
      dateRange,
      department,
      propertyId,
      dateRangeFilter
    });

    try {
      // Re-fetch all data
      // Fetch maintenance tasks
      let tasksQuery = supabase
        .from('maintenance_tasks')
        .select('*')
        .gte('created_at', dateRangeFilter.startDate.toISOString())
        .lte('created_at', dateRangeFilter.endDate.toISOString());

      // Apply department filter
      if (department !== 'All departments') {
        // Map department to task type or category
        const taskType = department.toLowerCase();
        tasksQuery = tasksQuery.ilike('title', `%${taskType}%`);
      }

      // Apply property filter
      if (propertyId !== 'All properties') {
        tasksQuery = tasksQuery.eq('property_id', propertyId);
      }

      const { data: tasksData, error: tasksError } = await tasksQuery;

      if (tasksError) throw tasksError;

      setMaintenanceTasks(tasksData || []);
      console.log(`[useOperationsData] Loaded ${tasksData?.length || 0} maintenance tasks`);

      // Fetch damage reports
      let damageQuery = supabase
        .from('damage_reports')
        .select('*, properties(name)')
        .gte('created_at', dateRangeFilter.startDate.toISOString())
        .lte('created_at', dateRangeFilter.endDate.toISOString());

      // Apply property filter
      if (propertyId !== 'All properties') {
        damageQuery = damageQuery.eq('property_id', propertyId);
      }

      const { data: damageData, error: damageError } = await damageQuery;

      if (damageError) throw damageError;

      setDamageReports(damageData || []);
      console.log(`[useOperationsData] Loaded ${damageData?.length || 0} damage reports`);

      // Fetch bookings
      let bookingsQuery = supabase
        .from('bookings')
        .select('*, properties(name)')
        .gte('check_in_date', dateRangeFilter.startDate.toISOString().split('T')[0])
        .lte('check_out_date', dateRangeFilter.endDate.toISOString().split('T')[0]);

      // Apply property filter
      if (propertyId !== 'All properties') {
        bookingsQuery = bookingsQuery.eq('property_id', propertyId);
      }

      const { data: bookingsData, error: bookingsError } = await bookingsQuery;

      if (bookingsError) throw bookingsError;

      setBookings(bookingsData || []);
      console.log(`[useOperationsData] Loaded ${bookingsData?.length || 0} bookings`);

      // Dispatch a custom event to notify that operations data has been refreshed
      window.dispatchEvent(new CustomEvent('stayfu-operations-data-refreshed'));

    } catch (err: any) {
      console.error('Error refreshing operations data:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
      isFetchingRef.current = false;
    }
  }, [userId, dateRange, department, propertyId, dateRangeFilter]);

  // Add event listeners for all custom events that should trigger data refresh
  useEffect(() => {
    // Handler for the original operations data refreshed event
    const handleOperationsDataRefreshed = () => {
      console.log('[useOperationsData] Received stayfu-operations-data-refreshed event');
      if (!isFetchingRef.current) {
        refreshData();
      }
    };

    // Handler for route change events
    const handleRouteChanged = (event: CustomEvent) => {
      console.log('[useOperationsData] Received stayfu-route-changed event', event.detail);
      if (!isFetchingRef.current) {
        // If we're navigating to the operations page, refresh data
        if (event.detail?.to?.includes('/operations')) {
          console.log('[useOperationsData] Route changed to operations, refreshing data');
          refreshData();
        }
      }
    };

    // Handler for navigation events (from our custom navigation)
    const handleNavigationOccurred = (event: CustomEvent) => {
      console.log('[useOperationsData] Received stayfu-navigation-occurred event', event.detail);
      if (!isFetchingRef.current) {
        // If we're navigating to the operations page, refresh data
        if (event.detail?.to?.includes('/operations')) {
          console.log('[useOperationsData] Navigation occurred to operations, refreshing data');
          refreshData();
        }
      }
    };

    // REMOVED: Visibility change handler that refreshes data
    // This was causing data to disappear when the app comes back into focus

    // Handler for network online events - keeping this as it's useful
    const handleNetworkOnline = () => {
      console.log('[useOperationsData] Received stayfu-network-online event');
      if (!isFetchingRef.current && window.location.hash.includes('/operations')) {
        console.log('[useOperationsData] Network is back online while on operations page, refreshing data');
        refreshData();
      }
    };

    // Handler for general data refreshed events
    const handleDataRefreshed = (event: CustomEvent) => {
      console.log('[useOperationsData] Received stayfu-data-refreshed event', event.detail);
      if (!isFetchingRef.current && window.location.hash.includes('/operations')) {
        console.log('[useOperationsData] Data refreshed while on operations page, refreshing operations data');
        refreshData();
      }
    };

    // Listen for all custom events
    window.addEventListener('stayfu-operations-data-refreshed', handleOperationsDataRefreshed);
    window.addEventListener('stayfu-route-changed', handleRouteChanged as EventListener);
    // REMOVED: Visibility change event listener - causing data to disappear on refocus
    window.addEventListener('stayfu-network-online', handleNetworkOnline);
    window.addEventListener('stayfu-data-refreshed', handleDataRefreshed as EventListener);
    window.addEventListener('stayfu-navigation-occurred', handleNavigationOccurred as EventListener);

    // Set up cleanup function
    return () => {
      console.log('[useOperationsData] Component unmounting, cleaning up');
      window.removeEventListener('stayfu-operations-data-refreshed', handleOperationsDataRefreshed);
      window.removeEventListener('stayfu-route-changed', handleRouteChanged as EventListener);
      // REMOVED: Visibility change event listener - causing data to disappear on refocus
      window.removeEventListener('stayfu-network-online', handleNetworkOnline);
      window.removeEventListener('stayfu-data-refreshed', handleDataRefreshed as EventListener);
      window.removeEventListener('stayfu-navigation-occurred', handleNavigationOccurred as EventListener);
    };
  }, [refreshData]);

  return {
    metrics,
    tasksCompletedData,
    tasksByDayData,
    reportedIssuesData,
    timeReadyData,
    properties,
    isLoading,
    error,
    refreshData
  };
};
