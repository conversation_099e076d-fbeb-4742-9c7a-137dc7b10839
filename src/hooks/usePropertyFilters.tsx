
import { useState, useMemo } from 'react';
import { Property } from './useProperties';
import { PropertyStatisticsMap } from './usePropertyStatistics';

export interface FilterState {
  searchTerm?: string;
  city?: string;
  minBedrooms?: number;
  maxBedrooms?: number;
  minBathrooms?: number;
  maxBathrooms?: number;
  occupancyStatus?: 'all' | 'occupied' | 'vacant';
  maintenanceStatus?: 'all' | 'critical' | 'high' | 'none';
  damageStatus?: 'all' | 'open' | 'none';
  syncStatus?: 'all' | 'synced' | 'needs_sync' | 'no_calendar';
  inventoryStatus?: 'all' | 'low_stock' | 'out_of_stock' | 'good';
}

export const usePropertyFilters = (properties: Property[], statistics?: PropertyStatisticsMap) => {
  const [filters, setFilters] = useState<FilterState>({
    searchTerm: '',
    city: '',
    minBedrooms: 0,
    maxBedrooms: 10,
    minBathrooms: 0,
    maxBathrooms: 10,
    occupancyStatus: 'all',
    maintenanceStatus: 'all',
    damageStatus: 'all',
    syncStatus: 'all',
    inventoryStatus: 'all'
  });

  const filteredProperties = useMemo(() => {
    return properties.filter(property => {
      // Filter by search term
      if (filters.searchTerm && !property.name.toLowerCase().includes(filters.searchTerm.toLowerCase())) {
        return false;
      }

      // Filter by city
      if (filters.city && filters.city !== 'all_cities' && property.city !== filters.city) {
        return false;
      }

      // Filter by bedrooms
      if (
        (filters.minBedrooms !== undefined && property.bedrooms < filters.minBedrooms) ||
        (filters.maxBedrooms !== undefined && property.bedrooms > filters.maxBedrooms)
      ) {
        return false;
      }

      // Filter by bathrooms
      if (
        (filters.minBathrooms !== undefined && property.bathrooms < filters.minBathrooms) ||
        (filters.maxBathrooms !== undefined && property.bathrooms > filters.maxBathrooms)
      ) {
        return false;
      }

      // Get property statistics for advanced filtering
      const propertyStats = statistics?.[property.id];

      // Filter by occupancy status
      if (filters.occupancyStatus && filters.occupancyStatus !== 'all') {
        const isOccupied = property.is_occupied;
        if (filters.occupancyStatus === 'occupied' && !isOccupied) {
          return false;
        }
        if (filters.occupancyStatus === 'vacant' && isOccupied) {
          return false;
        }
      }

      // Filter by maintenance status
      if (filters.maintenanceStatus && filters.maintenanceStatus !== 'all' && propertyStats) {
        if (filters.maintenanceStatus === 'critical' && propertyStats.maintenanceTasks.critical === 0) {
          return false;
        }
        if (filters.maintenanceStatus === 'high' && propertyStats.maintenanceTasks.high === 0) {
          return false;
        }
        if (filters.maintenanceStatus === 'none' && propertyStats.maintenanceTasks.total > 0) {
          return false;
        }
      }

      // Filter by damage status
      if (filters.damageStatus && filters.damageStatus !== 'all' && propertyStats) {
        if (filters.damageStatus === 'open' && propertyStats.damageReports.open === 0) {
          return false;
        }
        if (filters.damageStatus === 'none' && propertyStats.damageReports.open > 0) {
          return false;
        }
      }

      // Filter by sync status
      if (filters.syncStatus && filters.syncStatus !== 'all') {
        const hasCalendar = !!property.ical_url;
        const lastSync = property.last_ical_sync;
        const isRecentSync = lastSync && new Date(lastSync) > new Date(Date.now() - 24 * 60 * 60 * 1000);

        if (filters.syncStatus === 'no_calendar' && hasCalendar) {
          return false;
        }
        if (filters.syncStatus === 'synced' && (!hasCalendar || !isRecentSync)) {
          return false;
        }
        if (filters.syncStatus === 'needs_sync' && (!hasCalendar || isRecentSync)) {
          return false;
        }
      }

      // Filter by inventory status
      if (filters.inventoryStatus && filters.inventoryStatus !== 'all' && propertyStats) {
        if (filters.inventoryStatus === 'out_of_stock' && propertyStats.inventoryItems.outOfStock === 0) {
          return false;
        }
        if (filters.inventoryStatus === 'low_stock' && propertyStats.inventoryItems.lowStock === 0) {
          return false;
        }
        if (filters.inventoryStatus === 'good' &&
            (propertyStats.inventoryItems.outOfStock > 0 || propertyStats.inventoryItems.lowStock > 0)) {
          return false;
        }
      }

      return true;
    });
  }, [properties, filters, statistics]);

  return {
    filters,
    setFilters,
    filteredProperties
  };
};
