import { useState, useEffect, useCallback } from 'react';
import { fetchWithCache, clearCache, checkSession, refreshAuthToken } from '@/utils/sessionUtils';
import { useAuth } from '@/contexts/AuthContext';

/**
 * Custom hook for fetching data with session management and caching
 * @param key - The key to cache the data under
 * @param fetchFunction - The function to fetch the data
 * @param dependencies - Dependencies to trigger refetch
 * @param expirationMinutes - How long to cache the data in minutes (default: 5)
 * @returns Object with data, loading state, error, and refetch function
 */
export function useSessionData<T>(
  key: string,
  fetchFunction: () => Promise<T>,
  dependencies: any[] = [],
  expirationMinutes = 5
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { authState } = useAuth();

  // Create a stable cache key that includes the user ID
  const cacheKey = `${key}:${authState.user?.id || 'anonymous'}`;

  // Function to fetch data
  const fetchData = useCallback(async (forceRefresh = false) => {
    if (!authState?.isAuthenticated && key !== 'public') {
      console.log('Not authenticated, skipping data fetch for:', key);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Check session validity before fetching
      if (key !== 'public') {
        const isSessionValid = await checkSession();
        if (!isSessionValid) {
          console.log(`Session invalid before fetching data for ${key}, attempting refresh...`);
          const refreshed = await refreshAuthToken();
          if (!refreshed) {
            console.error('Failed to refresh token, cannot fetch data');
            throw new Error('Session expired. Please refresh the page or log in again.');
          }
        }
      }

      console.log(`Fetching data for ${key}${forceRefresh ? ' (forced refresh)' : ''}...`);
      const result = await fetchWithCache<T>(
        cacheKey,
        fetchFunction,
        expirationMinutes,
        forceRefresh
      );

      setData(result);
      setError(null);
    } catch (err: any) {
      console.error(`Error fetching data for ${key}:`, err);

      // Handle network errors specifically
      if (err.message && err.message.includes('Failed to fetch')) {
        console.log(`Network error detected for ${key}, will retry on next render`);
        setError(new Error('Network connection issue. Please check your internet connection.'));
      } else if (err.message && (err.message.includes('JWT') || err.message.includes('session'))) {
        console.log(`Authentication error detected for ${key}`);
        setError(new Error('Authentication error. Please refresh the page or log in again.'));
      } else {
        setError(err instanceof Error ? err : new Error(String(err)));
      }

      // Don't clear existing data on error to allow for graceful degradation
    } finally {
      setLoading(false);
    }
  }, [cacheKey, fetchFunction, expirationMinutes, authState?.isAuthenticated, key]);

  // Refetch function that can be called manually
  const refetch = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);

  // Clear data when user changes
  useEffect(() => {
    if (key !== 'public') {
      setData(null);
    }
  }, [authState.user?.id, key]);

  // Fetch data on mount and when dependencies change
  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchData, ...dependencies]);

  // Clear cache when component unmounts
  useEffect(() => {
    return () => {
      // We don't clear the cache on unmount anymore to improve performance
      // between page navigations
    };
  }, []);

  return { data, loading, error, refetch };
}
