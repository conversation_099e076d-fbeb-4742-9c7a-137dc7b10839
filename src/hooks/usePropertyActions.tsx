
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Property } from './useProperties';
import { fetchCalendarData } from '@/utils/propertyUtils';
import { Property as CardProperty, CollectionWithBudget } from '@/components/properties/PropertyCard';

export const usePropertyActions = (
  userId: string | undefined,
  properties: Property[],
  setProperties: React.Dispatch<React.SetStateAction<Property[]>>,
  fetchProperties: () => Promise<void>
) => {
  const [isAddPropertyOpen, setIsAddPropertyOpen] = useState(false);
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editProperty, setEditProperty] = useState<CardProperty | null>(null);
  const [newProperty, setNewProperty] = useState<any>({
    name: '',
    address: '',
    city: '',
    state: '',
    zip: '',
    bedrooms: 1,
    bathrooms: 1,
    budget: 0,
    imageUrl: '',
    iCalUrl: '',
    collections: []
  });

  const handleAddProperty = async () => {
    try {
      if (!userId) {
        toast.error('You must be logged in to add a property');
        return;
      }

      // Process collections for database storage
      let collectionsToSave: any[] = [];
      
      if (newProperty.collections && Array.isArray(newProperty.collections)) {
        collectionsToSave = newProperty.collections.map(c => {
          if (typeof c === 'string') return c;
          if (c && typeof c === 'object' && 'name' in c) return c;
          return null;
        }).filter(Boolean);
      }
      
      console.log('Collections being saved:', collectionsToSave);

      // Map from component format to database format
      const dbProperty = {
        user_id: userId,
        name: newProperty.name,
        address: newProperty.address,
        city: newProperty.city,
        state: newProperty.state,
        zip: newProperty.zip,
        bedrooms: newProperty.bedrooms || 1,
        bathrooms: newProperty.bathrooms || 1,
        budget: newProperty.budget || 0,
        image_url: newProperty.imageUrl,
        ical_url: newProperty.iCalUrl,
        next_booking: newProperty.nextBooking,
        collections: collectionsToSave
      };
      
      console.log("Adding new property:", dbProperty);
      
      const { data, error } = await supabase
        .from('properties')
        .insert(dbProperty)
        .select()
        .single();
      
      if (error) {
        console.error("Supabase error:", error);
        throw error;
      }
      
      if (data) {
        console.log("Property added successfully:", data);
        
        // Process collections from the database for UI
        let uiCollections: CollectionWithBudget[] = [];
        
        if (data.collections) {
          if (Array.isArray(data.collections)) {
            uiCollections = data.collections.map((col: any) => {
              if (typeof col === 'string') {
                return { name: col };
              } else if (col && typeof col === 'object' && 'name' in col) {
                return col as CollectionWithBudget;
              }
              return { name: String(col) };
            });
          }
        }
        
        const formattedProperty: Property = {
          id: data.id,
          name: data.name,
          address: data.address,
          city: data.city,
          state: data.state,
          zip: data.zip,
          image_url: data.image_url,
          bedrooms: data.bedrooms || 1,
          bathrooms: data.bathrooms || 1,
          budget: data.budget || 0,
          ical_url: data.ical_url || '',
          next_booking: data.next_booking || '',
          collections: uiCollections,
          last_ical_sync: data.last_ical_sync || ''
        };
        
        setProperties([formattedProperty, ...properties]);
        toast.success('Property added successfully');
        setIsAddPropertyOpen(false);
        
        // Reset the new property form
        setNewProperty({
          name: '',
          address: '',
          city: '',
          state: '',
          zip: '',
          bedrooms: 1,
          bathrooms: 1,
          budget: 0,
          imageUrl: '',
          iCalUrl: '',
          collections: []
        });
        
        // If the property has an iCal URL, fetch calendar data
        if (data.ical_url && userId) {
          fetchCalendarData(data.ical_url, data.id, userId, fetchProperties);
        }
      }
    } catch (error: any) {
      console.error('Error adding property:', error);
      toast.error(`Failed to add property: ${error.message || 'Unknown error'}`);
    }
  };
  
  const handleUpdateProperty = async (propertyId: string, updatedData: Partial<Property>) => {
    try {
      console.log("Updating property with data:", updatedData);
      
      // Process collections for database storage
      let collectionsToSave: any[] = [];
      
      if (updatedData.collections) {
        if (Array.isArray(updatedData.collections)) {
          collectionsToSave = updatedData.collections.map(c => {
            if (typeof c === 'string') return c;
            if (c && typeof c === 'object' && 'name' in c) return c;
            return null;
          }).filter(Boolean);
        }
      }
      
      console.log('Collections to update:', collectionsToSave);
      
      // Ensure the database field names are used
      const dataToUpdate: any = {
        name: updatedData.name,
        address: updatedData.address,
        city: updatedData.city,
        state: updatedData.state,
        zip: updatedData.zip,
        image_url: updatedData.image_url,
        bedrooms: updatedData.bedrooms,
        bathrooms: updatedData.bathrooms,
        budget: updatedData.budget,
        ical_url: updatedData.ical_url,
        next_booking: updatedData.next_booking,
        collections: collectionsToSave
      };
      
      console.log("Data to update in Supabase:", dataToUpdate);
      
      const { error } = await supabase
        .from('properties')
        .update(dataToUpdate)
        .eq('id', propertyId);
      
      if (error) throw error;
      
      console.log("Property updated successfully");
      
      // Refresh the property list after update
      fetchProperties();
      
      // If the iCal URL was updated, fetch the calendar data
      if (userId && updatedData.ical_url && (!selectedProperty?.ical_url || updatedData.ical_url !== selectedProperty.ical_url)) {
        fetchCalendarData(updatedData.ical_url, propertyId, userId, fetchProperties);
      }
      
      toast.success('Property updated successfully');
      setIsDetailOpen(false);
    } catch (error: any) {
      console.error('Error updating property:', error);
      toast.error('Failed to update property');
    }
  };
  
  const handleDeleteProperty = async (propertyId: string) => {
    try {
      const { error } = await supabase
        .from('properties')
        .delete()
        .eq('id', propertyId);
      
      if (error) throw error;
      
      setProperties(properties.filter(property => property.id !== propertyId));
      toast.success('Property deleted successfully');
      setIsDetailOpen(false);
    } catch (error: any) {
      console.error('Error deleting property:', error);
      toast.error('Failed to delete property');
    }
  };
  
  const handleViewProperty = (property: Property) => {
    setSelectedProperty(property);
    setIsDetailOpen(true);
  };
  
  return {
    isAddPropertyOpen,
    setIsAddPropertyOpen,
    isDetailOpen,
    setIsDetailOpen,
    selectedProperty,
    setSelectedProperty,
    isEditMode,
    setIsEditMode,
    editProperty,
    setEditProperty,
    newProperty,
    setNewProperty,
    handleAddProperty,
    handleUpdateProperty,
    handleDeleteProperty,
    handleViewProperty
  };
};
