export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      backups: {
        Row: {
          backup_data: Json | null
          created_at: string | null
          id: number
          includes_auth: boolean
          includes_database: boolean
          includes_edge_functions: boolean
          includes_storage: boolean
          schema: Json | null
          status: string
          updated_at: string | null
        }
        Insert: {
          backup_data?: Json | null
          created_at?: string | null
          id?: never
          includes_auth: boolean
          includes_database: boolean
          includes_edge_functions: boolean
          includes_storage: boolean
          schema?: Json | null
          status: string
          updated_at?: string | null
        }
        Update: {
          backup_data?: Json | null
          created_at?: string | null
          id?: never
          includes_auth?: boolean
          includes_database?: boolean
          includes_edge_functions?: boolean
          includes_storage?: boolean
          schema?: Json | null
          status?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      bookings: {
        Row: {
          check_in_date: string
          check_out_date: string
          created_at: string | null
          id: string
          property_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          check_in_date: string
          check_out_date: string
          created_at?: string | null
          id?: string
          property_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          check_in_date?: string
          check_out_date?: string
          created_at?: string | null
          id?: string
          property_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "bookings_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      collections: {
        Row: {
          created_at: string
          id: string
          name: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "collections_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      damage_invoices: {
        Row: {
          created_at: string
          damage_report_id: string
          due_date: string | null
          file_name: string | null
          file_path: string | null
          file_url: string | null
          id: string
          invoice_number: string | null
          issue_date: string | null
          notes: string | null
          provider_id: string | null
          status: string
          total_amount: number | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          damage_report_id: string
          due_date?: string | null
          file_name?: string | null
          file_path?: string | null
          file_url?: string | null
          id?: string
          invoice_number?: string | null
          issue_date?: string | null
          notes?: string | null
          provider_id?: string | null
          status?: string
          total_amount?: number | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          damage_report_id?: string
          due_date?: string | null
          file_name?: string | null
          file_path?: string | null
          file_url?: string | null
          id?: string
          invoice_number?: string | null
          issue_date?: string | null
          notes?: string | null
          provider_id?: string | null
          status?: string
          total_amount?: number | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "damage_invoices_damage_report_id_fkey"
            columns: ["damage_report_id"]
            isOneToOne: false
            referencedRelation: "damage_reports"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "damage_invoices_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "maintenance_providers"
            referencedColumns: ["id"]
          },
        ]
      }
      damage_notes: {
        Row: {
          content: string
          created_at: string
          created_by: string | null
          damage_report_id: string
          id: string
          private: boolean
          updated_at: string
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string
          created_by?: string | null
          damage_report_id: string
          id?: string
          private?: boolean
          updated_at?: string
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string
          created_by?: string | null
          damage_report_id?: string
          id?: string
          private?: boolean
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "damage_notes_damage_report_id_fkey"
            columns: ["damage_report_id"]
            isOneToOne: false
            referencedRelation: "damage_reports"
            referencedColumns: ["id"]
          },
        ]
      }
      damage_photos: {
        Row: {
          caption: string | null
          created_at: string
          damage_report_id: string
          file_name: string
          file_path: string
          id: string
          media_type: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          caption?: string | null
          created_at?: string
          damage_report_id: string
          file_name: string
          file_path: string
          id?: string
          media_type?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          caption?: string | null
          created_at?: string
          damage_report_id?: string
          file_name?: string
          file_path?: string
          id?: string
          media_type?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "damage_photos_damage_report_id_fkey"
            columns: ["damage_report_id"]
            isOneToOne: false
            referencedRelation: "damage_reports"
            referencedColumns: ["id"]
          },
        ]
      }
      damage_reports: {
        Row: {
          created_at: string
          description: string
          id: string
          platform: string | null
          property_id: string
          provider_id: string | null
          status: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          description: string
          id?: string
          platform?: string | null
          property_id: string
          provider_id?: string | null
          status?: string
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          description?: string
          id?: string
          platform?: string | null
          property_id?: string
          provider_id?: string | null
          status?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "damage_reports_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "damage_reports_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "maintenance_providers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "damage_reports_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      extension_api_tokens: {
        Row: {
          created_at: string | null
          id: string
          last_used: string | null
          metadata: Json | null
          revoked_at: string | null
          token_hash: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          last_used?: string | null
          metadata?: Json | null
          revoked_at?: string | null
          token_hash: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          last_used?: string | null
          metadata?: Json | null
          revoked_at?: string | null
          token_hash?: string
          user_id?: string | null
        }
        Relationships: []
      }
      inventory_items: {
        Row: {
          amazon_url: string | null
          asin: string | null
          collection: string
          collection_id: string | null
          created_at: string
          id: string
          image_url: string | null
          min_quantity: number
          name: string
          price: number | null
          property_id: string
          quantity: number
          updated_at: string
          user_id: string
          walmart_item_id: string | null
          walmart_url: string | null
        }
        Insert: {
          amazon_url?: string | null
          asin?: string | null
          collection: string
          collection_id?: string | null
          created_at?: string
          id?: string
          image_url?: string | null
          min_quantity?: number
          name: string
          price?: number | null
          property_id: string
          quantity?: number
          updated_at?: string
          user_id: string
          walmart_item_id?: string | null
          walmart_url?: string | null
        }
        Update: {
          amazon_url?: string | null
          asin?: string | null
          collection?: string
          collection_id?: string | null
          created_at?: string
          id?: string
          image_url?: string | null
          min_quantity?: number
          name?: string
          price?: number | null
          property_id?: string
          quantity?: number
          updated_at?: string
          user_id?: string
          walmart_item_id?: string | null
          walmart_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "inventory_items_collection_id_fkey"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "collections"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_items_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_items_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      invitations: {
        Row: {
          created_at: string
          email: string
          expires_at: string
          id: string
          invited_by: string
          role: Database["public"]["Enums"]["user_role"]
          status: string
          team_id: string
          token: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          email: string
          expires_at: string
          id?: string
          invited_by: string
          role: Database["public"]["Enums"]["user_role"]
          status?: string
          team_id: string
          token: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          email?: string
          expires_at?: string
          id?: string
          invited_by?: string
          role?: Database["public"]["Enums"]["user_role"]
          status?: string
          team_id?: string
          token?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "invitations_invited_by_fkey"
            columns: ["invited_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      invoice_items: {
        Row: {
          amount: number
          created_at: string
          description: string
          id: string
          invoice_id: string
          quantity: number
          unit_price: number
          updated_at: string
        }
        Insert: {
          amount: number
          created_at?: string
          description: string
          id?: string
          invoice_id: string
          quantity: number
          unit_price: number
          updated_at?: string
        }
        Update: {
          amount?: number
          created_at?: string
          description?: string
          id?: string
          invoice_id?: string
          quantity?: number
          unit_price?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "invoice_items_invoice_id_fkey"
            columns: ["invoice_id"]
            isOneToOne: false
            referencedRelation: "damage_invoices"
            referencedColumns: ["id"]
          },
        ]
      }
      maintenance_providers: {
        Row: {
          created_at: string
          email: string | null
          id: string
          name: string
          notes: string | null
          phone: string | null
          specialty: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          email?: string | null
          id?: string
          name: string
          notes?: string | null
          phone?: string | null
          specialty?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          email?: string | null
          id?: string
          name?: string
          notes?: string | null
          phone?: string | null
          specialty?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "maintenance_providers_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      maintenance_requests: {
        Row: {
          created_at: string
          description: string
          id: string
          priority: string
          property_id: string
          provider_id: string | null
          status: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          description: string
          id?: string
          priority?: string
          property_id: string
          provider_id?: string | null
          status?: string
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          description?: string
          id?: string
          priority?: string
          property_id?: string
          provider_id?: string | null
          status?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "maintenance_requests_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "maintenance_requests_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      maintenance_tasks: {
        Row: {
          assigned_to: string | null
          created_at: string
          description: string | null
          due_date: string | null
          id: string
          property_id: string | null
          property_name: string
          provider_email: string | null
          provider_id: string | null
          severity: string
          status: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          assigned_to?: string | null
          created_at?: string
          description?: string | null
          due_date?: string | null
          id?: string
          property_id?: string | null
          property_name: string
          provider_email?: string | null
          provider_id?: string | null
          severity?: string
          status?: string
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          assigned_to?: string | null
          created_at?: string
          description?: string | null
          due_date?: string | null
          id?: string
          property_id?: string | null
          property_name?: string
          provider_email?: string | null
          provider_id?: string | null
          severity?: string
          status?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "maintenance_tasks_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          email: string
          first_name: string | null
          id: string
          is_super_admin: boolean
          last_name: string | null
          role: Database["public"]["Enums"]["user_role"]
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          email: string
          first_name?: string | null
          id: string
          is_super_admin?: boolean
          last_name?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          email?: string
          first_name?: string | null
          id?: string
          is_super_admin?: boolean
          last_name?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          updated_at?: string
        }
        Relationships: []
      }
      properties: {
        Row: {
          address: string
          bathrooms: number | null
          bedrooms: number | null
          budget: number | null
          city: string
          collections: Json | null
          created_at: string
          current_checkout: string | null
          ical_url: string | null
          id: string
          image_url: string | null
          is_occupied: boolean | null
          last_ical_sync: string | null
          name: string
          next_booking: string | null
          next_checkin_date: string | null
          next_checkin_formatted: string | null
          state: string
          updated_at: string
          user_id: string
          zip: string
        }
        Insert: {
          address: string
          bathrooms?: number | null
          bedrooms?: number | null
          budget?: number | null
          city: string
          collections?: Json | null
          created_at?: string
          current_checkout?: string | null
          ical_url?: string | null
          id?: string
          image_url?: string | null
          is_occupied?: boolean | null
          last_ical_sync?: string | null
          name: string
          next_booking?: string | null
          next_checkin_date?: string | null
          next_checkin_formatted?: string | null
          state: string
          updated_at?: string
          user_id: string
          zip: string
        }
        Update: {
          address?: string
          bathrooms?: number | null
          bedrooms?: number | null
          budget?: number | null
          city?: string
          collections?: Json | null
          created_at?: string
          current_checkout?: string | null
          ical_url?: string | null
          id?: string
          image_url?: string | null
          is_occupied?: boolean | null
          last_ical_sync?: string | null
          name?: string
          next_booking?: string | null
          next_checkin_date?: string | null
          next_checkin_formatted?: string | null
          state?: string
          updated_at?: string
          user_id?: string
          zip?: string
        }
        Relationships: [
          {
            foreignKeyName: "properties_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      purchase_order_items: {
        Row: {
          amazon_url: string | null
          created_at: string
          id: string
          inventory_item_id: string | null
          item_name: string
          price: number | null
          purchase_order_id: string
          quantity: number
          walmart_url: string | null
        }
        Insert: {
          amazon_url?: string | null
          created_at?: string
          id?: string
          inventory_item_id?: string | null
          item_name: string
          price?: number | null
          purchase_order_id: string
          quantity: number
          walmart_url?: string | null
        }
        Update: {
          amazon_url?: string | null
          created_at?: string
          id?: string
          inventory_item_id?: string | null
          item_name?: string
          price?: number | null
          purchase_order_id?: string
          quantity?: number
          walmart_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "purchase_order_items_inventory_item_id_fkey"
            columns: ["inventory_item_id"]
            isOneToOne: false
            referencedRelation: "inventory_items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_order_items_purchase_order_id_fkey"
            columns: ["purchase_order_id"]
            isOneToOne: false
            referencedRelation: "purchase_orders"
            referencedColumns: ["id"]
          },
        ]
      }
      purchase_orders: {
        Row: {
          created_at: string
          id: string
          is_archived: boolean
          notes: string | null
          property_id: string
          status: Database["public"]["Enums"]["po_status"]
          total_price: number | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          is_archived?: boolean
          notes?: string | null
          property_id: string
          status?: Database["public"]["Enums"]["po_status"]
          total_price?: number | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          is_archived?: boolean
          notes?: string | null
          property_id?: string
          status?: Database["public"]["Enums"]["po_status"]
          total_price?: number | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "purchase_orders_property_id_fkey"
            columns: ["property_id"]
            isOneToOne: false
            referencedRelation: "properties"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_orders_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      service_providers: {
        Row: {
          created_at: string | null
          email: string
          first_name: string | null
          id: string
          last_name: string | null
          status: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          first_name?: string | null
          id: string
          last_name?: string | null
          status?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          first_name?: string | null
          id?: string
          last_name?: string | null
          status?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      team_invitations: {
        Row: {
          created_at: string
          email: string
          expires_at: string
          id: string
          invited_by: string
          role: Database["public"]["Enums"]["user_role"]
          status: string
          team_id: string
          team_name: string | null
          token: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          email: string
          expires_at: string
          id?: string
          invited_by: string
          role: Database["public"]["Enums"]["user_role"]
          status?: string
          team_id: string
          team_name?: string | null
          token: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          email?: string
          expires_at?: string
          id?: string
          invited_by?: string
          role?: Database["public"]["Enums"]["user_role"]
          status?: string
          team_id?: string
          team_name?: string | null
          token?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_invitations_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      team_members: {
        Row: {
          added_by: string
          created_at: string
          id: string
          status: string
          team_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          added_by: string
          created_at?: string
          id?: string
          status?: string
          team_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          added_by?: string
          created_at?: string
          id?: string
          status?: string
          team_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_members_added_by_fkey"
            columns: ["added_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_members_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      teams: {
        Row: {
          created_at: string
          id: string
          name: string
          owner_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          owner_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          owner_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "teams_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_permissions: {
        Row: {
          created_at: string
          enabled: boolean
          id: string
          permission: Database["public"]["Enums"]["permission_type"]
          team_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          enabled?: boolean
          id?: string
          permission: Database["public"]["Enums"]["permission_type"]
          team_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          enabled?: boolean
          id?: string
          permission?: Database["public"]["Enums"]["permission_type"]
          team_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_permissions_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_permissions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_preferences: {
        Row: {
          created_at: string
          id: string
          onboarding_state: Json
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          onboarding_state?: Json
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          onboarding_state?: Json
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      user_settings: {
        Row: {
          animations: boolean
          compact_mode: boolean
          created_at: string
          dark_mode: boolean
          email_notifications: boolean
          id: string
          inventory_alerts: boolean
          push_notifications: boolean
          updated_at: string
          user_id: string
          weekly_summary: boolean
        }
        Insert: {
          animations?: boolean
          compact_mode?: boolean
          created_at?: string
          dark_mode?: boolean
          email_notifications?: boolean
          id?: string
          inventory_alerts?: boolean
          push_notifications?: boolean
          updated_at?: string
          user_id: string
          weekly_summary?: boolean
        }
        Update: {
          animations?: boolean
          compact_mode?: boolean
          created_at?: string
          dark_mode?: boolean
          email_notifications?: boolean
          id?: string
          inventory_alerts?: boolean
          push_notifications?: boolean
          updated_at?: string
          user_id?: string
          weekly_summary?: boolean
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      accept_team_invitation: {
        Args: {
          invitation_token: string
          accepting_user_id: string
        }
        Returns: boolean
      }
      accept_team_invitation_safe: {
        Args: {
          invitation_token: string
          accepting_user_id: string
        }
        Returns: boolean
      }
      can_manage_invitations: {
        Args: {
          target_team_id: string
        }
        Returns: boolean
      }
      can_manage_invitations_safe: {
        Args: {
          target_team_id: string
        }
        Returns: boolean
      }
      can_manage_permissions_safe: {
        Args: {
          target_team_id: string
        }
        Returns: boolean
      }
      can_manage_service_providers: {
        Args: {
          user_id: string
        }
        Returns: boolean
      }
      can_manage_staff: {
        Args: {
          user_id: string
        }
        Returns: boolean
      }
      can_manage_team_members: {
        Args: {
          target_team_id: string
        }
        Returns: boolean
      }
      create_team_invitation:
        | {
            Args: {
              p_team_id: string
              p_email: string
              p_role: Database["public"]["Enums"]["user_role"]
              p_invited_by: string
              p_token?: string
              p_expires_in?: unknown
            }
            Returns: string
          }
        | {
            Args: {
              p_team_id: string
              p_email: string
              p_role: string
              p_invited_by: string
              p_token?: string
              p_expires_in?: unknown
            }
            Returns: string
          }
        | {
            Args: {
              team_id: string
              email: string
              role: Database["public"]["Enums"]["user_role"]
              expires_in?: unknown
            }
            Returns: string
          }
        | {
            Args: {
              team_id: string
              email: string
              role: Database["public"]["Enums"]["user_role"]
              invitation_token: string
              expires_in?: unknown
            }
            Returns: string
          }
      get_all_tables: {
        Args: Record<PropertyKey, never>
        Returns: {
          tablename: string
        }[]
      }
      get_database_size: {
        Args: Record<PropertyKey, never>
        Returns: {
          size_bytes: string
          size_pretty: string
        }[]
      }
      get_functions: {
        Args: Record<PropertyKey, never>
        Returns: {
          function_name: string
        }[]
      }
      get_table_columns: {
        Args: {
          table_name: string
        }
        Returns: {
          column_name: string
          data_type: string
          is_nullable: boolean
          column_default: string
        }[]
      }
      get_table_size: {
        Args: {
          table_name: string
        }
        Returns: {
          size_bytes: string
          size_pretty: string
        }[]
      }
      get_table_structure: {
        Args: {
          table_name: string
        }
        Returns: {
          column_name: string
          data_type: string
          is_nullable: boolean
          column_default: string
        }[]
      }
      get_tables: {
        Args: Record<PropertyKey, never>
        Returns: {
          table_name: string
        }[]
      }
      get_user_role: {
        Args: {
          user_id: string
        }
        Returns: string
      }
      get_user_role_sd: {
        Args: {
          user_id: string
        }
        Returns: string
      }
      get_user_team_access: {
        Args: {
          team_id: string
        }
        Returns: boolean
      }
      get_user_team_ids: {
        Args: Record<PropertyKey, never>
        Returns: string[]
      }
      get_user_teams: {
        Args: {
          user_id: string
        }
        Returns: string[]
      }
      has_permission: {
        Args: {
          user_id: string
          permission_name: Database["public"]["Enums"]["permission_type"]
          team_id?: string
        }
        Returns: boolean
      }
      is_admin_user: {
        Args: {
          user_id: string
        }
        Returns: boolean
      }
      is_current_user_super_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_super_admin: {
        Args: {
          uid: string
        }
        Returns: boolean
      }
      is_super_admin_sd: {
        Args: {
          user_id: string
        }
        Returns: boolean
      }
      is_team_member: {
        Args: {
          team_id: string
          user_id: string
        }
        Returns: boolean
      }
      is_team_member_sd: {
        Args: {
          team_id: string
          user_id: string
        }
        Returns: boolean
      }
      is_team_owner: {
        Args: {
          team_id: string
          user_id: string
        }
        Returns: boolean
      }
      is_team_owner_sd: {
        Args: {
          team_id: string
          user_id: string
        }
        Returns: boolean
      }
      run_sql: {
        Args: {
          sql: string
        }
        Returns: undefined
      }
      user_can_access_team: {
        Args: {
          user_id: string
          team_id: string
        }
        Returns: boolean
      }
      user_can_access_team_members: {
        Args: {
          target_team_id: string
        }
        Returns: boolean
      }
      user_can_access_team_sd: {
        Args: {
          user_id: string
          team_id: string
        }
        Returns: boolean
      }
      user_is_team_member: {
        Args: {
          team_id: string
        }
        Returns: boolean
      }
      user_is_team_owner: {
        Args: {
          team_id: string
        }
        Returns: boolean
      }
      user_owns_team: {
        Args: {
          team_id: string
        }
        Returns: boolean
      }
      verify_backup: {
        Args: {
          backup_id: string
        }
        Returns: {
          is_valid: boolean
          tables_count: number
          rows_count: number
          issues: string[]
        }[]
      }
    }
    Enums: {
      permission_type:
        | "manage_properties"
        | "submit_damage_reports"
        | "manage_inventory"
        | "view_inventory"
        | "manage_staff"
        | "manage_service_providers"
        | "view_reports"
        | "edit_reports"
        | "admin_dashboard_access"
        | "impersonate_users"
        | "edit_user_data"
        | "add_users"
        | "delete_users"
        | "manage_subscriptions"
        | "admin"
      po_status: "pending" | "ordered" | "delivered" | "archived"
      user_role:
        | "super_admin"
        | "admin"
        | "property_manager"
        | "staff"
        | "service_provider"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
