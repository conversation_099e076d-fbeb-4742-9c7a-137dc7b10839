
import React from 'react';
import { useParams } from 'react-router-dom';

const CollectionDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Collection Details</h1>
      <p>Collection ID: {id}</p>
      {/* Collection details will be displayed here */}
    </div>
  );
};

export default CollectionDetail;
