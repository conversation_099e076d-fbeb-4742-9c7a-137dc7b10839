import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'sonner';

const Register: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { signUp, signIn } = useAuth();

  // Initialize email from location state if available
  useEffect(() => {
    const { state } = location;
    if (state?.email) {
      setEmail(state.email);
    }
  }, [location]);

  // Check for pending invitation in localStorage
  useEffect(() => {
    const pendingInvitation = localStorage.getItem('pendingInvitation');
    const pendingEmail = localStorage.getItem('pendingInvitationEmail');

    if (pendingEmail && !email) {
      console.log('Found pending invitation email:', pendingEmail);
      setEmail(pendingEmail);
    }
  }, [email]);

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Check if this registration is for a pending invitation
      const pendingInvitation = localStorage.getItem('pendingInvitation');
      const pendingEmail = localStorage.getItem('pendingInvitationEmail');

      // If the email matches a pending invitation, use the service_provider role
      const role = (pendingEmail && pendingEmail === email)
        ? 'service_provider'
        : (location.state?.role || 'property_manager');

      console.log('Registering with role:', role, 'for email:', email);
      const { error } = await signUp(email, password, firstName, lastName, role);
      if (error) throw error;

      console.log('Registration successful');

      // For service providers and invitation signups, we'll auto-login
      if (role === 'service_provider' || (pendingInvitation && pendingEmail === email)) {
        toast.success('Registration successful! Logging you in...');

        // Auto login after registration for service providers and invitation signups
        try {
          // Wait a moment for the Edge Function to complete
          await new Promise(resolve => setTimeout(resolve, 1000));

          const { error: loginError } = await signIn(email, password);
          if (loginError) {
            console.error('Auto-login failed:', loginError);
            // If auto-login fails, show a message but continue with the flow
            toast.error('Auto-login failed. Please try logging in manually.');

            // If this is an invitation, we'll still redirect to the invitation page
            if (pendingInvitation) {
              console.log('Redirecting to invitation page despite login failure');
              // We'll continue with the flow below
            } else {
              // For non-invitation service providers, redirect to login
              setTimeout(() => {
                navigate('/#/login', { state: { email } });
              }, 1500);
              return; // Exit early
            }
          } else {
            console.log('Auto-login successful');

            // If this is an invitation, we'll redirect to the dashboard
            if (pendingInvitation) {
              console.log('Redirecting to dashboard after successful login');
              navigate('/#/dashboard');
              return; // Exit early
            }
          }
        } catch (loginError) {
          console.error('Auto-login exception:', loginError);
          // Continue with the flow even if auto-login fails
          toast.error('Auto-login failed. Please try logging in manually.');
        }
      } else {
        // Email confirmation required for regular users
        toast.success('Registration successful! Please check your email to confirm your account.');
      }

      // If there's a pending invitation, redirect to the invitation page
      if (pendingInvitation && pendingEmail === email) {
        console.log('Redirecting to pending invitation:', pendingInvitation);
        // Get team_id from URL if available
        const urlParams = new URLSearchParams(window.location.search);
        const teamId = urlParams.get('team_id');
        navigate(`/invite?token=${pendingInvitation}${teamId ? `&team_id=${teamId}` : ''}`);

        // Clear localStorage after redirecting
        localStorage.removeItem('pendingInvitation');
        localStorage.removeItem('pendingInvitationEmail');
      }
      // If there's a return URL in the state, navigate there
      else if (location.state?.returnUrl) {
        // Fix for URLs that might be full URLs
        const returnUrl = location.state.returnUrl;
        console.log('Return URL:', returnUrl);

        // Check if it's a full URL or just a path
        if (returnUrl.startsWith('http')) {
          try {
            // Extract the path from the URL
            const url = new URL(returnUrl);
            const path = url.pathname + url.search + url.hash;
            console.log('Extracted path from URL:', path);
            navigate(path);
          } catch (error) {
            console.error('Error parsing return URL:', error);
            navigate('/dashboard');
          }
        } else {
          // It's already a path, just navigate to it
          navigate(returnUrl);
        }
      } else {
        navigate('/dashboard');
      }
    } catch (error) {
      console.error('Registration error:', error);
      toast.error('Registration failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">Create an Account</CardTitle>
          <CardDescription className="text-center">
            {location.state?.role === 'service_provider'
              ? 'Sign up to manage maintenance tasks and services'
              : 'Sign up to start managing your properties'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleRegister} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="firstName" className="text-sm font-medium">First Name</label>
                <Input
                  id="firstName"
                  placeholder="John"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="lastName" className="text-sm font-medium">Last Name</label>
                <Input
                  id="lastName"
                  placeholder="Doe"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium">Email</label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                readOnly={!!location.state?.email} // Make read-only if email is provided
              />
              {location.state?.email && (
                <p className="text-xs text-muted-foreground mt-1">
                  Email is pre-filled from the maintenance request
                </p>
              )}
            </div>
            <div className="space-y-2">
              <label htmlFor="password" className="text-sm font-medium">Password</label>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                minLength={8}
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? 'Creating account...' : 'Create account'}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex-col space-y-2">
          <div className="text-sm text-center">
            Already have an account?{' '}
            <Link to="/#/login" className="text-blue-600 hover:underline">
              Log in
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default Register;
