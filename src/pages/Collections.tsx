
import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '../contexts/AuthContext';
import PageTransition from '../components/layout/PageTransition';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';

interface Collection {
  id: string;
  name: string;
  propertyCount?: number;
}

const Collections: React.FC = () => {
  const { authState } = useAuth();
  const userId = authState.user?.id;
  
  const [collections, setCollections] = useState<Collection[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (userId) {
      fetchCollections();
    }
  }, [userId]);

  const fetchCollections = async () => {
    try {
      setLoading(true);
      
      // Fetch collections from Supabase
      const { data, error } = await supabase
        .from('collections')
        .select('*')
        .eq('user_id', userId);
      
      if (error) {
        throw error;
      }
      
      if (data) {
        // Transform the data to our Collection type
        const collections: Collection[] = data.map(item => ({
          id: item.id,
          name: item.name,
          propertyCount: 0 // We'll calculate this later
        }));
        
        // Fetch properties to count each collection's usage
        const { data: propertiesData, error: propertiesError } = await supabase
          .from('properties')
          .select('collections');
        
        if (propertiesError) {
          console.error('Error fetching properties for collection counts:', propertiesError);
        } else if (propertiesData) {
          // Count properties for each collection
          const collectionCounts: Record<string, number> = {};
          
          propertiesData.forEach(property => {
            if (property.collections && Array.isArray(property.collections)) {
              property.collections.forEach((collection: any) => {
                const collectionName = typeof collection === 'object' ? collection.name : collection;
                collectionCounts[collectionName] = (collectionCounts[collectionName] || 0) + 1;
              });
            }
          });
          
          // Update collection counts
          collections.forEach(collection => {
            collection.propertyCount = collectionCounts[collection.name] || 0;
          });
        }
        
        setCollections(collections);
      }
    } catch (error) {
      console.error('Error fetching collections:', error);
      toast.error('Failed to load collections');
    } finally {
      setLoading(false);
    }
  };

  const addCollection = async () => {
    const name = prompt('Enter collection name:');
    if (!name) return;
    
    try {
      // Insert collection into Supabase
      const { data, error } = await supabase
        .from('collections')
        .insert({
          user_id: userId,
          name,
        })
        .select()
        .single();
      
      if (error) {
        throw error;
      }
      
      // Add the new collection to state
      setCollections([
        ...collections,
        {
          id: data.id,
          name: data.name,
          propertyCount: 0
        }
      ]);
      
      toast.success(`Collection "${name}" added successfully`);
    } catch (error) {
      console.error('Error adding collection:', error);
      toast.error('Failed to add collection');
    }
  };

  return (
    <PageTransition>
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Property Collections</h1>
          <Button onClick={addCollection} className="flex items-center gap-2">
            <Plus size={16} />
            Add Collection
          </Button>
        </div>
        
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-32 w-full rounded-xl" />
            ))}
          </div>
        ) : collections.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {collections.map(collection => (
              <div key={collection.id} className="p-6 glass rounded-xl hover:shadow-md transition-shadow">
                <h3 className="text-xl font-semibold mb-2">{collection.name}</h3>
                <p className="text-muted-foreground">
                  {collection.propertyCount} {collection.propertyCount === 1 ? 'property' : 'properties'}
                </p>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center p-12 glass rounded-xl">
            <h3 className="text-xl font-medium text-muted-foreground mb-4">No collections found</h3>
            <p className="text-muted-foreground mb-6">
              Collections help you organize your properties into groups
            </p>
            <Button onClick={addCollection}>Create your first collection</Button>
          </div>
        )}
      </div>
    </PageTransition>
  );
};

export default Collections;
