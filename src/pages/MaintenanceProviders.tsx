import React, { useEffect, useState } from 'react';
import PageTransition from '@/components/layout/PageTransition';
import ProviderManagement from '@/components/maintenance/ProviderManagement';
import { Skeleton } from '@/components/ui/skeleton';
import { useProviders } from '@/hooks/useProviders';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';

const MaintenanceProviders: React.FC = () => {
  const { authState } = useAuth();
  const [retryCount, setRetryCount] = useState(0);
  
  const { 
    providers, 
    loading, 
    error,
    fetchProviders,
    addProvider, 
    updateProvider, 
    deleteProvider 
  } = useProviders();
  
  // Add debugging logs
  console.log("[MaintenanceProviders] Auth state:", authState);
  console.log("[MaintenanceProviders] Render with providers:", providers);
  
  const handleRetry = () => {
    console.log("[MaintenanceProviders] Manually retrying provider fetch");
    setRetryCount(prev => prev + 1);
    fetchProviders();
  };
  
  // Refetch providers when auth state changes or on manual retry
  useEffect(() => {
    if (authState.user?.id) {
      console.log("[MaintenanceProviders] Auth state changed or retry initiated, fetching providers");
      fetchProviders();
    }
  }, [authState.user?.id, fetchProviders, retryCount]);
  
  // Auto-retry if there's an error
  useEffect(() => {
    if (error && retryCount < 3) {
      console.error("[MaintenanceProviders] Error fetching providers, auto-retrying:", error);
      const timer = setTimeout(() => {
        setRetryCount(prev => prev + 1);
        fetchProviders();
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [error, retryCount, fetchProviders]);
  
  return (
    <PageTransition>
      <div className="container mx-auto px-4 py-8 mt-16 sm:mt-20">
        <h1 className="text-3xl font-bold mb-6">Maintenance Providers</h1>
        
        {loading ? (
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <Skeleton key={i} className="h-24 w-full rounded-lg" />
            ))}
          </div>
        ) : error ? (
          <div className="text-center p-8 border border-red-200 rounded-lg">
            <p className="text-destructive mb-4">Error loading providers: {typeof error === 'string' ? error : (error && typeof error === 'object' && 'message' in error) ? (error as {message: string}).message : "Unknown error"}</p>
            <Button 
              onClick={handleRetry}
              variant="outline"
              className="flex items-center gap-2"
            >
              <RefreshCw size={16} />
              Retry Loading Providers
            </Button>
          </div>
        ) : (
          <ProviderManagement
            providers={providers}
            onAddProvider={addProvider}
            onUpdateProvider={updateProvider}
            onDeleteProvider={deleteProvider}
          />
        )}
      </div>
    </PageTransition>
  );
};

export default MaintenanceProviders;
