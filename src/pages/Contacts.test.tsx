
import { render, screen } from '@/tests/utils/test-utils';
import Contacts from './Contacts';
import { BrowserRouter } from 'react-router-dom';

// Mock useContacts hook
jest.mock('@/hooks/useContacts', () => ({
  useContacts: jest.fn(() => ({
    contacts: [],
    loading: false,
    error: null,
    fetchContacts: jest.fn()
  }))
}));

describe('Contacts Page', () => {
  it('renders correctly with empty state', () => {
    render(
      <BrowserRouter>
        <Contacts />
      </BrowserRouter>
    );
    
    // Check that the header is present
    expect(screen.getByText('Contacts')).toBeInTheDocument();
    
    // Check for the add contact button
    expect(screen.getByRole('button', { name: /add contact/i })).toBeInTheDocument();
    
    // Check for empty state message
    expect(screen.getByText(/no contacts found/i)).toBeInTheDocument();
  });
  
  it('should have a link to add contacts', () => {
    render(
      <BrowserRouter>
        <Contacts />
      </BrowserRouter>
    );
    
    const addContactLink = screen.getByRole('link', { name: /add contact/i });
    expect(addContactLink).toBeInTheDocument();
    expect(addContactLink.getAttribute('href')).toBe('/contacts/add');
  });
});
