import React, { useState, useEffect, useRef } from 'react';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import PageTransition from '@/components/layout/PageTransition';
import DamageReportCard from '@/components/damages/DamageReportCard';
import AddDamageReportDialog from '@/components/damages/AddDamageReportDialog';
import DamageReportDetailDialog from '@/components/damages/DamageReportDetailDialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Plus, FilePlus2, Filter, Trash2, Search, RefreshCw, FileWarning } from 'lucide-react';
import { StandardPageHeader, SearchBar, FilterBar, FilterDropdown, FilterOption, StandardEmptyState } from '@/components/ui/StandardizedUI';
// import RefreshButton from '@/components/common/RefreshButton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Property, Provider } from '@/types/damages';
import { usePermissions } from '@/hooks/usePermissionsFixed';
import { useDamageReportsQueryV2, DamageReport } from '@/hooks/useDamageReportsQueryV2';

const Damages = () => {
  const navigate = useNavigate();
  const { authState } = useAuth();
  const userId = authState.user?.id;
  const { isAdmin, isOwner, userTeams, getAccessiblePropertyIds } = usePermissions();
  const { damageReports, loading: isLoading, error, fetchDamageReports: refreshDamageReports, addDamageReport, updateDamageReport, deleteDamageReport } = useDamageReportsQueryV2();

  // Debug log for damage reports
  useEffect(() => {
    console.log('Damages component - damageReports:', damageReports);
    console.log('Damages component - isLoading:', isLoading);
    console.log('Damages component - error:', error);
    console.log('Damages component - authState:', authState);
  }, [damageReports, isLoading, error, authState]);

  // State for UI
  const [properties, setProperties] = useState<Property[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [selectedReport, setSelectedReport] = useState<DamageReport | null>(null);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);

  // Track data loading state
  const [isLoadingProperties, setIsLoadingProperties] = useState(false);
  const [isLoadingProviders, setIsLoadingProviders] = useState(false);

  const fetchProperties = async () => {
    if (!userId) return;

    // Prevent duplicate fetches
    if (isLoadingProperties) return;

    try {
      setIsLoadingProperties(true);
      console.log('Damages - fetchProperties - Starting fetch');

      // Use the new RPC function to get all properties for the user
      const { data, error } = await supabase.rpc(
        'get_user_properties',
        { p_user_id: userId }
      );

      if (error) {
        console.error('Error fetching properties with RPC:', error);
        // Fall back to the old method if RPC fails
        fallbackFetchProperties();
        return;
      }

      if (data) {
        console.log('Damages - fetchProperties - Loaded', data.length, 'properties with RPC');
        setProperties(data);
      }
    } catch (error) {
      console.error('Error fetching properties:', error);
      toast.error('Failed to load properties');
      // Try fallback method
      fallbackFetchProperties();
    } finally {
      setIsLoadingProperties(false);
    }
  };

  // Fallback method if RPC fails
  const fallbackFetchProperties = async () => {
    try {
      console.log('Damages - fallbackFetchProperties - Starting fetch');

      let query = supabase
        .from('properties')
        .select('id, name, address, city, state');

      console.log('Damages - fallbackFetchProperties - isAdmin:', isAdmin());
      console.log('Damages - fallbackFetchProperties - isOwner:', isOwner());

      // If user is not an admin or owner, only show properties they have access to
      if (!isAdmin() && !isOwner()) {
        const accessiblePropertyIds = await getAccessiblePropertyIds();
        console.log('Damages - fallbackFetchProperties - accessiblePropertyIds:', accessiblePropertyIds);

        if (accessiblePropertyIds.length > 0) {
          query = query.in('id', accessiblePropertyIds);
        } else {
          // For property managers, show their properties
          query = query.eq('user_id', userId);
        }
      } else if (isAdmin()) {
        // Admins can see all properties
        console.log('Damages - fallbackFetchProperties - Admin fetching all properties');
      } else {
        // Property owners see their own properties
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query;

      if (error) throw error;

      if (data) {
        console.log('Damages - fallbackFetchProperties - Loaded', data.length, 'properties');
        setProperties(data);
      }
    } catch (error) {
      console.error('Error in fallback fetch properties:', error);
      toast.error('Failed to load properties');
    }
  };

  const fetchProviders = async () => {
    if (!userId) return;

    // Prevent duplicate fetches
    if (isLoadingProviders) return;

    try {
      setIsLoadingProviders(true);
      console.log('Damages - fetchProviders - Starting fetch');

      // Try our RPC function first
      try {
        const { data: rpcData, error: rpcError } = await supabase.rpc(
          'get_providers'
        );

        if (rpcError) {
          console.error('[Damages] Error fetching providers with RPC:', rpcError);
          // Fall back to direct query
        } else if (rpcData && rpcData.length > 0) {
          console.log('[Damages] Providers fetched with RPC:', rpcData.length);
          // Format the providers
          const formattedProviders = rpcData.map((provider: any) => ({
            id: provider.id,
            name: provider.name || '',
            email: provider.email || '',
            phone: provider.phone || '',
            specialty: provider.specialty || ''
          }));
          setProviders(formattedProviders);
          setIsLoadingProviders(false);
          return;
        }
      } catch (rpcException) {
        console.error('[Damages] Exception fetching providers with RPC:', rpcException);
      }

      // Fallback to direct query
      const { data, error } = await supabase
        .from('maintenance_providers')
        .select('id, name, email, phone, specialty')
        .eq('user_id', userId);

      if (error) throw error;

      if (data) {
        console.log('Damages - fetchProviders - Loaded', data.length, 'providers');
        setProviders(data);
      }
    } catch (error) {
      console.error('Error fetching providers:', error);
      toast.error('Failed to load maintenance providers');
    } finally {
      setIsLoadingProviders(false);
    }
  };

  // Single useEffect for all data loading
  useEffect(() => {
    // Only load data if we have a userId and are authenticated
    if (!userId || !authState?.isAuthenticated) return;

    console.log('Damages component - Initial data load');

    // Load data with a slight delay to avoid race conditions
    const timer = setTimeout(() => {
      console.log('Damages component - Loading data...');
      fetchProperties();
      fetchProviders();
      refreshDamageReports();
    }, 300);

    // Cleanup function
    return () => {
      clearTimeout(timer);
    };
  }, [userId, authState?.isAuthenticated, refreshDamageReports]);

  // Listen for the global data refresh event
  useEffect(() => {
    const handleDataRefreshed = (event: CustomEvent) => {
      console.log('[Damages] Received stayfu-data-refreshed event', event.detail);

      // Only refresh if this event is relevant to us
      const refreshedRoute = event.detail?.route || '';

      // If it's a global refresh or specifically for the damages route
      if (!refreshedRoute || refreshedRoute === '/damages' ||
          window.location.pathname.includes('/damages')) {
        console.log('[Damages] Refreshing data based on global refresh event');

        // Just refresh damage reports without showing toasts
        refreshDamageReports();
        fetchProperties();
        fetchProviders();
      }
    };

    // Add event listener for unified data refresh event
    window.addEventListener('stayfu-data-refreshed', handleDataRefreshed as EventListener);

    return () => {
      window.removeEventListener('stayfu-data-refreshed', handleDataRefreshed as EventListener);
    };
  }, [refreshDamageReports, fetchProperties, fetchProviders]);

  // No need for a cleanup effect anymore

  // This function is now handled by the useDamageReports hook
  // We're using refreshDamageReports instead

  const handleSaveDamageReport = async (report: Omit<DamageReport, 'id' | 'created_at' | 'updated_at' | 'property_name' | 'provider_name'>) => {
    try {
      const success = await addDamageReport(report);

      if (success) {
        setAddDialogOpen(false);
        toast.success('Damage report created successfully');
      }
    } catch (error) {
      console.error('Error creating damage report:', error);
      toast.error('Failed to create damage report');
      throw error;
    }
  };

  const handleUpdateReport = async (updatedReport: Partial<DamageReport>) => {
    if (!selectedReport) return;

    try {
      console.log("Damages - Updating report:", updatedReport);

      const success = await updateDamageReport(selectedReport.id, updatedReport);

      if (success) {
        // Update the selected report with the new data
        const propertyName = properties.find(p => p.id === updatedReport.property_id)?.name;
        const providerName = updatedReport.provider_id
          ? providers.find(p => p.id === updatedReport.provider_id)?.name
          : undefined;

        setSelectedReport(prev => prev ? {
          ...prev,
          ...updatedReport,
          property_name: propertyName || prev.property_name,
          provider_name: providerName
        } : null);

        toast.success('Damage report updated successfully');
      }
    } catch (error) {
      console.error('Error updating damage report:', error);
      toast.error('Failed to update damage report');
      throw error;
    }
  };

  const handleDeleteReport = async (id: string) => {
    try {
      const success = await deleteDamageReport(id);

      if (success) {
        if (selectedReport?.id === id) {
          setSelectedReport(null);
          setDetailDialogOpen(false);
        }

        toast.success('Damage report deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting damage report:', error);
      toast.error('Failed to delete damage report');
    }
  };

  const handleViewReport = (reportId: string) => {
    const report = damageReports.find(r => r.id === reportId);
    if (report) {
      setSelectedReport(report);
      setDetailDialogOpen(true);
    } else {
      toast.error('Report not found');
    }
  };

  const filteredReports = damageReports.filter(report => {
    const matchesSearch =
      report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.property_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.provider_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.platform?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || report.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  return (
    <PageTransition>
      <div className="container mx-auto px-4 pt-8 pb-12 max-w-7xl">
        <StandardPageHeader
          title="Damage Reports"
          description="Document and track damage to your properties"
          searchQuery={searchTerm}
          onSearchChange={setSearchTerm}
          searchPlaceholder="Search damage reports..."
          onRefresh={async () => {
            try {
              console.log('[Damages] Manual refresh requested');
              toast.loading('Refreshing damage reports...', { id: 'damages-refresh-toast' });

              // Simplified sequential refresh approach to prevent race conditions
              // First refresh damage reports
              await refreshDamageReports();

              // Then fetch properties and providers
              await fetchProperties();
              await fetchProviders();

              toast.success('Damage reports refreshed successfully', { id: 'damages-refresh-toast' });

              // Global refresh functionality has been removed
              console.log('[Damages] Global refresh functionality has been removed');
            } catch (error) {
              console.error('[Damages] Error refreshing data:', error);
              toast.error('Failed to refresh damage reports', { id: 'damages-refresh-toast' });
            }
          }}
          isLoading={isLoading}
          primaryActionLabel="New Damage Report"
          onPrimaryAction={() => setAddDialogOpen(true)}
          primaryActionIcon={<Plus className="h-4 w-4 mr-2" />}
        />

        <FilterBar className="mb-6">
          <FilterDropdown
            label="Status"
            value={statusFilter}
            onChange={setStatusFilter}
          >
            <FilterOption value="all">All Statuses</FilterOption>
            <FilterOption value="open">Open</FilterOption>
            <FilterOption value="pending">Pending</FilterOption>
            <FilterOption value="in_progress">In Progress</FilterOption>
            <FilterOption value="completed">Completed</FilterOption>
            <FilterOption value="rejected">Rejected</FilterOption>
          </FilterDropdown>
        </FilterBar>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {isLoading ? (
            [...Array(6)].map((_, index) => (
              <Skeleton key={index} className="h-48 rounded-lg" />
            ))
          ) : filteredReports.length > 0 ? (
            filteredReports.map(report => (
              <DamageReportCard
                key={report.id}
                report={report}
                onViewDetails={handleViewReport}
                onDelete={() => handleDeleteReport(report.id)}
              />
            ))
          ) : (
            <div className="col-span-full">
              <StandardEmptyState
                title="No Damage Reports Found"
                description={searchTerm || statusFilter !== 'all'
                  ? 'No reports match your search criteria. Try adjusting your filters.'
                  : 'You haven\'t created any damage reports yet. Create your first report to start documenting property damages.'}
                icon={<FileWarning className="h-12 w-12 text-muted-foreground" />}
                actionLabel="Create First Report"
                onAction={() => setAddDialogOpen(true)}
                actionIcon={<Plus className="h-4 w-4 mr-2" />}
              />
            </div>
          )}
        </div>
      </div>

      <AddDamageReportDialog
        open={addDialogOpen}
        onOpenChange={setAddDialogOpen}
        onSave={handleSaveDamageReport}
        properties={properties}
        providers={providers}
      />

      {selectedReport && (
        <DamageReportDetailDialog
          open={detailDialogOpen}
          setOpen={setDetailDialogOpen}
          damageReportId={selectedReport.id}
        />
      )}
    </PageTransition>
  );
};

export default Damages;
