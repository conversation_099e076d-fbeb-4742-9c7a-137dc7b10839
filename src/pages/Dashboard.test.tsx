import { render, screen } from '@testing-library/react';
import { useDashboardData } from '../hooks/useDashboardData';
import Dashboard from './Dashboard';

jest.mock('../hooks/useDashboardData');

const mockDashboardData = {
  properties: 5,
  activeMaintenanceTasks: 3,
  pendingInvoices: 2
};

describe('Dashboard', () => {
  beforeEach(() => {
    (useDashboardData as jest.Mock).mockReturnValue({
      data: mockDashboardData,
      loading: false,
      error: null
    });
  });

  it('renders dashboard stats', () => {
    render(<Dashboard />);
    
    expect(screen.getByText('5')).toBeInTheDocument(); // Properties
    expect(screen.getByText('3')).toBeInTheDocument(); // Active Tasks
    expect(screen.getByText('2')).toBeInTheDocument(); // Pending Invoices
  });

  it('shows loading state', () => {
    (useDashboardData as jest.Mock).mockReturnValue({
      data: null,
      loading: true,
      error: null
    });

    render(<Dashboard />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });
});
