import React, { useState } from 'react';
import PageTransition from '../components/layout/PageTransition';
import { motion } from 'framer-motion';
import { 
  PlusCircle, 
  Package, 
  Wrench, 
  AlertCircle, 
  Building2, 
  Check,
  Upload,
  X
} from 'lucide-react';

interface EntryCategory {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  color: string;
}

const entryCategories: EntryCategory[] = [
  {
    id: 'inventory',
    title: 'Inventory Item',
    description: 'Add a new item to your property inventory',
    icon: Package,
    color: 'bg-blue-500',
  },
  {
    id: 'maintenance',
    title: 'Maintenance Task',
    description: 'Create a new maintenance task for a property',
    icon: Wrench,
    color: 'bg-amber-500',
  },
  {
    id: 'damage',
    title: 'Damage Report',
    description: 'Report damage at one of your properties',
    icon: AlertCircle,
    color: 'bg-red-500',
  },
  {
    id: 'property',
    title: 'Property',
    description: 'Add a new property to your portfolio',
    icon: Building2,
    color: 'bg-green-500',
  },
];

const mockProperties = [
  { id: '1', name: 'Oceanview Villa' },
  { id: '2', name: 'Downtown Loft' },
  { id: '3', name: 'Mountain Retreat' },
  { id: '4', name: 'Lakefront Cabin' },
  { id: '5', name: 'Desert Oasis' },
];

const mockCollections = [
  { id: '1', name: 'Bathroom' },
  { id: '2', name: 'Kitchen' },
  { id: '3', name: 'Living Room' },
  { id: '4', name: 'Bedroom' },
  { id: '5', name: 'Outdoor' },
];

const mockSeverityOptions = [
  { id: 'low', name: 'Low' },
  { id: 'medium', name: 'Medium' },
  { id: 'high', name: 'High' },
  { id: 'critical', name: 'Critical' },
];

const AddEntry = () => {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [formValues, setFormValues] = useState({
    title: '',
    description: '',
    propertyId: '',
    collectionId: '',
    quantity: '1',
    minQuantity: '1',
    price: '',
    amazonUrl: '',
    severity: 'medium',
  });
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  const handleImageDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      const reader = new FileReader();
      
      reader.onload = (event) => {
        if (event.target && typeof event.target.result === 'string') {
          setSelectedImage(event.target.result);
        }
      };
      
      reader.readAsDataURL(file);
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const reader = new FileReader();
      
      reader.onload = (event) => {
        if (event.target && typeof event.target.result === 'string') {
          setSelectedImage(event.target.result);
        }
      };
      
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      console.log('Form submitted:', {
        category: selectedCategory,
        ...formValues,
        image: selectedImage ? 'Image data included' : null,
      });
      
      setIsSubmitting(false);
      setIsSuccess(true);
      
      // Reset form after a delay
      setTimeout(() => {
        setSelectedCategory(null);
        setFormValues({
          title: '',
          description: '',
          propertyId: '',
          collectionId: '',
          quantity: '1',
          minQuantity: '1',
          price: '',
          amazonUrl: '',
          severity: 'medium',
        });
        setSelectedImage(null);
        setIsSuccess(false);
      }, 2000);
    }, 1500);
  };

  return (
    <PageTransition>
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-24 pb-32">
        <div className="mb-8">
          <h1 className="text-4xl font-bold tracking-tight mb-2">Add New Entry</h1>
          <p className="text-muted-foreground">
            Create a new entry for your properties
          </p>
        </div>
        
        {!selectedCategory ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {entryCategories.map((category) => (
              <motion.div
                key={category.id}
                className="glass p-6 rounded-xl cursor-pointer hover:shadow-md transition-all"
                whileHover={{ y: -5 }}
                onClick={() => setSelectedCategory(category.id)}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <div className={`w-12 h-12 rounded-full flex items-center justify-center mb-4 ${category.color}`}>
                  <category.icon size={20} className="text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2">{category.title}</h3>
                <p className="text-muted-foreground">{category.description}</p>
              </motion.div>
            ))}
          </div>
        ) : isSuccess ? (
          <motion.div
            className="glass p-8 rounded-xl text-center"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Check size={32} className="text-green-600" />
            </div>
            <h2 className="text-2xl font-semibold mb-2">Entry Created Successfully!</h2>
            <p className="text-muted-foreground mb-6">
              Your new {entryCategories.find(c => c.id === selectedCategory)?.title.toLowerCase()} has been added to your account.
            </p>
            <button 
              className="bg-primary text-white px-6 py-2 rounded-lg font-medium inline-flex items-center justify-center"
              onClick={() => setSelectedCategory(null)}
            >
              <PlusCircle size={18} className="mr-2" />
              Add Another Entry
            </button>
          </motion.div>
        ) : (
          <motion.div
            key={selectedCategory}
            className="glass p-6 rounded-xl"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <div className="flex items-center justify-between mb-6 pb-4 border-b border-border">
              <div className="flex items-center">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 ${entryCategories.find(c => c.id === selectedCategory)?.color}`}>
                  {React.createElement(
                    entryCategories.find(c => c.id === selectedCategory)?.icon || 'div',
                    { size: 18, className: "text-white" }
                  )}
                </div>
                <div>
                  <h2 className="text-xl font-semibold">{entryCategories.find(c => c.id === selectedCategory)?.title}</h2>
                  <p className="text-sm text-muted-foreground">{entryCategories.find(c => c.id === selectedCategory)?.description}</p>
                </div>
              </div>
              <button 
                className="text-muted-foreground hover:text-foreground"
                onClick={() => setSelectedCategory(null)}
              >
                <X size={20} />
              </button>
            </div>
            
            <form onSubmit={handleSubmit}>
              <div className="space-y-5">
                <div>
                  <label htmlFor="propertyId" className="block text-sm font-medium mb-1">
                    Property
                  </label>
                  <select
                    id="propertyId"
                    name="propertyId"
                    value={formValues.propertyId}
                    onChange={handleChange}
                    className="w-full p-2 rounded-lg glass border border-border focus:outline-none focus:ring-2 focus:ring-primary/30"
                    required
                  >
                    <option value="">Select a property</option>
                    {mockProperties.map(property => (
                      <option key={property.id} value={property.id}>
                        {property.name}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label htmlFor="title" className="block text-sm font-medium mb-1">
                    {selectedCategory === 'inventory' ? 'Item Name' : 'Title'}
                  </label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    value={formValues.title}
                    onChange={handleChange}
                    className="w-full p-2 rounded-lg glass border border-border focus:outline-none focus:ring-2 focus:ring-primary/30"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="description" className="block text-sm font-medium mb-1">
                    Description
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={formValues.description}
                    onChange={handleChange}
                    rows={3}
                    className="w-full p-2 rounded-lg glass border border-border focus:outline-none focus:ring-2 focus:ring-primary/30"
                  />
                </div>
                
                {selectedCategory === 'inventory' && (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="collectionId" className="block text-sm font-medium mb-1">
                          Collection
                        </label>
                        <select
                          id="collectionId"
                          name="collectionId"
                          value={formValues.collectionId}
                          onChange={handleChange}
                          className="w-full p-2 rounded-lg glass border border-border focus:outline-none focus:ring-2 focus:ring-primary/30"
                          required
                        >
                          <option value="">Select a collection</option>
                          {mockCollections.map(collection => (
                            <option key={collection.id} value={collection.id}>
                              {collection.name}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label htmlFor="price" className="block text-sm font-medium mb-1">
                          Price
                        </label>
                        <div className="relative">
                          <span className="absolute left-3 top-2.5">$</span>
                          <input
                            type="number"
                            id="price"
                            name="price"
                            value={formValues.price}
                            onChange={handleChange}
                            className="w-full pl-7 p-2 rounded-lg glass border border-border focus:outline-none focus:ring-2 focus:ring-primary/30"
                            placeholder="0.00"
                            step="0.01"
                            min="0"
                          />
                        </div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="quantity" className="block text-sm font-medium mb-1">
                          Quantity
                        </label>
                        <input
                          type="number"
                          id="quantity"
                          name="quantity"
                          value={formValues.quantity}
                          onChange={handleChange}
                          className="w-full p-2 rounded-lg glass border border-border focus:outline-none focus:ring-2 focus:ring-primary/30"
                          min="1"
                          required
                        />
                      </div>
                      <div>
                        <label htmlFor="minQuantity" className="block text-sm font-medium mb-1">
                          Minimum Quantity
                        </label>
                        <input
                          type="number"
                          id="minQuantity"
                          name="minQuantity"
                          value={formValues.minQuantity}
                          onChange={handleChange}
                          className="w-full p-2 rounded-lg glass border border-border focus:outline-none focus:ring-2 focus:ring-primary/30"
                          min="1"
                          required
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="amazonUrl" className="block text-sm font-medium mb-1">
                        Amazon URL (optional)
                      </label>
                      <input
                        type="url"
                        id="amazonUrl"
                        name="amazonUrl"
                        value={formValues.amazonUrl}
                        onChange={handleChange}
                        className="w-full p-2 rounded-lg glass border border-border focus:outline-none focus:ring-2 focus:ring-primary/30"
                        placeholder="https://amazon.com/..."
                      />
                    </div>
                  </>
                )}
                
                {selectedCategory === 'maintenance' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="severity" className="block text-sm font-medium mb-1">
                        Severity
                      </label>
                      <select
                        id="severity"
                        name="severity"
                        value={formValues.severity}
                        onChange={handleChange}
                        className="w-full p-2 rounded-lg glass border border-border focus:outline-none focus:ring-2 focus:ring-primary/30"
                        required
                      >
                        {mockSeverityOptions.map(option => (
                          <option key={option.id} value={option.id}>
                            {option.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label htmlFor="dueDate" className="block text-sm font-medium mb-1">
                        Due Date
                      </label>
                      <input
                        type="date"
                        id="dueDate"
                        name="dueDate"
                        className="w-full p-2 rounded-lg glass border border-border focus:outline-none focus:ring-2 focus:ring-primary/30"
                        required
                      />
                    </div>
                  </div>
                )}
                
                {(selectedCategory === 'damage' || selectedCategory === 'inventory' || selectedCategory === 'property') && (
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Image {selectedCategory === 'damage' ? '(recommended)' : '(optional)'}
                    </label>
                    <div
                      className={`border-2 border-dashed rounded-lg p-4 text-center ${
                        isDragging ? 'border-primary bg-primary/5' : 'border-border'
                      }`}
                      onDragOver={(e) => {
                        e.preventDefault();
                        setIsDragging(true);
                      }}
                      onDragLeave={() => setIsDragging(false)}
                      onDrop={handleImageDrop}
                    >
                      {selectedImage ? (
                        <div className="relative">
                          <img
                            src={selectedImage}
                            alt="Selected"
                            className="max-h-48 mx-auto rounded"
                          />
                          <button
                            type="button"
                            className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1"
                            onClick={() => setSelectedImage(null)}
                          >
                            <X size={16} />
                          </button>
                        </div>
                      ) : (
                        <div className="py-4">
                          <Upload size={32} className="mx-auto text-muted-foreground mb-2" />
                          <p className="text-sm text-muted-foreground mb-2">
                            Drag and drop an image here, or click to select
                          </p>
                          <input
                            type="file"
                            accept="image/*"
                            className="hidden"
                            id="image-upload"
                            onChange={handleImageChange}
                          />
                          <label
                            htmlFor="image-upload"
                            className="bg-primary/10 text-primary px-4 py-2 rounded cursor-pointer text-sm inline-block"
                          >
                            Select Image
                          </label>
                        </div>
                      )}
                    </div>
                  </div>
                )}
                
                <div className="pt-4">
                  <button
                    type="submit"
                    className="w-full bg-primary text-white px-4 py-2 rounded-lg font-medium flex items-center justify-center"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="h-5 w-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Check size={18} className="mr-2" />
                        Create {entryCategories.find(c => c.id === selectedCategory)?.title}
                      </>
                    )}
                  </button>
                </div>
              </div>
            </form>
          </motion.div>
        )}
      </div>
    </PageTransition>
  );
};

export default AddEntry;
