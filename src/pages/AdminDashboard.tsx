import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Database, Shield, Settings, Users, ArrowRight, AlertCircle } from 'lucide-react';
import AdminErrorState from '@/components/admin/AdminErrorState';
import UsersTab from '@/components/admin/UsersTab';
import DatabaseTab from '@/components/admin/DatabaseTab';
import SettingsTab from '@/components/admin/SettingsTab';
import { UserProfile } from '@/types/auth';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import useAdmin from '@/hooks/useAdmin';

const AdminDashboard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { authState, startImpersonation } = useAuth();
  const { fetchUsers, impersonateUser, isLoading, error, isSuperAdmin } = useAdmin();

  const [tab, setTab] = useState<string>('users');
  const [isImpersonating, setIsImpersonating] = useState<boolean>(false);
  const [usersData, setUsersData] = useState<UserProfile[]>([]);
  const [isUsersRefreshing, setIsUsersRefreshing] = useState<boolean>(false);

  useEffect(() => {
    // Check if user is admin
    if (!authState?.isLoading && !authState?.profile?.is_super_admin) {
      toast({
        title: "Access Denied",
        description: "You don't have permission to access this page.",
        variant: "destructive"
      });
      navigate('/dashboard');
    }
  }, [authState?.isLoading, authState?.profile?.is_super_admin, navigate, toast]);

  // Function to refresh users data
  const refreshUsers = async (isRefresh = false) => {
    if (isRefresh) {
      setIsUsersRefreshing(true);
    }

    try {
      const users = await fetchUsers();
      setUsersData(users);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setIsUsersRefreshing(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    if (authState?.profile?.is_super_admin) {
      refreshUsers();
    }
  }, [authState?.profile?.is_super_admin]);

  if (authState?.isLoading) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  if (!authState?.profile?.is_super_admin) {
    return <AdminErrorState
      error="You don't have permission to access the admin dashboard."
      isRefreshing={false}
      onRefresh={async () => {
        window.location.reload();
      }}
    />;
  }

  const handleImpersonateUser = async (userId: string) => {
    setIsImpersonating(true);
    try {
      console.log('AdminDashboard: Starting impersonation for user:', userId);

      // First try our useAdmin hook's impersonateUser function
      const success = await impersonateUser(userId);

      if (success) {
        toast({
          title: "Impersonation Started",
          description: "You are now impersonating another user. Click 'Exit Impersonation' in the top navbar to return to your admin account.",
        });
        navigate('/dashboard');
        return;
      }

      // Fall back to the AuthContext method if our hook fails
      await startImpersonation(userId);

      // Wait a moment for the state to update
      await new Promise(resolve => setTimeout(resolve, 500));

      // Check if impersonation was successful
      if (authState?.isImpersonating) {
        toast({
          title: "Impersonation Started",
          description: "You are now impersonating another user. Click 'Exit Impersonation' in the top navbar to return to your admin account.",
        });
        navigate('/dashboard');
      } else {
        console.error('Impersonation did not set isImpersonating state');
        toast({
          title: "Impersonation Failed",
          description: "Failed to impersonate user. Please try again.",
          variant: "destructive"
        });
      }
    } catch (error: any) {
      console.error('Error impersonating user:', error);
      toast({
        title: "Impersonation Failed",
        description: error.message || "An error occurred while impersonating user.",
        variant: "destructive"
      });
    } finally {
      setIsImpersonating(false);
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        <Button variant="outline" onClick={() => navigate('/dashboard')}>
          Back to Dashboard <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Admin Access</AlertTitle>
        <AlertDescription>
          You have super admin privileges. Be careful with the actions you take here as they can affect the entire system.
        </AlertDescription>
      </Alert>

      <Card>
        <Tabs value={tab} onValueChange={setTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="users" className="flex items-center">
              <Users className="mr-2 h-4 w-4" /> Users
            </TabsTrigger>
            <TabsTrigger value="database" className="flex items-center">
              <Database className="mr-2 h-4 w-4" /> Database
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center">
              <Shield className="mr-2 h-4 w-4" /> Security
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center">
              <Settings className="mr-2 h-4 w-4" /> Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="users" className="p-4">
            <UsersTab
              users={usersData}
              isLoading={isLoading}
              isRefreshing={isUsersRefreshing}
              onRefresh={() => refreshUsers(true)}
              onImpersonate={handleImpersonateUser}
              currentUserId={authState?.user?.id}
              onUserUpdated={() => refreshUsers(true)}
            />
          </TabsContent>

          <TabsContent value="database" className="p-4">
            <DatabaseTab />
          </TabsContent>

          <TabsContent value="security" className="p-4">
            <h2 className="text-2xl font-bold mb-4">Security Settings</h2>
            <p className="text-muted-foreground">Security settings and audit logs will be available here.</p>
          </TabsContent>

          <TabsContent value="settings" className="p-4">
            <SettingsTab />
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
};

export default AdminDashboard;
