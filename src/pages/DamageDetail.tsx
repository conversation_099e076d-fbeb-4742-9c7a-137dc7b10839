
import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDamageDetail } from '@/hooks/useDamageDetail';
import OverviewTab from '@/components/damages/detail-tabs/OverviewTab';
import PhotosTab from '@/components/damages/detail-tabs/photos/PhotosTab';
import NotesTab from '@/components/damages/detail-tabs/NotesTab';
import InvoiceTab from '@/components/damages/detail-tabs/InvoiceTab';
import GenerateReportsTab from '@/components/damages/detail-tabs/GenerateReportsTab';
import DamageDetailHeader from '@/components/damages/detail/DamageDetailHeader';
import DamageDetailSkeleton from '@/components/damages/detail/DamageDetailSkeleton';
import DamageNotFound from '@/components/damages/detail/DamageNotFound';
import { generateDamageReportPdf } from '@/components/damages/GenerateDamageReportPdf';
import { toast } from 'sonner';

const DamageDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [isExporting, setIsExporting] = useState(false);
  
  const {
    report,
    properties,
    providers,
    isLoading,
    property,
    photos,
    notes,
    invoices,
    handleUpdateReport,
    refreshPhotos
  } = useDamageDetail(id);

  const handleDownloadReport = async (options: {
    includePhotos: boolean;
    includeNotes: boolean;
    includeInvoices: boolean;
  }) => {
    if (!report || !id) return;
    
    try {
      setIsExporting(true);
      toast.info('Generating report, please wait...');
      
      // Find the provider if assigned
      const provider = report.provider_id
        ? providers.find(p => p.id === report.provider_id)
        : null;
      
      console.log("Generating PDF with:", {
        photosCount: photos.length,
        notesCount: notes.length,
        invoicesCount: invoices.length,
        options
      });
      
      // Force refresh photos to ensure we have the latest URLs
      await refreshPhotos();
      
      // Debug logs to check the data being passed
      console.log("Photos being passed to PDF generator:", photos);
      console.log("Photos URLs available:", photos.map(p => p.url || 'No URL'));
      console.log("Notes being passed to PDF generator:", notes);
      console.log("Invoices being passed to PDF generator:", invoices);
      
      await generateDamageReportPdf(
        report,
        options.includePhotos ? photos : [],
        options.includeNotes ? notes : [],
        options.includeInvoices ? invoices : [],
        property || undefined,
        provider || undefined,
        options
      );
      
      toast.success('Report generated successfully');
    } catch (error) {
      console.error('Error exporting PDF:', error);
      toast.error('Failed to generate report');
    } finally {
      setIsExporting(false);
    }
  };

  if (isLoading) {
    return <DamageDetailSkeleton />;
  }

  if (!report) {
    return <DamageNotFound />;
  }

  return (
    <div className="space-y-6">
      <DamageDetailHeader
        id={id || ''}
        onDownload={() => handleDownloadReport({
          includePhotos: true,
          includeNotes: true,
          includeInvoices: true
        })}
        isExporting={isExporting}
      />
      
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid grid-cols-5 mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="photos">Photos</TabsTrigger>
          <TabsTrigger value="invoices">Invoices</TabsTrigger>
          <TabsTrigger value="notes">Notes</TabsTrigger>
          <TabsTrigger value="generate-reports">Generate Reports</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview">
          <OverviewTab 
            damageReport={report} 
            property={property}
            properties={properties}
            providers={providers}
            onUpdateReport={handleUpdateReport}
          />
        </TabsContent>
        
        <TabsContent value="photos">
          <PhotosTab reportId={id || ''} />
        </TabsContent>
        
        <TabsContent value="invoices">
          <InvoiceTab damageReport={report} providers={providers} />
        </TabsContent>
        
        <TabsContent value="notes">
          <NotesTab reportId={id || ''} />
        </TabsContent>
        
        <TabsContent value="generate-reports">
          <GenerateReportsTab 
            isExporting={isExporting}
            onExport={handleDownloadReport}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DamageDetail;
