
import React from 'react';
import { useParams } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const ContactDetail = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Contact Details</h1>
        <Link to={`/contacts/${id}/edit`}>
          <Button variant="outline">Edit Contact</Button>
        </Link>
      </div>
      
      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">Contact Information</h2>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>Contact ID: {id}</p>
            <p className="text-muted-foreground">
              Contact details will be displayed here once implemented.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ContactDetail;
