import React, { useEffect, useRef } from 'react';
import PageTransition from '@/components/layout/PageTransition';
import AutomationRules from '@/components/automation/AutomationRules';
import { useNavigationRefresh } from '@/hooks/useNavigationRefresh';
import { useQueryClient } from '@tanstack/react-query';

const TaskAutomation: React.FC = () => {
  // Use the useNavigationRefresh hook to refresh data when navigating to this page
  const { refreshRouteData } = useNavigationRefresh();
  const queryClient = useQueryClient();
  const initialLoadDoneRef = useRef(false);

  // Set up a one-time effect to refresh data when the component mounts
  useEffect(() => {
    // Skip if we've already done the initial load
    if (initialLoadDoneRef.current) return;

    // Mark that we've done the initial load
    initialLoadDoneRef.current = true;

    console.log('[TaskAutomation] Component mounted, refreshing route data');

    // Use the actual route path that matches the navigation
    refreshRouteData('/maintenance/automation');

    // Also dispatch a custom event to notify that we're on the task automation page
    window.dispatchEvent(new CustomEvent('stayfu-page-loaded', {
      detail: {
        page: 'task-automation',
        path: '/maintenance/automation'
      }
    }));
  }, [refreshRouteData]);

  // Removed visibility change handler to prevent data disappearing issues
  // The global React Query configuration with refetchOnWindowFocus: false will handle this correctly

  return (
    <PageTransition>
      <div className="container mx-auto py-6">
        <AutomationRules />
      </div>
    </PageTransition>
  );
};

export default TaskAutomation;
