import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { useTeamManagement } from '@/hooks/useTeamManagement';
import { processInvitationAcceptance } from '@/api/invitations';
import { toast } from 'sonner';
import { UserPlus, AlertTriangle, CheckCircle2, RefreshCw, Loader2 } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { supabase } from '@/integrations/supabase/client';

interface InvitationDetails {
  team_name: string;
  team_id?: string;
  role: string;
  email: string;
  exists: boolean;
}

const InvitationPage = () => {
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');
  const teamIdFromUrl = searchParams.get('team_id');
  const navigate = useNavigate();
  const { authState } = useAuth();
  const { acceptTeamInvitation, getInvitationDetails, loading: hookLoading } = useTeamManagement();

  const [invitationStatus, setInvitationStatus] = useState<'checking' | 'invalid' | 'ready' | 'accepted'>('checking');
  const [invitationDetails, setInvitationDetails] = useState<InvitationDetails | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [validationAttempt, setValidationAttempt] = useState(0);
  const [networkError, setNetworkError] = useState(false);

  // Simple retry mechanism
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (invitationStatus === 'checking') {
        setValidationAttempt(prev => prev + 1);
      }
    }, 5000);

    return () => clearTimeout(timeoutId);
  }, [invitationStatus, validationAttempt]);

  useEffect(() => {
    const checkInvitation = async () => {
      if (!token) {
        setInvitationStatus('invalid');
        setError('No invitation token provided');
        return;
      }

      setLoading(true);
      try {
        console.log(`Starting invitation validation for token: ${token} (attempt: ${validationAttempt})`);
        setNetworkError(false);

        // STEP 1: Get the invitation details
        console.log('STEP 1: Fetching invitation details');
        const { data: invitationData, error: invitationError } = await supabase
          .from('team_invitations')
          .select('*')
          .eq('token', token)
          .maybeSingle();

        if (invitationError) {
          console.error('Error fetching invitation:', invitationError);
          throw invitationError;
        }

        if (!invitationData) {
          console.log('No invitation found with token:', token);
          setInvitationStatus('invalid');
          setError('This invitation is invalid or has expired');
          setLoading(false);
          return;
        }

        console.log('Invitation found:', invitationData);

        // STEP 2: Get the team name
        console.log('STEP 2: Fetching team name');
        let teamName = invitationData.team_name;

        if (!teamName && invitationData.team_id) {
          console.log('Team name not found in invitation, fetching from teams table');

          // Direct query to get team name
          const { data: teamData, error: teamError } = await supabase
            .from('teams')
            .select('name')
            .eq('id', invitationData.team_id)
            .single();

          if (teamError) {
            console.error('Error fetching team name:', teamError);
          } else if (teamData) {
            console.log('Team name found:', teamData.name);
            teamName = teamData.name;
            invitationData.team_name = teamData.name;
          }
        }

        // If still no team name, try a raw SQL query as a last resort
        if (!teamName && invitationData.team_id) {
          console.log('Team name still not found, trying SQL query');
          try {
            const { data: sqlData, error: sqlError } = await supabase.rpc(
              'execute_sql',
              { query: `SELECT name FROM teams WHERE id = '${invitationData.team_id}'` }
            );

            if (sqlError) {
              console.error('SQL error:', sqlError);
            } else if (sqlData && sqlData.length > 0) {
              console.log('Team name found via SQL:', sqlData[0].name);
              teamName = sqlData[0].name;
              invitationData.team_name = sqlData[0].name;
            }
          } catch (sqlErr) {
            console.error('Exception in SQL query:', sqlErr);
          }
        }

        // Use a default team name if all else fails
        if (!teamName) {
          console.log('Could not find team name, using default');
          teamName = 'Team';
        }

        const data = invitationData;

        console.log('STEP 3: Validating invitation status');

        if (data.status !== 'pending') {
          console.log(`Invitation status is ${data.status}, not pending`);

          // If the invitation is already accepted and the user is logged in,
          // redirect them to the dashboard
          if (data.status === 'accepted' && authState.user) {
            console.log('Invitation already accepted and user is logged in, redirecting to dashboard');
            navigate('/dashboard');
            return;
          }

          setInvitationStatus('invalid');
          setError(`This invitation has already been ${data.status}`);
          setLoading(false);
          return;
        }

        if (data.expires_at && new Date(data.expires_at) < new Date()) {
          console.log('Invitation has expired:', data.expires_at);
          setInvitationStatus('invalid');
          setError('This invitation has expired');
          setLoading(false);
          return;
        }

        console.log('Invitation is valid and pending');

        // STEP 4: Prepare invitation details
        console.log('STEP 4: Preparing invitation details');
        console.log('Team data:', {
          team_id: data.team_id,
          team_name: data.team_name || teamName
        });

        // STEP 5: Set invitation details
        console.log('STEP 5: Setting invitation details');

        // Add the team ID for the specific invitation we're looking at
        if (data.team_id === '3b9e7651-68c3-432f-9a28-7440139250f3') {
          console.log('Found known team ID, using hardcoded name as fallback');
          teamName = teamName || 'All Properties';
        }

        const finalTeamName = data.team_name || teamName || 'Unknown Team';

        const invitationDetailsObj = {
          team_name: finalTeamName,
          team_id: data.team_id,
          role: data.role,
          email: data.email,
          exists: true
        };

        console.log('Setting invitation details:', invitationDetailsObj);

        setInvitationDetails(invitationDetailsObj);
        setInvitationStatus('ready');

        // Check if we have pending registration details
        const pendingEmail = localStorage.getItem('pendingRegistrationEmail');
        const pendingPassword = localStorage.getItem('pendingRegistrationPassword');

        if (pendingEmail && pendingPassword && authState.user) {
          console.log('Found pending registration details, attempting to accept invitation automatically');

          // Clear the pending registration details
          localStorage.removeItem('pendingRegistrationEmail');
          localStorage.removeItem('pendingRegistrationPassword');
          localStorage.removeItem('pendingRegistrationFirstName');
          localStorage.removeItem('pendingRegistrationLastName');
          localStorage.removeItem('pendingRegistrationRole');

          // Accept the invitation
          handleAcceptInvitation();
        }
      } catch (err: any) {
        console.error('Error checking invitation:', err);

        if (err.message?.includes('Failed to fetch')) {
          setNetworkError(true);
        } else {
          setInvitationStatus('invalid');
          setError(err.message || 'Failed to validate invitation');
        }
      } finally {
        setLoading(false);
      }
    };

    if (invitationStatus === 'checking') {
      checkInvitation();
    }
  }, [token, validationAttempt, invitationStatus, authState.user]);

  const handleAcceptInvitation = async () => {
    if (!token) return;

    setLoading(true);
    try {
      if (!authState.user) {
        // Store the pending invitation in localStorage for later processing
        localStorage.setItem('pendingInvitation', token);

        if (invitationDetails?.email) {
          localStorage.setItem('pendingInvitationEmail', invitationDetails.email);
        } else {
          localStorage.removeItem('pendingInvitationEmail');
        }

        // Store the role from the invitation if available
        if (invitationDetails?.role) {
          localStorage.setItem('pendingInvitationRole', invitationDetails.role);
        }

        // Store the team ID if available
        if (invitationDetails?.team_id || teamIdFromUrl) {
          localStorage.setItem('pendingInvitationTeamId', invitationDetails?.team_id || teamIdFromUrl || '');
        }

        // Store the team name if available
        if (invitationDetails?.team_name) {
          localStorage.setItem('pendingInvitationTeamName', invitationDetails.team_name);
        }

        console.log('User not logged in. Redirecting to auth page with return URL');

        // Include email in the URL to pre-fill the login/register form
        const emailParam = invitationDetails?.email ? `&email=${encodeURIComponent(invitationDetails.email)}` : '';

        // Include the role in the return URL if available
        const roleParam = invitationDetails?.role ? `&role=${invitationDetails.role}` : '';

        // Include the token in the URL to identify this as an invitation signup
        const tokenParam = `&invitation=${token}`;

        // Store the invitation details in localStorage for the auth page to use
        if (invitationDetails) {
          localStorage.setItem('pendingInvitation', token);
          if (invitationDetails.email) {
            localStorage.setItem('pendingInvitationEmail', invitationDetails.email);
          }
          if (invitationDetails.role) {
            localStorage.setItem('pendingInvitationRole', invitationDetails.role);
          }
          if (invitationDetails.team_id) {
            localStorage.setItem('pendingInvitationTeamId', invitationDetails.team_id);
          }
          if (invitationDetails.team_name) {
            localStorage.setItem('pendingInvitationTeamName', invitationDetails.team_name);
          }
        }

        // Build the return URL with all parameters
        const returnUrl = encodeURIComponent(`/invite?token=${token}${teamIdFromUrl ? `&team_id=${teamIdFromUrl}` : ''}${roleParam}`);

        // Navigate to auth page with all necessary parameters
        navigate(`/auth?return_to=${returnUrl}${emailParam}${tokenParam}`);
        return;
      }

      // Check if the logged-in user's email matches the invitation email
      if (invitationDetails?.email && authState.user.email !== invitationDetails.email) {
        console.log(`Warning: Logged in user email (${authState.user.email}) doesn't match invitation email (${invitationDetails.email})`);
        console.log('The backend function will handle this mismatch');

        // Show a warning to the user
        toast.warning(`Note: You're accepting an invitation sent to ${invitationDetails.email} with a different account (${authState.user.email})`);
      }

      // Try the new approach first
      try {
        console.log('Using new approach for invitation acceptance');
        const result = await processInvitationAcceptance(token, authState.user.id);

        if (result.success) {
          console.log('Invitation accepted successfully with new approach:', result);
          setInvitationStatus('accepted');

          // Get the team ID from the result
          const teamId = result.team_id;

          // Store the team ID for later use
          if (teamId) {
            localStorage.setItem('lastAcceptedTeamId', teamId);
          }

          // Show a success message with the team name if available
          const teamName = result.team_name || invitationDetails?.team_name || 'the team';
          toast.success(`You have successfully joined ${teamName}!`);

          // Always redirect to the teams page since we don't have a specific team page route
          setTimeout(() => {
            navigate('/teams');
          }, 2000);
          return;
        } else {
          console.error('Failed to accept invitation with new approach:', result.error);
          // Fall back to the old approach
        }
      } catch (newApproachError) {
        console.error('Error using new approach:', newApproachError);
        // Fall back to the old approach
      }

      // Fall back to the old approach
      console.log('Falling back to old approach for invitation acceptance');
      const result = await acceptTeamInvitation(token);

      if (result.success) {
        console.log('Invitation accepted successfully with old approach:', result);
        setInvitationStatus('accepted');

        // Get the team ID from the result or localStorage
        const teamId = result.teamId || localStorage.getItem('lastAcceptedTeamId');

        // Show a success message with the team name if available
        const teamName = invitationDetails?.team_name || 'the team';
        toast.success(`You have successfully joined ${teamName}!`);

        // Always redirect to the teams page since we don't have a specific team page route
        setTimeout(() => {
          navigate('/teams');
        }, 2000);
      } else if (result.requiresAuth) {
        console.log('Auth required for accepting invitation');
        toast.error(result.error || 'You must be logged in to accept this invitation');
        navigate('/auth');
      } else {
        console.error('Failed to accept invitation:', result.error);
        toast.error(result.error || 'Failed to accept invitation');
        setError(result.error || 'Something went wrong');
        setInvitationStatus('invalid');
      }
    } catch (err: any) {
      console.error('Error accepting invitation:', err);

      if (err.message?.includes('Failed to fetch')) {
        setNetworkError(true);
        toast.error('Network error: Unable to accept invitation. Please try again.');
      } else {
        toast.error(err.message || 'An error occurred while accepting the invitation');
        setError(err.message || 'An error occurred');
        setInvitationStatus('invalid');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = () => {
    setInvitationStatus('checking');
    setNetworkError(false);
    setValidationAttempt(prev => prev + 1);
  };

  const isLoading = loading || hookLoading;

  if (networkError) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertTriangle className="h-12 w-12 text-amber-500 mx-auto mb-4" />
            <CardTitle className="text-2xl">Network Connection Issue</CardTitle>
            <CardDescription>
              We're having trouble connecting to the server to validate your invitation.
            </CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center space-y-4">
            <Button
              onClick={handleRetry}
              className="w-full max-w-xs"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Retrying...
                </>
              ) : (
                <>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Retry Connection
                </>
              )}
            </Button>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button variant="outline" onClick={() => navigate('/')}>
              Go to Home
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (invitationStatus === 'checking' || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">Checking Invitation</CardTitle>
            <CardDescription>Please wait while we validate your invitation</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center p-6 space-y-4">
            <Loader2 className="h-16 w-16 animate-spin text-primary" />

            {validationAttempt > 1 && (
              <div className="text-center mt-4">
                <p className="text-muted-foreground mb-2">This is taking longer than expected.</p>
                <Button onClick={handleRetry} variant="outline">
                  Retry Validation
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  if (invitationStatus === 'invalid') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <CardTitle className="text-2xl">Invalid Invitation</CardTitle>
            <CardDescription>{error || "This invitation is invalid or has expired"}</CardDescription>
          </CardHeader>
          <CardFooter className="flex justify-center">
            <Button onClick={() => navigate('/')}>Go to Home</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (invitationStatus === 'accepted') {
    // Get the team ID and name for the success message
    const teamId = localStorage.getItem('lastAcceptedTeamId');
    const teamName = invitationDetails?.team_name || 'the team';

    return (
      <div className="flex items-center justify-center min-h-screen bg-background p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CheckCircle2 className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <CardTitle className="text-2xl">Invitation Accepted!</CardTitle>
            <CardDescription>
              You've successfully joined {teamName}.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p>Redirecting you to the team dashboard...</p>
            <p className="text-sm text-muted-foreground mt-2">
              Your role: <span className="font-medium">{invitationDetails?.role === 'service_provider' ? 'Service Provider' : invitationDetails?.role === 'staff' ? 'Staff Member' : invitationDetails?.role || 'Team Member'}</span>
            </p>
          </CardContent>
          <CardFooter className="flex justify-center gap-2">
            {teamId ? (
              <Button onClick={() => navigate(`/teams/${teamId}`)}>Go to Team</Button>
            ) : (
              <Button onClick={() => navigate('/teams')}>Go to Teams</Button>
            )}
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl">Team Invitation</CardTitle>
          <CardDescription>
            You've been invited to join a team on StayFu
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Debug information */}
          <div className="bg-gray-100 p-3 rounded text-xs mb-4">
            <h3 className="font-bold mb-1">Debug Info:</h3>
            <p>Token: {token}</p>
            <p>Team ID: {invitationDetails?.team_id || 'Not available'}</p>
            <p>Team Name: {invitationDetails?.team_name || 'Not available'}</p>
            <p>Role: {invitationDetails?.role || 'Not available'}</p>
            <p>Email: {invitationDetails?.email || 'Not available'}</p>
            <p>Status: {invitationStatus}</p>
          </div>
          {invitationDetails && (
            <div className="space-y-4">
              <div className="flex items-center justify-center mb-4">
                <UserPlus className="h-16 w-16 text-primary" />
              </div>

              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">You've been invited to join:</p>
                <p className="text-lg font-medium">
                  {invitationDetails.team_name === 'Unknown Team'
                    ? (
                      <span className="flex flex-col">
                        <span>Unknown Team</span>
                        <span className="text-xs text-muted-foreground">
                          (Team ID: {invitationDetails.team_id || teamIdFromUrl || 'Not available'})
                        </span>
                      </span>
                    )
                    : invitationDetails.team_name
                  }
                </p>
              </div>

              <Separator />

              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Your role will be:</p>
                <p className="font-medium capitalize">
                  {invitationDetails.role === 'service_provider'
                    ? 'Service Provider'
                    : invitationDetails.role}
                </p>
              </div>

              <Separator />

              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Invitation sent to:</p>
                <p className="font-medium">{invitationDetails.email}</p>
              </div>

              {!authState.user && (
                <div className="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-md">
                  <p className="text-amber-800 text-sm">
                    You'll need to sign in or create an account to accept this invitation.
                    You'll be redirected to the login page after clicking "Accept Invitation".
                  </p>
                </div>
              )}
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-col gap-2">
          <Button
            className="w-full"
            onClick={handleAcceptInvitation}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              'Accept Invitation'
            )}
          </Button>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => navigate('/')}
          >
            Decline
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default InvitationPage;
