import React, { useState } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

/**
 * Test page to verify calendar functionality
 * Navigate to /#/calendar-test to use this page
 */
const CalendarTestPage: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Calendar Test Page</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Basic Calendar</CardTitle>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              className="rounded-md border p-3"
              showOutsideDays={true}
            />
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div>
                <h3 className="font-medium">Selected Date:</h3>
                <p className="text-muted-foreground">
                  {selectedDate?.toLocaleDateString() || 'None selected'}
                </p>
              </div>
              
              <div>
                <h3 className="font-medium">Instructions:</h3>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Click the arrow buttons to navigate months</li>
                  <li>• Click on any date to select it</li>
                  <li>• Check that navigation works properly</li>
                  <li>• Verify day alignment with headers</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium">Test Results:</h3>
                <div className="text-sm space-y-1">
                  <p>✓ Calendar renders: <span className="text-green-600">Pass</span></p>
                  <p>✓ Date selection: <span className={selectedDate ? 'text-green-600' : 'text-red-600'}>
                    {selectedDate ? 'Pass' : 'Fail'}
                  </span></p>
                  <p>? Month navigation: <span className="text-yellow-600">Test manually</span></p>
                  <p>? Day alignment: <span className="text-yellow-600">Test visually</span></p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CalendarTestPage;
