import { render, screen, waitFor } from '@testing-library/react';
import { useProviders } from '../hooks/useProviders';
import MaintenanceProviders from './MaintenanceProviders';

jest.mock('../hooks/useProviders');

const mockProviders = [
  { id: '1', name: 'Provider 1', status: 'active' },
  { id: '2', name: 'Provider 2', status: 'inactive' },
];

describe('MaintenanceProviders', () => {
  beforeEach(() => {
    (useProviders as jest.Mock).mockReturnValue({
      providers: mockProviders,
      loading: false,
      error: null
    });
  });

  it('renders the providers list', async () => {
    render(<MaintenanceProviders />);
    
    await waitFor(() => {
      expect(screen.getByText('Provider 1')).toBeInTheDocument();
      expect(screen.getByText('Provider 2')).toBeInTheDocument();
    });
  });

  it('shows loading state', () => {
    (useProviders as jest.Mock).mockReturnValue({
      providers: [],
      loading: true,
      error: null
    });

    render(<MaintenanceProviders />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });
});
