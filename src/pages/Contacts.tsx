
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const Contacts = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Contacts</h1>
        <Link to="/#/contacts/add">
          <Button variant="default">Add Contact</Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <h2 className="text-xl font-semibold">Contact List</h2>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            No contacts found. Click the "Add Contact" button to create your first contact.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default Contacts;
