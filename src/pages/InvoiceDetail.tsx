
import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { <PERSON>, CardHeader, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { fetchInvoiceById, downloadInvoiceFile } from '@/components/damages/detail-tabs/invoice/api/invoiceApi';
import { Invoice } from '@/types/damages';
import { format } from 'date-fns';

const InvoiceDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getInvoiceDetails = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const invoiceData = await fetchInvoiceById(id);
        setInvoice(invoiceData);
      } catch (err) {
        console.error('Error fetching invoice:', err);
        setError('Failed to load invoice details');
      } finally {
        setLoading(false);
      }
    };

    getInvoiceDetails();
  }, [id]);

  const handleDownload = async () => {
    if (!invoice?.file_path) {
      console.error('No file path available');
      return;
    }

    try {
      const fileBlob = await downloadInvoiceFile(invoice.file_path);
      if (!fileBlob) {
        console.error('Failed to download file');
        return;
      }

      // Create a download link
      const url = URL.createObjectURL(fileBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = invoice.file_name || `invoice-${invoice.invoice_number}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Error downloading file:', err);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !invoice) {
    return (
      <Card className="mt-6">
        <CardContent className="py-12">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-red-500">Error Loading Invoice</h2>
            <p className="mt-2 text-muted-foreground">{error || "The requested invoice could not be found"}</p>
            <Link to="/#/damages">
              <Button variant="outline" className="mt-4">Back to Damage Reports</Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Invoice #{invoice.invoice_number}</h1>
        <div className="space-x-2">
          <Link to={`/damages/${invoice.damage_report_id}`}>
            <Button variant="outline">Back to Damage Report</Button>
          </Link>
          {invoice.file_path && (
            <Button onClick={handleDownload}>Download PDF</Button>
          )}
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between">
            <div>
              <h2 className="text-2xl font-semibold">Invoice Details</h2>
              <p className="text-muted-foreground">
                {invoice.issue_date && format(new Date(invoice.issue_date), 'MMMM d, yyyy')}
              </p>
            </div>
            <div className="text-right">
              <p className={`text-lg font-medium ${invoice.status === 'paid' ? 'text-green-600' : 'text-amber-600'}`}>
                {invoice.status.toUpperCase()}
              </p>
              <p>{invoice.provider_name}</p>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium">Service Provider</h3>
              <p>{invoice.provider_name || 'N/A'}</p>
              <p>{invoice.maintenance_providers?.email || 'N/A'}</p>
              <p>{invoice.maintenance_providers?.phone || 'N/A'}</p>
              <p>{invoice.maintenance_providers?.specialty || 'N/A'}</p>
            </div>
            <div>
              <h3 className="font-medium">Invoice Information</h3>
              <p>Invoice #: {invoice.invoice_number}</p>
              <p>Issue Date: {invoice.issue_date && format(new Date(invoice.issue_date), 'MMMM d, yyyy')}</p>
              <p>Due Date: {invoice.due_date && format(new Date(invoice.due_date), 'MMMM d, yyyy')}</p>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="font-medium mb-2">Notes</h3>
            <p className="whitespace-pre-wrap">{invoice.notes || 'No notes available'}</p>
          </div>
        </CardContent>

        <CardFooter className="flex justify-between border-t pt-6">
          <div>
            <p className="text-muted-foreground">Total Amount</p>
            <p className="text-2xl font-bold">${invoice.total_amount.toFixed(2)}</p>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default InvoiceDetail;
