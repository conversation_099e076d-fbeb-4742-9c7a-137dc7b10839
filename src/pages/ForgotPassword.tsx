
import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'sonner';
import { supabase } from '../integrations/supabase/client';

const ForgotPassword: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { authState } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // First, request the password reset via Supabase Auth
      // Get the base URL without any hash
      const baseUrl = window.location.origin;
      // Get the hostname to determine which domain we're on
      const hostname = window.location.hostname;

      // Determine the appropriate redirect URL based on the current domain
      let redirectUrl = `${baseUrl}/reset-password`;

      // For localhost development, we need to include the port
      if (hostname === 'localhost') {
        const port = window.location.port;
        redirectUrl = `http://localhost:${port}/reset-password`;
      }

      console.log('Using redirect URL for password reset:', redirectUrl);

      // Log out any existing session to ensure a clean password reset flow
      try {
        // Check if we have a valid session before attempting to sign out
        const { data: sessionData } = await supabase.auth.getSession();

        if (sessionData && sessionData.session) {
          // We have a valid session, proceed with sign out
          await supabase.auth.signOut();
        } else {
          console.log('No active session found, skipping sign out');
        }
      } catch (signOutError) {
        console.warn('Error during sign out (continuing anyway):', signOutError);
        // Continue with the password reset flow regardless
      }

      // Request password reset
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: redirectUrl,
      });

      if (error) throw error;

      setIsSubmitted(true);
      toast.success('Password reset email sent');
    } catch (error) {
      console.error('Password reset error:', error);
      toast.error('Failed to send reset email');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">Reset Password</CardTitle>
          <CardDescription className="text-center">Enter your email to receive a password reset link</CardDescription>
        </CardHeader>
        <CardContent>
          {isSubmitted ? (
            <div className="text-center space-y-4">
              <p className="text-green-600">
                We've sent a password reset email to <strong>{email}</strong>.
              </p>
              <p className="text-gray-700">
                Please check your inbox and follow the instructions in the email to reset your password.
              </p>
              <p className="text-gray-500 text-sm">
                If you don't see the email within a few minutes, check your spam folder or try again.
              </p>

            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium">Email</label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? 'Sending...' : 'Send reset link'}
              </Button>
            </form>
          )}
        </CardContent>
        <CardFooter className="flex-col space-y-2">
          <div className="text-sm text-center">
            <Link to="/#/login" className="text-blue-600 hover:underline">
              Back to login
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default ForgotPassword;
