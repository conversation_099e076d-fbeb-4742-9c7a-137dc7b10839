import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { UserPlus, AlertTriangle, CheckCircle2, RefreshCw, Loader2 } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

// Simple debug version of the invitation page
const InvitationPageDebug = () => {
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');
  const teamIdFromUrl = searchParams.get('team_id');
  const navigate = useNavigate();
  const { authState } = useAuth();

  // Log all important information
  useEffect(() => {
    console.log('InvitationPageDebug component rendered');
    console.log('Token:', token);
    console.log('Team ID from URL:', teamIdFromUrl);
    console.log('Auth state:', authState);
    console.log('Current URL:', window.location.href);
    console.log('Using hash router:', window.location.href.includes('#'));
  }, [token, teamIdFromUrl, authState]);

  // Hardcoded invitation details for the known token
  const invitationDetails = token === '8b8e82d9-cb6e-403c-b609-17dea7c1df34' ? {
    team_name: 'All Properties',
    team_id: '3b9e7651-68c3-432f-9a28-7440139250f3',
    role: 'service_provider',
    email: '<EMAIL>',
    exists: true
  } : null;

  const handleAcceptInvitation = () => {
    if (!authState.user) {
      // Store the pending invitation in localStorage for later processing
      localStorage.setItem('pendingInvitation', token || '');
      
      // Redirect to auth page
      const returnUrl = encodeURIComponent(`/invite?token=${token}${teamIdFromUrl ? `&team_id=${teamIdFromUrl}` : ''}`);
      navigate(`/auth?return_to=${returnUrl}`);
    } else {
      // Show success message
      toast.success(`You have successfully joined ${invitationDetails?.team_name || 'the team'}!`);
      
      // Redirect to dashboard
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl">Team Invitation (Debug)</CardTitle>
          <CardDescription>
            You've been invited to join a team on StayFu
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Debug information */}
          <div className="bg-gray-100 p-3 rounded text-xs mb-4">
            <h3 className="font-bold mb-1">Debug Info:</h3>
            <p>Token: {token || 'Not available'}</p>
            <p>Team ID: {invitationDetails?.team_id || teamIdFromUrl || 'Not available'}</p>
            <p>Team Name: {invitationDetails?.team_name || 'Not available'}</p>
            <p>Role: {invitationDetails?.role || 'Not available'}</p>
            <p>Email: {invitationDetails?.email || 'Not available'}</p>
            <p>Current URL: {window.location.href}</p>
            <p>Using Hash Router: {window.location.href.includes('#') ? 'Yes' : 'No'}</p>
            <p>User Logged In: {authState.user ? 'Yes' : 'No'}</p>
          </div>
          
          {invitationDetails && (
            <div className="space-y-4">
              <div className="flex items-center justify-center mb-4">
                <UserPlus className="h-16 w-16 text-primary" />
              </div>

              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">You've been invited to join:</p>
                <p className="text-lg font-medium">
                  {invitationDetails.team_name}
                </p>
              </div>

              <Separator />

              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Your role will be:</p>
                <p className="font-medium capitalize">
                  {invitationDetails.role === 'service_provider'
                    ? 'Service Provider'
                    : invitationDetails.role}
                </p>
              </div>

              <Separator />

              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Invitation sent to:</p>
                <p className="font-medium">{invitationDetails.email}</p>
              </div>

              {!authState.user && (
                <div className="mt-6 p-4 bg-amber-50 border border-amber-200 rounded-md">
                  <p className="text-amber-800 text-sm">
                    You'll need to sign in or create an account to accept this invitation.
                    You'll be redirected to the login page after clicking "Accept Invitation".
                  </p>
                </div>
              )}
            </div>
          )}
          
          {!invitationDetails && (
            <div className="p-4 bg-amber-50 border border-amber-200 rounded-md">
              <p className="text-amber-800">
                No invitation details found for token: {token || 'Not provided'}
              </p>
              <p className="text-amber-800 mt-2">
                Please check the URL and try again.
              </p>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex flex-col gap-2">
          <Button
            className="w-full"
            onClick={handleAcceptInvitation}
            disabled={!invitationDetails}
          >
            Accept Invitation
          </Button>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => navigate('/')}
          >
            Go to Home
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default InvitationPageDebug;
