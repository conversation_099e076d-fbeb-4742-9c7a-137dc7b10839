
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { FileDown, Printer, ArrowLeft } from 'lucide-react';
import { toast } from 'sonner';
import { generateDamageReportPdf } from '@/components/damages/GenerateDamageReportPdf';

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  amount: number;
}

interface Provider {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  specialty?: string;
  notes?: string; // Used to store address information
}

interface Property {
  id: string;
  name: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
}

interface DamageReport {
  id: string;
  title: string;
  description: string;
  status: string;
  created_at: string;
  updated_at: string;
  property_id: string;
  property_name?: string;
  platform?: string;
  provider_name?: string;
}

interface Invoice {
  id: string;
  invoice_number: string;
  damage_report_id: string;
  provider_id?: string;
  issue_date?: string;
  due_date?: string;
  status: string;
  total_amount: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

const InvoicePage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [items, setItems] = useState<InvoiceItem[]>([]);
  const [provider, setProvider] = useState<Provider | null>(null);
  const [property, setProperty] = useState<Property | null>(null);
  const [report, setReport] = useState<DamageReport | null>(null);

  useEffect(() => {
    const fetchInvoiceData = async () => {
      if (!id) return;

      setIsLoading(true);
      try {
        // Fetch invoice data
        const { data: invoiceData, error: invoiceError } = await supabase
          .from('damage_invoices')
          .select('*')
          .eq('id', id)
          .single();

        if (invoiceError) throw invoiceError;
        setInvoice(invoiceData);

        // Fetch invoice items
        const { data: itemsData, error: itemsError } = await supabase
          .from('invoice_items')
          .select('*')
          .eq('invoice_id', id)
          .order('created_at', { ascending: true });

        if (itemsError) throw itemsError;
        setItems(itemsData || []);

        // Fetch provider info if available
        if (invoiceData.provider_id) {
          const { data: providerData, error: providerError } = await supabase
            .from('maintenance_providers')
            .select('*')
            .eq('id', invoiceData.provider_id)
            .single();

          if (providerError) {
            console.error('Error fetching provider:', providerError);
          } else {
            setProvider(providerData);
          }
        }

        // Fetch damage report
        const { data: reportData, error: reportError } = await supabase
          .from('damage_reports')
          .select('*')
          .eq('id', invoiceData.damage_report_id)
          .single();

        if (reportError) {
          console.error('Error fetching report:', reportError);
        } else {
          setReport(reportData);

          // Fetch property data
          const { data: propertyData, error: propertyError } = await supabase
            .from('properties')
            .select('*')
            .eq('id', reportData.property_id)
            .single();

          if (propertyError) {
            console.error('Error fetching property:', propertyError);
          } else {
            setProperty(propertyData);
          }
        }
      } catch (error) {
        console.error('Error loading invoice data:', error);
        toast.error('Failed to load invoice data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchInvoiceData();
  }, [id]);

  const handlePrint = () => {
    window.print();
  };

  const handleExportPdf = async () => {
    if (!report || !invoice) return;

    setIsLoading(true);
    try {
      await generateDamageReportPdf(
        report,
        [],
        [],
        [{ 
          ...invoice,
          provider_name: provider?.name,
          items: items
        }],
        property || undefined,
        provider || undefined,
        {
          includePhotos: false,
          includeNotes: false,
          includeInvoices: true,
          specificInvoiceId: invoice.id
        }
      );
      
      toast.success('PDF exported successfully');
    } catch (error) {
      console.error('Error exporting PDF:', error);
      toast.error('Failed to export PDF');
    } finally {
      setIsLoading(false);
    }
  };

  const formatAddress = (notes?: string): string[] => {
    if (!notes) return ['No address available'];

    // Try to parse the address from notes
    // This is a simple approach - you might need more sophisticated parsing
    const lines = notes.split('\n');
    return lines.length > 0 ? lines : ['No address available'];
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!invoice || !report) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-2xl font-bold mb-4">Invoice Not Found</h1>
          <p>The requested invoice could not be loaded.</p>
          <Button 
            className="mt-4" 
            variant="outline" 
            onClick={() => navigate('/damages')}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Damages
          </Button>
        </div>
      </div>
    );
  }

  // Format provider address from notes field
  const providerAddress = formatAddress(provider?.notes);

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="print:hidden mb-6 flex justify-between items-center">
        <Button 
          variant="outline" 
          onClick={() => navigate(`/damages`)}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Damages
        </Button>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={handlePrint}
            disabled={isLoading}
          >
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
          <Button 
            onClick={handleExportPdf}
            disabled={isLoading}
          >
            <FileDown className="mr-2 h-4 w-4" />
            Export PDF
          </Button>
        </div>
      </div>

      {/* Professional Invoice */}
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-4xl mx-auto print:shadow-none">
        {/* Invoice Header */}
        <div className="flex justify-between items-start mb-10">
          <div>
            <h1 className="text-4xl font-serif text-gray-700 mb-8">INVOICE</h1>
            <div className="text-sm">
              <h3 className="font-bold text-gray-700 mb-1">BILL TO:</h3>
              <p>{property?.name || 'Property'}</p>
              <p>{property?.address || ''}</p>
              <p>{property?.city && property?.state ? `${property.city}, ${property.state} ${property?.zip || ''}` : ''}</p>
              <p className="mt-2">Ref: {report.title}</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm mb-4"><strong>No: </strong>{invoice.invoice_number}</p>
            <div className="text-sm">
              <h3 className="font-bold text-gray-700 mb-1">FROM:</h3>
              <p className="font-medium">{provider?.name || 'Provider'}</p>
              {providerAddress.map((line, index) => (
                <p key={index}>{line}</p>
              ))}
              {provider?.email && <p>{provider.email}</p>}
              {provider?.phone && <p>{provider.phone}</p>}
            </div>
          </div>
        </div>

        {/* Invoice Date */}
        <div className="mb-8 text-sm">
          <p><strong>Date: </strong>{invoice.issue_date 
            ? format(new Date(invoice.issue_date), 'MMMM d, yyyy') 
            : format(new Date(invoice.created_at), 'MMMM d, yyyy')}</p>
          {invoice.due_date && (
            <p><strong>Due Date: </strong>{format(new Date(invoice.due_date), 'MMMM d, yyyy')}</p>
          )}
          <p><strong>Status: </strong>
            <span className={`capitalize font-medium ${
              invoice.status === 'paid' ? 'text-green-600' : 
              invoice.status === 'pending' ? 'text-amber-600' : 'text-gray-600'
            }`}>
              {invoice.status}
            </span>
          </p>
        </div>

        {/* Invoice Items Table */}
        <div className="overflow-x-auto mb-8">
          <table className="min-w-full border-collapse">
            <thead>
              <tr className="bg-gray-800 text-white">
                <th className="p-3 text-left">DESCRIPTION</th>
                <th className="p-3 text-right">UNIT PRICE</th>
                <th className="p-3 text-center">QTY</th>
                <th className="p-3 text-right">TOTAL</th>
              </tr>
            </thead>
            <tbody>
              {items.map((item, index) => (
                <tr key={item.id} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                  <td className="border p-3">{item.description}</td>
                  <td className="border p-3 text-right">${item.unit_price.toFixed(2)}</td>
                  <td className="border p-3 text-center">{item.quantity}</td>
                  <td className="border p-3 text-right">${item.amount.toFixed(2)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Invoice Summary */}
        <div className="flex justify-end mb-8">
          <div className="w-60">
            <div className="flex justify-between py-2">
              <span className="font-medium">Subtotal:</span>
              <span>${invoice.total_amount.toFixed(2)}</span>
            </div>
            
            {/* Can add tax/discount here if needed */}
            {/* <div className="flex justify-between py-2 border-t border-gray-200">
              <span className="font-medium">Tax (0%):</span>
              <span>$0.00</span>
            </div> */}
            
            <div className="flex justify-between py-2 border-t border-b border-gray-800 font-bold">
              <span>AMOUNT DUE:</span>
              <span>${invoice.total_amount.toFixed(2)}</span>
            </div>
          </div>
        </div>

        {/* Payment Info and Notes */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {invoice.notes && (
            <div>
              <h3 className="font-bold text-gray-700 mb-2">NOTES:</h3>
              <p className="text-sm">{invoice.notes}</p>
            </div>
          )}
          
          {provider && (
            <div>
              <h3 className="font-bold text-gray-700 mb-2">PAYMENT INFO:</h3>
              <div className="text-sm">
                <p><strong>Provider:</strong> {provider.name}</p>
                {provider.specialty && <p><strong>Specialty:</strong> {provider.specialty}</p>}
                {provider.email && <p><strong>Email:</strong> {provider.email}</p>}
                {provider.phone && <p><strong>Phone:</strong> {provider.phone}</p>}
              </div>
            </div>
          )}
        </div>

        {/* Thank You Message */}
        <div className="text-center mt-12 mb-4">
          <p className="text-3xl font-serif text-gray-700 italic">Thank You!</p>
        </div>
      </div>
    </div>
  );
};

export default InvoicePage;
