import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { User, Session, UserProfile } from '@/types/auth';

// REBUILT: Simple auth state interface with profile support
interface AuthState {
  user: User | null
  session: Session | null
  profile: UserProfile | null
  isAuthenticated: boolean
  isLoading: boolean
}

interface AuthContextProps {
  authState: AuthState
  signIn: (email: string, password: string) => Promise<{ error?: any }>
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
  signUp: (email: string, password: string, firstName: string, lastName: string, role?: string) => Promise<{ error?: any }>
}

export const AuthContext = createContext<AuthContextProps | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    profile: null,
    isAuthenticated: false,
    isLoading: true
  });

  // Function to fetch user profile - designed to be non-blocking
  const fetchUserProfile = async (userId: string): Promise<UserProfile | null> => {
    try {
      console.log('[AuthContext] Fetching user profile for ID:', userId);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('[AuthContext] Error fetching user profile:', error);
        // Return null instead of throwing error to prevent auth flow from breaking
        return null;
      }

      console.log('[AuthContext] User profile fetched successfully:', data);
      return data;
    } catch (error) {
      console.error('[AuthContext] Exception fetching user profile:', error);
      // Return null instead of throwing error to prevent auth flow from breaking
      return null;
    }
  };

  // Helper function to update profile in auth state
  const updateProfileInState = (profile: UserProfile | null) => {
    setAuthState(prevState => ({
      ...prevState,
      profile: profile
    }));
  };

  // REBUILT: Simple auth initialization
  useEffect(() => {
    const initAuth = async () => {
      try {
        console.log('Initializing auth...')
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Error getting session:', error)
        } else {
          console.log('Session retrieved:', session?.user?.id || 'No session')
        }

        setAuthState({
          user: session?.user ?? null,
          session: session,
          profile: null, // Will be set later by separate effect
          isAuthenticated: !!session?.user,
          isLoading: false
        })
      } catch (error) {
        console.error('Error in initAuth:', error)
        setAuthState({
          user: null,
          session: null,
          profile: null,
          isAuthenticated: false,
          isLoading: false
        })
      }
    }

    initAuth()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id || 'No session')
        
        setAuthState({
          user: session?.user ?? null,
          session: session,
          profile: null, // Will be set later by separate effect
          isAuthenticated: !!session?.user,
          isLoading: false
        })
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  // Separate effect for profile fetching - completely independent of auth state
  useEffect(() => {
    const fetchProfileForUser = async () => {
      if (authState.user?.id && authState.isAuthenticated && !authState.isLoading) {
        console.log('[AuthContext] Fetching profile in separate effect for user:', authState.user.id);
        const profile = await fetchUserProfile(authState.user.id);
        updateProfileInState(profile);
      } else if (!authState.isAuthenticated) {
        // Clear profile when user is not authenticated
        updateProfileInState(null);
      }
    };

    // Small delay to ensure auth state is stable
    const timeoutId = setTimeout(fetchProfileForUser, 200);
    
    return () => clearTimeout(timeoutId);
  }, [authState.user?.id, authState.isAuthenticated, authState.isLoading]);

  const signIn = async (email: string, password: string) => {
    try {
      console.log('Attempting to sign in:', email)
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        console.error('Sign in error:', error)
        return { error }
      }

      console.log('Sign in successful:', data.user?.id)
      return { data }
    } catch (error) {
      console.error('Exception during sign in:', error)
      return { error }
    }
  }

  const signUp = async (email: string, password: string, firstName: string, lastName: string, role: string = 'property_manager') => {
    try {
      console.log('Attempting to sign up:', email)
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName,
            role: role
          }
        }
      })

      if (error) {
        console.error('Sign up error:', error)
        return { error }
      }

      console.log('Sign up successful:', data.user?.id)
      return { data }
    } catch (error) {
      console.error('Exception during sign up:', error)
      return { error }
    }
  }

  const signOut = async () => {
    try {
      await supabase.auth.signOut()
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const refreshProfile = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.refreshSession()
      
      if (error) {
        console.error('Error refreshing session:', error)
        return
      }

      // Update auth state first
      setAuthState(prev => ({
        ...prev,
        user: session?.user ?? null,
        session: session,
        isAuthenticated: !!session?.user
      }))

      // Then fetch profile asynchronously (non-blocking)
      if (session?.user?.id) {
        console.log('[AuthContext] Refreshing profile for user:', session.user.id);
        const profile = await fetchUserProfile(session.user.id);
        updateProfileInState(profile);
      }
    } catch (error) {
      console.error('Error in refreshProfile:', error)
    }
  }

  return (
    <AuthContext.Provider value={{
      authState,
      signIn,
      signOut,
      refreshProfile,
      signUp
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
