import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

interface OnboardingState {
  hasSeenDashboardTutorial: boolean;
  hasSeenPropertiesTutorial: boolean;
  hasSeenMaintenanceTutorial: boolean;
  hasSeenInventoryTutorial: boolean;
  hasSeenDamagesTutorial: boolean;
}

interface OnboardingContextType {
  onboardingState: OnboardingState;
  markTutorialSeen: (tutorial: keyof OnboardingState) => void;
  resetTutorials: () => void;
  restartTutorial: (tutorial: keyof OnboardingState) => void;
}

const defaultOnboardingState: OnboardingState = {
  hasSeenDashboardTutorial: false,
  hasSeenPropertiesTutorial: false,
  hasSeenMaintenanceTutorial: false,
  hasSeenInventoryTutorial: false,
  hasSeenDamagesTutorial: false
};

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

export const OnboardingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { authState } = useAuth();
  const [onboardingState, setOnboardingState] = useState<OnboardingState>(defaultOnboardingState);

  // Create a memoized toast function to avoid calling during render
  const showErrorToast = useCallback((message: string) => {
    toast.error(message);
  }, []);

  const showSuccessToast = useCallback((message: string) => {
    toast.success(message);
  }, []);

  // Load onboarding state from Supabase when user logs in
  useEffect(() => {
    const loadOnboardingState = async () => {
      // Check if authState and authState.user exist before accessing user.id
      if (authState?.user?.id) {
        try {
          console.log("Loading onboarding state for user:", authState.user.id);

          // First try to get the user's preferences
          const { data, error } = await supabase
            .from('user_preferences')
            .select('onboarding_state')
            .eq('user_id', authState.user.id)
            .maybeSingle();

          if (error) {
            console.error('Error loading onboarding state:', error);
            // Don't show toast for permission errors during impersonation
            if (error.code !== '42501') {
              // Use setTimeout to ensure this doesn't happen during render
              setTimeout(() => {
                showErrorToast('Failed to load user preferences');
              }, 0);
            }
            // Use default state in memory but don't try to save it
            setOnboardingState(defaultOnboardingState);
            return;
          }

          if (data?.onboarding_state) {
            // We have existing preferences, use them
            setOnboardingState(data.onboarding_state as OnboardingState);
          } else {
            // No preferences found, try to create default ones
            try {
              // First try using RPC function which bypasses RLS
              const { error: rpcError } = await supabase.rpc(
                'create_user_preferences',
                {
                  p_user_id: authState.user.id,
                  p_onboarding_state: defaultOnboardingState
                }
              );

              if (rpcError) {
                console.error('Error creating preferences with RPC:', rpcError);
                // Fall back to direct insert
                const { error: insertError } = await supabase
                  .from('user_preferences')
                  .upsert({
                    user_id: authState.user.id,
                    onboarding_state: defaultOnboardingState
                  }, {
                    onConflict: 'user_id'
                  });

                if (insertError) {
                  console.error('Error creating preferences:', insertError);
                  // Don't show toast for permission errors during impersonation
                  if (insertError.code !== '42501') {
                    setTimeout(() => {
                      showErrorToast('Failed to initialize user preferences');
                    }, 0);
                  }
                }
              }
            } catch (createError) {
              console.error('Exception creating preferences:', createError);
            }

            // Use default state in memory regardless of whether we could save it
            setOnboardingState(defaultOnboardingState);
          }
        } catch (error) {
          console.error('Error in onboarding state loading:', error);
          // Use default state in memory as fallback
          setOnboardingState(defaultOnboardingState);
        }
      }
    };

    loadOnboardingState();
  }, [authState?.user?.id]); // Update dependency to include the authState check

  const markTutorialSeen = async (tutorial: keyof OnboardingState) => {
    if (!authState?.user?.id) return;

    const updatedState = {
      ...onboardingState,
      [tutorial]: true
    };

    // Update state in memory immediately for better UX
    setOnboardingState(updatedState);

    try {
      // First try using the RPC function which bypasses RLS
      const { data: rpcResult, error: rpcError } = await supabase.rpc(
        'update_user_preferences',
        {
          p_user_id: authState.user.id,
          p_onboarding_state: updatedState
        }
      );

      if (rpcError) {
        console.error('Error updating preferences with RPC:', rpcError);

        // Fall back to direct update
        const { error } = await supabase
          .from('user_preferences')
          .upsert({
            user_id: authState.user.id,
            onboarding_state: updatedState
          }, {
            onConflict: 'user_id'
          });

        if (error) {
          console.error('Error updating onboarding state:', error);
          // Only show error if it's not a permission error
          if (error.code !== '42501') {
            setTimeout(() => {
              showErrorToast('Failed to save tutorial progress');
            }, 0);
          }
        }
      }
    } catch (error) {
      console.error('Error updating onboarding state:', error);
    }
  };

  const resetTutorials = async () => {
    if (!authState?.user?.id) return;

    // Update state in memory immediately for better UX
    setOnboardingState(defaultOnboardingState);

    try {
      // First try using the RPC function which bypasses RLS
      const { data: rpcResult, error: rpcError } = await supabase.rpc(
        'update_user_preferences',
        {
          p_user_id: authState.user.id,
          p_onboarding_state: defaultOnboardingState
        }
      );

      if (rpcError) {
        console.error('Error resetting preferences with RPC:', rpcError);

        // Fall back to direct update
        const { error } = await supabase
          .from('user_preferences')
          .upsert({
            user_id: authState.user.id,
            onboarding_state: defaultOnboardingState
          }, {
            onConflict: 'user_id'
          });

        if (error) {
          console.error('Error resetting onboarding state:', error);
          // Only show error if it's not a permission error
          if (error.code !== '42501') {
            setTimeout(() => {
              showErrorToast('Failed to reset tutorials');
            }, 0);
          }
        } else {
          setTimeout(() => {
            showSuccessToast('Tutorials have been reset');
          }, 0);
        }
      } else {
        setTimeout(() => {
          showSuccessToast('Tutorials have been reset');
        }, 0);
      }
    } catch (error) {
      console.error('Error resetting onboarding state:', error);
    }
  };

  const restartTutorial = async (tutorial: keyof OnboardingState) => {
    if (!authState?.user?.id) return;

    const updatedState = {
      ...onboardingState,
      [tutorial]: false
    };

    // Update state in memory immediately for better UX
    setOnboardingState(updatedState);

    try {
      // First try using the RPC function which bypasses RLS
      const { data: rpcResult, error: rpcError } = await supabase.rpc(
        'update_user_preferences',
        {
          p_user_id: authState.user.id,
          p_onboarding_state: updatedState
        }
      );

      if (rpcError) {
        console.error('Error restarting tutorial with RPC:', rpcError);

        // Fall back to direct update
        const { error } = await supabase
          .from('user_preferences')
          .upsert({
            user_id: authState.user.id,
            onboarding_state: updatedState
          }, {
            onConflict: 'user_id'
          });

        if (error) {
          console.error('Error restarting tutorial:', error);
          // Only show error if it's not a permission error
          if (error.code !== '42501') {
            setTimeout(() => {
              showErrorToast('Failed to restart tutorial');
            }, 0);
          }
        } else {
          setTimeout(() => {
            showSuccessToast(`${tutorial.replace('hasSeen', '').replace('Tutorial', '')} tutorial restarted`);
          }, 0);
        }
      } else {
        setTimeout(() => {
          showSuccessToast(`${tutorial.replace('hasSeen', '').replace('Tutorial', '')} tutorial restarted`);
        }, 0);
      }
    } catch (error) {
      console.error('Error restarting tutorial:', error);
    }
  };

  return (
    <OnboardingContext.Provider value={{
      onboardingState,
      markTutorialSeen,
      resetTutorials,
      restartTutorial
    }}>
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboarding = () => {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
};
