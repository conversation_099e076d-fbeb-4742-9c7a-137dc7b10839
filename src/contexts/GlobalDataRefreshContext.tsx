import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * A simplified context for refreshing data using React Query's built-in functionality.
 * This removes all the complex caching, toast notifications, and custom events.
 */

interface GlobalDataRefreshContextType {
  refreshAllData: () => Promise<void>;
  refreshRouteData: (route: string) => Promise<void>;
  isRefreshing: boolean;
}

const GlobalDataRefreshContext = createContext<GlobalDataRefreshContextType | undefined>(undefined);

interface GlobalDataRefreshProviderProps {
  children: ReactNode;
}

// Map routes to their relevant query keys
const routeQueryKeyMap: Record<string, string[]> = {
  '/dashboard': ['properties', 'maintenanceTasks', 'inventoryItems', 'damageReports', 'purchaseOrders'],
  '/properties': ['properties'],
  '/maintenance': ['maintenanceTasks', 'properties'],
  '/inventory': ['inventoryItems', 'properties'],
  '/damages': ['damageReports', 'properties'],
  '/purchase-orders': ['purchaseOrders', 'properties', 'inventoryItems'],
  '/teams': ['teamsV2', 'teamMembersV2', 'teamPropertiesV2', 'teamInvitationsV2'],
  '/collections': ['collections', 'inventoryItems', 'properties'],
  '/operations': ['properties', 'maintenanceTasks', 'bookings', 'damageReports'],
  '/task-automation': ['automationRulesV2', 'maintenanceTasksV2'],
};

export const GlobalDataRefreshProvider: React.FC<GlobalDataRefreshProviderProps> = ({ children }) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const queryClient = useQueryClient();

  // Function to refresh all data
  const refreshAllData = useCallback(async () => {
    if (isRefreshing) {
      console.log('[GlobalDataRefreshContext] Already refreshing, skipping duplicate refresh');
      return;
    }

    try {
      setIsRefreshing(true);
      console.log('[GlobalDataRefreshContext] Refreshing all data...');

      // Invalidate all queries
      await queryClient.invalidateQueries();

      // Force refetch all queries
      await queryClient.refetchQueries({
        type: 'all',
        stale: true
      });

      console.log('[GlobalDataRefreshContext] All data refreshed successfully');
    } catch (error) {
      console.error('[GlobalDataRefreshContext] Error refreshing data:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [isRefreshing, queryClient]);

  // Cache to prevent duplicate refreshes
  const refreshCache = new Map<string, number>();
  const REFRESH_COOLDOWN = 2000; // 2 seconds cooldown between refreshes for the same route

  // Routes that are currently having issues and should be refreshed less frequently or not at all
  const REFRESH_BLACKLIST = new Set<string>([]); // Previously included '/teams' but it's now fixed

  // Function to refresh data for a specific route
  const refreshRouteData = useCallback(async (route: string) => {
    if (isRefreshing) {
      console.log('[GlobalDataRefreshContext] Already refreshing, skipping duplicate refresh');
      return;
    }

    // Handle both hash and non-hash routes
    // First, normalize the route by removing any leading or trailing slashes and hash
    let cleanRoute = route;

    // Handle hash router format (e.g., '/#/properties' or '#/properties')
    if (cleanRoute.includes('#/')) {
      // Extract the part after '#/'
      cleanRoute = cleanRoute.split('#/')[1] || '';
    } else if (cleanRoute.startsWith('#')) {
      // Handle case where it's just '#/properties'
      cleanRoute = cleanRoute.substring(1);
    }

    // Ensure the route starts with a slash
    if (!cleanRoute.startsWith('/')) {
      cleanRoute = '/' + cleanRoute;
    }

    // Remove trailing slash if present
    if (cleanRoute.length > 1 && cleanRoute.endsWith('/')) {
      cleanRoute = cleanRoute.slice(0, -1);
    }

    // Handle nested routes by extracting the base route
    // For example: '/properties/123' -> '/properties'
    // Special cases for specific routes
    let baseRoute: string;

    // Special case for maintenance/automation
    if (cleanRoute.startsWith('/maintenance/automation')) {
      baseRoute = '/task-automation';
      console.log(`[GlobalDataRefreshContext] Special case: mapping /maintenance/automation to ${baseRoute}`);
    }
    // Special case for teams with IDs
    else if (cleanRoute.match(/^\/teams\/[a-zA-Z0-9-]+$/)) {
      baseRoute = '/teams';
      console.log(`[GlobalDataRefreshContext] Special case: mapping ${cleanRoute} to ${baseRoute}`);
    }
    // Special case for properties with IDs
    else if (cleanRoute.match(/^\/properties\/[a-zA-Z0-9-]+$/)) {
      baseRoute = '/properties';
      console.log(`[GlobalDataRefreshContext] Special case: mapping ${cleanRoute} to ${baseRoute}`);
    }
    // Default case - extract the first segment
    else {
      const segments = cleanRoute.split('/').filter(Boolean);
      baseRoute = segments.length > 0 ? `/${segments[0]}` : '/';
    }

    console.log(`[GlobalDataRefreshContext] Original route: ${route}, Cleaned route: ${cleanRoute}, Base route: ${baseRoute}`);

    // Skip routes that are known to cause issues
    if (REFRESH_BLACKLIST.has(baseRoute)) {
      console.log(`[GlobalDataRefreshContext] Route ${baseRoute} is blacklisted, skipping refresh entirely`);
      return;
    }

    // Check if we've refreshed this route recently
    const now = Date.now();
    const lastRefreshTime = refreshCache.get(baseRoute) || 0;

    if (now - lastRefreshTime < REFRESH_COOLDOWN) {
      console.log(`[GlobalDataRefreshContext] Route ${baseRoute} was refreshed recently, skipping`);
      return;
    }

    // Update the cache
    refreshCache.set(baseRoute, now);

    // Set refreshing flag
    setIsRefreshing(true);

    try {
      // Get query keys for this route
      const queryKeys = routeQueryKeyMap[baseRoute] || [];

      if (queryKeys.length > 0) {
        console.log(`[GlobalDataRefreshContext] Found query keys for route ${baseRoute}:`, queryKeys);

        // Invalidate specific query keys with less aggressive options
        for (const key of queryKeys) {
          await queryClient.invalidateQueries({
            queryKey: [key],
            refetchType: 'active', // Only refetch active queries
            exact: false
          });
        }

        // Dispatch a custom event to notify that data has been refreshed
        // But only if we haven't dispatched one recently for this route
        window.dispatchEvent(new CustomEvent('stayfu-data-refreshed', {
          detail: {
            route: baseRoute,
            timestamp: now
          }
        }));

        console.log(`[GlobalDataRefreshContext] Route data refreshed successfully for ${baseRoute}`);
      } else {
        console.log(`[GlobalDataRefreshContext] No query keys found for route: ${baseRoute}, refreshing all data`);
        // If no specific keys found, refresh all data
        await refreshAllData();
      }
    } catch (error) {
      console.error(`[GlobalDataRefreshContext] Error refreshing data for route ${route}:`, error);
    } finally {
      setIsRefreshing(false);
    }
  }, [isRefreshing, queryClient, refreshAllData]);

  return (
    <GlobalDataRefreshContext.Provider value={{ refreshAllData, refreshRouteData, isRefreshing }}>
      {children}
    </GlobalDataRefreshContext.Provider>
  );
};

export const useGlobalDataRefresh = (): GlobalDataRefreshContextType => {
  const context = useContext(GlobalDataRefreshContext);
  if (context === undefined) {
    throw new Error('useGlobalDataRefresh must be used within a GlobalDataRefreshProvider');
  }
  return context;
};
