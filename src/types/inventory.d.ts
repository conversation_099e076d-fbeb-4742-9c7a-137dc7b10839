
// Add or merge this with your existing types file
export interface InventoryItem {
  id: string;
  name: string;
  property_id: string;
  collection: string;
  quantity: number;
  min_quantity: number;
  price?: number;
  amazon_url?: string;
  walmart_url?: string;
  image_url?: string;
  user_id: string;
  created_at?: string;
  updated_at?: string;
  last_ordered?: string;
  propertyName?: string; // From the join with properties
}

export interface FormattedInventoryItem {
  id?: string;
  name: string;
  propertyId: string;
  propertyName?: string;
  collection: string;
  quantity: number;
  minQuantity: number;
  price?: number;
  amazonUrl?: string;
  walmartUrl?: string;
  imageUrl?: string;
  lastOrdered?: string;
  hasProcessedImage?: boolean;
}

export interface ExtensionImportProduct {
  name: string;
  price: number;
  imageUrl: string;
  amazonUrl?: string;
  propertyId?: string;
  propertyName?: string;
  collection?: string;
  quantity?: number;
  minQuantity?: number;
  hasProcessedImage?: boolean;
}

export interface ScrapedProduct {
  name: string;
  price: number;
  imageUrl: string;
  url?: string;
  error?: string;
}

export interface FilterOptions {
  property: string;
  collection: string;
  lowStock: boolean;
  stockStatus?: string; // Optional for backward compatibility
}
