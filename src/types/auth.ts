
export enum PermissionType {
  <PERSON><PERSON>GE_PROPERTIES = 'manage_properties',
  SUBMIT_DAMAGE_REPORTS = 'submit_damage_reports',
  MANAGE_INVENTORY = 'manage_inventory',
  VIEW_INVENTORY = 'view_inventory',
  MANAGE_STAFF = 'manage_staff',
  MANAGE_SERVICE_PROVIDERS = 'manage_service_providers',
  VIEW_REPORTS = 'view_reports',
  EDIT_REPORTS = 'edit_reports',
  ADMIN_DASHBOARD_ACCESS = 'admin_dashboard_access',
  IMPERSONATE_USERS = 'impersonate_users',
  EDIT_USER_DATA = 'edit_user_data',
  ADD_USERS = 'add_users',
  DELETE_USERS = 'delete_users',
  MANAGE_SUBSCRIPTIONS = 'manage_subscriptions',
  ADMIN = 'admin',
  MANAGE_PURCHASE_ORDERS = 'manage_purchase_orders',
  VIEW_PURCHASE_ORDERS = 'view_purchase_orders',
  MANAGE_DAMAGE_REPORTS = 'manage_damage_reports',
  VIEW_DAMAGE_REPORTS = 'view_damage_reports',
  MANAGE_MAINTENANCE = 'manage_maintenance',
  VIEW_MAINTENANCE = 'view_maintenance',
  MANAGE_TEAM = 'manage_team',
  VIEW_TEAM = 'view_team'
  // The following permissions are not in the database enum and should not be used:
  // VIEW_ASSIGNED_TASKS = 'view_assigned_tasks',
  // CREATE_MAINTENANCE_TASK = 'create_maintenance_task',
  // SUBMIT_DAMAGE_REPORT = 'submit_damage_report'
}

export type UserPermission = {
  id: string;
  user_id: string;
  team_id?: string;
  permission: PermissionType;
  enabled: boolean;
  created_at: string;
  updated_at: string;
};

export type UserProfile = {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  role?: string;
  is_super_admin?: boolean;
  created_at?: string;
  updated_at?: string;
};

// Add these additional types needed for AuthContext
export type User = {
  id: string;
  email?: string;
  aud: string;
  created_at: string;
  app_metadata: {
    provider?: string;
    [key: string]: any;
  };
  user_metadata: {
    [key: string]: any;
  };
};

export type UserRole = 'property_manager' | 'staff' | 'service_provider' | 'admin' | 'super_admin';

export type Session = {
  access_token: string;
  token_type: string;
  expires_at: number;
  refresh_token: string;
  user: User;
};

export type AuthState = {
  user: User | null;
  session: Session | null;
  profile: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
  isImpersonating?: boolean;
  originalUser?: User | null;
};
