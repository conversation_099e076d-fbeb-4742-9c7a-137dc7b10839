// Declaration file for Deno modules used in Supabase Edge Functions
declare module "https://deno.land/std@0.168.0/http/server.ts" {
  export function serve(handler: (req: Request) => Response | Promise<Response>): void;
}

declare module "https://esm.sh/@supabase/supabase-js@2.7.1" {
  export function createClient(url: string, key: string): any;
}

declare module "https://esm.sh/ical@0.8.0" {
  const ical: {
    parseICS(data: string): any;
  };
  export default ical;
}

declare namespace Deno {
  export interface Env {
    get(key: string): string | undefined;
  }
  export const env: Env;
}