
export interface UserSettings {
  id: string;
  user_id: string;
  dark_mode: boolean;
  compact_mode: boolean;
  animations: boolean;
  email_notifications: boolean;
  push_notifications: boolean;
  weekly_summary: boolean;
  inventory_alerts: boolean;
  created_at: string;
  updated_at: string;
}

export type UserSettingKey = 'dark_mode' | 'compact_mode' | 'animations' | 
  'email_notifications' | 'push_notifications' | 'weekly_summary' | 'inventory_alerts';
