
export interface Property {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
}

export interface Provider {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  specialty?: string;
}

export interface DamageReport {
  id: string;
  title: string;
  description: string;
  status: string;
  property_id: string;
  property_name?: string;
  created_at: string;
  updated_at: string;
  provider_id?: string;
  provider_name?: string;
  platform?: string;
}

export interface Invoice {
  id: string;
  invoice_number: string;
  damage_report_id: string;
  provider_id: string;
  total_amount: number;
  issue_date: string;
  due_date: string;
  status: string;
  notes: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  maintenance_providers?: { name: string; email?: string; phone?: string; specialty?: string; notes?: string };
  file_path?: string;
  file_name?: string;
  file_url?: string;
  provider_name?: string;
}

export interface DamagePhoto {
  id: string;
  damage_report_id: string;
  file_path: string;
  file_name: string;
  caption?: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  url?: string;
}

export interface DamageNote {
  id: string;
  damage_report_id: string;
  content: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  private: boolean;
}
