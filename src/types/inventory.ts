
// Define consistent types that match the database schema
export interface InventoryItem {
  id: string;
  user_id: string;
  property_id: string;
  team_id?: string;
  name: string;
  collection: string;
  quantity: number;
  min_quantity: number;
  price?: number;
  amazon_url?: string;
  walmart_url?: string;
  target_url?: string;
  image_url?: string;
  last_ordered?: string;
  created_at: string;
  updated_at: string;
}

// Frontend interface that uses camelCase for component usage
export interface FormattedInventoryItem {
  id: string;
  name: string;
  propertyId: string;
  propertyName?: string;
  teamId?: string;
  collection: string;
  quantity: number;
  minQuantity: number;
  price?: number;
  amazonUrl?: string;
  walmartUrl?: string;
  imageUrl?: string;
  lastOrdered?: string;
  hasProcessedImage?: boolean;
}

export interface Property {
  id: string;
  name: string;
}

export type POStatus = 'pending' | 'ordered' | 'delivered' | 'archived';

export interface PurchaseOrder {
  id: string;
  user_id: string;
  property_id: string;
  property_name?: string; // Used for display
  status: POStatus;
  total_price?: number;
  notes?: string;
  is_archived: boolean;
  created_at: string;
  updated_at: string;
  items?: PurchaseOrderItem[];
}

export interface PurchaseOrderItem {
  id: string;
  purchase_order_id: string;
  inventory_item_id?: string;
  item_name: string;
  quantity: number;
  price?: number;
  amazon_url?: string;
  walmart_url?: string;
  target_url?: string;
  created_at: string;
}

// Frontend version for forms
export interface FormattedPurchaseOrderItem {
  id: string;
  purchaseOrderId: string;
  inventoryItemId?: string;
  name: string;
  quantity: number;
  price?: number;
  amazonUrl?: string;
  walmartUrl?: string;
  imageUrl?: string;
}

// Type used for Amazon scraped products
export interface ScrapedProduct {
  name: string;
  price?: number;
  imageUrl?: string;
  url?: string;
  error?: string;
}

// Type used for Amazon extension import products
export interface ExtensionImportProduct {
  name: string;
  propertyId?: string;
  propertyName?: string;
  collection?: string;
  quantity?: number;
  minQuantity?: number;
  price?: number;
  amazonUrl?: string;
  imageUrl?: string;
  hasProcessedImage?: boolean;
}
