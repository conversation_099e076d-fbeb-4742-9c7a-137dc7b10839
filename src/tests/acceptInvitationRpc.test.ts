import { acceptInvitationDirect } from '../api/invitations';
import { supabase } from '../../integrations/supabase/client';

// This is a placeholder token and user ID.
// A real test would require creating a pending invitation in the database first.
const testToken = 'a1b2c3d4-e5f6-7890-1234-567890abcdef'; // Replace with a valid token from a pending invitation
const testUserId = 'b37de603-a3db-4012-8e3a-6be476780ef6'; // Replace with a valid user ID

async function runTest() {
  console.log(`Testing acceptInvitationDirect with token: ${testToken} and user ID: ${testUserId}`);

  const result = await acceptInvitationDirect(testToken, testUserId);

  if (result.success) {
    console.log('Test succeeded:', result);
  } else {
    console.error('Test failed:', result.error);
  }
}

runTest();