
import { http, HttpResponse } from 'msw';
import { testProperties } from '../data/properties';

export const handlers = [
  // Properties endpoints
  http.get('*/rest/v1/properties*', () => {
    return HttpResponse.json({ data: testProperties, error: null });
  }),

  // Team endpoints for testing
  http.get('*/rest/v1/teams*', () => {
    return HttpResponse.json({ 
      data: [
        { 
          id: 'test-team-id',
          name: 'Test Team',
          owner_id: 'test-user-id',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          member_count: 3
        }
      ], 
      error: null 
    });
  }),

  http.get('*/rest/v1/team_members*', () => {
    return HttpResponse.json({ 
      data: [
        { 
          id: 'test-member-1',
          user_id: 'test-user-id',
          team_id: 'test-team-id',
          email: '<EMAIL>',
          first_name: 'Test',
          last_name: 'User',
          role: 'property_manager',
          status: 'active',
          created_at: new Date().toISOString(),
          profiles: {
            id: 'test-user-id',
            email: '<EMAIL>',
            first_name: 'Test',
            last_name: 'User',
            role: 'property_manager',
            avatar_url: null
          }
        }
      ], 
      error: null 
    });
  }),

  http.get('*/rest/v1/team_invitations*', () => {
    // Allow for caching by returning a 304 Not Modified occasionally
    // This helps prevent infinite loops in development
    if (Math.random() > 0.7) {
      return new HttpResponse(null, { status: 304 });
    }
    
    return HttpResponse.json({ 
      data: [
        { 
          id: 'test-invitation-1',
          team_id: 'test-team-id',
          email: '<EMAIL>',
          invited_by: 'test-user-id',
          role: 'staff',
          token: 'test-token-123',
          status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          teams: { name: 'Test Team' }
        }
      ], 
      error: null 
    });
  }),

  // Auth endpoints
  http.post('*/auth/v1/token*', ({ request }) => {
    // If it's a refresh token request, return a new token to prevent auth loops
    const url = new URL(request.url);
    if (url.searchParams.get('grant_type') === 'refresh_token') {
      return HttpResponse.json({
        access_token: 'new-mock-token-' + Date.now(),
        refresh_token: 'new-mock-refresh-token-' + Date.now(),
        expires_in: 3600,
        user: { id: 'test-user-id', email: '<EMAIL>' }
      });
    }
    
    return HttpResponse.json({ 
      access_token: 'mock-token', 
      refresh_token: 'mock-refresh-token',
      expires_in: 3600,
      user: { id: 'test-user-id', email: '<EMAIL>' } 
    });
  }),

  http.post('*/auth/v1/signup*', () => {
    return HttpResponse.json({ 
      data: { user: { id: 'new-user-id', email: '<EMAIL>' } },
      error: null
    });
  }),

  // Add more handlers as needed for your tests
];
