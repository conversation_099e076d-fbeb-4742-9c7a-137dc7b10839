// This is a test script to verify that team members can see tasks for properties in their teams
// Run this in the browser console when logged in as a team member

async function testTeamMemberTasks() {
  console.log('Testing team member tasks...');
  
  // Get the user's teams
  const { data: userTeams, error: teamsError } = await supabase
    .from('team_members')
    .select('team_id')
    .eq('user_id', supabase.auth.user().id)
    .eq('status', 'active');
  
  if (teamsError) {
    console.error('Error fetching user teams:', teamsError);
    return;
  }
  
  console.log('User teams:', userTeams);
  
  if (userTeams.length === 0) {
    console.log('User is not a member of any teams');
    return;
  }
  
  const teamIds = userTeams.map(tm => tm.team_id);
  
  // Get properties for these teams
  const { data: teamProperties, error: propertiesError } = await supabase
    .from('team_properties')
    .select('property_id')
    .in('team_id', teamIds);
  
  if (propertiesError) {
    console.error('Error fetching team properties:', propertiesError);
    return;
  }
  
  console.log('Team properties:', teamProperties);
  
  if (teamProperties.length === 0) {
    console.log('No properties found for user teams');
    return;
  }
  
  const propertyIds = teamProperties.map(tp => tp.property_id);
  
  // Get maintenance tasks for these properties
  const { data: tasks, error: tasksError } = await supabase
    .from('maintenance_tasks')
    .select('*')
    .in('property_id', propertyIds);
  
  if (tasksError) {
    console.error('Error fetching maintenance tasks:', tasksError);
    return;
  }
  
  console.log('Found', tasks.length, 'maintenance tasks for team properties');
  console.log('Tasks:', tasks);
}

// Run the test
testTeamMemberTasks();
