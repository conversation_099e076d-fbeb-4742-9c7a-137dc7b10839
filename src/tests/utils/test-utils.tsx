
import React, { PropsWithChildren } from 'react';
import { render, RenderOptions, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
// Mock AuthContext since the real one depends on browser APIs we can't use in tests
const MockAuthContext = React.createContext({
  authState: {
    user: { id: 'test-user-id', email: '<EMAIL>' },
    profile: {
      id: 'test-user-id',
      first_name: 'Test',
      last_name: 'User',
      email: '<EMAIL>',
      is_super_admin: false
    },
    isLoading: false,
    isAuthenticated: true,
    error: null,
    session: null,
    isImpersonating: false,
    originalUser: null
  },
  signIn: jest.fn().mockResolvedValue({ error: null }),
  signOut: jest.fn().mockResolvedValue(undefined),
  signUp: jest.fn().mockResolvedValue({ error: null }),
  startImpersonation: jest.fn().mockResolvedValue(undefined),
  stopImpersonation: jest.fn().mockResolvedValue(undefined),
  refreshProfile: jest.fn().mockResolvedValue(undefined)
});
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import userEvent from '@testing-library/user-event';
import { renderHook as originalRenderHook } from '@testing-library/react';
import { Toaster } from '@/components/ui/toaster';

// Re-export everything from testing-library
export * from '@testing-library/react';

// Create a custom render function that includes providers
const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'> & { 
    withAuth?: boolean;
    withRouter?: boolean;
    withQuery?: boolean;
    withToaster?: boolean;
    authState?: any;
  }
) => {
  const {
    withAuth = true,
    withRouter = true,
    withQuery = true,
    withToaster = true,
    authState = {
      user: { id: 'test-user-id', email: '<EMAIL>' },
      profile: { 
        id: 'test-user-id', 
        first_name: 'Test', 
        last_name: 'User', 
        email: '<EMAIL>', 
        is_super_admin: false 
      },
      isLoading: false,
      isImpersonating: false,
      originalUser: null,
      error: null
    },
    ...renderOptions
  } = options || {};

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  function Wrapper({ children }: PropsWithChildren<{}>): JSX.Element {
    let content = <>{children}</>;

    if (withAuth) {
      content = (
        <MockAuthContext.Provider value={{
          authState,
          signIn: jest.fn().mockResolvedValue({ error: null }),
          signOut: jest.fn().mockResolvedValue(undefined),
          signUp: jest.fn().mockResolvedValue({ error: null }),
          startImpersonation: jest.fn().mockResolvedValue(undefined),
          stopImpersonation: jest.fn().mockResolvedValue(undefined),
          refreshProfile: jest.fn().mockResolvedValue(undefined)
        }}>
          {content}
        </MockAuthContext.Provider>
      );
    }

    if (withQuery) {
      content = (
        <QueryClientProvider client={queryClient}>
          {content}
        </QueryClientProvider>
      );
    }

    // Remove Router wrapping capability - tests should handle their own routing context
    if (withRouter) {
      console.warn('Router wrapping is disabled - tests should handle their own routing context');
    }

    if (withToaster) {
      content = (
        <>
          {content}
          <Toaster />
        </>
      );
    }

    return content;
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// Custom renderHook function that wraps in the same providers
const renderHook = <Result, Props>(
  hook: (props: Props) => Result,
  options?: Parameters<typeof originalRenderHook<Result, Props>>[1] & {
    withAuth?: boolean;
    withRouter?: boolean;
    withQuery?: boolean;
    withToaster?: boolean;
    authState?: any;
  }
) => {
  const {
    withAuth = true,
    withRouter = true,
    withQuery = true,
    withToaster = true,
    authState,
    ...renderHookOptions
  } = options || {};

  const wrapper = ({ children }: PropsWithChildren<{}>) => {
    let content = <>{children}</>;

    if (withAuth) {
      content = (
        <MockAuthContext.Provider
          value={{
            authState: authState || {
              user: { id: 'test-user-id', email: '<EMAIL>' },
              profile: {
                id: 'test-user-id',
                first_name: 'Test',
                last_name: 'User',
                email: '<EMAIL>',
                is_super_admin: false
              },
              isLoading: false,
              isImpersonating: false,
              originalUser: null,
              error: null
            },
            signIn: jest.fn().mockResolvedValue({ error: null }),
            signOut: jest.fn().mockResolvedValue(undefined),
            signUp: jest.fn().mockResolvedValue({ error: null }),
            startImpersonation: jest.fn().mockResolvedValue(undefined),
            stopImpersonation: jest.fn().mockResolvedValue(undefined),
            refreshProfile: jest.fn().mockResolvedValue(undefined)
          }}
        >
          {content}
        </MockAuthContext.Provider>
      );
    }

    if (withQuery) {
      const queryClient = new QueryClient({
        defaultOptions: {
          queries: {
            retry: false,
          },
        },
      });
      content = (
        <QueryClientProvider client={queryClient}>{content}</QueryClientProvider>
      );
    }

    if (withRouter) {
      content = <BrowserRouter>{content}</BrowserRouter>;
    }

    if (withToaster) {
      content = (
        <>
          {content}
          <Toaster />
        </>
      );
    }

    return content;
  };

  return originalRenderHook(hook, { wrapper, ...renderHookOptions });
};

// Override the render method
export { customRender as render, renderHook, userEvent, screen, waitFor };
