
import { render, screen, fireEvent, waitFor } from '../utils/test-utils';
import ComponentWith<PERSON>pi from '@/components/path/to/ComponentWithApi';

describe('ComponentWithApi Integration', () => {
  it('loads and displays data from API', async () => {
    render(<ComponentWithApi />);
    
    // Check loading state
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
    });
    
    // Verify data is displayed
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
  });
  
  it('handles form submission and API updates', async () => {
    render(<ComponentWithApi />);
    
    // Wait for component to load
    await waitFor(() => {
      expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
    });
    
    // Fill out form
    fireEvent.change(screen.getByLabelText(/name/i), {
      target: { value: 'New Item' },
    });
    
    // Submit form
    fireEvent.click(screen.getByRole('button', { name: /submit/i }));
    
    // Verify loading state during submission
    expect(screen.getByText(/submitting/i)).toBeInTheDocument();
    
    // Verify success state
    await waitFor(() => {
      expect(screen.getByText(/successfully created/i)).toBeInTheDocument();
    });
    
    // Verify new item is in the list
    expect(screen.getByText('New Item')).toBeInTheDocument();
  });
});
