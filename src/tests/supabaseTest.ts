// Simple Supabase connection test
// NOTE: This is a test file. For production, use environment variables.

// Use environment variables if available, fallback to development values for testing
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://pwaeknalhosfwuxkpaet.supabase.co";
const SUPABASE_KEY = import.meta.env.VITE_SUPABASE_PUBLISHABLE_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4";

async function testSupabase() {
  const { createClient } = await import('@supabase/supabase-js');
  const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

  console.log('Testing Supabase connection...');
  
  // Try selecting all columns to detect schema
  const { data: columns, error: selectError } = await supabase
    .from('team_members')
    .select('*')
    .limit(0); // Returns only column info

  if (selectError) {
    console.error('Column detection error:', selectError);
  } else {
    console.log('Detected columns:', Object.keys(columns[0] || {}));
  }

  // Try inserting a test record
  const { data: insertData, error: insertError } = await supabase
    .from('team_members')
    .insert({
      team_id: '00000000-0000-0000-0000-000000000000', // dummy UUID
      user_id: '00000000-0000-0000-0000-000000000000', // dummy UUID 
      role: 'member',
      status: 'active'
    })
    .select();

  if (insertError) {
    console.log('Insert test failed (expected if RLS enabled):', insertError.message);
  } else {
    console.log('Insert test succeeded:', insertData);
  }
}

testSupabase();
