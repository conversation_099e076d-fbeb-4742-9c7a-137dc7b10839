
import { render, screen, fireEvent, waitFor } from '@/tests/utils/test-utils';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';

// Mock component that uses auth context
function TestComponent() {
  const { authState, signIn, signOut } = useAuth();
  
  return (
    <div>
      <p>User: {authState.user ? authState.user.email : 'Not logged in'}</p>
      <button onClick={() => signIn('<EMAIL>', 'password')}>Sign In</button>
      <button onClick={() => signOut()}>Sign Out</button>
    </div>
  );
}

// Mock toast
jest.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn()
  })
}));

// Mock supabase
jest.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: { session: null },
        error: null
      }),
      onAuthStateChange: jest.fn().mockReturnValue({
        data: { subscription: { unsubscribe: jest.fn() } }
      }),
      signInWithPassword: jest.fn().mockResolvedValue({
        data: {
          user: { id: 'test-user-id', email: '<EMAIL>' },
          session: { access_token: 'test-token' }
        },
        error: null
      }),
      signOut: jest.fn().mockResolvedValue({ error: null }),
      signUp: jest.fn().mockResolvedValue({
        data: { user: { id: 'new-user-id' } },
        error: null
      }),
      admin: {
        getUserById: jest.fn().mockResolvedValue({
          data: { user: { id: 'impersonated-user-id', email: '<EMAIL>' } },
          error: null
        })
      }
    },
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
    then: jest.fn().mockImplementation((callback) => {
      return Promise.resolve(callback({
        data: {
          id: 'test-user-id',
          first_name: 'Test',
          last_name: 'User',
          email: '<EMAIL>',
          is_super_admin: false
        },
        error: null
      }));
    })
  }
}));

describe('AuthContext', () => {
  it('provides authState with initial null user', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    expect(screen.getByText('User: Not logged in')).toBeInTheDocument();
  });
  
  it('handles sign in', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    fireEvent.click(screen.getByText('Sign In'));
    
    await waitFor(() => {
      expect(screen.getByText('User: <EMAIL>')).toBeInTheDocument();
    });
  });
  
  it('handles sign out', async () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    // First sign in
    fireEvent.click(screen.getByText('Sign In'));
    
    await waitFor(() => {
      expect(screen.getByText('User: <EMAIL>')).toBeInTheDocument();
    });
    
    // Then sign out
    fireEvent.click(screen.getByText('Sign Out'));
    
    await waitFor(() => {
      expect(screen.getByText('User: Not logged in')).toBeInTheDocument();
    });
  });
});
