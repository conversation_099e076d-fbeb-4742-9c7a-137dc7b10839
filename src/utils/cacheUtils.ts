/**
 * Simplified utility functions for data refreshing using React Query
 *
 * This module provides basic functions for managing React Query's cache
 * and refreshing data across the application.
 */

import { QueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

// Global query client for use across the application
let globalQueryClient: QueryClient | null = null;

/**
 * Set the global query client for use across the application
 */
export const setGlobalQueryClient = (queryClient: QueryClient) => {
  globalQueryClient = queryClient;
};

/**
 * Get the global query client
 */
export const getGlobalQueryClient = (): QueryClient => {
  if (!globalQueryClient) {
    // Create a new query client if one doesn't exist
    globalQueryClient = new QueryClient({
      defaultOptions: {
        queries: {
          refetchOnWindowFocus: 'always', // Changed to 'always' to match global configuration
          refetchOnMount: 'always', // Changed to 'always' to match global configuration
          refetchOnReconnect: true,
          retry: 3,
          retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
          staleTime: 1000 * 60 * 5, // 5 minutes - consistent with other settings
          gcTime: 30 * 60 * 1000, // 30 minutes - consistent with other settings
          networkMode: 'always',
          keepPreviousData: true, // Keep previous data while fetching new data
        },
      },
    });
  }
  return globalQueryClient;
};

/**
 * Refresh all data using React Query's built-in functionality
 */
export const refreshAllData = async (): Promise<boolean> => {
  try {
    // Get the global query client
    const queryClient = getGlobalQueryClient();

    // First, invalidate all queries
    await queryClient.invalidateQueries();

    // Then, force refetch all queries
    await queryClient.refetchQueries({
      type: 'all',
      stale: true
    });

    console.log('[cacheUtils] All data refreshed successfully');
    return true;
  } catch (error) {
    console.error('[cacheUtils] Error refreshing data:', error);
    return false;
  }
};

/**
 * Setup a simple visibility change handler that refreshes data when the page becomes visible
 *
 * NOTE: This function is intentionally disabled to prevent focus-related issues.
 * It now only handles network reconnection events.
 */
export const setupGlobalVisibilityChangeHandler = () => {
  if (typeof document === 'undefined') return;

  // Handle online event
  const handleOnline = () => {
    console.log('[cacheUtils] Network is now online, refreshing data...');

    try {
      // Get the global query client
      const queryClient = getGlobalQueryClient();

      // Invalidate all queries
      queryClient.invalidateQueries();

      // Force refetch all queries
      queryClient.refetchQueries({
        type: 'all',
        stale: true
      });

      console.log('[cacheUtils] Forced refetch of all queries due to network reconnection');
    } catch (error) {
      console.error('[cacheUtils] Error refreshing data on network reconnection:', error);
    }
  };

  // Add event listeners - only for network events, not visibility
  window.addEventListener('online', handleOnline);

  // Return cleanup function
  return () => {
    window.removeEventListener('online', handleOnline);
  };
};

/**
 * For backward compatibility
 */
export const refreshAllDataSimple = refreshAllData;
export const clearCache = refreshAllData;
export const clearCacheAndRefresh = refreshAllData;

/**
 * Retry a Supabase fetch operation with exponential backoff
 * @param fetchFn The fetch function to retry
 * @param maxRetries Maximum number of retries
 * @param options Additional options
 * @returns Promise with the fetch result
 */
export const retrySupabaseFetch = async (
  fetchFn: () => Promise<any>,
  maxRetries = 3,
  options: {
    initialDelay?: number;
    maxDelay?: number;
    operation?: string;
  } = {}
): Promise<any> => {
  const {
    initialDelay = 1000,
    maxDelay = 10000,
    operation = 'data fetch'
  } = options;

  let lastError: any = null;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      // Attempt the fetch
      const result = await fetchFn();
      return result;
    } catch (error: any) {
      lastError = error;

      // Check if it's a network error
      const isNetworkError = error.message && (
        error.message.includes('Failed to fetch') ||
        error.message.includes('NetworkError') ||
        error.message.includes('Network request failed') ||
        error.message.includes('Fetch timeout')
      );

      if (isNetworkError) {
        console.error(`[cacheUtils] Network error on attempt ${attempt + 1}/${maxRetries + 1}:`, error);

        // If we've reached max retries, throw
        if (attempt === maxRetries) {
          console.error('[cacheUtils] Max retries reached, giving up');
          throw error;
        }

        // Wait with exponential backoff before retrying
        const delay = Math.min(initialDelay * Math.pow(2, attempt), maxDelay);
        console.log(`[cacheUtils] Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));

        // Try to refresh the Supabase connection before retrying
        if (attempt > 0) {
          try {
            const { data, error: refreshError } = await supabase.auth.refreshSession();
            if (refreshError) {
              console.error('[cacheUtils] Error refreshing Supabase session:', refreshError);
            } else if (data.session) {
              console.log('[cacheUtils] Refreshed Supabase auth session before retry');
            }
          } catch (e) {
            console.error('[cacheUtils] Exception refreshing Supabase session:', e);
          }
        }
      } else {
        // Not a network error, just throw it
        throw error;
      }
    }
  }

  // If we get here, all retries failed
  throw lastError;
};

/**
 * Get all query keys related to a specific page
 * @param page The page name (e.g., 'dashboard', 'maintenance', 'inventory')
 * @returns Array of query keys
 */
export const getPageQueryKeys = (page: string): string[] => {
  // Common keys used across multiple pages
  const commonKeys = ['properties', 'teams', 'teamMembers', 'user', 'profile'];

  // Include both kebab-case and camelCase versions of keys for compatibility
  switch (page) {
    case 'dashboard':
      return [
        ...commonKeys,
        'maintenanceTasks', 'maintenance-tasks',
        'inventoryItems', 'inventory-items', 'inventory',
        'damageReports', 'damage-reports',
        'purchaseOrders', 'purchase-orders',
        'dashboard', 'dashboard-data'
      ];
    case 'maintenance':
      return [
        ...commonKeys,
        'maintenanceTasks', 'maintenance-tasks',
        'maintenanceProviders', 'maintenance-providers',
        'providers'
      ];
    case 'inventory':
      return [
        ...commonKeys,
        'inventory', 'inventory-items',
        'collections', 'categories'
      ];
    case 'operations':
      return [
        ...commonKeys,
        'operations', 'operations-data',
        'maintenanceTasks', 'maintenance-tasks',
        'damageReports', 'damage-reports',
        'bookings', 'reservations'
      ];
    case 'damages':
      return [
        ...commonKeys,
        'damageReports', 'damage-reports'
      ];
    case 'purchase-orders':
      return [
        ...commonKeys,
        'purchaseOrders', 'purchase-orders',
        'inventory', 'inventory-items'
      ];
    default:
      // For unknown pages, invalidate all common keys plus the page name itself
      return [...commonKeys, page, page.replace(/-/g, ''), page.replace(/([A-Z])/g, '-$1').toLowerCase()];
  }
};

/**
 * Refresh data for a specific page
 * @param page The page name
 * @returns Promise that resolves when all queries are invalidated
 */
export const refreshPageData = async (page: string): Promise<boolean> => {
  try {
    console.log(`[refreshPageData] Refreshing data for ${page} page`);

    // Get the query keys for this page
    const queryKeys = getPageQueryKeys(page);
    const queryClient = getGlobalQueryClient();

    // Invalidate and refetch each query key
    for (const key of queryKeys) {
      await queryClient.invalidateQueries({ queryKey: [key], exact: false });
      await queryClient.refetchQueries({ queryKey: [key], exact: false, type: 'all' });
    }

    console.log(`[refreshPageData] Successfully refreshed data for ${page} page`);
    return true;
  } catch (e) {
    console.error(`[refreshPageData] Error refreshing ${page} data:`, e);
    return false;
  }
};

/**
 * Setup visibility change handler for a component
 *
 * NOTE: This function is intentionally disabled to prevent focus-related issues.
 * It now only handles force refresh events, not visibility changes.
 *
 * @param refreshFunction Function to call when a force refresh is triggered
 * @param options Additional options
 * @returns Cleanup function
 */
export const setupVisibilityChangeHandler = (
  refreshFunction: () => Promise<void> | void,
  options: {
    debugName?: string; // Name for debugging
    forceRefreshEventName?: string; // Custom event name to force refresh
  } = {}
) => {
  const {
    debugName = 'Component',
    forceRefreshEventName
  } = options;

  // Handle force refresh
  const handleForceRefresh = () => {
    console.log(`[${debugName}] Force refresh triggered`);
    refreshFunction();
  };

  // Set up event listeners - only for force refresh events, not visibility
  window.addEventListener('stayfu-data-refreshed', handleForceRefresh);
  window.addEventListener('stayfu-cache-cleared', handleForceRefresh);

  // Add custom force refresh event listener if provided
  if (forceRefreshEventName) {
    window.addEventListener(forceRefreshEventName, handleForceRefresh);
  }

  // Return cleanup function
  return () => {
    window.removeEventListener('stayfu-data-refreshed', handleForceRefresh);
    window.removeEventListener('stayfu-cache-cleared', handleForceRefresh);

    if (forceRefreshEventName) {
      window.removeEventListener(forceRefreshEventName, handleForceRefresh);
    }
  };
};
