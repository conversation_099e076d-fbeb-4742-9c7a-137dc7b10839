/**
 * Utility functions for session management and data loading
 */

import { supabase } from '@/integrations/supabase/client';

/**
 * Check if the user has a valid session
 * @returns Promise<boolean> - true if the user has a valid session
 */
export const checkSession = async (): Promise<boolean> => {
  const startTime = Date.now();

  try {
    // First try to get the session from Supabase
    console.log(`[checkSession] Calling supabase.auth.getSession()`);
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.error(`[checkSession] Error checking session:`, error);
      // Try to get session from localStorage as fallback
      return checkLocalSession();
    }

    if (!data.session) {
      console.log(`[checkSession] No active session found in Supabase`);
      // Try to get session from localStorage as fallback
      return checkLocalSession();
    }

    // Check if the session is expired or about to expire
    const expiresAt = data.session.expires_at;
    if (expiresAt) {
      const now = Math.floor(Date.now() / 1000); // Current time in seconds
      const timeUntilExpiry = expiresAt - now;
      const hoursUntilExpiry = Math.floor(timeUntilExpiry / 3600);
      const minutesUntilExpiry = Math.floor((timeUntilExpiry % 3600) / 60);

      console.log(`[checkSession] Session found with user: ${data.session.user?.email || 'unknown'}`);
      console.log(`[checkSession] Session expires in: ${hoursUntilExpiry}h ${minutesUntilExpiry}m (${timeUntilExpiry}s)`);

      // Store session check info for debugging
      try {
        const sessionChecks = JSON.parse(localStorage.getItem('stayfu_session_checks') || '[]');
        sessionChecks.push({
          time: new Date().toISOString(),
          user: data.session.user?.email || 'unknown',
          expiresAt: new Date(expiresAt * 1000).toISOString(),
          timeUntilExpiry: {
            seconds: timeUntilExpiry,
            minutes: minutesUntilExpiry,
            hours: hoursUntilExpiry
          },
          url: window.location.href
        });

        // Keep only the last 20 checks
        if (sessionChecks.length > 20) {
          sessionChecks.shift();
        }

        localStorage.setItem('stayfu_session_checks', JSON.stringify(sessionChecks));
      } catch (e) {
        console.error('Error saving session check to localStorage:', e);
      }

      // If expired or less than 12 hours until expiry, refresh the token
      // Increased from 1 hour to 12 hours to ensure sessions remain valid longer
      if (expiresAt < now || timeUntilExpiry < 43200) { // 12 hours in seconds
        console.log(`[checkSession] Session ${expiresAt < now ? 'is expired' : 'expires soon'}, attempting to refresh`);
        return await refreshAuthToken();
      }

      // Save the session to localStorage for backup
      try {
        localStorage.setItem('stayfu_auth_token', JSON.stringify(data.session));

        // Also store a flag indicating we have a valid session
        localStorage.setItem('stayfu_has_session', 'true');
        localStorage.setItem('stayfu_session_expires', new Date(expiresAt * 1000).toISOString());
      } catch (e) {
        console.error(`[checkSession] Error saving session to localStorage:`, e);
      }
    } else {
      console.log(`[checkSession] Session found but no expiration time`);
    }

    const endTime = Date.now();
    console.log(`[checkSession] Session check completed in ${endTime - startTime}ms`);
    return true;
  } catch (e) {
    console.error(`[checkSession] Exception checking session:`, e);
    // Try to get session from localStorage as fallback
    return checkLocalSession();
  }
};

/**
 * Check if there's a valid session in localStorage
 * @returns boolean - true if a valid session was found in localStorage
 */
export const checkLocalSession = (): boolean => {
  try {
    // Try to get session from localStorage
    const localSession = localStorage.getItem('stayfu_auth_token');
    if (!localSession) {
      console.log('No session found in localStorage');
      return false;
    }

    // Parse the session
    const session = JSON.parse(localSession);
    if (!session || !session.expires_at) {
      console.log('Invalid session format in localStorage');
      return false;
    }

    // Check if the session is expired
    const expiresAt = session.expires_at;
    const now = Math.floor(Date.now() / 1000); // Current time in seconds

    if (expiresAt < now) {
      console.log('Session in localStorage is expired');
      return false;
    }

    // Session is valid
    const timeUntilExpiry = expiresAt - now;
    const hoursUntilExpiry = Math.floor(timeUntilExpiry / 3600);
    console.log(`Session in localStorage valid for approximately ${hoursUntilExpiry} more hours`);

    return true;
  } catch (e) {
    console.error('Error checking local session:', e);
    return false;
  }
};

/**
 * Refresh the auth token if needed
 * @returns Promise<boolean> - true if the token was refreshed successfully
 */
export const refreshAuthToken = async (): Promise<boolean> => {
  try {
    console.log('Attempting to refresh auth token');

    // First try to refresh the session
    const { data, error } = await supabase.auth.refreshSession();

    if (error) {
      console.error('Error refreshing token:', error);

      // If we get an error refreshing, try to get the current session
      try {
        const { data: sessionData } = await supabase.auth.getSession();
        if (sessionData.session) {
          console.log('Current session is still valid despite refresh error');

          // Save the session to localStorage for backup
          try {
            localStorage.setItem('stayfu_auth_token', JSON.stringify(sessionData.session));
          } catch (storageError) {
            console.error('Error saving session to localStorage:', storageError);
          }

          return true;
        }
      } catch (sessionError) {
        console.error('Error getting current session:', sessionError);
      }

      // Try to use the session from localStorage as a last resort
      return checkLocalSession();
    }

    if (data.session) {
      const expiresAt = new Date(data.session.expires_at! * 1000);
      const now = new Date();
      const hoursUntilExpiry = Math.floor((expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60));

      console.log('Token refreshed successfully:');
      console.log(`- Expires at: ${expiresAt.toLocaleString()}`);
      console.log(`- Valid for approximately: ${hoursUntilExpiry} hours`);
      console.log(`- Access token length: ${data.session.access_token.length} chars`);
      console.log(`- Refresh token length: ${data.session.refresh_token.length} chars`);

      // Store the session in localStorage for backup
      try {
        localStorage.setItem('stayfu_auth_token', JSON.stringify(data.session));
      } catch (storageError) {
        console.error('Error saving session to localStorage:', storageError);
      }

      // Store the last refresh time in localStorage for debugging
      try {
        localStorage.setItem('stayfu_last_token_refresh', JSON.stringify({
          time: now.toISOString(),
          expiresAt: expiresAt.toISOString(),
          validForHours: hoursUntilExpiry
        }));
      } catch (storageError) {
        console.error('Error saving refresh time to localStorage:', storageError);
      }

      return true;
    } else {
      console.log('No session returned from refresh');
      // Try to use the session from localStorage as a last resort
      return checkLocalSession();
    }
  } catch (e) {
    console.error('Exception refreshing token:', e);
    // Try to use the session from localStorage as a last resort
    return checkLocalSession();
  }
};

/**
 * Wrapper for data fetching functions that handles session checking and token refreshing
 * @param fetchFunction - The function to fetch data
 * @param retryCount - Number of retries (default: 1)
 * @returns Promise<T> - The fetched data
 */
export const withSessionCheck = async <T>(
  fetchFunction: () => Promise<T>,
  retryCount = 1
): Promise<T> => {
  try {
    // Check if we have a valid session before fetching
    const isSessionValid = await checkSession();
    if (!isSessionValid) {
      console.log('No valid session found, attempting to refresh token...');
      const refreshed = await refreshAuthToken();

      if (!refreshed) {
        console.error('Failed to refresh token, redirecting to login');
        // Redirect to login page with reason using hash routing
        window.location.href = '/#/login?reason=session_expired';
        throw new Error('Session expired. Please log in again.');
      }
    }

    // Now try to fetch the data
    return await fetchFunction();
  } catch (error: any) {
    // If we get a 401 Unauthorized error, try to refresh the token
    if (error?.status === 401 || error?.code === 'PGRST301' || error?.message?.includes('JWT') ||
        error?.message?.includes('Failed to fetch')) {
      console.log('Session expired or network error, attempting to refresh token...');

      if (retryCount <= 0) {
        console.error('Max retries reached, giving up');
        throw error;
      }

      const refreshed = await refreshAuthToken();

      if (refreshed) {
        console.log('Token refreshed successfully, retrying fetch...');
        // Retry the fetch with the new token
        return withSessionCheck(fetchFunction, retryCount - 1);
      } else {
        console.error('Failed to refresh token');
        // Redirect to login page with reason using hash routing
        window.location.href = '/#/login?reason=session_expired';
        throw new Error('Session expired. Please log in again.');
      }
    }

    // For other errors, just throw
    throw error;
  }
};

/**
 * Cache data in localStorage with expiration
 * @param key - The key to store the data under
 * @param data - The data to store
 * @param expirationMinutes - How long to cache the data in minutes (default: 5)
 */
export const cacheData = <T>(key: string, data: T, expirationMinutes = 5): void => {
  const expirationMs = expirationMinutes * 60 * 1000;
  const item = {
    data,
    expiration: Date.now() + expirationMs
  };

  localStorage.setItem(key, JSON.stringify(item));
};

/**
 * Get cached data from localStorage if it's not expired
 * @param key - The key to retrieve the data from
 * @returns T | null - The cached data or null if expired or not found
 */
export const getCachedData = <T>(key: string): T | null => {
  const item = localStorage.getItem(key);

  if (!item) {
    return null;
  }

  try {
    const parsed = JSON.parse(item);

    if (parsed.expiration < Date.now()) {
      // Data is expired, remove it
      localStorage.removeItem(key);
      return null;
    }

    return parsed.data as T;
  } catch (e) {
    console.error('Error parsing cached data:', e);
    localStorage.removeItem(key);
    return null;
  }
};

// clearCache function has been removed

/**
 * Fetch data with caching
 * @param key - The key to cache the data under
 * @param fetchFunction - The function to fetch the data
 * @param expirationMinutes - How long to cache the data in minutes (default: 5)
 * @param forceRefresh - Whether to force a refresh of the data (default: false)
 * @returns Promise<T> - The fetched or cached data
 */
export const fetchWithCache = async <T>(
  key: string,
  fetchFunction: () => Promise<T>,
  expirationMinutes = 5,
  forceRefresh = false
): Promise<T> => {
  const cacheKey = `cache:${key}`;

  // Check cache first unless forceRefresh is true
  if (!forceRefresh) {
    const cachedData = getCachedData<T>(cacheKey);

    if (cachedData) {
      console.log(`Using cached data for ${key}`);
      return cachedData;
    }
  }

  // Fetch fresh data with session check
  const data = await withSessionCheck(fetchFunction);

  // Cache the data
  cacheData(cacheKey, data, expirationMinutes);

  return data;
};

/**
 * Debug function to check session status - can be called from browser console
 * Usage: window.checkSessionStatus()
 */
export const checkSessionStatus = async (): Promise<void> => {
  try {
    console.group('Session Status Check');

    // Get current session
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.error('Error getting session:', error);
      return;
    }

    if (!data.session) {
      console.log('No active session found');
      return;
    }

    // Display session info
    const expiresAt = new Date(data.session.expires_at! * 1000);
    const now = new Date();
    const timeUntilExpiry = Math.floor((expiresAt.getTime() - now.getTime()) / 1000);
    const hoursUntilExpiry = Math.floor(timeUntilExpiry / 3600);
    const minutesUntilExpiry = Math.floor((timeUntilExpiry % 3600) / 60);

    console.log('Current Session Info:');
    console.log(`- User: ${data.session.user.email}`);
    console.log(`- Created at: ${new Date(data.session.created_at).toLocaleString()}`);
    console.log(`- Expires at: ${expiresAt.toLocaleString()}`);
    console.log(`- Time until expiry: ${hoursUntilExpiry}h ${minutesUntilExpiry}m`);

    // Get last refresh info
    const lastRefreshStr = localStorage.getItem('stayfu_last_token_refresh');
    if (lastRefreshStr) {
      try {
        const lastRefresh = JSON.parse(lastRefreshStr);
        console.log('Last Token Refresh:');
        console.log(`- Time: ${new Date(lastRefresh.time).toLocaleString()}`);
        console.log(`- Expires at: ${new Date(lastRefresh.expiresAt).toLocaleString()}`);
        console.log(`- Valid for: ${lastRefresh.validForHours} hours`);
      } catch (e) {
        console.error('Error parsing last refresh info:', e);
      }
    } else {
      console.log('No record of last token refresh');
    }

    console.groupEnd();
  } catch (e) {
    console.error('Error checking session status:', e);
    console.groupEnd();
  }
};

// Make the function available in the global scope for debugging
if (typeof window !== 'undefined') {
  (window as any).checkSessionStatus = checkSessionStatus;

  // Add debug utility functions to window
  (window as any).getSessionDebugInfo = () => {
    try {
      // Get session info
      const sessionStr = localStorage.getItem('stayfu_auth_token');
      let sessionInfo = { exists: false, expiresAt: null, timeUntilExpiry: null };

      if (sessionStr) {
        try {
          const session = JSON.parse(sessionStr);
          if (session && session.expires_at) {
            const now = Math.floor(Date.now() / 1000);
            const expiresAt = session.expires_at;
            const timeUntilExpiry = expiresAt - now;

            sessionInfo = {
              exists: true,
              expiresAt: new Date(expiresAt * 1000).toISOString(),
              timeUntilExpiry: {
                seconds: timeUntilExpiry,
                minutes: Math.floor(timeUntilExpiry / 60),
                hours: Math.floor(timeUntilExpiry / 3600)
              }
            };
          }
        } catch (e) {
          console.error('Error parsing session:', e);
        }
      }

      // Get debug data from localStorage
      const debugData = {
        sessionChecks: localStorage.getItem('stayfu_session_checks'),
        lastTokenRefresh: localStorage.getItem('stayfu_last_token_refresh'),
        sessionExpired: localStorage.getItem('stayfu_session_expired'),
        lastSessionRefresh: localStorage.getItem('stayfu_last_session_refresh'),
        tokenRefreshed: localStorage.getItem('stayfu_token_refreshed'),
        dataRefreshes: localStorage.getItem('stayfu_data_refreshes'),
        visibilityChanges: localStorage.getItem('stayfu_data_visibility_changes'),
        routeChanges: localStorage.getItem('stayfu_route_changes'),
        userActivities: localStorage.getItem('stayfu_user_activities')
      };

      return {
        session: sessionInfo,
        localStorage: {
          hasSession: localStorage.getItem('stayfu_has_session') === 'true',
          sessionExpires: localStorage.getItem('stayfu_session_expires')
        },
        debugData,
        timestamp: new Date().toISOString()
      };
    } catch (e) {
      console.error('Error getting session debug info:', e);
      return { error: String(e) };
    }
  };

  // Add a method to clear all debug data
  (window as any).clearAllDebugData = () => {
    try {
      localStorage.removeItem('stayfu_session_checks');
      localStorage.removeItem('stayfu_last_token_refresh');
      localStorage.removeItem('stayfu_session_expired');
      localStorage.removeItem('stayfu_last_session_refresh');
      localStorage.removeItem('stayfu_token_refreshed');
      localStorage.removeItem('stayfu_data_refreshes');
      localStorage.removeItem('stayfu_data_visibility_changes');
      localStorage.removeItem('stayfu_route_changes');
      localStorage.removeItem('stayfu_user_activities');
      localStorage.removeItem('stayfu_debug_logs');
      console.log('All debug data cleared from localStorage');
      return true;
    } catch (e) {
      console.error('Error clearing debug data:', e);
      return false;
    }
  };

  // Add a method to download all debug data
  (window as any).downloadAllDebugData = () => {
    try {
      const debugData = (window as any).getSessionDebugInfo();
      const dataStr = JSON.stringify(debugData, null, 2);

      // Format data for download
      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      // Create download link
      const a = document.createElement('a');
      a.href = url;
      a.download = `stayfu-debug-data-${new Date().toISOString().replace(/:/g, '-')}.json`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 100);

      console.log('Debug data downloaded');
      return true;
    } catch (e) {
      console.error('Error downloading debug data:', e);
      return false;
    }
  };

  console.log('Debug utilities added to window object: checkSessionStatus, getSessionDebugInfo, clearAllDebugData, downloadAllDebugData');
}
