import { Property as DatabaseProperty } from '@/hooks/useProperties';
import { Property as CardProperty, CollectionWithBudget } from '@/components/properties/PropertyCard';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Utility function to check if an image exists
export const checkImageExists = async (url: string): Promise<boolean> => {
  if (!url) return false;

  // For now, skip the network check and assume images exist
  // This prevents network errors from blocking the UI
  // TODO: Re-enable when network connectivity is more stable
  return true;

  // Original implementation commented out to prevent network errors
  /*
  try {
    // Create an AbortController to set a timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 second timeout

    try {
      // Use the AbortController signal with the fetch request
      const response = await fetch(url, {
        method: 'HEAD',
        signal: controller.signal,
        // Add cache control to prevent caching issues
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (fetchError: any) {
      clearTimeout(timeoutId);

      // Handle specific fetch errors
      if (fetchError.name === 'AbortError') {
        console.warn(`Image check timed out for: ${url}`);
      } else {
        console.error('Error checking image existence:', fetchError);
      }

      // For network errors, try an alternative approach with Image object
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        img.src = url;

        // Set another timeout for the image load
        setTimeout(() => {
          img.src = '';
          resolve(false);
        }, 3000);
      });
    }
  } catch (error) {
    console.error('Unexpected error checking image existence:', error);
    return false;
  }
  */
};

export const mapPropertyToCardProperty = (dbProperty: DatabaseProperty): CardProperty => {
  // Ensure image_url is properly formatted
  let imageUrl = dbProperty.image_url || '';

  // Log the original image URL for debugging
  console.log(`[propertyUtils] Original image_url for ${dbProperty.name}:`, imageUrl);

  // If the URL is not empty but doesn't start with http/https, add the Supabase URL prefix
  if (imageUrl && !imageUrl.startsWith('http')) {
    // This is a fallback in case the URL is a relative path
    imageUrl = `https://pwaeknalhosfwuxkpaet.supabase.co/storage/v1/object/public/${imageUrl}`;
    console.log(`[propertyUtils] Fixed image_url:`, imageUrl);
  }

  // Add cache-busting parameter to prevent caching issues
  if (imageUrl) {
    const separator = imageUrl.includes('?') ? '&' : '?';
    imageUrl = `${imageUrl}${separator}_t=${Date.now()}`;
    console.log(`[propertyUtils] Added cache-busting to image_url:`, imageUrl);
  }

  // Ensure numeric values for bedrooms and bathrooms
  const bedrooms = typeof dbProperty.bedrooms === 'number' ? dbProperty.bedrooms :
                  parseInt(String(dbProperty.bedrooms || '0')) || 0;

  const bathrooms = typeof dbProperty.bathrooms === 'number' ? dbProperty.bathrooms :
                   parseInt(String(dbProperty.bathrooms || '0')) || 0;

  // Ensure budget is a number
  const budget = typeof dbProperty.budget === 'number' ? dbProperty.budget :
               parseFloat(String(dbProperty.budget || '0')) || 0;

  // Log the property data for debugging
  console.log(`[propertyUtils] Mapping property ${dbProperty.name}:`, {
    bedrooms: dbProperty.bedrooms,
    parsedBedrooms: bedrooms,
    bathrooms: dbProperty.bathrooms,
    parsedBathrooms: bathrooms,
    budget: dbProperty.budget,
    parsedBudget: budget
  });

  return {
    id: dbProperty.id,
    name: dbProperty.name,
    address: dbProperty.address,
    city: dbProperty.city,
    state: dbProperty.state,
    zip: dbProperty.zip,
    imageUrl: imageUrl,
    bedrooms: bedrooms,
    bathrooms: bathrooms,
    budget: budget,
    collections: dbProperty.collections || [],
    next_booking: dbProperty.next_booking,
    ical_url: dbProperty.ical_url,
    is_occupied: dbProperty.is_occupied,
    current_checkout: dbProperty.current_checkout,
    next_checkin_date: dbProperty.next_checkin_date,
    next_checkin_formatted: dbProperty.next_checkin_formatted,
    last_ical_sync: dbProperty.last_ical_sync,
    timezone: dbProperty.timezone,
    check_in_time: dbProperty.check_in_time,
    check_out_time: dbProperty.check_out_time
  };
};

export const fetchCalendarData = async (
  url: string,
  propertyId: string,
  userId: string,
  onComplete?: () => Promise<void>
) => {
  try {
    const { data, error } = await supabase.functions.invoke('fetch-ical-data', {
      body: { url, propertyId, userId }
    });

    if (error) {
      console.error('Error fetching iCal data:', error);
      toast.error('Failed to sync calendar');
      throw error;
    }

    console.log("Calendar data response:", data);

    if (data.success) {
      toast.success('Calendar synced successfully');
      if (onComplete) {
        await onComplete();
      }
    } else {
      toast.error(data.error || 'Calendar sync failed');
    }

    return data;
  } catch (error: any) {
    console.error('Error in fetchCalendarData:', error);
    toast.error('Failed to sync calendar');
    throw error;
  }
};

export const syncAllPropertyCalendars = async (userId: string): Promise<boolean> => {
  try {
    toast.loading('Starting calendar sync for all properties...');

    // Call the sync-property-calendars edge function
    const { data, error } = await supabase.functions.invoke('sync-property-calendars', {
      body: {
        forceSyncAll: true
      }
    });

    if (error) {
      console.error('Error invoking sync-property-calendars function:', error);
      toast.error(`Failed to sync calendars: ${error.message}`);
      return false;
    }

    if (data.success) {
      const synced = data.results.filter(r => r.status === 'success').length;
      const failed = data.results.filter(r => r.status === 'error').length;

      toast.success(`Calendar sync complete: ${synced} properties updated, ${failed} failed`);
      return true;
    } else {
      toast.error(`Error syncing calendars: ${data.error || 'Unknown error'}`);
      return false;
    }
  } catch (error) {
    console.error('Error syncing calendars:', error);
    toast.error(`Failed to sync calendars: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return false;
  }
};
