import { Property } from '@/hooks/useProperties';
import { fetchCalendarData } from './propertyUtils';
import { supabase } from '@/integrations/supabase/client';

/**
 * Checks if a property's iCal data needs to be synced
 * @param property The property to check
 * @param maxHours Maximum hours since last sync (default: 8)
 * @returns Boolean indicating if sync is needed
 */
export const isCalendarSyncNeeded = (property: Property, maxHours = 8): boolean => {
  if (!property.ical_url) {
    return false; // No iCal URL, no sync needed
  }

  if (!property.last_ical_sync) {
    return true; // Never synced before
  }

  const lastSync = new Date(property.last_ical_sync);
  const now = new Date();
  const hoursSinceLastSync = (now.getTime() - lastSync.getTime()) / (1000 * 60 * 60);

  return hoursSinceLastSync > maxHours;
};

/**
 * Syncs iCal data for a property if needed
 * @param property The property to sync
 * @param userId The current user ID
 * @param maxHours Maximum hours since last sync (default: 8)
 * @param onComplete Optional callback to run after sync completes
 * @returns Promise that resolves when sync is complete
 */
export const syncPropertyCalendarIfNeeded = async (
  property: Property,
  userId: string,
  maxHours = 8,
  onComplete?: () => Promise<void>
): Promise<void> => {
  if (!property || !property.id || !userId || !property.ical_url) {
    return;
  }

  if (isCalendarSyncNeeded(property, maxHours)) {
    console.log(`[syncPropertyCalendarIfNeeded] Syncing calendar for property ${property.id} (${property.name})`);
    try {
      await fetchCalendarData(property.ical_url, property.id, userId, onComplete);
    } catch (error) {
      console.error(`[syncPropertyCalendarIfNeeded] Error syncing calendar for property ${property.id}:`, error);
    }
  }
};

/**
 * Syncs iCal data for all properties that need it
 * @param properties Array of properties to check
 * @param userId The current user ID
 * @param maxHours Maximum hours since last sync (default: 8)
 * @returns Promise that resolves when all syncs are complete
 */
export const syncAllPropertiesCalendarsIfNeeded = async (
  properties: Property[],
  userId: string,
  maxHours = 8
): Promise<void> => {
  if (!properties || !userId) {
    return;
  }

  const propertiesNeedingSync = properties.filter(p => isCalendarSyncNeeded(p, maxHours));
  
  if (propertiesNeedingSync.length === 0) {
    console.log('[syncAllPropertiesCalendarsIfNeeded] No properties need syncing');
    return;
  }

  console.log(`[syncAllPropertiesCalendarsIfNeeded] Syncing calendars for ${propertiesNeedingSync.length} properties`);
  
  try {
    // Use the Edge Function to sync multiple properties at once
    const { data, error } = await supabase.functions.invoke('sync-property-calendars', {
      body: { 
        propertyIds: propertiesNeedingSync.map(p => p.id),
        forceSyncAll: true
      }
    });
    
    if (error) {
      console.error('[syncAllPropertiesCalendarsIfNeeded] Error syncing calendars:', error);
    } else {
      console.log('[syncAllPropertiesCalendarsIfNeeded] Calendars synced successfully:', data);
    }
  } catch (error) {
    console.error('[syncAllPropertiesCalendarsIfNeeded] Error syncing calendars:', error);
  }
};
