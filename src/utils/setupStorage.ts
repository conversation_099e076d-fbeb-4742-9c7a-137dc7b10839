
import { supabase } from '@/integrations/supabase/client';

/**
 * Checks if the required storage buckets exist
 * and sets global variables for their names
 * Falls back to using original image URLs if storage is not available
 *
 * This function is called on app initialization and after createStorageBuckets
 * to ensure the buckets are available for use
 */
export async function ensureStorageBuckets(): Promise<boolean> {
  try {
    console.log('[setupStorage] Checking for storage buckets...');

    // Set default values regardless of storage availability
    window.INVENTORY_BUCKET_NAME = 'inventory';
    window.INVENTORY_FOLDER_PATH = 'inventory-images';

    // Check if the required buckets exist
    const { data: buckets, error } = await supabase.storage.listBuckets();

    if (error) {
      console.error('[setupStorage] Error checking storage buckets:', error);
      console.warn('[setupStorage] Storage access error. Will use edge functions for uploads.');
      return false;
    }

    if (!buckets || buckets.length === 0) {
      console.warn('[setupStorage] No storage buckets found. Will use edge functions for uploads.');
      return false;
    }

    console.log('[setupStorage] Available buckets:', buckets.map((b: { name: string }) => b.name).join(', '));

    // Check for both inventory and inventory-images buckets
    const inventoryBucketExists = buckets.some((bucket: { name: string }) => bucket.name === 'inventory');
    const inventoryImagesBucketExists = buckets.some((bucket: { name: string }) => bucket.name === 'inventory-images');

    console.log('[setupStorage] Storage bucket check:', {
      inventoryBucketExists,
      inventoryImagesBucketExists
    });

    // Set the correct bucket name based on what exists
    if (inventoryBucketExists) {
      window.INVENTORY_BUCKET_NAME = 'inventory';
    } else if (inventoryImagesBucketExists) {
      window.INVENTORY_BUCKET_NAME = 'inventory-images';
    } else {
      console.warn('[setupStorage] No inventory storage buckets found. Will use edge functions for uploads.');
      return false;
    }

    // Try to verify we can access the bucket
    try {
      const hasPermissions = await testStoragePermissions();
      if (!hasPermissions) {
        console.warn('[setupStorage] Insufficient permissions to access storage. Will use edge functions for uploads.');
        return false;
      }
    } catch (err) {
      console.error('[setupStorage] Error testing storage permissions:', err);
      return false;
    }

    console.log(`[setupStorage] Storage setup complete. Using bucket: ${window.INVENTORY_BUCKET_NAME}, folder: ${window.INVENTORY_FOLDER_PATH}`);
    return true;
  } catch (error) {
    console.error('[setupStorage] Error in ensureStorageBuckets:', error);
    return false;
  }
}

/**
 * Test if we have permission to interact with storage
 */
async function testStoragePermissions(): Promise<boolean> {
  try {
    console.log('[setupStorage] Testing storage permissions...');

    // Get the bucket name from the global variable
    const bucketName = window.INVENTORY_BUCKET_NAME || 'inventory';
    console.log(`[setupStorage] Testing permissions for bucket: ${bucketName}`);

    // Just try to list objects to see if we have permissions
    const { error } = await supabase.storage
      .from(bucketName)
      .list();

    if (error && error.message.includes('does not exist')) {
      console.log('[setupStorage] Bucket does not exist, but we can query storage');
      return true;
    } else if (error) {
      console.error('[setupStorage] Error testing storage permissions:', error);
      return false;
    }

    console.log('[setupStorage] Successfully listed files in inventory bucket');
    return true;
  } catch (error) {
    console.error('[setupStorage] Error testing storage permissions:', error);
    return false;
  }
}

/**
 * Create a test file in the storage bucket to verify permissions
 * This is useful for checking if the buckets are properly configured
 */
export async function testStorageBuckets(): Promise<boolean> {
  try {
    console.log('[setupStorage] Testing storage bucket access...');

    const testData = new Blob(['test'], { type: 'text/plain' });
    const bucketName = window.INVENTORY_BUCKET_NAME || 'inventory';
    const testPath = `test-${Date.now()}.txt`;

    // Try to upload a test file
    const { error } = await supabase.storage
      .from(bucketName)
      .upload(testPath, testData, {
        cacheControl: '3600',
        upsert: true
      });

    if (error) {
      console.error('[setupStorage] Error testing storage bucket:', error);
      return false;
    }

    console.log('[setupStorage] Test file uploaded successfully');

    // Clean up test file
    await supabase.storage
      .from(bucketName)
      .remove([testPath]);

    console.log('[setupStorage] Test file removed successfully');

    return true;
  } catch (error) {
    console.error('[setupStorage] Error testing storage buckets:', error);
    return false;
  }
}
