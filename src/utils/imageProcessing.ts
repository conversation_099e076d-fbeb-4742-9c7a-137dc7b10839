
// Import removed to fix unused import warning

// Add a global type declaration for the bucket name and folder path
declare global {
  interface Window {
    INVENTORY_BUCKET_NAME?: string;
    INVENTORY_FOLDER_PATH?: string;
  }
}

import { supabase } from '@/integrations/supabase/client';

/**
 * Downloads an image from a URL, compresses it, and uploads it to Supabase storage
 * If the storage bucket doesn't exist, returns the original URL
 * Handles base64 data URLs by converting them to files and uploading to Supabase
 */
export async function processAndUploadImage(
  imageUrl: string,
  bucketName: string = 'inventory',
  folderPath: string = 'inventory-images',
  fileName?: string
): Promise<string> {
  // Check if the URL is a base64 data URL
  if (imageUrl.startsWith('data:image')) {
    try {
      console.log('Processing base64 data URL');

      // Convert base64 to blob
      const response = await fetch(imageUrl);
      const blob = await response.blob();

      // Generate a unique filename if not provided
      const timestamp = new Date().getTime();
      const randomString = Math.random().toString(36).substring(2, 10);
      const generatedFileName = fileName || `${timestamp}-${randomString}.jpg`;
      const filePath = `${folderPath}/${generatedFileName}`;

      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from(bucketName)
        .upload(filePath, blob, {
          contentType: 'image/jpeg',
          upsert: true
        });

      if (error) {
        console.error('Error uploading base64 image to storage:', error);
        return imageUrl; // Return original URL on error
      }

      // Get the public URL
      const { data: publicUrlData } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filePath);

      console.log('Base64 image uploaded successfully:', publicUrlData.publicUrl);
      return publicUrlData.publicUrl;
    } catch (error) {
      console.error('Error processing base64 image:', error);
      return imageUrl; // Return original URL on error
    }
  }

  // For non-base64 URLs, just return the original URL
  return imageUrl;
}

// Placeholder for future implementation

/**
 * Creates inventory buckets if they don't exist
 * Simplified version that just returns true to prevent blocking the dashboard
 */
export async function createInventoryBuckets(): Promise<boolean> {
  try {
    // Set global variables for bucket names
    window.INVENTORY_BUCKET_NAME = 'inventory';
    window.INVENTORY_FOLDER_PATH = 'inventory-images';

    // For dashboard performance, just return true
    return true;
  } catch (error) {
    console.error('Error checking inventory buckets:', error);
    return false;
  }
}
