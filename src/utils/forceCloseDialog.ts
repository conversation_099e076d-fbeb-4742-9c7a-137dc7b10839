/**
 * Utility to force close a dialog after a specified timeout
 * This is a failsafe mechanism to ensure dialogs don't get stuck open
 */
export const forceCloseDialog = (
  onClose: () => void,
  timeout: number = 3000,
  message: string = 'Force closing dialog after timeout'
): void => {
  // Set a timeout to force close the dialog
  setTimeout(() => {
    console.log(message);
    onClose();
  }, timeout);
};
