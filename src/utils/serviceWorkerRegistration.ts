
export function register() {
  if (!('serviceWorker' in navigator)) {
    console.log('Service workers are not supported by this browser');
    return;
  }

  // Add a timeout to detect if service worker registration is taking too long
  const timeoutId = setTimeout(() => {
    console.warn('Service Worker registration is taking too long. It might be blocked by the browser.');
  }, 5000);

  window.addEventListener('load', () => {
    clearTimeout(timeoutId);

    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';
    if (isDevelopment) {
      console.log('Running in development mode. Service Worker registration will proceed but may have HMR issues.');
    }

    // Ensure we're using the correct base URL for the service worker
    const baseUrl = window.location.origin;
    const swUrl = `${baseUrl}/service-worker.js`;

    // First check if the service worker is already controlling this page
    if (navigator.serviceWorker.controller) {
      console.log('Service Worker is already controlling this page.');

      // Set up event listeners for existing service worker
      setupServiceWorkerListeners();
      return;
    }

    // Unregister existing service workers first
    unregisterExistingServiceWorkers()
      .then(() => {
        // After unregistering, register the new service worker
        registerServiceWorker(swUrl);
      })
      .catch(error => {
        console.error('Error during service worker registration process:', error);

        // Try direct registration as fallback
        registerServiceWorker(swUrl);
      });

    // Set up event listeners
    setupServiceWorkerListeners();
  });
}

// Helper function to unregister existing service workers
function unregisterExistingServiceWorkers() {
  return navigator.serviceWorker.getRegistrations()
    .then(registrations => {
      const unregisterPromises = registrations.map(registration => {
        return registration.unregister()
          .then(() => {
            console.log('Service Worker unregistered successfully');
          })
          .catch(error => {
            console.error('Error unregistering Service Worker:', error);
          });
      });

      return Promise.all(unregisterPromises);
    });
}

// Helper function to register a new service worker
function registerServiceWorker(swUrl) {
  setTimeout(() => {
    navigator.serviceWorker
      .register(swUrl, {
        scope: '/',
        updateViaCache: 'none' // Always check for updates
      })
      .then(registration => {
        console.log('Service Worker registered with scope:', registration.scope);

        // Check for updates to the service worker
        registration.addEventListener('updatefound', () => {
          const installingWorker = registration.installing;
          if (installingWorker == null) {
            return;
          }

          installingWorker.addEventListener('statechange', () => {
            if (installingWorker.state === 'installed') {
              if (navigator.serviceWorker.controller) {
                // At this point, the updated precached content has been fetched
                console.log('New content is available and will be used when all tabs for this page are closed.');

                // Notify the user about the update
                showUpdateNotification();
              } else {
                // At this point, everything has been precached
                console.log('Content is cached for offline use.');
              }
            }
          });
        });
      })
      .catch(error => {
        console.error('Error during service worker registration:', error);
      });
  }, 1000); // Add a small delay to ensure unregistration completes
}

// Helper function to show update notification
function showUpdateNotification() {
  const updateNotification = document.createElement('div');
  updateNotification.className = 'fixed bottom-4 right-4 bg-primary text-white p-4 rounded-lg shadow-lg z-50';
  updateNotification.innerHTML = `
    <p class="mb-2">New version available!</p>
    <div class="flex gap-2">
      <button id="update-app" class="px-3 py-1 bg-white text-primary rounded">Update now</button>
      <button id="dismiss-update" class="px-3 py-1 bg-primary-foreground text-primary rounded">Later</button>
    </div>
  `;
  document.body.appendChild(updateNotification);

  document.getElementById('update-app')?.addEventListener('click', () => {
    window.location.reload();
  });

  document.getElementById('dismiss-update')?.addEventListener('click', () => {
    document.body.removeChild(updateNotification);
  });
}

// Helper function to set up service worker event listeners
function setupServiceWorkerListeners() {
  // Handle service worker updates
  let refreshing = false;
  navigator.serviceWorker.addEventListener('controllerchange', () => {
    if (!refreshing) {
      refreshing = true;
      window.location.reload();
    }
  });

  // Listen for messages from the service worker
  navigator.serviceWorker.addEventListener('message', (event) => {
    console.log('Message from service worker:', event.data);

    if (event.data === 'FORCE_REFRESH') {
      window.location.reload();
    }

    if (event.data?.type === 'AUTHENTICATION_ERROR') {
      // Redirect to login page if there's an authentication error
      window.location.href = '/#/login';
    }
  });
}

export function unregister() {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.ready
      .then(registration => {
        registration.unregister();
      })
      .catch(error => {
        console.error(error.message);
      });
  }
}

// Check for service worker updates manually
export function checkForUpdates() {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.ready
      .then(registration => {
        registration.update();
      })
      .catch(error => {
        console.error('Error checking for service worker updates:', error);
      });
  }
}

// Send a message to the service worker
export function sendMessageToSW(message: any) {
  return new Promise((resolve, reject) => {
    if (!navigator.serviceWorker.controller) {
      reject(new Error('No active service worker'));
      return;
    }

    const messageChannel = new MessageChannel();
    messageChannel.port1.onmessage = (event) => {
      resolve(event.data);
    };

    navigator.serviceWorker.controller.postMessage(message, [messageChannel.port2]);
  });
}
