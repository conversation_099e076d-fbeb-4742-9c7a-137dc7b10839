/**
 * Data Loading Test Utility
 * 
 * This utility helps test and verify that data loading works correctly
 * when the application returns from background/unfocused state.
 */

import { QueryClient } from '@tanstack/react-query';

export interface DataLoadingTestResult {
  success: boolean;
  message: string;
  details?: any;
}

export class DataLoadingTester {
  private queryClient: QueryClient;
  private testResults: DataLoadingTestResult[] = [];

  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
  }

  /**
   * Test if React Query configuration is consistent
   */
  testReactQueryConfig(): DataLoadingTestResult {
    try {
      const defaultOptions = this.queryClient.getDefaultOptions();
      const queries = defaultOptions.queries;

      // Check if refetchOnWindowFocus is set to 'always'
      if (queries?.refetchOnWindowFocus !== 'always') {
        return {
          success: false,
          message: 'React Query refetchOnWindowFocus should be set to "always"',
          details: { current: queries?.refetchOnWindowFocus }
        };
      }

      // Check other important settings
      const expectedSettings = {
        refetchOnReconnect: true,
        retry: 3,
        staleTime: 1000 * 60 * 5, // 5 minutes
        gcTime: 30 * 60 * 1000, // 30 minutes
        networkMode: 'always'
      };

      // Special check for refetchOnMount (can be true or 'always')
      const refetchOnMount = (queries as any)?.refetchOnMount;
      if (refetchOnMount !== true && refetchOnMount !== 'always') {
        return {
          success: false,
          message: `React Query refetchOnMount should be true or 'always'`,
          details: { expected: 'true or "always"', current: refetchOnMount }
        };
      }

      for (const [key, expectedValue] of Object.entries(expectedSettings)) {
        const currentValue = (queries as any)?.[key];
        if (currentValue !== expectedValue) {
          return {
            success: false,
            message: `React Query ${key} should be ${expectedValue}`,
            details: { expected: expectedValue, current: currentValue }
          };
        }
      }

      return {
        success: true,
        message: 'React Query configuration is correct'
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error testing React Query configuration',
        details: error
      };
    }
  }

  /**
   * Test if queries are properly cached
   */
  testQueryCache(): DataLoadingTestResult {
    try {
      const cache = this.queryClient.getQueryCache();
      const queries = cache.getAll();

      if (queries.length === 0) {
        return {
          success: false, // Changed back to false - this indicates the navigation issue
          message: 'Query cache is empty - this indicates navigation data loading issue',
          details: {
            note: 'If you started on Debug page and navigated to data pages, queries should have been initialized.',
            suggestion: 'This suggests the navigation-based query initialization is not working.',
            fix: 'Try refreshing the page or clicking "Fix Stuck Queries" button'
          }
        };
      }

      // Check if important queries exist (updated to match actual query keys)
      const importantQueryKeys = [
        'propertiesV2',
        'maintenanceTasksV2',
        'damageReportsV2',
        'inventoryV2',
        'purchaseOrders',
        'operationsData',
        'dashboardProperties'
      ];

      // Get unique query keys (remove duplicates)
      const foundQueries = [...new Set(queries.map(q => q.queryKey[0]).filter(Boolean))];

      // Check for stuck pending queries
      const pendingQueries = queries.filter(q =>
        q.state.status === 'pending' &&
        q.state.dataUpdatedAt === 0 &&
        Date.now() - q.state.dataUpdatedAt > 10000 // Pending for more than 10 seconds
      );

      const successfulQueries = queries.filter(q => q.state.status === 'success');
      const errorQueries = queries.filter(q => q.state.status === 'error');

      // Check for duplicate queries (same key, different instances)
      const queryKeyGroups = queries.reduce((acc, query) => {
        const key = query.queryKey[0];
        if (!acc[key]) acc[key] = [];
        acc[key].push(query);
        return acc;
      }, {} as Record<string, any[]>);

      const duplicateQueries = Object.entries(queryKeyGroups)
        .filter(([_, queries]) => queries.length > 1)
        .map(([key, queries]) => ({ key, count: queries.length }));

      // Provide detailed status information
      const details = {
        total: queries.length,
        unique: foundQueries.length,
        successful: successfulQueries.length,
        pending: queries.filter(q => q.state.status === 'pending').length,
        errors: errorQueries.length,
        stuckPending: pendingQueries.length,
        duplicates: duplicateQueries.length,
        queryKeys: foundQueries,
        stuckQueries: pendingQueries.map(q => q.queryKey[0]),
        duplicateDetails: duplicateQueries
      };

      // Check for issues and provide appropriate feedback
      const issues = [];

      if (pendingQueries.length > 0) {
        issues.push(`${pendingQueries.length} stuck pending queries`);
      }

      if (duplicateQueries.length > 0) {
        issues.push(`${duplicateQueries.length} duplicate query keys`);
      }

      if (issues.length > 0) {
        return {
          success: false,
          message: `Query cache issues: ${issues.join(', ')}`,
          details
        };
      }

      // If we have mostly successful queries, that's good
      if (successfulQueries.length > 0) {
        return {
          success: true,
          message: `Query cache healthy: ${successfulQueries.length} successful, ${foundQueries.length} unique keys`,
          details
        };
      }

      return {
        success: true,
        message: `Query cache contains ${foundQueries.length} unique queries`,
        details
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error testing query cache',
        details: error
      };
    }
  }

  /**
   * Test window focus behavior
   */
  async testWindowFocusBehavior(): Promise<DataLoadingTestResult> {
    try {
      // Get initial query count
      const initialQueries = this.queryClient.getQueryCache().getAll();
      const initialCount = initialQueries.length;

      // Simulate window focus event
      window.dispatchEvent(new Event('focus'));

      // Wait a bit for any refetches to trigger
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check if queries were refetched
      const afterQueries = this.queryClient.getQueryCache().getAll();
      const refetchedQueries = afterQueries.filter(q => 
        q.state.dataUpdatedAt > Date.now() - 2000 // Updated in last 2 seconds
      );

      return {
        success: true,
        message: `Window focus test completed`,
        details: {
          initialQueryCount: initialCount,
          refetchedQueries: refetchedQueries.length,
          refetchedKeys: refetchedQueries.map(q => q.queryKey[0])
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error testing window focus behavior',
        details: error
      };
    }
  }

  /**
   * Test for stuck pending queries and fix them
   */
  async testAndFixStuckQueries(): Promise<DataLoadingTestResult> {
    try {
      const cache = this.queryClient.getQueryCache();
      const queries = cache.getAll();

      // Find queries that are stuck in pending state
      const stuckQueries = queries.filter(q =>
        q.state.status === 'pending' &&
        q.state.dataUpdatedAt === 0 &&
        Date.now() - q.state.fetchFailureCount > 5000 // Pending for more than 5 seconds
      );

      if (stuckQueries.length === 0) {
        return {
          success: true,
          message: 'No stuck queries found',
          details: { totalQueries: queries.length }
        };
      }

      // Try to fix stuck queries by invalidating them
      console.log(`[DataLoadingTester] Found ${stuckQueries.length} stuck queries, attempting to fix...`);

      for (const query of stuckQueries) {
        try {
          // More aggressive approach: remove and invalidate
          console.log(`[DataLoadingTester] Removing and invalidating stuck query:`, query.queryKey);
          this.queryClient.removeQueries({ queryKey: query.queryKey });
          await this.queryClient.invalidateQueries({ queryKey: query.queryKey });

          // Also try to cancel any ongoing requests
          await this.queryClient.cancelQueries({ queryKey: query.queryKey });

        } catch (error) {
          console.error(`[DataLoadingTester] Failed to fix query:`, query.queryKey, error);
        }
      }

      // Wait longer for queries to potentially resolve
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Check if queries are still stuck
      const stillStuckQueries = cache.getAll().filter(q =>
        q.state.status === 'pending' &&
        q.state.dataUpdatedAt === 0 &&
        stuckQueries.some(stuck => stuck.queryKey === q.queryKey)
      );

      return {
        success: stillStuckQueries.length === 0,
        message: stillStuckQueries.length === 0
          ? `Fixed ${stuckQueries.length} stuck queries`
          : `${stillStuckQueries.length} queries still stuck after fix attempt`,
        details: {
          originalStuck: stuckQueries.length,
          stillStuck: stillStuckQueries.length,
          fixed: stuckQueries.length - stillStuckQueries.length,
          stuckQueryKeys: stuckQueries.map(q => q.queryKey[0])
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error testing and fixing stuck queries',
        details: error
      };
    }
  }

  /**
   * Test visibility change behavior
   */
  async testVisibilityChangeBehavior(): Promise<DataLoadingTestResult> {
    try {
      // Get initial query count
      const initialQueries = this.queryClient.getQueryCache().getAll();
      const initialCount = initialQueries.length;

      // Simulate visibility change event
      Object.defineProperty(document, 'visibilityState', {
        writable: true,
        value: 'visible'
      });
      document.dispatchEvent(new Event('visibilitychange'));

      // Wait a bit for any refetches to trigger
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check if queries were refetched
      const afterQueries = this.queryClient.getQueryCache().getAll();
      const refetchedQueries = afterQueries.filter(q => 
        q.state.dataUpdatedAt > Date.now() - 2000 // Updated in last 2 seconds
      );

      return {
        success: true,
        message: `Visibility change test completed`,
        details: {
          initialQueryCount: initialCount,
          refetchedQueries: refetchedQueries.length,
          refetchedKeys: refetchedQueries.map(q => q.queryKey[0])
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error testing visibility change behavior',
        details: error
      };
    }
  }

  /**
   * Trigger some basic data loading to populate cache
   */
  async triggerDataLoading(): Promise<void> {
    try {
      // Try to trigger some basic queries that should exist
      await this.queryClient.prefetchQuery({
        queryKey: ['test-query'],
        queryFn: () => Promise.resolve({ test: true }),
        staleTime: 1000
      });
    } catch (error) {
      console.warn('Could not trigger test data loading:', error);
    }
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<DataLoadingTestResult[]> {
    this.testResults = [];

    // Test React Query configuration
    this.testResults.push(this.testReactQueryConfig());

    // Test query cache (after potentially triggering some data loading)
    this.testResults.push(this.testQueryCache());

    // Test and fix stuck queries
    this.testResults.push(await this.testAndFixStuckQueries());

    // Test window focus behavior
    this.testResults.push(await this.testWindowFocusBehavior());

    // Test visibility change behavior
    this.testResults.push(await this.testVisibilityChangeBehavior());

    return this.testResults;
  }

  /**
   * Get test summary
   */
  getTestSummary(): { passed: number; failed: number; total: number } {
    const passed = this.testResults.filter(r => r.success).length;
    const failed = this.testResults.filter(r => !r.success).length;
    const total = this.testResults.length;

    return { passed, failed, total };
  }

  /**
   * Log test results to console
   */
  logResults(): void {
    console.group('🧪 Data Loading Test Results');
    
    this.testResults.forEach((result, index) => {
      const icon = result.success ? '✅' : '❌';
      console.log(`${icon} Test ${index + 1}: ${result.message}`);
      
      if (result.details) {
        console.log('   Details:', result.details);
      }
    });

    const summary = this.getTestSummary();
    console.log(`\n📊 Summary: ${summary.passed}/${summary.total} tests passed`);
    
    if (summary.failed > 0) {
      console.warn(`⚠️  ${summary.failed} tests failed - data loading issues may persist`);
    } else {
      console.log('🎉 All tests passed - data loading should work correctly!');
    }
    
    console.groupEnd();
  }
}

/**
 * Quick test function that can be called from browser console
 */
export const testDataLoading = async (queryClient: QueryClient): Promise<void> => {
  const tester = new DataLoadingTester(queryClient);
  await tester.runAllTests();
  tester.logResults();
};

// Make it available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).testDataLoading = testDataLoading;
}
