import { toast } from 'sonner';
import { AuthError } from '@supabase/supabase-js';

/**
 * Standardized error messages for authentication errors
 */
export const AUTH_ERROR_MESSAGES = {
  // Session errors
  'AuthSessionMissingError': 'Your session has expired. Please log in again.',
  'AuthRetryableFetchError': 'Network error. Please check your connection and try again.',
  'AuthInvalidCredentialsError': 'Invalid email or password. Please try again.',
  'AuthInvalidTokenError': 'Your session has expired. Please log in again.',
  'AuthApiError': 'Authentication error. Please try again.',
  'AuthUnknownError': 'An unknown error occurred. Please try again.',
  
  // Network errors
  'NetworkError': 'Network error. Please check your connection and try again.',
  'TimeoutError': 'Request timed out. Please try again.',
  
  // Generic errors
  'GenericError': 'An error occurred. Please try again.',
  'ServerError': 'Server error. Please try again later.',
  'DatabaseError': 'Database error. Please try again later.',
};

/**
 * Type for authentication error handling options
 */
export interface AuthErrorHandlingOptions {
  showToast?: boolean;
  redirectToLogin?: boolean;
  logError?: boolean;
  onError?: (error: Error) => void;
}

/**
 * Standardized function for handling authentication errors
 * 
 * @param error - The error to handle
 * @param options - Options for error handling
 * @returns The error message
 */
export function handleAuthError(
  error: Error | AuthError | any,
  options: AuthErrorHandlingOptions = {}
): string {
  const {
    showToast = true,
    redirectToLogin = false,
    logError = true,
    onError
  } = options;

  // Default error message
  let errorMessage = AUTH_ERROR_MESSAGES.GenericError;
  let errorType = 'GenericError';
  let shouldRedirect = redirectToLogin;

  // Log the error if enabled
  if (logError) {
    console.error('[AuthErrorHandler] Authentication error:', error);
  }

  // Determine the error type and message
  if (error instanceof Error) {
    // Check for specific error types
    if (error.message.includes('AuthSessionMissingError') || error.name === 'AuthSessionMissingError') {
      errorType = 'AuthSessionMissingError';
      errorMessage = AUTH_ERROR_MESSAGES.AuthSessionMissingError;
      shouldRedirect = true;
    } else if (error.message.includes('AuthRetryableFetchError') || error.name === 'AuthRetryableFetchError') {
      errorType = 'AuthRetryableFetchError';
      errorMessage = AUTH_ERROR_MESSAGES.AuthRetryableFetchError;
      shouldRedirect = false;
    } else if (error.message.includes('AuthInvalidCredentialsError') || error.name === 'AuthInvalidCredentialsError') {
      errorType = 'AuthInvalidCredentialsError';
      errorMessage = AUTH_ERROR_MESSAGES.AuthInvalidCredentialsError;
      shouldRedirect = false;
    } else if (error.message.includes('AuthInvalidTokenError') || error.name === 'AuthInvalidTokenError') {
      errorType = 'AuthInvalidTokenError';
      errorMessage = AUTH_ERROR_MESSAGES.AuthInvalidTokenError;
      shouldRedirect = true;
    } else if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
      errorType = 'NetworkError';
      errorMessage = AUTH_ERROR_MESSAGES.NetworkError;
      shouldRedirect = false;
    } else if (error.message.includes('timeout') || error.message.includes('Timeout')) {
      errorType = 'TimeoutError';
      errorMessage = AUTH_ERROR_MESSAGES.TimeoutError;
      shouldRedirect = false;
    } else if (error.message.includes('AuthApiError') || error.name === 'AuthApiError') {
      errorType = 'AuthApiError';
      errorMessage = AUTH_ERROR_MESSAGES.AuthApiError;
      shouldRedirect = false;
    } else if (error.message.includes('Server error') || error.message.includes('500')) {
      errorType = 'ServerError';
      errorMessage = AUTH_ERROR_MESSAGES.ServerError;
      shouldRedirect = false;
    } else if (error.message.includes('Database error') || error.message.includes('database')) {
      errorType = 'DatabaseError';
      errorMessage = AUTH_ERROR_MESSAGES.DatabaseError;
      shouldRedirect = false;
    }
  } else if (error && typeof error === 'object') {
    // Handle Supabase AuthError or other error objects
    if (error.message) {
      if (error.message.includes('AuthSessionMissingError')) {
        errorType = 'AuthSessionMissingError';
        errorMessage = AUTH_ERROR_MESSAGES.AuthSessionMissingError;
        shouldRedirect = true;
      } else if (error.message.includes('Invalid login credentials')) {
        errorType = 'AuthInvalidCredentialsError';
        errorMessage = AUTH_ERROR_MESSAGES.AuthInvalidCredentialsError;
        shouldRedirect = false;
      } else if (error.message.includes('Invalid token')) {
        errorType = 'AuthInvalidTokenError';
        errorMessage = AUTH_ERROR_MESSAGES.AuthInvalidTokenError;
        shouldRedirect = true;
      } else {
        // Use the error message from the object
        errorMessage = error.message;
      }
    }
  }

  // Show toast if enabled
  if (showToast) {
    toast.error(errorMessage);
  }

  // Redirect to login if needed
  if (shouldRedirect) {
    // Store the current path to redirect back after login
    const currentPath = window.location.hash.replace('#', '') || '/dashboard';
    sessionStorage.setItem('redirectAfterLogin', currentPath);
    
    // Use a short timeout to ensure the toast is visible
    setTimeout(() => {
      window.location.href = `/#/login?reason=${errorType.toLowerCase()}`;
    }, 1000);
  }

  // Call the onError callback if provided
  if (onError) {
    onError(error);
  }

  return errorMessage;
}
