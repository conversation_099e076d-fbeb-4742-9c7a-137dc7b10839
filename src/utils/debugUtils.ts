/**
 * Utility functions for debugging React Query and data loading issues
 */

import { QueryClient } from '@tanstack/react-query';

/**
 * Debug the React Query cache for a specific query key
 * @param queryClient The React Query client
 * @param queryKey The query key to debug
 * @returns Information about the query
 */
export const debugQueryCache = (
  queryClient: QueryClient,
  queryKey: string | unknown[]
): {
  exists: boolean;
  isStale: boolean;
  lastUpdated: string | null;
  data: any;
  queryCount: number;
} => {
  // Normalize query key to array
  const normalizedQueryKey = Array.isArray(queryKey) ? queryKey : [queryKey];
  
  // Get all queries that match this key
  const queries = queryClient.getQueriesData({ queryKey: normalizedQueryKey });
  
  // If no queries match, return empty result
  if (queries.length === 0) {
    return {
      exists: false,
      isStale: true,
      lastUpdated: null,
      data: null,
      queryCount: 0
    };
  }
  
  // Get the first query that matches
  const [, data] = queries[0];
  
  // Get the query state
  const queryState = queryClient.getQueryState(normalizedQueryKey);
  
  return {
    exists: true,
    isStale: queryState?.isStale ?? true,
    lastUpdated: queryState?.dataUpdatedAt ? new Date(queryState.dataUpdatedAt).toISOString() : null,
    data,
    queryCount: queries.length
  };
};

/**
 * Debug all queries in the React Query cache
 * @param queryClient The React Query client
 * @returns Information about all queries
 */
export const debugAllQueries = (queryClient: QueryClient): Record<string, any> => {
  const result: Record<string, any> = {};
  
  // Get all query keys
  const queries = queryClient.getQueryCache().getAll();
  
  // For each query, get its state
  queries.forEach(query => {
    const queryKey = JSON.stringify(query.queryKey);
    result[queryKey] = {
      isStale: query.isStale(),
      lastUpdated: query.state.dataUpdatedAt ? new Date(query.state.dataUpdatedAt).toISOString() : null,
      status: query.state.status,
      hasData: !!query.state.data
    };
  });
  
  return result;
};

/**
 * Force refresh all queries for a specific route
 * @param queryClient The React Query client
 * @param queryKeys Array of query keys to refresh
 * @returns Promise that resolves when all queries are refreshed
 */
export const forceRefreshQueries = async (
  queryClient: QueryClient,
  queryKeys: string[]
): Promise<void> => {
  console.log(`[debugUtils] Force refreshing queries: ${queryKeys.join(', ')}`);
  
  // First invalidate all queries
  for (const key of queryKeys) {
    await queryClient.invalidateQueries({
      queryKey: [key],
      refetchType: 'all',
      exact: false
    });
  }
  
  // Then refetch all queries
  for (const key of queryKeys) {
    await queryClient.refetchQueries({
      queryKey: [key],
      exact: false,
      type: 'all'
    });
  }
  
  console.log(`[debugUtils] Finished refreshing queries: ${queryKeys.join(', ')}`);
};

/**
 * Check if a query exists in the cache
 * @param queryClient The React Query client
 * @param queryKey The query key to check
 * @returns Whether the query exists
 */
export const queryExists = (
  queryClient: QueryClient,
  queryKey: string | unknown[]
): boolean => {
  // Normalize query key to array
  const normalizedQueryKey = Array.isArray(queryKey) ? queryKey : [queryKey];
  
  // Get all queries that match this key
  const queries = queryClient.getQueriesData({ queryKey: normalizedQueryKey });
  
  return queries.length > 0;
};
