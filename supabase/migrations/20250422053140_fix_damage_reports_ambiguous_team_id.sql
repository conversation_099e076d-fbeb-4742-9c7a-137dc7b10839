-- Fix ambiguous team_id column reference in get_user_damage_reports function

-- Drop the existing function
DROP FUNCTION IF EXISTS get_user_damage_reports(UUID);

-- Recreate the function with fully qualified column names
CREATE OR REPLACE FUNCTION get_user_damage_reports(p_user_id UUID)
RETURNS TABLE (
    id UUID,
    title TEXT,
    description TEXT,
    status TEXT,
    property_id UUID,
    property_name TEXT,
    user_id UUID,
    user_email VARCHAR(255),
    provider_id UUID,
    provider_name TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    platform TEXT,
    team_id UUID,
    property_owner_id UUID,
    source TEXT
) LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
    v_teams UUID[];
BEGIN
    -- Get user's own reports
    RETURN QUERY
    SELECT
        dr.id,
        dr.title,
        dr.description,
        dr.status,
        dr.property_id,
        dr.property_name,
        dr.user_id,
        dr.user_email,
        dr.provider_id,
        dr.provider_name,
        dr.created_at,
        dr.updated_at,
        dr.platform,
        dr.team_id,
        dr.property_owner_id,
        'own'::TEXT AS source
    FROM
        damage_reports_with_property dr
    WHERE
        dr.user_id = p_user_id;

    -- Get teams the user is a member of
    SELECT array_agg(tm.team_id) INTO v_teams
    FROM team_members tm
    WHERE tm.user_id = p_user_id AND tm.status = 'active';

    -- If user is in any teams, get team reports
    IF v_teams IS NOT NULL AND array_length(v_teams, 1) > 0 THEN
        -- Get reports for properties in teams the user is a member of
        RETURN QUERY
        SELECT DISTINCT ON (dr.id)
            dr.id,
            dr.title,
            dr.description,
            dr.status,
            dr.property_id,
            dr.property_name,
            dr.user_id,
            dr.user_email,
            dr.provider_id,
            dr.provider_name,
            dr.created_at,
            dr.updated_at,
            dr.platform,
            dr.team_id,
            dr.property_owner_id,
            'team'::TEXT AS source
        FROM
            damage_reports_with_property dr
        WHERE
            (dr.team_id = ANY(v_teams) OR dr.property_id IN (
                SELECT tp.property_id FROM team_properties tp WHERE tp.team_id = ANY(v_teams)
            ))
            AND dr.user_id != p_user_id;
    END IF;

    -- For admins, get all other reports
    IF EXISTS (SELECT 1 FROM profiles p_prof WHERE p_prof.id = p_user_id AND (p_prof.is_super_admin = true OR p_prof.role = 'admin')) THEN
        RETURN QUERY
        SELECT DISTINCT ON (dr.id)
            dr.id,
            dr.title,
            dr.description,
            dr.status,
            dr.property_id,
            dr.property_name,
            dr.user_id,
            dr.user_email,
            dr.provider_id,
            dr.provider_name,
            dr.created_at,
            dr.updated_at,
            dr.platform,
            dr.team_id,
            dr.property_owner_id,
            'admin'::TEXT AS source
        FROM
            damage_reports_with_property dr
        WHERE
            dr.user_id != p_user_id
            AND (v_teams IS NULL OR NOT (dr.team_id = ANY(v_teams) OR dr.property_id IN (
                SELECT tp.property_id FROM team_properties tp WHERE tp.team_id = ANY(v_teams)
            )));
    END IF;

    -- For service providers, get reports assigned to them
    IF EXISTS (SELECT 1 FROM profiles p_prof WHERE p_prof.id = p_user_id AND p_prof.role = 'service_provider') THEN
        RETURN QUERY
        SELECT DISTINCT ON (dr.id)
            dr.id,
            dr.title,
            dr.description,
            dr.status,
            dr.property_id,
            dr.property_name,
            dr.user_id,
            dr.user_email,
            dr.provider_id,
            dr.provider_name,
            dr.created_at,
            dr.updated_at,
            dr.platform,
            dr.team_id,
            dr.property_owner_id,
            'assigned'::TEXT AS source
        FROM
            damage_reports_with_property dr
        WHERE
            dr.provider_id = p_user_id
            AND dr.user_id != p_user_id
            AND (v_teams IS NULL OR NOT (dr.team_id = ANY(v_teams) OR dr.property_id IN (
                SELECT tp.property_id FROM team_properties tp WHERE tp.team_id = ANY(v_teams)
            )));
    END IF;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_user_damage_reports(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_damage_reports(UUID) TO anon;

-- Fix the get_team_damage_reports function as well
DROP FUNCTION IF EXISTS get_team_damage_reports(UUID);
CREATE OR REPLACE FUNCTION get_team_damage_reports(p_team_id UUID)
RETURNS TABLE (
    id UUID,
    title TEXT,
    description TEXT,
    status TEXT,
    property_id UUID,
    property_name TEXT,
    user_id UUID,
    user_email VARCHAR(255),
    provider_id UUID,
    provider_name TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    platform TEXT,
    team_id UUID
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    RETURN QUERY
    SELECT
        dr.id,
        dr.title,
        dr.description,
        dr.status,
        dr.property_id,
        p.name AS property_name,
        dr.user_id,
        u.email AS user_email,
        dr.provider_id,
        mp.name AS provider_name,
        dr.created_at,
        dr.updated_at,
        dr.platform,
        dr.team_id
    FROM
        damage_reports dr
    JOIN
        properties p ON dr.property_id = p.id
    LEFT JOIN
        auth.users u ON dr.user_id = u.id
    LEFT JOIN
        maintenance_providers mp ON dr.provider_id = mp.id
    WHERE
        dr.team_id = p_team_id
        OR dr.property_id IN (
            SELECT tp.property_id FROM team_properties tp WHERE tp.team_id = p_team_id
        )
    ORDER BY
        dr.created_at DESC;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_team_damage_reports(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_team_damage_reports(UUID) TO anon;

-- Fix the set_damage_report_team_id function
CREATE OR REPLACE FUNCTION set_damage_report_team_id()
RETURNS TRIGGER AS $$
DECLARE
    v_team_ids UUID[];
BEGIN
    -- Get all team_ids associated with this property
    SELECT array_agg(tp.team_id) INTO v_team_ids
    FROM team_properties tp
    WHERE tp.property_id = NEW.property_id;

    -- If there's exactly one team, set the team_id
    IF array_length(v_team_ids, 1) = 1 THEN
        NEW.team_id := v_team_ids[1];
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
