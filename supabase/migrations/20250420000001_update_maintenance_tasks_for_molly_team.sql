-- Update maintenance tasks for properties in <PERSON>'s team
UPDATE maintenance_tasks mt
SET team_id = '3b9e7651-68c3-432f-9a28-7440139250f3'
WHERE mt.property_id IN (
  SELECT property_id 
  FROM team_properties 
  WHERE team_id = '3b9e7651-68c3-432f-9a28-7440139250f3'
);

-- Log the update
DO $$
DECLARE
  updated_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO updated_count FROM maintenance_tasks WHERE team_id = '3b9e7651-68c3-432f-9a28-7440139250f3';
  RAISE NOTICE 'Updated % maintenance tasks for Molly''s team', updated_count;
END $$;
