-- Add delete policy for team members to delete damage reports for their properties

-- First, check if the policy already exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'damage_reports'
        AND policyname = 'Team members can delete damage reports for their properties'
    ) THEN
        -- Create the policy
        CREATE POLICY "Team members can delete damage reports for their properties"
        ON damage_reports
        FOR DELETE
        TO public
        USING (has_damage_report_access(id));
    END IF;
END;
$$;

-- Update the has_damage_report_access function to include service providers
CREATE OR REPLACE FUNCTION public.has_damage_report_access(p_report_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  v_property_id uuid;
  v_team_id uuid;
BEGIN
  -- Get the property_id and team_id for this report
  SELECT property_id, team_id INTO v_property_id, v_team_id
  FROM damage_reports
  WHERE id = p_report_id;

  -- Super admins and admins have access to all reports
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Report creator has access
  IF EXISTS (
    SELECT 1 FROM damage_reports
    WHERE id = p_report_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to reports for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to reports directly associated with their teams
  IF v_team_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_members tm
    WHERE tm.team_id = v_team_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  -- Service providers have access to reports assigned to them
  IF EXISTS (
    SELECT 1 FROM damage_reports
    WHERE id = p_report_id AND provider_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$function$;
