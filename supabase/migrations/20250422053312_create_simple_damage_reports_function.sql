-- Create a simpler version of the damage reports function

-- Create a simple function to get all damage reports for a user
CREATE OR REPLACE FUNCTION get_all_damage_reports(p_user_id UUID)
RETURNS TABLE (
    id UUID,
    title TEXT,
    description TEXT,
    status TEXT,
    property_id UUID,
    property_name TEXT,
    user_id UUID,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    provider_id UUID,
    provider_name TEXT,
    platform TEXT
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    -- Return all damage reports the user has access to
    RETURN QUERY
    SELECT DISTINCT ON (dr.id)
        dr.id,
        dr.title,
        dr.description,
        dr.status,
        dr.property_id,
        p.name AS property_name,
        dr.user_id,
        dr.created_at,
        dr.updated_at,
        dr.provider_id,
        mp.name AS provider_name,
        dr.platform
    FROM
        damage_reports dr
    JOIN
        properties p ON dr.property_id = p.id
    LEFT JOIN
        maintenance_providers mp ON dr.provider_id = mp.id
    WHERE
        -- User's own reports
        dr.user_id = p_user_id
        -- Admin access
        OR EXISTS (SELECT 1 FROM profiles WHERE id = p_user_id AND (is_super_admin = true OR role = 'admin'))
        -- Team member access
        OR EXISTS (
            SELECT 1 FROM team_properties tp
            JOIN team_members tm ON tp.team_id = tm.team_id
            WHERE tp.property_id = dr.property_id AND tm.user_id = p_user_id AND tm.status = 'active'
        )
        -- Team direct access
        OR EXISTS (
            SELECT 1 FROM team_members tm
            WHERE tm.team_id = dr.team_id AND tm.user_id = p_user_id AND tm.status = 'active'
        )
        -- Service provider access
        OR (dr.provider_id = p_user_id)
    ORDER BY
        dr.id, dr.created_at DESC;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_all_damage_reports(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_all_damage_reports(UUID) TO anon;

-- Update the useDamageReports.tsx file to use this function instead
