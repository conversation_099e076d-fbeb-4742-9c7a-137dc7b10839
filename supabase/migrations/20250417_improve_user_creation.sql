-- Improve the handle_new_user function to be more robust
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if a profile already exists for this user
  IF NOT EXISTS (SELECT 1 FROM public.profiles WHERE id = new.id) THEN
    -- Create the profile if it doesn't exist
    INSERT INTO public.profiles (
      id, 
      email, 
      first_name, 
      last_name, 
      role, 
      is_super_admin,
      created_at,
      updated_at
    )
    VALUES (
      new.id,
      new.email,
      COALESCE(new.raw_user_meta_data->>'first_name', 'User'),
      COALESCE(new.raw_user_meta_data->>'last_name', ''),
      COALESCE((new.raw_user_meta_data->>'role')::user_role, 'property_manager'::user_role),
      COALESCE((new.raw_user_meta_data->>'is_super_admin')::boolean, FALSE),
      NOW(),
      NOW()
    );
    
    -- Log successful profile creation
    RAISE LOG 'Profile created for user %', new.id;
  ELSE
    -- Log that profile already exists
    RAISE LOG 'Profile already exists for user %', new.id;
  END IF;
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't block signup
    RAISE LOG 'Error in handle_new_user: %', SQLERRM;
    RETURN NEW;
END;
$$;

-- Create a function to ensure all users have profiles
CREATE OR REPLACE FUNCTION public.ensure_all_users_have_profiles()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_record RECORD;
BEGIN
  -- Find all users without profiles
  FOR user_record IN 
    SELECT au.id, au.email, au.raw_user_meta_data 
    FROM auth.users au
    LEFT JOIN public.profiles p ON p.id = au.id
    WHERE p.id IS NULL
  LOOP
    -- Create a profile for each user
    INSERT INTO public.profiles (
      id, 
      email, 
      first_name, 
      last_name, 
      role, 
      is_super_admin,
      created_at,
      updated_at
    )
    VALUES (
      user_record.id,
      user_record.email,
      COALESCE(user_record.raw_user_meta_data->>'first_name', 'User'),
      COALESCE(user_record.raw_user_meta_data->>'last_name', ''),
      COALESCE((user_record.raw_user_meta_data->>'role')::user_role, 'property_manager'::user_role),
      COALESCE((user_record.raw_user_meta_data->>'is_super_admin')::boolean, FALSE),
      NOW(),
      NOW()
    );
    
    RAISE LOG 'Created missing profile for user %', user_record.id;
  END LOOP;
END;
$$;

-- Run the function to ensure all existing users have profiles
SELECT ensure_all_users_have_profiles();

-- Create a scheduled function to periodically check for users without profiles
CREATE OR REPLACE FUNCTION public.scheduled_ensure_profiles()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  PERFORM ensure_all_users_have_profiles();
END;
$$;

-- Create a cron job to run the function every hour
-- Uncomment this if you have pg_cron extension enabled
-- SELECT cron.schedule('0 * * * *', 'SELECT scheduled_ensure_profiles()');
