-- Add RLS policy for service providers to create maintenance tasks
CREATE POLICY "Service providers can create maintenance tasks"
ON maintenance_tasks
FOR INSERT
TO public
WITH CHECK (
  -- Service providers can create tasks
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'service_provider'
  )
);

-- Update the can_create_maintenance_task function to allow service providers
CREATE OR REPLACE FUNCTION public.can_create_maintenance_task(p_property_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  v_team_id uuid;
BEGIN
  -- Super admins and admins can create tasks for any property
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Property owners can create tasks for their properties
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE id = p_property_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Service providers can create tasks
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'service_provider'
  ) THEN
    RETURN TRUE;
  END IF;

  -- Get the team_id for this property
  SELECT team_id INTO v_team_id
  FROM team_properties
  WHERE property_id = p_property_id
  LIMIT 1;

  -- If property is not in any team, only the owner can create tasks
  IF v_team_id IS NULL THEN
    RETURN FALSE;
  END IF;

  -- Check if the user has permission to create maintenance tasks for this team
  RETURN EXISTS (
    SELECT 1 FROM user_permissions up
    JOIN team_members tm ON up.team_id = tm.team_id AND up.user_id = tm.user_id
    WHERE tm.team_id = v_team_id
    AND tm.user_id = auth.uid()
    AND tm.status = 'active'
    AND (
      up.permission = 'create_maintenance_task'::permission_type OR
      up.permission = 'manage_maintenance'::permission_type
    )
    AND up.enabled = true
  );
END;
$function$;
