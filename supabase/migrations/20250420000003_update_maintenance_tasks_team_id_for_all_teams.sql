-- This migration updates the team_id for maintenance tasks based on the team_properties table
-- For each property that belongs to multiple teams, we need to ensure the maintenance tasks
-- are associated with all relevant teams

-- First, create a temporary table to store the property-team mappings
CREATE TEMP TABLE property_team_mappings AS
SELECT property_id, team_id
FROM team_properties;

-- For each maintenance task with a property_id, update its team_id to match the team_id from team_properties
-- If a property belongs to multiple teams, we'll use the most recently created team
UPDATE maintenance_tasks mt
SET team_id = (
  SELECT team_id
  FROM property_team_mappings
  WHERE property_id = mt.property_id
  LIMIT 1
)
WHERE mt.property_id IS NOT NULL
AND mt.property_id IN (SELECT property_id FROM property_team_mappings);

-- Log the update
DO $$
DECLARE
  updated_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO updated_count FROM maintenance_tasks WHERE team_id IS NOT NULL;
  RAISE NOTICE 'Updated % maintenance tasks with team_id', updated_count;
END $$;
