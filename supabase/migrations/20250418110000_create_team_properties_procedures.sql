-- Drop existing functions that might be causing issues
DROP FUNCTION IF EXISTS public.add_property_to_team(uuid, uuid, uuid);
DROP FUNCTION IF EXISTS public.remove_property_from_team(uuid, uuid, uuid);

-- Create a stored procedure to safely add a property to a team
CREATE OR REPLACE PROCEDURE public.sp_add_property_to_team(
  p_team_id uuid,
  p_property_id uuid,
  p_user_id uuid
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_is_owner boolean;
  v_owns_property boolean;
  v_already_exists boolean;
BEGIN
  -- Check if user owns the team
  SELECT EXISTS (
    SELECT 1 FROM teams
    WHERE id = p_team_id AND owner_id = p_user_id
  ) INTO v_is_owner;
  
  IF NOT v_is_owner THEN
    RAISE EXCEPTION 'User does not own this team';
  END IF;
  
  -- Check if user owns the property
  SELECT EXISTS (
    SELECT 1 FROM properties
    WHERE id = p_property_id AND user_id = p_user_id
  ) INTO v_owns_property;
  
  IF NOT v_owns_property THEN
    RAISE EXCEPTION 'User does not own this property';
  END IF;
  
  -- Check if property is already in team
  SELECT EXISTS (
    SELECT 1 FROM team_properties
    WHERE property_id = p_property_id AND team_id = p_team_id
  ) INTO v_already_exists;
  
  IF v_already_exists THEN
    RETURN; -- Already exists, consider it a success
  END IF;
  
  -- Add property to team
  INSERT INTO team_properties (team_id, property_id)
  VALUES (p_team_id, p_property_id);
  
  -- Update inventory items for this property to include the team_id
  UPDATE inventory_items
  SET team_id = p_team_id
  WHERE property_id = p_property_id
  AND team_id IS NULL;
  
EXCEPTION
  WHEN others THEN
    RAISE;
END;
$$;

-- Create a stored procedure to safely remove a property from a team
CREATE OR REPLACE PROCEDURE public.sp_remove_property_from_team(
  p_team_id uuid,
  p_property_id uuid,
  p_user_id uuid
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_is_owner boolean;
  v_exists boolean;
BEGIN
  -- Check if user owns the team
  SELECT EXISTS (
    SELECT 1 FROM teams
    WHERE id = p_team_id AND owner_id = p_user_id
  ) INTO v_is_owner;
  
  IF NOT v_is_owner THEN
    RAISE EXCEPTION 'User does not own this team';
  END IF;
  
  -- Check if property is in team
  SELECT EXISTS (
    SELECT 1 FROM team_properties
    WHERE property_id = p_property_id AND team_id = p_team_id
  ) INTO v_exists;
  
  IF NOT v_exists THEN
    RETURN; -- Not in team, consider it a success
  END IF;
  
  -- Remove property from team
  DELETE FROM team_properties
  WHERE team_id = p_team_id AND property_id = p_property_id;
  
  -- Update inventory items for this property to remove the team_id
  UPDATE inventory_items
  SET team_id = NULL
  WHERE property_id = p_property_id
  AND team_id = p_team_id;
  
EXCEPTION
  WHEN others THEN
    RAISE;
END;
$$;

-- Create a function to get team properties that bypasses RLS
CREATE OR REPLACE FUNCTION public.get_team_properties(p_team_id uuid)
RETURNS TABLE (property_id uuid)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT tp.property_id
  FROM team_properties tp
  WHERE tp.team_id = p_team_id;
END;
$$;
