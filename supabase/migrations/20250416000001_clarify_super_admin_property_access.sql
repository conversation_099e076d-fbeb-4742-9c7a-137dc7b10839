-- Add a comment to the has_property_access function to clarify super admin access
COMMENT ON FUNCTION public.has_property_access IS 'Determines if the current user has access to a property. Super admins and admins have access to all properties.';

-- Add a comment to the properties table to clarify access control
COMMENT ON TABLE public.properties IS 'Properties table with RLS policies. Super admins and admins have access to all properties through the has_property_access function.';

-- Add a comment to the "Team members can view properties in their teams" policy
COMMENT ON POLICY "Team members can view properties in their teams" ON public.properties IS 'Allows team members to view properties in their teams. Super admins and admins have access to all properties through the has_property_access function.';
