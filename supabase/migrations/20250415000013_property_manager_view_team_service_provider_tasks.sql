-- Allow property managers to see tasks created by service providers in their team using the function
DO $$
BEGIN
  -- Check if the policy already exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'maintenance_tasks' 
    AND policyname = 'Property managers can view team service provider tasks'
  ) THEN
    -- Create the policy if it doesn't exist
    EXECUTE $policy$
    CREATE POLICY "Property managers can view team service provider tasks"
    ON maintenance_tasks
    FOR SELECT
    TO public
    USING (
      EXISTS (
        SELECT 1 FROM profiles p
        WHERE p.id = auth.uid() 
          AND p.role = 'property_manager'
          AND is_service_provider_in_team(auth.uid(), maintenance_tasks.user_id)
      )
    );
    $policy$;
  END IF;
END
$$;
