-- Fix duplicate properties

-- Rename duplicate properties to make them unique
UPDATE properties
SET name = '<PERSON><PERSON> (<PERSON>)'
WHERE id = 'e4627eba-82be-49b0-bd74-e43dd270c402';

-- Fix other duplicate properties
UPDATE properties
SET name = '20235 True Rd (<PERSON>)'
WHERE id = 'dc65a697-2c3f-4e07-ad97-ecf6fef6983f';

UPDATE properties
SET name = '20235 True Rd (Molly)'
WHERE id = 'c9be08cb-6521-4de2-ae31-121ab9226064';

UPDATE properties
SET name = 'C st (<PERSON>)'
WHERE id = '375c8d62-63af-4fd7-90b3-b3c3f2efbec6';

UPDATE properties
SET name = 'C st (<PERSON>)'
WHERE id = '9a31c51e-f46f-4f4c-ac2a-088cf7e80983';

UPDATE properties
SET name = '<PERSON> (<PERSON>)'
WHERE id = '3d0b2b33-f3e3-4ae1-86ef-78faa98d4c26';

UPDATE properties
SET name = '<PERSON> (<PERSON>)'
WHERE id = 'd8a14728-5cd2-48e2-bac2-f14caffd523a';

UPDATE properties
SET name = 'Washington (Andy)'
WHERE id = '78761ce5-1907-442e-8c1c-78810cbc3b0d';

UPDATE properties
SET name = 'Washington (Molly)'
WHERE id = 'ab4f6aa6-0a89-4b6b-a912-5f1efc3b96d6';
