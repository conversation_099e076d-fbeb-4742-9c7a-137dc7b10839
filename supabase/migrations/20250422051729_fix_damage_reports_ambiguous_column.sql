-- Fix ambiguous column references in the damage_reports_with_property view and get_user_damage_reports function

-- Drop the existing function
DROP FUNCTION IF EXISTS get_user_damage_reports(UUID);

-- Drop the existing view
DROP VIEW IF EXISTS damage_reports_with_property;

-- Recreate the view with qualified column names
CREATE OR REPLACE VIEW damage_reports_with_property AS
SELECT
    dr.id AS id,
    dr.title AS title,
    dr.description AS description,
    dr.status AS status,
    dr.property_id AS property_id,
    p.name AS property_name,
    dr.user_id AS user_id,
    u.email AS user_email,
    dr.provider_id AS provider_id,
    mp.name AS provider_name,
    dr.created_at AS created_at,
    dr.updated_at AS updated_at,
    dr.platform AS platform,
    dr.team_id AS team_id,
    p.user_id AS property_owner_id
FROM
    damage_reports dr
JOIN
    properties p ON dr.property_id = p.id
LEFT JOIN
    auth.users u ON dr.user_id = u.id
LEFT JOIN
    maintenance_providers mp ON dr.provider_id = mp.id;

-- Recreate the function with qualified column names
CREATE OR REPLACE FUNCTION get_user_damage_reports(p_user_id UUID)
RETURNS TABLE (
    id UUID,
    title TEXT,
    description TEXT,
    status TEXT,
    property_id UUID,
    property_name TEXT,
    user_id UUID,
    user_email VARCHAR(255),
    provider_id UUID,
    provider_name TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    platform TEXT,
    team_id UUID,
    property_owner_id UUID,
    source TEXT
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    -- Get user's own reports
    RETURN QUERY
    SELECT
        dr.id,
        dr.title,
        dr.description,
        dr.status,
        dr.property_id,
        dr.property_name,
        dr.user_id,
        dr.user_email,
        dr.provider_id,
        dr.provider_name,
        dr.created_at,
        dr.updated_at,
        dr.platform,
        dr.team_id,
        dr.property_owner_id,
        'own'::TEXT AS source
    FROM
        damage_reports_with_property dr
    WHERE
        dr.user_id = p_user_id;

    -- Get reports for properties in teams the user is a member of
    -- but exclude reports already returned (user's own reports)
    RETURN QUERY
    SELECT DISTINCT ON (dr.id)
        dr.id,
        dr.title,
        dr.description,
        dr.status,
        dr.property_id,
        dr.property_name,
        dr.user_id,
        dr.user_email,
        dr.provider_id,
        dr.provider_name,
        dr.created_at,
        dr.updated_at,
        dr.platform,
        dr.team_id,
        dr.property_owner_id,
        'team'::TEXT AS source
    FROM
        damage_reports_with_property dr
    JOIN
        team_properties tp ON dr.property_id = tp.property_id
    JOIN
        team_members tm ON tp.team_id = tm.team_id
    WHERE
        tm.user_id = p_user_id
        AND tm.status = 'active'
        AND dr.user_id != p_user_id;

    -- Get reports directly associated with teams the user is a member of
    -- but exclude reports already returned
    RETURN QUERY
    SELECT DISTINCT ON (dr.id)
        dr.id,
        dr.title,
        dr.description,
        dr.status,
        dr.property_id,
        dr.property_name,
        dr.user_id,
        dr.user_email,
        dr.provider_id,
        dr.provider_name,
        dr.created_at,
        dr.updated_at,
        dr.platform,
        dr.team_id,
        dr.property_owner_id,
        'team_direct'::TEXT AS source
    FROM
        damage_reports_with_property dr
    JOIN
        team_members tm ON dr.team_id = tm.team_id
    WHERE
        tm.user_id = p_user_id
        AND tm.status = 'active'
        AND dr.user_id != p_user_id
        AND NOT EXISTS (
            SELECT 1 FROM team_properties tp
            WHERE tp.property_id = dr.property_id AND tp.team_id = tm.team_id
        );

    -- For admins, get all other reports
    IF EXISTS (SELECT 1 FROM profiles WHERE id = p_user_id AND (is_super_admin = true OR role = 'admin')) THEN
        RETURN QUERY
        SELECT DISTINCT ON (dr.id)
            dr.id,
            dr.title,
            dr.description,
            dr.status,
            dr.property_id,
            dr.property_name,
            dr.user_id,
            dr.user_email,
            dr.provider_id,
            dr.provider_name,
            dr.created_at,
            dr.updated_at,
            dr.platform,
            dr.team_id,
            dr.property_owner_id,
            'admin'::TEXT AS source
        FROM
            damage_reports_with_property dr
        WHERE
            dr.user_id != p_user_id
            AND NOT EXISTS (
                SELECT 1 FROM team_properties tp
                JOIN team_members tm ON tp.team_id = tm.team_id
                WHERE tp.property_id = dr.property_id AND tm.user_id = p_user_id AND tm.status = 'active'
            )
            AND (dr.team_id IS NULL OR NOT EXISTS (
                SELECT 1 FROM team_members tm
                WHERE tm.team_id = dr.team_id AND tm.user_id = p_user_id AND tm.status = 'active'
            ));
    END IF;

    -- For service providers, get reports assigned to them
    IF EXISTS (SELECT 1 FROM profiles WHERE id = p_user_id AND role = 'service_provider') THEN
        RETURN QUERY
        SELECT DISTINCT ON (dr.id)
            dr.id,
            dr.title,
            dr.description,
            dr.status,
            dr.property_id,
            dr.property_name,
            dr.user_id,
            dr.user_email,
            dr.provider_id,
            dr.provider_name,
            dr.created_at,
            dr.updated_at,
            dr.platform,
            dr.team_id,
            dr.property_owner_id,
            'assigned'::TEXT AS source
        FROM
            damage_reports_with_property dr
        WHERE
            dr.provider_id = p_user_id
            AND dr.user_id != p_user_id
            AND NOT EXISTS (
                SELECT 1 FROM team_properties tp
                JOIN team_members tm ON tp.team_id = tm.team_id
                WHERE tp.property_id = dr.property_id AND tm.user_id = p_user_id AND tm.status = 'active'
            )
            AND (dr.team_id IS NULL OR NOT EXISTS (
                SELECT 1 FROM team_members tm
                WHERE tm.team_id = dr.team_id AND tm.user_id = p_user_id AND tm.status = 'active'
            ));
    END IF;
END;
$$;

-- Grant permissions
ALTER VIEW damage_reports_with_property OWNER TO postgres;
GRANT SELECT ON damage_reports_with_property TO authenticated;
GRANT SELECT ON damage_reports_with_property TO anon;

GRANT EXECUTE ON FUNCTION get_user_damage_reports(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_damage_reports(UUID) TO anon;
