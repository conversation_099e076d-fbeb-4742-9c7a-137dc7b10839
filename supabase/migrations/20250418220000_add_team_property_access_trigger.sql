-- Create a function to ensure team members have access to properties
CREATE OR REP<PERSON>CE FUNCTION public.ensure_team_property_access()
RETURNS TRIGGER AS $$
BEGIN
  -- When a new team_property is created, grant access to all team members
  IF TG_OP = 'INSERT' THEN
    -- No action needed, RLS policies will handle access
    RETURN NEW;
  END IF;
  
  -- When a team_property is deleted, revoke access from all team members
  IF TG_OP = 'DELETE' THEN
    -- No action needed, RLS policies will handle access
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger on team_properties table
DROP TRIGGER IF EXISTS ensure_team_property_access_trigger ON team_properties;
CREATE TRIGGER ensure_team_property_access_trigger
AFTER INSERT OR DELETE ON team_properties
FOR EACH ROW
EXECUTE FUNCTION public.ensure_team_property_access();
