-- Allow property managers to see tasks for properties in their teams
DO $$
BEGIN
  -- Check if the policy already exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'maintenance_tasks' 
    AND policyname = 'Property managers can view tasks for team properties'
  ) THEN
    -- Create the policy if it doesn't exist
    EXECUTE $policy$
    CREATE POLICY "Property managers can view tasks for team properties"
    ON maintenance_tasks
    FOR SELECT
    TO public
    USING (
      EXISTS (
        SELECT 1 FROM profiles p
        WHERE p.id = auth.uid() AND p.role = 'property_manager'
        AND maintenance_tasks.property_id IS NOT NULL
        AND EXISTS (
          -- Check if the property is in the property manager's team
          SELECT 1 FROM team_members tm
          JOIN team_properties tp ON tm.team_id = tp.team_id
          WHERE tm.user_id = auth.uid() 
            AND tm.status = 'active'
            AND tp.property_id = maintenance_tasks.property_id
        )
      )
    );
    $policy$;
  END IF;
END
$$;
