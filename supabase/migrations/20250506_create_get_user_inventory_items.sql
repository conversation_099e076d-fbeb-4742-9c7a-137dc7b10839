-- Create the get_user_inventory_items function
CREATE OR R<PERSON>LACE FUNCTION get_user_inventory_items(p_user_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  property_id UUID,
  property_name TEXT,
  collection TEXT,
  quantity INTEGER,
  min_quantity INTEGER,
  price NUMERIC,
  amazon_url TEXT,
  walmart_url TEXT,
  image_url TEXT,
  last_ordered TIMESTAMP WITH TIME ZONE,
  user_id UUID
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    i.id,
    i.name,
    i.property_id,
    p.name AS property_name,
    i.collection,
    i.quantity,
    i.min_quantity,
    i.price,
    i.amazon_url,
    i.walmart_url,
    i.image_url,
    i.updated_at AS last_ordered,
    i.user_id
  FROM
    inventory_items i
  LEFT JOIN
    properties p ON i.property_id = p.id
  WHERE
    i.user_id = p_user_id
  ORDER BY
    i.name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
