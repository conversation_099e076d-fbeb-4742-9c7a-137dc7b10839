-- Create a more comprehensive function to check team membership
CREATE OR REPLACE FUNCTION public.is_team_member_with_property_access(
  p_user_id UUID,
  p_property_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
  is_member BOOLEAN;
BEGIN
  -- Check if the user is a super admin or admin
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = p_user_id AND (is_super_admin = true OR role = 'admin')
  ) INTO is_member;
  
  IF is_member THEN
    RETURN TRUE;
  END IF;
  
  -- Check if the user owns the property
  SELECT EXISTS (
    SELECT 1 FROM properties
    WHERE id = p_property_id AND user_id = p_user_id
  ) INTO is_member;
  
  IF is_member THEN
    RETURN TRUE;
  END IF;
  
  -- Check if the user is a member of a team that has access to the property
  SELECT EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = p_property_id
    AND tm.user_id = p_user_id
    AND tm.status = 'active'
  ) INTO is_member;
  
  RETURN is_member;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get complete property details with team access check
CREATE OR REPLACE FUNCTION public.get_property_with_team_access(
  p_property_id UUID,
  p_user_id UUID
) RETURNS SETOF properties AS $$
BEGIN
  -- Check if the user has access to the property
  IF NOT public.is_team_member_with_property_access(p_user_id, p_property_id) THEN
    RAISE EXCEPTION 'Access denied: User does not have permission to view this property';
  END IF;
  
  -- Return the property details
  RETURN QUERY
  SELECT * FROM properties
  WHERE id = p_property_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing policies for properties table
DROP POLICY IF EXISTS "Properties are viewable by owner" ON properties;
DROP POLICY IF EXISTS "Properties are viewable by team members" ON properties;
DROP POLICY IF EXISTS "Properties are viewable by admins" ON properties;
DROP POLICY IF EXISTS "Properties are editable by owner" ON properties;
DROP POLICY IF EXISTS "Properties are editable by team members with permission" ON properties;

-- Create new policies with improved team access
CREATE POLICY "Allow property access to owners"
ON properties
FOR ALL
USING (auth.uid() = user_id);

CREATE POLICY "Allow property access to team members"
ON properties
FOR SELECT
USING (
  public.is_team_member_with_property_access(auth.uid(), id)
);

CREATE POLICY "Allow property access to admins"
ON properties
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  )
);
