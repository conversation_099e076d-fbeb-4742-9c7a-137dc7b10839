-- Drop all existing policies for team_members to start fresh
DROP POLICY IF EXISTS "Team members are viewable by team members" ON team_members;
DROP POLICY IF EXISTS "Team members are viewable by admins" ON team_members;
DROP POLICY IF EXISTS "Team members are editable by team owners" ON team_members;

-- Create new policies without recursion
CREATE POLICY "Team members are viewable by the user themselves"
ON team_members
FOR SELECT
USING (
  auth.uid() = user_id
);

CREATE POLICY "Team members are viewable by team owners"
ON team_members
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM teams
    WHERE teams.id = team_members.team_id
    AND teams.owner_id = auth.uid()
  )
);

CREATE POLICY "Team members are viewable by admins"
ON team_members
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  )
);

CREATE POLICY "Team members are editable by team owners"
ON team_members
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM teams
    WHERE teams.id = team_members.team_id
    AND teams.owner_id = auth.uid()
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM teams
    WHERE teams.id = team_members.team_id
    AND teams.owner_id = auth.uid()
  )
);
