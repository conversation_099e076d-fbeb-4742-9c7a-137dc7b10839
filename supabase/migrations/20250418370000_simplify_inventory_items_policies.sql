-- Drop ALL policies on inventory_items table
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'inventory_items'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON inventory_items', policy_record.policyname);
    END LOOP;
END
$$;

-- Create a single, simple policy for inventory_items
CREATE POLICY "inventory_items_simple_policy"
ON inventory_items
FOR ALL
USING (true)
WITH CHECK (true);

-- Enable RLS on inventory_items
ALTER TABLE inventory_items ENABLE ROW LEVEL SECURITY;
