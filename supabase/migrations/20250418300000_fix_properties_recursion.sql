-- Drop all existing policies for properties to start fresh
DROP POLICY IF EXISTS "Allow property access to owners" ON properties;
DROP POLICY IF EXISTS "Allow property access to team members" ON properties;
DROP POLICY IF EXISTS "Allow property access to admins" ON properties;

-- Create new policies without recursion
CREATE POLICY "Properties are viewable by owners directly"
ON properties
FOR SELECT
USING (
  auth.uid() = user_id
);

CREATE POLICY "Properties are viewable by team members directly"
ON properties
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = properties.id
    AND tm.user_id = auth.uid()
    AND tm.status = 'active'
  )
);

CREATE POLICY "Properties are viewable by admins directly"
ON properties
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  )
);

CREATE POLICY "Properties are editable by owners directly"
ON properties
FOR UPDATE
USING (
  auth.uid() = user_id
)
WITH CHECK (
  auth.uid() = user_id
);

CREATE POLICY "Properties are editable by team members with permission directly"
ON properties
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    JOIN user_permissions up ON tm.team_id = up.team_id
    WHERE tp.property_id = properties.id
    AND tm.user_id = auth.uid()
    AND tm.status = 'active'
    AND up.user_id = auth.uid()
    AND up.permission = 'manage_properties'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    JOIN user_permissions up ON tm.team_id = up.team_id
    WHERE tp.property_id = properties.id
    AND tm.user_id = auth.uid()
    AND tm.status = 'active'
    AND up.user_id = auth.uid()
    AND up.permission = 'manage_properties'
  )
);
