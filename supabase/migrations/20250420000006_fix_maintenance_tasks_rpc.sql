-- Fix the get_maintenance_tasks_for_user function to properly handle team access
CREATE OR REPLACE FUNCTION get_maintenance_tasks_for_user(p_user_id UUID)
RETURNS SETOF maintenance_tasks
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_role TEXT;
    v_is_super_admin BOOLEAN;
BEGIN
    -- Get user role and super admin status
    SELECT role, is_super_admin INTO v_role, v_is_super_admin
    FROM profiles
    WHERE id = p_user_id;

    -- Super admins and admins can see all tasks
    IF v_is_super_admin OR v_role = 'super_admin' OR v_role = 'admin' THEN
        RETURN QUERY SELECT * FROM maintenance_tasks ORDER BY created_at DESC;
    END IF;

    -- Property managers can see their own tasks and tasks for their team properties
    IF v_role = 'property_manager' THEN
        RETURN QUERY
        SELECT DISTINCT mt.*
        FROM maintenance_tasks mt
        WHERE
            mt.user_id = p_user_id
            OR mt.property_id IN (
                SELECT p.id
                FROM properties p
                WHERE p.user_id = p_user_id
            )
            OR mt.team_id IN (
                SELECT t.id
                FROM teams t
                WHERE t.owner_id = p_user_id
            )
            OR mt.property_id IN (
                SELECT tp.property_id
                FROM team_properties tp
                JOIN teams t ON tp.team_id = t.id
                WHERE t.owner_id = p_user_id
            )
        ORDER BY mt.created_at DESC;
    END IF;

    -- Staff members can see tasks for properties in their teams
    IF v_role = 'staff' THEN
        RETURN QUERY
        SELECT DISTINCT mt.*
        FROM maintenance_tasks mt
        WHERE
            mt.user_id = p_user_id
            OR mt.team_id IN (
                SELECT tm.team_id
                FROM team_members tm
                WHERE tm.user_id = p_user_id AND tm.status = 'active'
            )
            OR mt.property_id IN (
                SELECT tp.property_id
                FROM team_properties tp
                JOIN team_members tm ON tp.team_id = tm.team_id
                WHERE tm.user_id = p_user_id AND tm.status = 'active'
            )
        ORDER BY mt.created_at DESC;
    END IF;

    -- Service providers can see tasks assigned to them
    IF v_role = 'service_provider' THEN
        RETURN QUERY
        SELECT *
        FROM maintenance_tasks
        WHERE
            user_id = p_user_id
            OR provider_id = p_user_id
            OR assigned_to = p_user_id
        ORDER BY created_at DESC;
    END IF;

    -- Default fallback - just return user's own tasks
    RETURN QUERY
    SELECT *
    FROM maintenance_tasks
    WHERE user_id = p_user_id
    ORDER BY created_at DESC;
END;
$$;

-- Add a simplified RLS policy for maintenance_tasks
DROP POLICY IF EXISTS maintenance_tasks_policy ON maintenance_tasks;

CREATE POLICY maintenance_tasks_policy ON maintenance_tasks
    USING (
        -- User owns the task
        user_id::text = auth.uid()::text
        -- User is assigned to the task
        OR assigned_to::text = auth.uid()::text
        -- User is the service provider for the task
        OR provider_id::text = auth.uid()::text
        -- User owns the property
        OR property_id IN (
            SELECT id FROM properties WHERE user_id::text = auth.uid()::text
        )
        -- User is a team member with access to the property
        OR property_id IN (
            SELECT tp.property_id
            FROM team_properties tp
            JOIN team_members tm ON tp.team_id = tm.team_id
            WHERE tm.user_id::text = auth.uid()::text AND tm.status = 'active'
        )
        -- User is a team member with access to the team
        OR team_id IN (
            SELECT team_id
            FROM team_members
            WHERE user_id::text = auth.uid()::text AND status = 'active'
        )
        -- User is a team owner
        OR team_id IN (
            SELECT id
            FROM teams
            WHERE owner_id::text = auth.uid()::text
        )
        -- User is a super admin or admin
        OR EXISTS (
            SELECT 1
            FROM profiles
            WHERE id::text = auth.uid()::text AND (is_super_admin OR role IN ('super_admin', 'admin'))
        )
    );
