-- Add an index on the property_id column in the maintenance_tasks table
-- This will improve query performance when filtering by property_id
CREATE INDEX IF NOT EXISTS maintenance_tasks_property_id_idx ON maintenance_tasks(property_id);

-- Add an index on the team_id column in the maintenance_tasks table
-- This will improve query performance when filtering by team_id
CREATE INDEX IF NOT EXISTS maintenance_tasks_team_id_idx ON maintenance_tasks(team_id);

-- Add a comment to explain the purpose of these indexes
COMMENT ON INDEX maintenance_tasks_property_id_idx IS 'Improves query performance when filtering maintenance tasks by property_id, especially for team members viewing tasks for their team properties';
COMMENT ON INDEX maintenance_tasks_team_id_idx IS 'Improves query performance when filtering maintenance tasks by team_id';
