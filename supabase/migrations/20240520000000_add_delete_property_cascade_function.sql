-- Create a function to delete a property and all its related data
CREATE OR REPLACE FUNCTION delete_property_cascade(property_id_param UUID)
RETURNS VOID AS $$
BEGIN
    -- Delete maintenance tasks associated with the property
    DELETE FROM maintenance_tasks WHERE property_id = property_id_param;
    
    -- Delete inventory items associated with the property
    DELETE FROM inventory_items WHERE property_id = property_id_param;
    
    -- Delete team_properties associations
    DELETE FROM team_properties WHERE property_id = property_id_param;
    
    -- Get all damage report IDs for this property
    DECLARE
        damage_report_ids UUID[];
    BEGIN
        SELECT array_agg(id) INTO damage_report_ids 
        FROM damage_reports 
        WHERE property_id = property_id_param;
        
        -- If there are damage reports, delete related data
        IF damage_report_ids IS NOT NULL AND array_length(damage_report_ids, 1) > 0 THEN
            -- Delete damage photos
            DELETE FROM damage_photos WHERE damage_report_id = ANY(damage_report_ids);
            
            -- Delete damage notes
            DELETE FROM damage_notes WHERE damage_report_id = ANY(damage_report_ids);
            
            -- Delete damage reports
            DELETE FROM damage_reports WHERE property_id = property_id_param;
        END IF;
    END;
    
    -- Finally, delete the property itself
    DELETE FROM properties WHERE id = property_id_param;
END;
$$ LANGUAGE plpgsql;

-- Create a function to delete a team and all its related data
CREATE OR REPLACE FUNCTION delete_team_cascade(team_id_param UUID)
RETURNS VOID AS $$
BEGIN
    -- Delete team_members associated with the team
    DELETE FROM team_members WHERE team_id = team_id_param;
    
    -- Delete team_properties associations
    DELETE FROM team_properties WHERE team_id = team_id_param;
    
    -- Update inventory items to remove team_id
    UPDATE inventory_items SET team_id = NULL WHERE team_id = team_id_param;
    
    -- Finally, delete the team itself
    DELETE FROM teams WHERE id = team_id_param;
END;
$$ LANGUAGE plpgsql;
