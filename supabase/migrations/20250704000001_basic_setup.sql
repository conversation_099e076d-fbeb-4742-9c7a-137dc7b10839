-- Basic setup migration for StayFu local development
-- This creates the essential tables and structure based on the backup schema

-- Create permission type enum (from backup)
CREATE TYPE IF NOT EXISTS permission_type AS ENUM (
    'manage_properties',
    'submit_damage_reports',
    'manage_inventory',
    'view_inventory',
    'manage_staff',
    'manage_service_providers',
    'view_reports',
    'edit_reports',
    'admin_dashboard_access',
    'impersonate_users',
    'edit_user_data',
    'add_users',
    'delete_users',
    'manage_subscriptions',
    'admin',
    'manage_purchase_orders',
    'view_purchase_orders',
    'manage_damage_reports',
    'view_damage_reports',
    'manage_maintenance',
    'view_maintenance',
    'manage_team',
    'view_team',
    'create_maintenance_task'
);

-- Create other enums
CREATE TYPE IF NOT EXISTS po_status AS ENUM (
    'pending',
    'ordered',
    'delivered',
    'archived'
);

CREATE TYPE IF NOT EXISTS user_role AS ENUM (
    'super_admin',
    'admin',
    'property_manager',
    'staff',
    'service_provider'
);

-- Profiles table
CREATE TABLE IF NOT EXISTS profiles (
    id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email text UNIQUE NOT NULL,
    full_name text,
    avatar_url text,
    role text DEFAULT 'user',
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Teams table
CREATE TABLE IF NOT EXISTS teams (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    description text,
    owner_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Properties table
CREATE TABLE IF NOT EXISTS properties (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    address text,
    description text,
    owner_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    team_id uuid REFERENCES teams(id) ON DELETE SET NULL,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Team members table
CREATE TABLE IF NOT EXISTS team_members (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    team_id uuid REFERENCES teams(id) ON DELETE CASCADE,
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    role text DEFAULT 'member',
    created_at timestamptz DEFAULT now(),
    UNIQUE(team_id, user_id)
);

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view teams they belong to" ON teams
    FOR SELECT USING (
        owner_id = auth.uid() OR 
        EXISTS (
            SELECT 1 FROM team_members 
            WHERE team_id = teams.id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Team owners can manage their teams" ON teams
    FOR ALL USING (owner_id = auth.uid());

CREATE POLICY "Users can view properties they have access to" ON properties
    FOR SELECT USING (
        owner_id = auth.uid() OR
        team_id IN (
            SELECT team_id FROM team_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Property owners can manage their properties" ON properties
    FOR ALL USING (owner_id = auth.uid());

-- Team members policies
CREATE POLICY "Users can view team memberships they belong to" ON team_members
    FOR SELECT USING (user_id = auth.uid() OR team_id IN (
        SELECT id FROM teams WHERE owner_id = auth.uid()
    ));

CREATE POLICY "Team owners can manage team memberships" ON team_members
    FOR ALL USING (team_id IN (
        SELECT id FROM teams WHERE owner_id = auth.uid()
    ));
