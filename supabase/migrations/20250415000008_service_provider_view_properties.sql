-- Ensure service providers can view properties in their teams
DO $$
BEGIN
  -- Check if the policy already exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'properties' 
    AND policyname = 'Service providers can view properties in their teams'
  ) THEN
    -- Create the policy if it doesn't exist
    EXECUTE $policy$
    CREATE POLICY "Service providers can view properties in their teams"
    ON properties
    FOR SELECT
    TO public
    USING (
      EXISTS (
        SELECT 1 
        FROM team_properties tp
        JOIN team_members tm ON tp.team_id = tm.team_id
        JOIN profiles p ON tm.user_id = p.id
        WHERE tp.property_id = properties.id 
        AND tm.user_id = auth.uid()
        AND tm.status = 'active'
        AND p.role = 'service_provider'
      )
    );
    $policy$;
  END IF;
END
$$;
