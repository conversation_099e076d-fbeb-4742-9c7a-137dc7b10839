-- Create a function to execute SQL queries with admin privileges
-- This is needed for the admin-force-delete-user function
CREATE OR REPLACE FUNCTION execute_sql_query(sql_query text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER -- Run with privileges of the function creator
AS $$
BEGIN
  EXECUTE sql_query;
END;
$$;

-- Grant execute permission to authenticated users
-- The admin-force-delete-user function will check if the user is a super admin
GRANT EXECUTE ON FUNCTION execute_sql_query TO authenticated;

-- Add comment to function
COMMENT ON FUNCTION execute_sql_query IS 'Executes a SQL query with admin privileges. Only to be used by super admins through secure edge functions.';
