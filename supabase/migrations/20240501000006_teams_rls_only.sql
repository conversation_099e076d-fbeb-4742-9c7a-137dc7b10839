-- Enable Row Level Security on teams-related tables
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_invitations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for teams table

-- Super Admins can do anything with teams
DROP POLICY IF EXISTS "Super Admins can do anything with teams" ON teams;
CREATE POLICY "Super Admins can do anything with teams"
ON teams
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND is_super_admin = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND is_super_admin = true
  )
);

-- <PERSON><PERSON> can do anything with teams
DROP POLICY IF EXISTS "Admins can do anything with teams" ON teams;
CREATE POLICY "Ad<PERSON> can do anything with teams"
ON teams
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Property Managers can create teams
DROP POLICY IF EXISTS "Property Managers can create teams" ON teams;
CREATE POLICY "Property Managers can create teams"
ON teams
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'property_manager'
  ) AND owner_id = auth.uid()
);

-- Users can view teams they own
DROP POLICY IF EXISTS "Users can view teams they own" ON teams;
CREATE POLICY "Users can view teams they own"
ON teams
FOR SELECT
USING (owner_id = auth.uid());

-- Users can view teams they are members of
DROP POLICY IF EXISTS "Users can view teams they are members of" ON teams;
CREATE POLICY "Users can view teams they are members of"
ON teams
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = teams.id AND user_id = auth.uid() AND status = 'active'
  )
);

-- Team owners can update their teams
DROP POLICY IF EXISTS "Team owners can update their teams" ON teams;
CREATE POLICY "Team owners can update their teams"
ON teams
FOR UPDATE
USING (owner_id = auth.uid())
WITH CHECK (owner_id = auth.uid());

-- Team owners can delete their teams
DROP POLICY IF EXISTS "Team owners can delete their teams" ON teams;
CREATE POLICY "Team owners can delete their teams"
ON teams
FOR DELETE
USING (owner_id = auth.uid());

-- RLS Policies for team_members table

-- Super Admins can do anything with team members
DROP POLICY IF EXISTS "Super Admins can do anything with team members" ON team_members;
CREATE POLICY "Super Admins can do anything with team members"
ON team_members
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND is_super_admin = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND is_super_admin = true
  )
);

-- Admins can do anything with team members
DROP POLICY IF EXISTS "Admins can do anything with team members" ON team_members;
CREATE POLICY "Admins can do anything with team members"
ON team_members
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Team owners can manage team members
DROP POLICY IF EXISTS "Team owners can manage team members" ON team_members;
CREATE POLICY "Team owners can manage team members"
ON team_members
USING (
  EXISTS (
    SELECT 1 FROM teams
    WHERE id = team_members.team_id AND owner_id = auth.uid()
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM teams
    WHERE id = team_members.team_id AND owner_id = auth.uid()
  )
);

-- Property Managers with manage_staff permission can manage staff members
DROP POLICY IF EXISTS "Property Managers can manage staff members" ON team_members;
CREATE POLICY "Property Managers can manage staff members"
ON team_members
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'property_manager'
  ) AND
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = team_members.team_id AND user_id = auth.uid() AND status = 'active'
  ) AND
  EXISTS (
    SELECT 1 FROM user_permissions
    WHERE user_id = auth.uid() AND team_id = team_members.team_id AND permission = 'manage_staff' AND enabled = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'property_manager'
  ) AND
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = team_members.team_id AND user_id = auth.uid() AND status = 'active'
  ) AND
  EXISTS (
    SELECT 1 FROM user_permissions
    WHERE user_id = auth.uid() AND team_id = team_members.team_id AND permission = 'manage_staff' AND enabled = true
  )
);

-- Property Managers with manage_service_providers permission can manage service providers
DROP POLICY IF EXISTS "Property Managers can manage service providers" ON team_members;
CREATE POLICY "Property Managers can manage service providers"
ON team_members
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'property_manager'
  ) AND
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = team_members.team_id AND user_id = auth.uid() AND status = 'active'
  ) AND
  EXISTS (
    SELECT 1 FROM user_permissions
    WHERE user_id = auth.uid() AND team_id = team_members.team_id AND permission = 'manage_service_providers' AND enabled = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'property_manager'
  ) AND
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = team_members.team_id AND user_id = auth.uid() AND status = 'active'
  ) AND
  EXISTS (
    SELECT 1 FROM user_permissions
    WHERE user_id = auth.uid() AND team_id = team_members.team_id AND permission = 'manage_service_providers' AND enabled = true
  )
);

-- Users can view team members in teams they belong to
DROP POLICY IF EXISTS "Users can view team members in their teams" ON team_members;
CREATE POLICY "Users can view team members in their teams"
ON team_members
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = team_members.team_id AND user_id = auth.uid() AND status = 'active'
  ) OR user_id = auth.uid()
);

-- RLS Policies for user_permissions table

-- Super Admins can do anything with permissions
DROP POLICY IF EXISTS "Super Admins can do anything with permissions" ON user_permissions;
CREATE POLICY "Super Admins can do anything with permissions"
ON user_permissions
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND is_super_admin = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND is_super_admin = true
  )
);

-- Admins can manage permissions except for Super Admins
DROP POLICY IF EXISTS "Admins can manage permissions except for Super Admins" ON user_permissions;
CREATE POLICY "Admins can manage permissions except for Super Admins"
ON user_permissions
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin'
  ) AND 
  NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = user_permissions.user_id AND is_super_admin = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin'
  ) AND 
  NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = user_permissions.user_id AND is_super_admin = true
  )
);

-- Property Managers can manage permissions for their team members
DROP POLICY IF EXISTS "Property Managers can manage permissions for their team members" ON user_permissions;
CREATE POLICY "Property Managers can manage permissions for their team members"
ON user_permissions
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'property_manager'
  ) AND 
  team_id IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM teams
    WHERE id = user_permissions.team_id AND owner_id = auth.uid()
  ) AND
  NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = user_permissions.user_id AND (role = 'admin' OR is_super_admin = true)
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'property_manager'
  ) AND 
  team_id IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM teams
    WHERE id = user_permissions.team_id AND owner_id = auth.uid()
  ) AND
  NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = user_permissions.user_id AND (role = 'admin' OR is_super_admin = true)
  )
);

-- Users can view their own permissions
DROP POLICY IF EXISTS "Users can view their own permissions" ON user_permissions;
CREATE POLICY "Users can view their own permissions"
ON user_permissions
FOR SELECT
USING (user_id = auth.uid());

-- Team members can view permissions for their team
DROP POLICY IF EXISTS "Team members can view permissions for their team" ON user_permissions;
CREATE POLICY "Team members can view permissions for their team"
ON user_permissions
FOR SELECT
USING (
  team_id IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = user_permissions.team_id AND user_id = auth.uid() AND status = 'active'
  )
);

-- RLS Policies for team_invitations table

-- Super Admins can do anything with team invitations
DROP POLICY IF EXISTS "Super Admins can do anything with team invitations" ON team_invitations;
CREATE POLICY "Super Admins can do anything with team invitations"
ON team_invitations
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND is_super_admin = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND is_super_admin = true
  )
);

-- Admins can do anything with team invitations
DROP POLICY IF EXISTS "Admins can do anything with team invitations" ON team_invitations;
CREATE POLICY "Admins can do anything with team invitations"
ON team_invitations
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Team owners can manage invitations for their teams
DROP POLICY IF EXISTS "Team owners can manage invitations for their teams" ON team_invitations;
CREATE POLICY "Team owners can manage invitations for their teams"
ON team_invitations
USING (
  EXISTS (
    SELECT 1 FROM teams
    WHERE id = team_invitations.team_id AND owner_id = auth.uid()
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM teams
    WHERE id = team_invitations.team_id AND owner_id = auth.uid()
  )
);

-- Property Managers with manage_staff permission can invite staff
DROP POLICY IF EXISTS "Property Managers can invite staff" ON team_invitations;
CREATE POLICY "Property Managers can invite staff"
ON team_invitations
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'property_manager'
  ) AND 
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = team_invitations.team_id AND user_id = auth.uid() AND status = 'active'
  ) AND 
  EXISTS (
    SELECT 1 FROM user_permissions
    WHERE user_id = auth.uid() AND team_id = team_invitations.team_id AND permission = 'manage_staff' AND enabled = true
  ) AND
  role = 'staff'
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'property_manager'
  ) AND 
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = team_invitations.team_id AND user_id = auth.uid() AND status = 'active'
  ) AND 
  EXISTS (
    SELECT 1 FROM user_permissions
    WHERE user_id = auth.uid() AND team_id = team_invitations.team_id AND permission = 'manage_staff' AND enabled = true
  ) AND
  role = 'staff'
);

-- Property Managers with manage_service_providers permission can invite service providers
DROP POLICY IF EXISTS "Property Managers can invite service providers" ON team_invitations;
CREATE POLICY "Property Managers can invite service providers"
ON team_invitations
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'property_manager'
  ) AND 
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = team_invitations.team_id AND user_id = auth.uid() AND status = 'active'
  ) AND 
  EXISTS (
    SELECT 1 FROM user_permissions
    WHERE user_id = auth.uid() AND team_id = team_invitations.team_id AND permission = 'manage_service_providers' AND enabled = true
  ) AND
  role = 'service_provider'
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'property_manager'
  ) AND 
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = team_invitations.team_id AND user_id = auth.uid() AND status = 'active'
  ) AND 
  EXISTS (
    SELECT 1 FROM user_permissions
    WHERE user_id = auth.uid() AND team_id = team_invitations.team_id AND permission = 'manage_service_providers' AND enabled = true
  ) AND
  role = 'service_provider'
);

-- Users can view invitations sent to their email
DROP POLICY IF EXISTS "Users can view invitations sent to their email" ON team_invitations;
CREATE POLICY "Users can view invitations sent to their email"
ON team_invitations
FOR SELECT
USING (
  email = (
    SELECT email FROM auth.users WHERE id = auth.uid()
  )
);
