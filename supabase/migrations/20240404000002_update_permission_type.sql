-- Update permission_type enum values
DO $$
BEGIN
  -- Add missing enum values if they don't exist
  BEGIN
    ALTER TYPE permission_type ADD VALUE IF NOT EXISTS 'manage_properties';
  EXCEPTION
    WHEN duplicate_object THEN NULL;
  END;
  
  BEGIN
    ALTER TYPE permission_type ADD VALUE IF NOT EXISTS 'manage_inventory';
  EXCEPTION
    WHEN duplicate_object THEN NULL;
  END;
  
  BEGIN
    ALTER TYPE permission_type ADD VALUE IF NOT EXISTS 'view_inventory';
  EXCEPTION
    WHEN duplicate_object THEN NULL;
  END;
  
  BEGIN
    ALTER TYPE permission_type ADD VALUE IF NOT EXISTS 'manage_purchase_orders';
  EXCEPTION
    WHEN duplicate_object THEN NULL;
  END;
  
  BEGIN
    ALTER TYPE permission_type ADD VALUE IF NOT EXISTS 'view_purchase_orders';
  EXCEPTION
    WHEN duplicate_object THEN NULL;
  END;
  
  BEGIN
    ALTER TYPE permission_type ADD VALUE IF NOT EXISTS 'manage_damage_reports';
  EXCEPTION
    WHEN duplicate_object THEN NULL;
  END;
  
  BEGIN
    ALTER TYPE permission_type ADD VALUE IF NOT EXISTS 'view_damage_reports';
  EXCEPTION
    WHEN duplicate_object THEN NULL;
  END;
  
  BEGIN
    ALTER TYPE permission_type ADD VALUE IF NOT EXISTS 'manage_maintenance';
  EXCEPTION
    WHEN duplicate_object THEN NULL;
  END;
  
  BEGIN
    ALTER TYPE permission_type ADD VALUE IF NOT EXISTS 'view_maintenance';
  EXCEPTION
    WHEN duplicate_object THEN NULL;
  END;
  
  BEGIN
    ALTER TYPE permission_type ADD VALUE IF NOT EXISTS 'manage_team';
  EXCEPTION
    WHEN duplicate_object THEN NULL;
  END;
  
  BEGIN
    ALTER TYPE permission_type ADD VALUE IF NOT EXISTS 'view_team';
  EXCEPTION
    WHEN duplicate_object THEN NULL;
  END;
END;
$$;
