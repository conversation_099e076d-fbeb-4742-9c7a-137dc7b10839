-- Drop existing functions
DROP FUNCTION IF EXISTS public.get_user_properties(uuid);
DROP FUNCTION IF EXISTS public.get_user_role_properties(uuid);

-- Recreate get_user_properties with zip column
CREATE OR REPLACE FUNCTION public.get_user_properties(p_user_id uuid)
RETURNS TABLE(
    id uuid, 
    name text, 
    address text, 
    city text, 
    state text, 
    zip text, 
    user_id uuid, 
    team_id uuid, 
    created_at timestamp with time zone, 
    updated_at timestamp with time zone
)
LANGUAGE sql
SECURITY DEFINER
AS $function$
    SELECT DISTINCT ON (p.id)
        p.id,
        p.name,
        p.address,
        p.city,
        p.state,
        p.zip,
        p.user_id,
        p.team_id,
        p.created_at,
        p.updated_at
    FROM
        properties p
    WHERE
        -- User's own properties
        p.user_id = p_user_id
        -- Admin access
        OR EXISTS (SELECT 1 FROM profiles prf WHERE prf.id = p_user_id AND (prf.is_super_admin = true OR prf.role = 'admin'))
        -- Team member access
        OR EXISTS (
            SELECT 1 FROM team_properties tp
            JOIN team_members tm ON tp.team_id = tm.team_id
            WHERE tp.property_id = p.id AND tm.user_id = p_user_id AND tm.status = 'active'
        )
    ORDER BY
        p.id, p.name;
$function$;

-- Create a function that routes to the appropriate properties function based on user role
CREATE OR REPLACE FUNCTION public.get_user_role_properties(p_user_id UUID)
RETURNS TABLE (
    id UUID,
    name TEXT,
    address TEXT,
    city TEXT,
    state TEXT,
    zip TEXT,
    user_id UUID,
    team_id UUID,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
    user_role TEXT;
BEGIN
    -- Get the user's role
    SELECT profiles.role INTO user_role
    FROM profiles
    WHERE profiles.id = p_user_id;
    
    -- Route to the appropriate function based on role
    IF user_role = 'service_provider' THEN
        RETURN QUERY
        SELECT * FROM get_service_provider_properties(p_user_id);
    ELSE
        -- For all other roles (property_manager, admin, etc.), use get_user_properties
        RETURN QUERY
        SELECT * FROM get_user_properties(p_user_id);
    END IF;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_user_properties(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_properties(UUID) TO anon;
GRANT EXECUTE ON FUNCTION public.get_user_role_properties(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_role_properties(UUID) TO anon;

-- Add comments to the functions
COMMENT ON FUNCTION public.get_user_properties(UUID) IS 'Gets properties accessible to a user, including their own properties and properties from teams they are members of';
COMMENT ON FUNCTION public.get_user_role_properties(UUID) IS 'Routes to the appropriate properties function based on user role';
