-- Fix duplicate damage reports by adding a unique constraint on title and property_id

-- First, identify and remove any existing duplicates
DO $$
DECLARE
    duplicate_record RECORD;
BEGIN
    -- Find duplicate records (same title and property_id)
    FOR duplicate_record IN (
        SELECT id, title, property_id, created_at,
               ROW_NUMBER() OVER (PARTITION BY title, property_id ORDER BY created_at DESC) as row_num
        FROM damage_reports
    )
    LOOP
        -- Delete all but the most recent duplicate (keep the newest one)
        IF duplicate_record.row_num > 1 THEN
            DELETE FROM damage_reports WHERE id = duplicate_record.id;
            RAISE NOTICE 'Deleted duplicate damage report: %', duplicate_record.id;
        END IF;
    END LOOP;
END;
$$;

-- Now add a unique constraint to prevent future duplicates
ALTER TABLE damage_reports
ADD CONSTRAINT damage_reports_title_property_id_key UNIQUE (title, property_id);

-- Add an index to improve query performance
CREATE INDEX IF NOT EXISTS damage_reports_property_id_idx ON damage_reports(property_id);
