-- Fix damage reports property_name issue

-- Drop the existing function
DROP FUNCTION IF EXISTS get_user_damage_reports_simple(UUID);

-- Recreate the function with property_name and provider_name
CREATE OR REPLACE FUNCTION get_user_damage_reports_simple(p_user_id UUID)
RETURNS TABLE (
    id UUID,
    title TEXT,
    description TEXT,
    status TEXT,
    property_id UUID,
    property_name TEXT,
    user_id UUID,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    provider_id UUID,
    provider_name TEXT,
    platform TEXT,
    team_id UUID
) LANGUAGE sql SECURITY DEFINER AS $$
    SELECT
        dr.id,
        dr.title,
        dr.description,
        dr.status,
        dr.property_id,
        p.name AS property_name,
        dr.user_id,
        dr.created_at,
        dr.updated_at,
        dr.provider_id,
        mp.name AS provider_name,
        dr.platform,
        dr.team_id
    FROM
        damage_reports dr
    JOIN
        properties p ON dr.property_id = p.id
    LEFT JOIN
        maintenance_providers mp ON dr.provider_id = mp.id
    WHERE
        -- User's own reports
        dr.user_id = p_user_id
        -- Admin access
        OR EXISTS (SELECT 1 FROM profiles prf WHERE prf.id = p_user_id AND (prf.is_super_admin = true OR prf.role = 'admin'))
        -- Team member access via property
        OR EXISTS (
            SELECT 1 FROM team_properties tp
            JOIN team_members tm ON tp.team_id = tm.team_id
            WHERE tp.property_id = dr.property_id AND tm.user_id = p_user_id AND tm.status = 'active'
        )
        -- Team member access via team
        OR EXISTS (
            SELECT 1 FROM team_members tm
            WHERE tm.team_id = dr.team_id AND tm.user_id = p_user_id AND tm.status = 'active'
        )
        -- Service provider access
        OR (dr.provider_id = p_user_id)
    ORDER BY dr.created_at DESC;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_user_damage_reports_simple(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_damage_reports_simple(UUID) TO anon;
