-- Drop existing policies for team_members table
DROP POLICY IF EXISTS "Team members are viewable by team members" ON team_members;
DROP POLICY IF EXISTS "Team members are viewable by team owners" ON team_members;
DROP POLICY IF EXISTS "Team members are editable by team owners" ON team_members;

-- Create new policies with improved access control
CREATE POLICY "Team members are viewable by team members"
ON team_members
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM team_members AS tm
    WHERE tm.team_id = team_members.team_id
    AND tm.user_id = auth.uid()
    AND tm.status = 'active'
  )
);

CREATE POLICY "Team members are viewable by admins"
ON team_members
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  )
);

CREATE POLICY "Team members are editable by team owners"
ON team_members
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM teams
    WHERE teams.id = team_members.team_id
    AND teams.owner_id = auth.uid()
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM teams
    WHERE teams.id = team_members.team_id
    AND teams.owner_id = auth.uid()
  )
);
