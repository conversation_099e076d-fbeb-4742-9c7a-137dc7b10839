-- Create default permissions for new users
CREATE OR <PERSON><PERSON>LACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Create a profile for the new user with default role of property_manager
  INSERT INTO profiles (id, email, role, is_super_admin)
  VALUES (
    NEW.id,
    NEW.email,
    'property_manager',
    FALSE
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to handle new users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_user();
