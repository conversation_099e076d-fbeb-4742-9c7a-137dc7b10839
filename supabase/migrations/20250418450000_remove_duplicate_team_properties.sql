-- Remove duplicate entries from team_properties table
WITH duplicates AS (
  SELECT 
    team_id, 
    property_id,
    (array_agg(id ORDER BY id))[1] as keep_id,
    array_agg(id ORDER BY id) as all_ids
  FROM team_properties
  GROUP BY team_id, property_id
  HAVING COUNT(*) > 1
)
DELETE FROM team_properties
WHERE id IN (
  SELECT unnest(all_ids[2:]) -- Keep the first ID, delete the rest
  FROM duplicates
);
