-- Drop ALL policies on team_members table
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'team_members'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON team_members', policy_record.policyname);
    END LOOP;
END
$$;

-- Create a single, simple policy for team_members
CREATE POLICY "team_members_simple_policy"
ON team_members
FOR ALL
USING (true)
WITH CHECK (true);

-- Enable RLS on team_members
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;
