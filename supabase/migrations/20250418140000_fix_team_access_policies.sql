-- Fix RLS policies for team members and properties

-- First, let's create a more robust function to check if a user can access team data
CREATE OR REPLACE FUNCTION public.user_can_access_team(target_team_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Super admins and admins can access all teams
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team owners can access their teams
  IF EXISTS (
    SELECT 1 FROM teams
    WHERE id = target_team_id AND owner_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members can access their teams
  IF EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = target_team_id AND user_id = auth.uid() AND status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;

-- Update the user_can_access_team_members function to use the new function
CREATE OR REPLACE FUNCTION public.user_can_access_team_members(target_team_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN user_can_access_team(target_team_id);
END;
$$;

-- Create a function to check if a user can see team properties
CREATE OR REPLACE FUNCTION public.user_can_access_team_properties(target_team_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN user_can_access_team(target_team_id);
END;
$$;

-- Update the has_property_access function to be more robust
CREATE OR REPLACE FUNCTION public.has_property_access(p_property_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_team_ids uuid[];
BEGIN
  -- Super admins and admins have access to all properties
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Property managers have access to properties they own
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE id = p_property_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Get all teams the user is a member of
  SELECT array_agg(team_id) INTO v_team_ids
  FROM team_members
  WHERE user_id = auth.uid() AND status = 'active';
  
  -- If user is not in any teams, they don't have access
  IF v_team_ids IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Check if the property is in any of the user's teams
  IF EXISTS (
    SELECT 1 FROM team_properties
    WHERE property_id = p_property_id AND team_id = ANY(v_team_ids)
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;

-- Update RLS policy for team_properties
DROP POLICY IF EXISTS "Users can view team_properties" ON team_properties;
CREATE POLICY "Users can view team_properties"
ON team_properties
FOR SELECT
USING (user_can_access_team_properties(team_id));

-- Add a policy for team_properties to allow team members to see them
DROP POLICY IF EXISTS "Team members can view team properties" ON team_properties;
CREATE POLICY "Team members can view team properties"
ON team_properties
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_members.team_id = team_properties.team_id
    AND team_members.user_id = auth.uid()
    AND team_members.status = 'active'
  )
);

-- Fix the policy for properties to ensure team members can see them
DROP POLICY IF EXISTS "Team members can view properties in their teams" ON properties;
CREATE POLICY "Team members can view properties in their teams"
ON properties
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = properties.id
    AND tm.user_id = auth.uid()
    AND tm.status = 'active'
  )
);

-- Create a function to debug team access issues
CREATE OR REPLACE FUNCTION public.debug_team_access(p_team_id uuid, p_user_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_is_super_admin boolean;
  v_is_admin boolean;
  v_is_team_owner boolean;
  v_is_team_member boolean;
  v_user_email text;
  v_team_name text;
  v_team_owner_id uuid;
BEGIN
  -- Get user info
  SELECT 
    is_super_admin, 
    role = 'admin',
    email
  INTO 
    v_is_super_admin,
    v_is_admin,
    v_user_email
  FROM profiles
  WHERE id = p_user_id;
  
  -- Get team info
  SELECT 
    name,
    owner_id
  INTO 
    v_team_name,
    v_team_owner_id
  FROM teams
  WHERE id = p_team_id;
  
  -- Check if user is team owner
  v_is_team_owner := (v_team_owner_id = p_user_id);
  
  -- Check if user is team member
  SELECT EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = p_team_id
    AND user_id = p_user_id
    AND status = 'active'
  ) INTO v_is_team_member;
  
  -- Return debug info
  RETURN jsonb_build_object(
    'user_id', p_user_id,
    'user_email', v_user_email,
    'team_id', p_team_id,
    'team_name', v_team_name,
    'team_owner_id', v_team_owner_id,
    'is_super_admin', v_is_super_admin,
    'is_admin', v_is_admin,
    'is_team_owner', v_is_team_owner,
    'is_team_member', v_is_team_member,
    'should_have_access', (v_is_super_admin OR v_is_admin OR v_is_team_owner OR v_is_team_member)
  );
END;
$$;
