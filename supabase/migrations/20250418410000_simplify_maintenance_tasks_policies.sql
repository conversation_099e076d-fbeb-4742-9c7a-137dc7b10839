-- Drop ALL policies on maintenance_tasks table
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'maintenance_tasks'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON maintenance_tasks', policy_record.policyname);
    END LOOP;
END
$$;

-- Create a single, simple policy for maintenance_tasks
CREATE POLICY "maintenance_tasks_simple_policy"
ON maintenance_tasks
FOR ALL
USING (true)
WITH CHECK (true);

-- Enable RLS on maintenance_tasks
ALTER TABLE maintenance_tasks ENABLE ROW LEVEL SECURITY;
