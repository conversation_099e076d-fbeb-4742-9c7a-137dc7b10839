-- This migration modifies the get_providers function to return maintenance providers
-- from all teams the user is a member of, ensuring service providers are synced
-- between all members of the team.

-- Drop the existing function
DROP FUNCTION IF EXISTS public.get_providers();

-- Recreate the function with team synchronization and fixed column references
CREATE OR REPLACE FUNCTION public.get_providers()
RETURNS TABLE(
  id uuid,
  name text,
  email text,
  phone text,
  specialty text,
  notes text,
  user_id uuid,
  created_at timestamp with time zone,
  updated_at timestamp with time zone,
  provider_type text
)
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
  v_user_id UUID;
  v_is_admin BOOLEAN;
  v_team_ids UUID[];
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();

  -- Check if the user is an admin
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = v_user_id AND (profiles.is_super_admin = true OR profiles.role = 'admin')
  ) INTO v_is_admin;

  -- Get all teams the user is a member of
  SELECT array_agg(team_members.team_id) INTO v_team_ids
  FROM team_members
  WHERE team_members.user_id = v_user_id AND team_members.status = 'active';

  -- Return maintenance providers based on access level
  IF v_is_admin THEN
    -- Admins can see all maintenance providers
    RETURN QUERY
    SELECT
      mp.id,
      mp.name,
      mp.email,
      mp.phone,
      mp.specialty,
      mp.notes,
      mp.user_id,
      mp.created_at,
      mp.updated_at,
      'maintenance'::TEXT AS provider_type
    FROM maintenance_providers mp;
  ELSE
    -- Return the user's own maintenance providers
    RETURN QUERY
    SELECT
      mp.id,
      mp.name,
      mp.email,
      mp.phone,
      mp.specialty,
      mp.notes,
      mp.user_id,
      mp.created_at,
      mp.updated_at,
      'maintenance'::TEXT AS provider_type
    FROM maintenance_providers mp
    WHERE mp.user_id = v_user_id

    UNION

    -- Return maintenance providers from team members
    SELECT
      mp.id,
      mp.name,
      mp.email,
      mp.phone,
      mp.specialty,
      mp.notes,
      mp.user_id,
      mp.created_at,
      mp.updated_at,
      'maintenance'::TEXT AS provider_type
    FROM maintenance_providers mp
    JOIN team_members tm ON mp.user_id = tm.user_id
    WHERE tm.team_id = ANY(v_team_ids)
    AND tm.status = 'active'
    AND mp.user_id != v_user_id;
  END IF;

  -- Return service providers formatted to match maintenance providers
  RETURN QUERY
  SELECT
    sp.id,
    (COALESCE(sp.first_name, '') || ' ' || COALESCE(sp.last_name, ''))::TEXT AS name,
    sp.email,
    NULL::TEXT AS phone,
    NULL::TEXT AS specialty,
    NULL::TEXT AS notes,
    NULL::UUID AS user_id,
    sp.created_at,
    sp.updated_at,
    'service'::TEXT AS provider_type
  FROM service_providers sp
  WHERE v_is_admin  -- Admins see all service providers
  OR (
    -- Regular users see service providers in their teams
    EXISTS (
      SELECT 1 FROM team_members tm1
      JOIN team_members tm2 ON tm1.team_id = tm2.team_id
      JOIN profiles p ON tm2.user_id = p.id
      WHERE tm1.user_id = v_user_id
      AND tm1.status = 'active'
      AND tm2.status = 'active'
      AND p.role = 'service_provider'
      AND p.email = sp.email
    )
  );

  RETURN;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_providers() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_providers() TO anon;

-- Add a comment to the function
COMMENT ON FUNCTION public.get_providers() IS 'Gets service providers from all teams the user is a member of, ensuring service providers are synced between all members of the team';
