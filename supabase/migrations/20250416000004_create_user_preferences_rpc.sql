-- Create an RPC function to create user preferences with SECURITY DEFINER
CREATE OR REPLACE FUNCTION public.create_user_preferences(p_user_id uuid, p_onboarding_state jsonb)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
    RAISE EXCEPTION 'User with ID % does not exist', p_user_id;
  END IF;

  -- Insert or update the user preferences
  INSERT INTO public.user_preferences (user_id, onboarding_state)
  VALUES (p_user_id, p_onboarding_state)
  ON CONFLICT (user_id) 
  DO UPDATE SET 
    onboarding_state = p_onboarding_state,
    updated_at = NOW();

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Error creating user preferences: %', SQLERRM;
    RETURN FALSE;
END;
$$;

COMMENT ON FUNCTION public.create_user_preferences IS 'Creates or updates user preferences with SECURITY DEFINER to bypass <PERSON><PERSON>.';

-- Create an RPC function to update user preferences with SECURITY DEFINER
CREATE OR REPLACE FUNCTION public.update_user_preferences(p_user_id uuid, p_onboarding_state jsonb)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the user exists
  IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
    RAISE EXCEPTION 'User with ID % does not exist', p_user_id;
  END IF;

  -- Update the user preferences
  UPDATE public.user_preferences
  SET 
    onboarding_state = p_onboarding_state,
    updated_at = NOW()
  WHERE user_id = p_user_id;

  -- If no rows were updated, the preferences don't exist yet, so create them
  IF NOT FOUND THEN
    INSERT INTO public.user_preferences (user_id, onboarding_state)
    VALUES (p_user_id, p_onboarding_state);
  END IF;

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Error updating user preferences: %', SQLERRM;
    RETURN FALSE;
END;
$$;

COMMENT ON FUNCTION public.update_user_preferences IS 'Updates user preferences with SECURITY DEFINER to bypass RLS.';
