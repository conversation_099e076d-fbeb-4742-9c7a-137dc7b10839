-- Drop existing policies for team_properties table
DROP POLICY IF EXISTS "Team properties are viewable by team members" ON team_properties;
DROP POLICY IF EXISTS "Team properties are viewable by property owners" ON team_properties;
DROP POLICY IF EXISTS "Team properties are viewable by team owners" ON team_properties;
DROP POLICY IF EXISTS "Team properties are editable by team owners" ON team_properties;

-- Create new policies with improved access control
CREATE POLICY "Team properties are viewable by team members"
ON team_properties
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_members.team_id = team_properties.team_id
    AND team_members.user_id = auth.uid()
    AND team_members.status = 'active'
  )
);

CREATE POLICY "Team properties are viewable by property owners"
ON team_properties
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM properties
    WHERE properties.id = team_properties.property_id
    AND properties.user_id = auth.uid()
  )
);

CREATE POLICY "Team properties are viewable by admins"
ON team_properties
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  )
);

CREATE POLICY "Team properties are editable by team owners"
ON team_properties
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM teams
    WHERE teams.id = team_properties.team_id
    AND teams.owner_id = auth.uid()
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM teams
    WHERE teams.id = team_properties.team_id
    AND teams.owner_id = auth.uid()
  )
);

-- Create a function to check if a property belongs to a team
CREATE OR REPLACE FUNCTION public.property_belongs_to_team(
  p_property_id UUID,
  p_team_id UUID
) RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM team_properties
    WHERE property_id = p_property_id
    AND team_id = p_team_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
