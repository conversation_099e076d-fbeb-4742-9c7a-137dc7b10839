-- Create team_properties table for many-to-many relationship between teams and properties
CREATE TABLE IF NOT EXISTS team_properties (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
  property_id UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(team_id, property_id)
);

-- Enable Row Level Security
ALTER TABLE team_properties ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for team_properties table

-- Super Ad<PERSON> can do anything with team_properties
CREATE POLICY "Super Admins can do anything with team_properties"
ON team_properties
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND is_super_admin = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND is_super_admin = true
  )
);

-- <PERSON><PERSON> can do anything with team_properties
CREATE POLICY "<PERSON><PERSON> can do anything with team_properties"
ON team_properties
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin'
  )
);

-- Property Managers can manage team_properties for teams they own
CREATE POLICY "Property Managers can manage team_properties for their teams"
ON team_properties
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'property_manager'
  ) AND
  EXISTS (
    SELECT 1 FROM teams
    WHERE id = team_properties.team_id AND owner_id = auth.uid()
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'property_manager'
  ) AND
  EXISTS (
    SELECT 1 FROM teams
    WHERE id = team_properties.team_id AND owner_id = auth.uid()
  ) AND
  EXISTS (
    SELECT 1 FROM properties
    WHERE id = team_properties.property_id AND user_id = auth.uid()
  )
);

-- Team members can view team_properties for teams they belong to
CREATE POLICY "Team members can view team_properties for their teams"
ON team_properties
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = team_properties.team_id AND user_id = auth.uid() AND status = 'active'
  )
);

-- Create function to check if a property belongs to a team
CREATE OR REPLACE FUNCTION is_property_in_team(property_id UUID, team_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM team_properties
    WHERE property_id = property_id AND team_id = team_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if a user has access to a property
CREATE OR REPLACE FUNCTION has_property_access(property_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  -- Super admins and admins have access to all properties
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Property managers have access to properties they own
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE id = property_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to properties in teams they belong to
  IF EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update RLS policies for properties table to use the new function

-- Team members can view properties in their teams
CREATE POLICY "Team members can view properties in their teams"
ON properties
FOR SELECT
USING (
  has_property_access(id)
);
