-- Drop ALL policies on properties table
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'properties'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON properties', policy_record.policyname);
    END LOOP;
END
$$;

-- Create a single, simple policy for properties
CREATE POLICY "properties_simple_policy"
ON properties
FOR ALL
USING (true)
WITH CHECK (true);

-- Enable RLS on properties
ALTER TABLE properties ENABLE ROW LEVEL SECURITY;
