-- StayFu Remote Schema Migration
-- This migration creates the complete schema from the remote Supabase database

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create profiles table (core user profiles)
CREATE TABLE IF NOT EXISTS profiles (
    id uuid PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email text,
    full_name text,
    avatar_url text,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create teams table
CREATE TABLE IF NOT EXISTS teams (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    description text,
    owner_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create team_members table
CREATE TABLE IF NOT EXISTS team_members (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    team_id uuid REFERENCES teams(id) ON DELETE CASCADE,
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    role text DEFAULT 'member',
    permissions jsonb DEFAULT '{}',
    created_at timestamptz DEFAULT now(),
    UNIQUE(team_id, user_id)
);

-- Create properties table
CREATE TABLE IF NOT EXISTS properties (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    name text NOT NULL,
    address text,
    city text,
    state text,
    zip text,
    bedrooms integer,
    bathrooms integer,
    property_type text,
    status text DEFAULT 'active',
    timezone text DEFAULT 'UTC',
    check_in_time time DEFAULT '15:00:00',
    check_out_time time DEFAULT '11:00:00',
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create team_properties table
CREATE TABLE IF NOT EXISTS team_properties (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    team_id uuid REFERENCES teams(id) ON DELETE CASCADE,
    property_id uuid REFERENCES properties(id) ON DELETE CASCADE,
    created_at timestamptz DEFAULT now(),
    UNIQUE(team_id, property_id)
);

-- Create bookings table
CREATE TABLE IF NOT EXISTS bookings (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id uuid REFERENCES properties(id) ON DELETE CASCADE,
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    check_in_date date NOT NULL,
    check_out_date date NOT NULL,
    guest_name text,
    guest_email text,
    guest_phone text,
    status text DEFAULT 'confirmed',
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create maintenance_requests table
CREATE TABLE IF NOT EXISTS maintenance_requests (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id uuid REFERENCES properties(id) ON DELETE CASCADE,
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    title text NOT NULL,
    description text,
    severity text DEFAULT 'medium',
    status text DEFAULT 'open',
    assigned_to uuid REFERENCES profiles(id),
    due_date date,
    completed_at timestamptz,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create maintenance_tasks table
CREATE TABLE IF NOT EXISTS maintenance_tasks (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    maintenance_request_id uuid REFERENCES maintenance_requests(id) ON DELETE CASCADE,
    title text NOT NULL,
    description text,
    status text DEFAULT 'pending',
    assigned_to uuid REFERENCES profiles(id),
    completed_at timestamptz,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create inventory_items table
CREATE TABLE IF NOT EXISTS inventory_items (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id uuid REFERENCES properties(id) ON DELETE CASCADE,
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    name text NOT NULL,
    description text,
    category text,
    quantity integer DEFAULT 0,
    min_quantity integer DEFAULT 0,
    unit text,
    cost_per_unit numeric(10,2),
    supplier text,
    location text,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create purchase_orders table
CREATE TABLE IF NOT EXISTS purchase_orders (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    property_id uuid REFERENCES properties(id) ON DELETE CASCADE,
    po_number text,
    supplier text,
    status text DEFAULT 'draft',
    total_amount numeric(10,2) DEFAULT 0,
    order_date date DEFAULT CURRENT_DATE,
    expected_delivery date,
    notes text,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create purchase_order_items table
CREATE TABLE IF NOT EXISTS purchase_order_items (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    purchase_order_id uuid REFERENCES purchase_orders(id) ON DELETE CASCADE,
    inventory_item_id uuid REFERENCES inventory_items(id) ON DELETE CASCADE,
    quantity integer NOT NULL,
    unit_cost numeric(10,2),
    total_cost numeric(10,2),
    created_at timestamptz DEFAULT now()
);

-- Create damage_reports table
CREATE TABLE IF NOT EXISTS damage_reports (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id uuid REFERENCES properties(id) ON DELETE CASCADE,
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    title text NOT NULL,
    description text,
    severity text DEFAULT 'medium',
    status text DEFAULT 'reported',
    location text,
    estimated_cost numeric(10,2),
    actual_cost numeric(10,2),
    reported_date date DEFAULT CURRENT_DATE,
    resolved_date date,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create damage_photos table
CREATE TABLE IF NOT EXISTS damage_photos (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    damage_report_id uuid REFERENCES damage_reports(id) ON DELETE CASCADE,
    file_path text NOT NULL,
    file_name text,
    file_size integer,
    uploaded_by uuid REFERENCES profiles(id),
    created_at timestamptz DEFAULT now()
);

-- Create damage_notes table
CREATE TABLE IF NOT EXISTS damage_notes (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    damage_report_id uuid REFERENCES damage_reports(id) ON DELETE CASCADE,
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    note text NOT NULL,
    created_at timestamptz DEFAULT now()
);

-- Create service_providers table
CREATE TABLE IF NOT EXISTS service_providers (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    name text NOT NULL,
    email text,
    phone text,
    address text,
    city text,
    state text,
    zip text,
    services jsonb DEFAULT '[]',
    rating numeric(3,2),
    notes text,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create maintenance_providers table
CREATE TABLE IF NOT EXISTS maintenance_providers (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    maintenance_request_id uuid REFERENCES maintenance_requests(id) ON DELETE CASCADE,
    service_provider_id uuid REFERENCES service_providers(id) ON DELETE CASCADE,
    status text DEFAULT 'assigned',
    assigned_date date DEFAULT CURRENT_DATE,
    completed_date date,
    cost numeric(10,2),
    notes text,
    created_at timestamptz DEFAULT now()
);

-- Create damage_invoices table
CREATE TABLE IF NOT EXISTS damage_invoices (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    damage_report_id uuid REFERENCES damage_reports(id) ON DELETE CASCADE,
    provider_id uuid REFERENCES service_providers(id),
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    invoice_number text,
    total_amount numeric(10,2),
    status text DEFAULT 'pending',
    issue_date date DEFAULT CURRENT_DATE,
    due_date date,
    notes text,
    created_at timestamptz DEFAULT now()
);

-- Create invoice_items table
CREATE TABLE IF NOT EXISTS invoice_items (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    damage_invoice_id uuid REFERENCES damage_invoices(id) ON DELETE CASCADE,
    description text NOT NULL,
    quantity integer DEFAULT 1,
    unit_price numeric(10,2),
    total_price numeric(10,2),
    created_at timestamptz DEFAULT now()
);

-- Create property_documents table
CREATE TABLE IF NOT EXISTS property_documents (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id uuid REFERENCES properties(id) ON DELETE CASCADE,
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    title text NOT NULL,
    content text,
    document_type text DEFAULT 'note',
    visibility text DEFAULT 'private',
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create property_files table
CREATE TABLE IF NOT EXISTS property_files (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    property_id uuid REFERENCES properties(id) ON DELETE CASCADE,
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    file_name text NOT NULL,
    file_path text NOT NULL,
    file_size integer,
    file_type text,
    title text,
    description text,
    visibility text DEFAULT 'private',
    created_at timestamptz DEFAULT now()
);

-- Create automation_rules table
CREATE TABLE IF NOT EXISTS automation_rules (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    name text NOT NULL,
    trigger_type text NOT NULL,
    task_type text NOT NULL,
    time_offset integer DEFAULT 0,
    property_ids jsonb DEFAULT '[]',
    title text,
    description text,
    severity text DEFAULT 'medium',
    assigned_to uuid REFERENCES profiles(id),
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create automation_queue table
CREATE TABLE IF NOT EXISTS automation_queue (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    booking_id uuid REFERENCES bookings(id) ON DELETE CASCADE,
    processed boolean DEFAULT false,
    processed_at timestamptz,
    created_at timestamptz DEFAULT now()
);

-- Create user_permissions table
CREATE TABLE IF NOT EXISTS user_permissions (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    team_id uuid REFERENCES teams(id) ON DELETE CASCADE,
    permissions jsonb DEFAULT '{}',
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    UNIQUE(user_id, team_id)
);

-- Create user_preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    preferences jsonb DEFAULT '{}',
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    UNIQUE(user_id)
);

-- Create user_settings table
CREATE TABLE IF NOT EXISTS user_settings (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    settings jsonb DEFAULT '{}',
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    UNIQUE(user_id)
);

-- Create collections table
CREATE TABLE IF NOT EXISTS collections (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    name text NOT NULL,
    description text,
    created_at timestamptz DEFAULT now()
);

-- Create invitations table
CREATE TABLE IF NOT EXISTS invitations (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    email text NOT NULL,
    role text DEFAULT 'member',
    invited_by uuid REFERENCES profiles(id) ON DELETE CASCADE,
    team_id uuid REFERENCES teams(id) ON DELETE CASCADE,
    token text UNIQUE,
    expires_at timestamptz,
    accepted_at timestamptz,
    created_at timestamptz DEFAULT now()
);

-- Create team_invitations table
CREATE TABLE IF NOT EXISTS team_invitations (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    team_id uuid REFERENCES teams(id) ON DELETE CASCADE,
    email text NOT NULL,
    role text DEFAULT 'member',
    invited_by uuid REFERENCES profiles(id) ON DELETE CASCADE,
    token text UNIQUE,
    expires_at timestamptz,
    accepted_at timestamptz,
    created_at timestamptz DEFAULT now()
);

-- Create extension_api_tokens table
CREATE TABLE IF NOT EXISTS extension_api_tokens (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES profiles(id) ON DELETE CASCADE,
    token text UNIQUE NOT NULL,
    name text,
    expires_at timestamptz,
    last_used_at timestamptz,
    created_at timestamptz DEFAULT now()
);

-- Create backups table
CREATE TABLE IF NOT EXISTS backups (
    id bigserial PRIMARY KEY,
    status text DEFAULT 'pending',
    includes_database boolean DEFAULT true,
    includes_storage boolean DEFAULT true,
    includes_auth boolean DEFAULT true,
    includes_edge_functions boolean DEFAULT true,
    schema jsonb,
    backup_data jsonb,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Enable Row Level Security on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchase_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchase_order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE damage_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE damage_photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE damage_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_providers ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance_providers ENABLE ROW LEVEL SECURITY;
ALTER TABLE damage_invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoice_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE property_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE property_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE automation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE automation_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE extension_api_tokens ENABLE ROW LEVEL SECURITY;
