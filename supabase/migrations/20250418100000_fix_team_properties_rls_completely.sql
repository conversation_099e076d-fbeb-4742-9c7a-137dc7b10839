-- This migration completely fixes the infinite recursion issue in team_properties RLS policies
-- by rewriting the policies to avoid any recursive calls

-- First, drop all existing policies on team_properties
DROP POLICY IF EXISTS "Super Admins can do anything with team_properties" ON team_properties;
DROP POLICY IF EXISTS "Ad<PERSON> can do anything with team_properties" ON team_properties;
DROP POLICY IF EXISTS "Team members can view team_properties for their teams" ON team_properties;
DROP POLICY IF EXISTS "Property Managers can manage team_properties for their teams" ON team_properties;

-- Create new policies without recursion
-- 1. Super Admins policy
CREATE POLICY "Super Admins can do anything with team_properties"
ON team_properties
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.is_super_admin = true
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.is_super_admin = true
  )
);

-- 2. Admins policy
CREATE POLICY "Ad<PERSON> can do anything with team_properties"
ON team_properties
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
  )
);

-- 3. Team members view policy
CREATE POLICY "Team members can view team_properties for their teams"
ON team_properties
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_members.team_id = team_properties.team_id
    AND team_members.user_id = auth.uid()
    AND team_members.status = 'active'
  )
);

-- 4. Property Managers policy - completely rewritten to avoid recursion
CREATE POLICY "Property Managers can manage team_properties for their teams"
ON team_properties
USING (
  -- Check if user is a property manager
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.role = 'property_manager'
  )
  AND
  -- Check if user owns the team
  EXISTS (
    SELECT 1 FROM teams
    WHERE teams.id = team_properties.team_id AND teams.owner_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if user is a property manager
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.role = 'property_manager'
  )
  AND
  -- Check if user owns the team
  EXISTS (
    SELECT 1 FROM teams
    WHERE teams.id = team_properties.team_id AND teams.owner_id = auth.uid()
  )
  AND
  -- Check if user owns the property
  EXISTS (
    SELECT 1 FROM properties
    WHERE properties.id = team_properties.property_id AND properties.user_id = auth.uid()
  )
);

-- Create a direct function to check if a property is in a team without using RLS
-- This function is used by other functions and doesn't rely on RLS policies
CREATE OR REPLACE FUNCTION public.check_property_in_team(p_property_id uuid, p_team_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM team_properties
    WHERE property_id = p_property_id AND team_id = p_team_id
  );
$$;

-- Create a function to safely add a property to a team
-- This function bypasses RLS and handles the operation directly
CREATE OR REPLACE FUNCTION public.add_property_to_team(
  p_team_id uuid,
  p_property_id uuid,
  p_user_id uuid
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_is_owner boolean;
  v_owns_property boolean;
  v_already_exists boolean;
BEGIN
  -- Check if user owns the team
  SELECT EXISTS (
    SELECT 1 FROM teams
    WHERE id = p_team_id AND owner_id = p_user_id
  ) INTO v_is_owner;
  
  IF NOT v_is_owner THEN
    RAISE EXCEPTION 'User does not own this team';
  END IF;
  
  -- Check if user owns the property
  SELECT EXISTS (
    SELECT 1 FROM properties
    WHERE id = p_property_id AND user_id = p_user_id
  ) INTO v_owns_property;
  
  IF NOT v_owns_property THEN
    RAISE EXCEPTION 'User does not own this property';
  END IF;
  
  -- Check if property is already in team
  SELECT check_property_in_team(p_property_id, p_team_id) INTO v_already_exists;
  
  IF v_already_exists THEN
    RETURN true; -- Already exists, consider it a success
  END IF;
  
  -- Add property to team
  INSERT INTO team_properties (team_id, property_id)
  VALUES (p_team_id, p_property_id);
  
  -- Update inventory items for this property to include the team_id
  UPDATE inventory_items
  SET team_id = p_team_id
  WHERE property_id = p_property_id
  AND team_id IS NULL;
  
  RETURN true;
EXCEPTION
  WHEN others THEN
    RAISE;
END;
$$;
