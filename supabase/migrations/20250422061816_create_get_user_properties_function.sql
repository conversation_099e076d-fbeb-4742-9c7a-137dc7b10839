-- Create a function to get all properties for a user

-- Drop the existing function
DROP FUNCTION IF EXISTS get_user_properties(UUID);

-- Create the function
CREATE FUNCTION get_user_properties(p_user_id UUID)
RETURNS TABLE (
    id UUID,
    name TEXT,
    address TEXT,
    city TEXT,
    state TEXT,
    user_id UUID,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) LANGUAGE sql SECURITY DEFINER AS $$
    SELECT DISTINCT ON (p.id)
        p.id,
        p.name,
        p.address,
        p.city,
        p.state,
        p.user_id,
        p.created_at,
        p.updated_at
    FROM
        properties p
    WHERE
        -- User's own properties
        p.user_id = p_user_id
        -- Admin access
        OR EXISTS (SELECT 1 FROM profiles prf WHERE prf.id = p_user_id AND (prf.is_super_admin = true OR prf.role = 'admin'))
        -- Team member access
        OR EXISTS (
            SELECT 1 FROM team_properties tp
            JOIN team_members tm ON tp.team_id = tm.team_id
            WHERE tp.property_id = p.id AND tm.user_id = p_user_id AND tm.status = 'active'
        )
    ORDER BY
        p.id, p.name;
$$;

-- <PERSON> execute permissions
GRANT EXECUTE ON FUNCTION get_user_properties(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_properties(UUID) TO anon;
