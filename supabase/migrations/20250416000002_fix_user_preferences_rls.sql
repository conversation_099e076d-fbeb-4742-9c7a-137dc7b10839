-- Create the has_user_access function first

-- Add RLS policy for user_preferences table to allow impersonated access
CREATE OR REPLACE FUNCTION public.has_user_access(p_user_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Super admins and admins have access to all users
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Users have access to their own data
  IF p_user_id = auth.uid() THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;

COMMENT ON FUNCTION public.has_user_access IS 'Determines if the current user has access to another user''s data. Super admins and admins have access to all users.';

-- Now create the policy using the function
CREATE POLICY "Allow impersonated access to user_preferences" ON public.user_preferences
    FOR ALL
    TO public
    USING (has_user_access(user_id));
