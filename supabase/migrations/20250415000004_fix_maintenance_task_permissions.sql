-- Add RLS policy for team members to create maintenance tasks for their properties
CREATE POLICY "Team members can create maintenance tasks for their properties"
ON maintenance_tasks
FOR INSERT
TO public
WITH CHECK (
  property_id IN (
    SELECT tp.property_id
    FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tm.user_id = auth.uid() AND tm.status = 'active'
  )
);

-- Update the has_maintenance_task_access function to also check if the user is assigned to the task
CREATE OR REPLACE FUNCTION public.has_maintenance_task_access(p_task_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  v_property_id uuid;
  v_provider_id uuid;
  v_provider_email text;
BEGIN
  -- Get the property_id and provider info for this task
  SELECT property_id, provider_id, provider_email INTO v_property_id, v_provider_id, v_provider_email
  FROM maintenance_tasks
  WHERE id = p_task_id;

  -- Super admins and admins have access to all tasks
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Task creator has access
  IF EXISTS (
    SELECT 1 FROM maintenance_tasks
    WHERE id = p_task_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Provider assigned to the task has access
  IF v_provider_id = auth.uid() THEN
    RETURN TRUE;
  END IF;

  -- Provider with matching email has access
  IF v_provider_email IS NOT NULL AND EXISTS (
    SELECT 1 FROM service_providers
    WHERE id = auth.uid() AND email = v_provider_email
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to tasks for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$function$;

-- Update existing maintenance tasks with team_id based on their property's team
UPDATE maintenance_tasks mt
SET team_id = tp.team_id
FROM team_properties tp
WHERE mt.property_id = tp.property_id
AND (mt.team_id IS NULL OR mt.team_id != tp.team_id);

-- Add a permission type for creating maintenance tasks if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_type
        WHERE typname = 'permission_type'
        AND 'create_maintenance_task' = ANY(enum_range(NULL::permission_type)::text[])
    ) THEN
        ALTER TYPE permission_type ADD VALUE IF NOT EXISTS 'create_maintenance_task';
    END IF;
EXCEPTION
    WHEN duplicate_object THEN
        -- Value already exists, do nothing
        NULL;
END$$;

-- Create a function to check if a user has permission to create maintenance tasks
CREATE OR REPLACE FUNCTION public.can_create_maintenance_task(p_property_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  v_team_id uuid;
BEGIN
  -- Super admins and admins can create tasks for any property
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Property owners can create tasks for their properties
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE id = p_property_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Get the team_id for this property
  SELECT team_id INTO v_team_id
  FROM team_properties
  WHERE property_id = p_property_id
  LIMIT 1;

  -- If property is not in any team, only the owner can create tasks
  IF v_team_id IS NULL THEN
    RETURN FALSE;
  END IF;

  -- Check if the user has permission to create maintenance tasks for this team
  RETURN EXISTS (
    SELECT 1 FROM user_permissions up
    JOIN team_members tm ON up.team_id = tm.team_id AND up.user_id = tm.user_id
    WHERE tm.team_id = v_team_id
    AND tm.user_id = auth.uid()
    AND tm.status = 'active'
    AND (
      up.permission = 'create_maintenance_task'::permission_type OR
      up.permission = 'manage_maintenance'::permission_type
    )
    AND up.enabled = true
  );
END;
$function$;
