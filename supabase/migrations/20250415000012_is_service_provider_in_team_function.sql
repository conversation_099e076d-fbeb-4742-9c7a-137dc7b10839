-- Create a function to check if a service provider is in a property manager's team
CREATE OR REPLACE FUNCTION public.is_service_provider_in_team(
  p_property_manager_id uuid,
  p_service_provider_id uuid
) RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM team_members tm1
    JOIN team_members tm2 ON tm1.team_id = tm2.team_id
    JOIN profiles sp ON tm2.user_id = sp.id
    WHERE tm1.user_id = p_property_manager_id
      AND tm1.status = 'active'
      AND tm2.user_id = p_service_provider_id
      AND tm2.status = 'active'
      AND sp.role = 'service_provider'
  );
END;
$$;
