-- Update RLS policies for damage_reports to include team-based access
CREATE OR REPLACE FUNCTION public.has_damage_report_access(p_report_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  v_property_id uuid;
BEGIN
  -- Get the property_id for this report
  SELECT property_id INTO v_property_id
  FROM damage_reports
  WHERE id = p_report_id;

  -- Super admins and admins have access to all reports
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Report creator has access
  IF EXISTS (
    SELECT 1 FROM damage_reports
    WHERE id = p_report_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to reports for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$function$;

-- Update RLS policies for maintenance_tasks to include team-based access
CREATE OR REPLACE FUNCTION public.has_maintenance_task_access(p_task_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  v_property_id uuid;
BEGIN
  -- Get the property_id for this task
  SELECT property_id INTO v_property_id
  FROM maintenance_tasks
  WHERE id = p_task_id;

  -- Super admins and admins have access to all tasks
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Task creator has access
  IF EXISTS (
    SELECT 1 FROM maintenance_tasks
    WHERE id = p_task_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to tasks for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$function$;

-- Update RLS policies for purchase_orders to include team-based access
CREATE OR REPLACE FUNCTION public.has_purchase_order_access(p_order_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  v_property_id uuid;
BEGIN
  -- Get the property_id for this order
  SELECT property_id INTO v_property_id
  FROM purchase_orders
  WHERE id = p_order_id;

  -- Super admins and admins have access to all orders
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Order creator has access
  IF EXISTS (
    SELECT 1 FROM purchase_orders
    WHERE id = p_order_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to orders for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$function$;

-- Update RLS policies for damage_photos to include team-based access
CREATE OR REPLACE FUNCTION public.has_damage_photo_access(p_photo_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  v_report_id uuid;
  v_property_id uuid;
BEGIN
  -- Get the report_id for this photo
  SELECT damage_report_id INTO v_report_id
  FROM damage_photos
  WHERE id = p_photo_id;

  -- Get the property_id for this report
  SELECT property_id INTO v_property_id
  FROM damage_reports
  WHERE id = v_report_id;

  -- Super admins and admins have access to all photos
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Photo creator has access
  IF EXISTS (
    SELECT 1 FROM damage_photos
    WHERE id = p_photo_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to photos for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$function$;

-- Update RLS policies for damage_notes to include team-based access
CREATE OR REPLACE FUNCTION public.has_damage_note_access(p_note_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  v_report_id uuid;
  v_property_id uuid;
BEGIN
  -- Get the report_id for this note
  SELECT damage_report_id INTO v_report_id
  FROM damage_notes
  WHERE id = p_note_id;

  -- Get the property_id for this report
  SELECT property_id INTO v_property_id
  FROM damage_reports
  WHERE id = v_report_id;

  -- Super admins and admins have access to all notes
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Note creator has access
  IF EXISTS (
    SELECT 1 FROM damage_notes
    WHERE id = p_note_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to notes for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$function$;
