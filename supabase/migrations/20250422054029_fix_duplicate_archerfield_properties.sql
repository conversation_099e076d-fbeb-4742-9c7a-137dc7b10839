-- Fix duplicate Archerfield properties

-- Rename one of the Archerfield properties to make it unique
UPDATE properties
SET name = 'Archerfield (<PERSON>)'
WHERE id = 'e4627eba-82be-49b0-bd74-e43dd270c402';

UPDATE properties
SET name = 'Archer<PERSON> (<PERSON>)'
WHERE id = '622e7939-1dbe-4dee-8d0e-3c7b2e071dfd';

-- Create a trigger to prevent duplicate property names for the same team
CREATE OR REPLACE FUNCTION prevent_duplicate_property_names_in_team()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if there's already a property with the same name in any of the teams this property belongs to
    IF EXISTS (
        SELECT 1
        FROM team_properties tp1
        JOIN properties p ON tp1.property_id = p.id
        JOIN team_properties tp2 ON tp1.team_id = tp2.team_id
        WHERE
            p.name = NEW.name
            AND p.id != NEW.id
            AND tp2.property_id = NEW.id
    ) THEN
        RAISE EXCEPTION 'A property with the same name already exists in one of the teams this property belongs to';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS prevent_duplicate_property_names_in_team_trigger ON properties;
CREATE TRIGGER prevent_duplicate_property_names_in_team_trigger
BEFORE INSERT OR UPDATE OF name ON properties
FOR EACH ROW
EXECUTE FUNCTION prevent_duplicate_property_names_in_team();
