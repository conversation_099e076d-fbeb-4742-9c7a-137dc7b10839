-- Drop ALL policies on team_properties table
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'team_properties'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON team_properties', policy_record.policyname);
    END LOOP;
END
$$;

-- Create a single, simple policy for team_properties
CREATE POLICY "team_properties_simple_policy"
ON team_properties
FOR ALL
USING (true)
WITH CHECK (true);

-- Enable RLS on team_properties
ALTER TABLE team_properties ENABLE ROW LEVEL SECURITY;
