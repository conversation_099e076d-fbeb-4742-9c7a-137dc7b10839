-- Fix the infinite recursion in is_property_in_team function
-- The issue is that the parameter names collide with the column names in the query

-- Drop the existing function
DROP FUNCTION IF EXISTS is_property_in_team;

-- Recreate the function with fixed parameter names
CREATE OR REPLACE FUNCTION is_property_in_team(p_property_id UUID, p_team_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM team_properties
    WHERE property_id = p_property_id AND team_id = p_team_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix the RLS policy for team_properties to avoid recursion
-- First, drop the existing policy if it exists
DROP POLICY IF EXISTS "Property Managers can manage team_properties for their teams" ON team_properties;

-- Recreate the policy with a safer implementation
CREATE POLICY "Property Managers can manage team_properties for their teams"
ON team_properties
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'property_manager'
  ) AND
  EXISTS (
    SELECT 1 FROM teams
    WHERE id = team_properties.team_id AND owner_id = auth.uid()
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'property_manager'
  ) AND
  EXISTS (
    SELECT 1 FROM teams
    WHERE id = team_properties.team_id AND owner_id = auth.uid()
  ) AND
  EXISTS (
    SELECT 1 FROM properties
    WHERE id = team_properties.property_id AND user_id = auth.uid()
  )
);
