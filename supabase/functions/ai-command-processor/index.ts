
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { GoogleGenerativeAI } from "https://esm.sh/@google/generative-ai@0.2.1";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

const GEMINI_API_KEY = Deno.env.get("GEMINI_API_KEY") || "AIzaSyD55Kn_94EdiW7czvu8qZ4G6R76vRL563s";
const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_ANON_KEY = Deno.env.get("SUPABASE_ANON_KEY") || "";
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

interface CommandRequest {
  command: string;
  userId: string;
}

interface CommandResponse {
  success: boolean;
  message: string;
  action?: string;
  entityType?: string;
  entityId?: string;
  errors?: string[];
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (!GEMINI_API_KEY) {
      throw new Error("GEMINI_API_KEY is not set");
    }

    if (!SUPABASE_URL || !SUPABASE_ANON_KEY || !SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error("Supabase environment variables are not set");
    }

    const requestBody = await req.json();
    console.log("Request body:", requestBody);

    const { command, userId } = requestBody as CommandRequest;

    if (!command || command.trim() === "") {
      throw new Error("Command is required");
    }

    if (!userId) {
      throw new Error("User ID is required");
    }

    console.log("Processing command:", command);
    console.log("User ID:", userId);

    // Initialize Supabase client with service role key for admin access
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

    // Initialize the Gemini API
    const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({
      model: "gemini-1.5-flash",
      generationConfig: {
        temperature: 0.2,
        topP: 0.95,
        topK: 40,
        maxOutputTokens: 8192,
      },
    });

    // Get user's data for context
    const { data: userProperties, error: propertiesError } = await supabase
      .from("properties")
      .select("id, name, address, city, state, zip, bedrooms, bathrooms")
      .eq("user_id", userId);

    if (propertiesError) {
      console.error("Error fetching properties:", propertiesError);
    }

    const { data: collections, error: collectionsError } = await supabase
      .from("collections")
      .select("id, name")
      .eq("user_id", userId);

    if (collectionsError) {
      console.error("Error fetching collections:", collectionsError);
    }

    // Get inventory items for context
    const { data: inventoryItems, error: inventoryError } = await supabase
      .from("inventory_items")
      .select(`
        id, name, quantity, min_quantity, collection,
        properties:property_id (id, name)
      `)
      .eq("user_id", userId);

    if (inventoryError) {
      console.error("Error fetching inventory items:", inventoryError);
    }

    // Format inventory items for better context
    const formattedInventoryItems = inventoryItems?.map(item => ({
      id: item.id,
      name: item.name,
      quantity: item.quantity,
      minQuantity: item.min_quantity,
      collection: item.collection,
      propertyId: item.properties?.id,
      propertyName: item.properties?.name,
      isLowStock: item.quantity <= item.min_quantity
    }));

    // Prepare the prompt with the user's context
    const prompt = `
    You are a property management assistant that helps users manage their vacation rental properties.

    Your job is to understand the user's command and convert it into a specific action in our system.

    User data context:
    - Properties: ${JSON.stringify(userProperties || [])}
    - Collections: ${JSON.stringify(collections || [])}
    - Inventory Items: ${JSON.stringify(formattedInventoryItems || [])}

    The system supports the following actions:
    1. Add a property (needs name, address, city, state, zip, and optionally bedrooms and bathrooms)
    2. Create a collection (needs name and optionally budget)
    3. Add a maintenance task (needs title, property, and optionally severity, description, due date)
    4. Add inventory items (needs name, quantity, property, and optionally collection, min_quantity, price, image_url) - ONLY use this if the item doesn't already exist
    5. Update inventory item (needs item name or id, and the new quantity and/or min_quantity) - ALWAYS use this if a similar item already exists
    6. Create purchase order (needs items to include, can be "all low stock items" or specific items by name)

    IMPORTANT RULES FOR INVENTORY MANAGEMENT:
    1. When the user mentions an inventory item, ALWAYS check if a similar item already exists in their inventory. If it does, use updateInventoryItem instead of addInventoryItem.
    2. For similarity matching, consider singular/plural forms (e.g., "towel" and "towels" are the same), common variations (e.g., "bath towel" and "bathroom towel"), and ignore case.
    3. If the user says things like "We need more X", "We're down to only Y X", "Add Z more X", "Increase X by Z", or "We're running low on X", this ALWAYS means to update an existing item, not create a new one.
    4. If the user mentions a specific property, make sure to match the inventory item for that property.
    5. If multiple similar items exist, prefer exact matches over partial matches, and prefer matching by property if specified.
    6. For quantity updates, if the user says "add 5 more towels", this means to INCREASE the current quantity by 5, not set it to 5.
    7. If the user doesn't specify a quantity for an update, assume they want to increase by a reasonable amount (e.g., 5 for consumables, 1-2 for durable goods).
    8. If the user mentions a minimum quantity (e.g., "we need at least 10 towels"), set both the current quantity and min_quantity accordingly.

    EXAMPLES OF INVENTORY UPDATES (not creations):
    - "We need more towels at Beach House" → updateInventoryItem with property="Beach House", name="towels", increase quantity
    - "Add 3 more wine glasses" → updateInventoryItem with name="wine glasses", increase quantity by 3
    - "We're down to only 2 toilet paper rolls" → updateInventoryItem with name="toilet paper", set appropriate quantity
    - "Increase the minimum stock of paper towels to 10" → updateInventoryItem with name="paper towels", set min_quantity=10
    - "We're running low on dish soap" → updateInventoryItem with name="dish soap", increase quantity

    EXAMPLES OF PURCHASE ORDER CREATION:
    - "Create a purchase order for all low stock items" → createPurchaseOrder with allLowStock=true
    - "Create a PO for all items that need restocking" → createPurchaseOrder with allLowStock=true
    - "Make a purchase order for towels and toilet paper" → createPurchaseOrder with items=["towels", "toilet paper"]
    - "Order more cleaning supplies" → createPurchaseOrder with items matching cleaning supplies
    - "Create purchase order for low stock items at Beach House" → createPurchaseOrder with allLowStock=true, property="Beach House"
    - "Order supplies for Ocean View property" → createPurchaseOrder with allLowStock=true, property="Ocean View"
    - "Create PO for bathroom supplies" → createPurchaseOrder with items matching bathroom supplies
    - "Order everything we need for the downtown apartment" → createPurchaseOrder with allLowStock=true, property="downtown apartment"

    Based on the user's command, determine what action they want to take and extract the necessary information.

    Respond with a JSON object that contains:
    - action: The action to take (addProperty, createCollection, addMaintenanceTask, addInventoryItem, updateInventoryItem, createPurchaseOrder)
    - data: An object containing the extracted information needed for the action
    - message: A user-friendly message describing what will be done

    If you cannot understand the command or it doesn't match any of the supported actions, respond with:
    {
      "action": "unknown",
      "message": "I'm sorry, I couldn't understand your request. Please try again with a more specific command."
    }

    The command from the user is: "${command}"

    Only return the JSON object, nothing else.
    `;

    // Call the Gemini API
    console.log("Sending prompt to Gemini API");
    const result = await model.generateContent(prompt);
    const response = result.response;
    const textResponse = response.text();

    console.log("Gemini API response:", textResponse);

    // Parse the AI response
    try {
      // Extract JSON if it's within markdown code blocks
      const jsonMatch = textResponse.match(/```(?:json)?\s*([\s\S]*?)\s*```/) ||
                         textResponse.match(/\{[\s\S]*\}/);
      const jsonText = jsonMatch ? jsonMatch[1] || jsonMatch[0] : textResponse;
      const parsedResponse = JSON.parse(jsonText.trim());

      console.log("Parsed response:", parsedResponse);

      // Process the AI's suggested action
      let processingResult: CommandResponse;

      switch (parsedResponse.action) {
        case "addProperty":
          processingResult = await handleAddProperty(supabase, userId, parsedResponse.data);
          break;
        case "createCollection":
          processingResult = await handleCreateCollection(supabase, userId, parsedResponse.data);
          break;
        case "addMaintenanceTask":
          processingResult = await handleAddMaintenanceTask(supabase, userId, parsedResponse.data);
          break;
        case "addInventoryItem":
          processingResult = await handleAddInventoryItem(supabase, userId, parsedResponse.data);
          break;
        case "updateInventoryItem":
          processingResult = await handleUpdateInventoryItem(supabase, userId, parsedResponse.data);
          break;
        case "createPurchaseOrder":
          processingResult = await handleCreatePurchaseOrder(supabase, userId, parsedResponse.data);
          break;
        default:
          processingResult = {
            success: false,
            message: parsedResponse.message || "I couldn't understand your request. Please try again with a more specific command."
          };
      }

      return new Response(JSON.stringify(processingResult), {
        status: 200,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      });

    } catch (e) {
      console.error("Error parsing Gemini response as JSON:", e);
      console.log("Raw response:", textResponse);

      return new Response(
        JSON.stringify({
          success: false,
          message: "I had trouble understanding that. Could you rephrase your request?"
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json", ...corsHeaders },
        }
      );
    }

  } catch (error: any) {
    console.error("Error in AI command processor:", error);

    return new Response(
      JSON.stringify({
        success: false,
        message: error.message || "An error occurred while processing your request"
      }),
      {
        status: 200, // Return 200 even for errors to handle them gracefully in the UI
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

// Handler functions for different actions

async function handleAddProperty(supabase: any, userId: string, data: any): Promise<CommandResponse> {
  try {
    // Validate required fields
    if (!data.name || !data.address || !data.city || !data.state) {
      return {
        success: false,
        message: "Missing required property information. Please provide at least name, address, city, and state."
      };
    }

    // Insert the new property
    const { data: newProperty, error } = await supabase
      .from("properties")
      .insert({
        user_id: userId,
        name: data.name,
        address: data.address,
        city: data.city,
        state: data.state,
        zip: data.zip || "",
        bedrooms: data.bedrooms || 1,
        bathrooms: data.bathrooms || 1
      })
      .select()
      .single();

    if (error) {
      console.error("Error adding property:", error);
      return {
        success: false,
        message: `Failed to add property: ${error.message}`
      };
    }

    return {
      success: true,
      message: `Successfully added property "${data.name}"`,
      action: "addProperty",
      entityType: "property",
      entityId: newProperty.id
    };
  } catch (error: any) {
    console.error("Error in handleAddProperty:", error);
    return {
      success: false,
      message: `Error adding property: ${error.message}`
    };
  }
}

async function handleCreateCollection(supabase: any, userId: string, data: any): Promise<CommandResponse> {
  try {
    // Validate required fields
    if (!data.name) {
      return {
        success: false,
        message: "Collection name is required."
      };
    }

    // Insert the new collection
    const { data: newCollection, error } = await supabase
      .from("collections")
      .insert({
        user_id: userId,
        name: data.name
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating collection:", error);
      return {
        success: false,
        message: `Failed to create collection: ${error.message}`
      };
    }

    let additionalMessage = "";

    // If budget was specified, update the message
    if (data.budget) {
      additionalMessage = ` with a budget of $${data.budget}`;
    }

    return {
      success: true,
      message: `Successfully created collection "${data.name}"${additionalMessage}`,
      action: "createCollection",
      entityType: "collection",
      entityId: newCollection.id
    };
  } catch (error: any) {
    console.error("Error in handleCreateCollection:", error);
    return {
      success: false,
      message: `Error creating collection: ${error.message}`
    };
  }
}

async function handleAddMaintenanceTask(supabase: any, userId: string, data: any): Promise<CommandResponse> {
  try {
    // Validate required fields
    if (!data.title) {
      return {
        success: false,
        message: "Maintenance task title is required."
      };
    }

    // Get property ID if property name was provided
    let propertyId = null;
    let propertyName = "General";

    if (data.property) {
      const { data: propertyData, error: propertyError } = await supabase
        .from("properties")
        .select("id, name")
        .eq("user_id", userId)
        .ilike("name", `%${data.property}%`)
        .limit(1)
        .single();

      if (propertyError) {
        console.warn("Property not found:", data.property);
      } else if (propertyData) {
        propertyId = propertyData.id;
        propertyName = propertyData.name;
      }
    }

    // Insert the new maintenance task
    const { data: newTask, error } = await supabase
      .from("maintenance_tasks")
      .insert({
        user_id: userId,
        title: data.title,
        description: data.description || "",
        property_id: propertyId,
        property_name: propertyName,
        severity: data.severity || "medium",
        status: "new",
        due_date: data.dueDate || null
      })
      .select()
      .single();

    if (error) {
      console.error("Error adding maintenance task:", error);
      return {
        success: false,
        message: `Failed to add maintenance task: ${error.message}`
      };
    }

    return {
      success: true,
      message: `Successfully added maintenance task "${data.title}" for ${propertyName}`,
      action: "addMaintenanceTask",
      entityType: "maintenance_task",
      entityId: newTask.id
    };
  } catch (error: any) {
    console.error("Error in handleAddMaintenanceTask:", error);
    return {
      success: false,
      message: `Error adding maintenance task: ${error.message}`
    };
  }
}

async function handleAddInventoryItem(supabase: any, userId: string, data: any): Promise<CommandResponse> {
  try {
    // Validate required fields
    if (!data.name) {
      return {
        success: false,
        message: "Inventory item name is required."
      };
    }

    // Get property ID if property name was provided
    let propertyId = null;

    if (data.property) {
      const { data: propertyData, error: propertyError } = await supabase
        .from("properties")
        .select("id")
        .eq("user_id", userId)
        .ilike("name", `%${data.property}%`)
        .limit(1)
        .single();

      if (propertyError) {
        console.warn("Property not found:", data.property);
      } else if (propertyData) {
        propertyId = propertyData.id;
      }
    }

    if (!propertyId) {
      return {
        success: false,
        message: "Please specify a valid property for the inventory item."
      };
    }

    // Get collection ID if collection name was provided
    let collectionId = null;

    if (data.collection) {
      const { data: collectionData, error: collectionError } = await supabase
        .from("collections")
        .select("id")
        .eq("user_id", userId)
        .ilike("name", `%${data.collection}%`)
        .limit(1)
        .single();

      if (collectionError) {
        console.warn("Collection not found:", data.collection);
      } else if (collectionData) {
        collectionId = collectionData.id;
      }
    }

    // Insert the new inventory item
    const { data: newItem, error } = await supabase
      .from("inventory_items")
      .insert({
        user_id: userId,
        name: data.name,
        property_id: propertyId,
        quantity: data.quantity || 1,
        min_quantity: data.minQuantity || 1,
        collection_id: collectionId,
        collection: data.collection || "General"
      })
      .select()
      .single();

    if (error) {
      console.error("Error adding inventory item:", error);
      return {
        success: false,
        message: `Failed to add inventory item: ${error.message}`
      };
    }

    return {
      success: true,
      message: `Successfully added ${data.quantity || 1} ${data.name} to inventory`,
      action: "addInventoryItem",
      entityType: "inventory_item",
      entityId: newItem.id
    };
  } catch (error: any) {
    console.error("Error in handleAddInventoryItem:", error);
    return {
      success: false,
      message: `Error adding inventory item: ${error.message}`
    };
  }
}

async function handleUpdateInventoryItem(supabase: any, userId: string, data: any): Promise<CommandResponse> {
  try {
    if (!data.name && !data.id) {
      return {
        success: false,
        message: "Item name or ID is required to update an inventory item."
      };
    }

    // Find the item by name or ID
    let query = supabase
      .from("inventory_items")
      .select("*, properties(name)")
      .eq("user_id", userId);

    if (data.id) {
      query = query.eq("id", data.id);
    } else if (data.name) {
      // Try to find a close match for the item name
      // First try with exact match
      const { data: exactItems } = await query
        .ilike("name", data.name)
        .limit(5);

      if (exactItems && exactItems.length > 0) {
        // Found exact matches
        console.log("Found exact matches for inventory item:", exactItems.length);

        // If property is specified, try to find a match for that property
        if (data.property) {
          const propertyMatch = exactItems.find(item =>
            item.properties &&
            item.properties.name &&
            item.properties.name.toLowerCase().includes(data.property.toLowerCase())
          );

          if (propertyMatch) {
            console.log("Found property match for inventory item:", propertyMatch.id);
            return await updateInventoryItemQuantity(supabase, propertyMatch, data);
          }
        }

        // If no property match or no property specified, use the first exact match
        return await updateInventoryItemQuantity(supabase, exactItems[0], data);
      }

      // Try with partial match if no exact match found
      const { data: items, error: findError } = await query
        .ilike("name", `%${data.name}%`)
        .limit(5);

      if (findError) {
        console.error("Error finding inventory item:", findError);
        return {
          success: false,
          message: `Error finding inventory item: ${findError.message}`
        };
      }

      if (!items || items.length === 0) {
        return {
          success: false,
          message: `Inventory item "${data.name || data.id}" not found.`
        };
      }

      // If property is specified, try to find a match for that property
      if (data.property && items.length > 1) {
        const propertyMatch = items.find(item =>
          item.properties &&
          item.properties.name &&
          item.properties.name.toLowerCase().includes(data.property.toLowerCase())
        );

        if (propertyMatch) {
          console.log("Found property match for inventory item:", propertyMatch.id);
          return await updateInventoryItemQuantity(supabase, propertyMatch, data);
        }
      }

      // Use the first match if no property match or no property specified
      return await updateInventoryItemQuantity(supabase, items[0], data);
    }

    // If we get here, we're using ID-based lookup
    const { data: items, error: findError } = await query.limit(1);

    if (findError) {
      console.error("Error finding inventory item:", findError);
      return {
        success: false,
        message: `Error finding inventory item: ${findError.message}`
      };
    }

    if (!items || items.length === 0) {
      return {
        success: false,
        message: `Inventory item "${data.name || data.id}" not found.`
      };
    }

    // Update the item
    return await updateInventoryItemQuantity(supabase, items[0], data);
  } catch (error: any) {
    console.error("Error in handleUpdateInventoryItem:", error);
    return {
      success: false,
      message: `Error updating inventory item: ${error.message}`
    };
  }
}

// Helper function to update inventory item quantity
async function updateInventoryItemQuantity(supabase: any, item: any, data: any): Promise<CommandResponse> {
  try {
    const updates: any = {};
    const originalQuantity = item.quantity || 0;
    const originalMinQuantity = item.min_quantity || 0;
    let quantityChanged = false;
    let minQuantityChanged = false;
    let quantityChangeDescription = "";

    // Handle different quantity update scenarios
    if (data.quantity !== undefined) {
      // Direct quantity setting
      updates.quantity = data.quantity;
      quantityChanged = true;
      quantityChangeDescription = `set to ${data.quantity}`;
    } else if (data.increaseQuantity !== undefined) {
      // Explicit increase
      updates.quantity = originalQuantity + data.increaseQuantity;
      quantityChanged = true;
      quantityChangeDescription = `increased by ${data.increaseQuantity} to ${updates.quantity}`;
    } else if (data.decreaseQuantity !== undefined) {
      // Explicit decrease
      updates.quantity = Math.max(0, originalQuantity - data.decreaseQuantity);
      quantityChanged = true;
      quantityChangeDescription = `decreased by ${data.decreaseQuantity} to ${updates.quantity}`;
    } else if (data.addMore !== undefined) {
      // "Add more" scenario
      updates.quantity = originalQuantity + data.addMore;
      quantityChanged = true;
      quantityChangeDescription = `increased by ${data.addMore} to ${updates.quantity}`;
    } else if (data.setMinimum !== undefined) {
      // If user wants to ensure a minimum level
      const targetMinimum = data.setMinimum;
      if (originalQuantity < targetMinimum) {
        updates.quantity = targetMinimum;
        quantityChanged = true;
        quantityChangeDescription = `increased to minimum level of ${targetMinimum}`;
      }
    } else if (data.isLowStock === true) {
      // Generic "low stock" or "need more" scenario
      // Increase by a reasonable default amount based on item type
      const defaultIncrease = Math.max(5, originalMinQuantity * 2);
      updates.quantity = originalQuantity + defaultIncrease;
      quantityChanged = true;
      quantityChangeDescription = `increased by ${defaultIncrease} to ${updates.quantity} (default restock amount)`;
    }

    // Handle minimum quantity updates
    if (data.minQuantity !== undefined) {
      updates.min_quantity = data.minQuantity;
      minQuantityChanged = true;
    }

    if (data.min_quantity !== undefined) {
      updates.min_quantity = data.min_quantity;
      minQuantityChanged = true;
    }

    // If setting a minimum level that's also higher than current min_quantity, update that too
    if (data.setMinimum !== undefined && data.setMinimum > originalMinQuantity) {
      updates.min_quantity = data.setMinimum;
      minQuantityChanged = true;
    }

    if (Object.keys(updates).length === 0) {
      return {
        success: false,
        message: "No updates provided for the inventory item."
      };
    }

    // Update the item
    const { data: _updatedItem, error: updateError } = await supabase
      .from("inventory_items")
      .update(updates)
      .eq("id", item.id)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating inventory item:", updateError);
      return {
        success: false,
        message: `Failed to update inventory item: ${updateError.message}`
      };
    }

    // Build a descriptive message about what was updated
    let updateMessage = "";

    if (quantityChanged) {
      updateMessage += `quantity ${quantityChangeDescription}`;
    }

    if (minQuantityChanged) {
      if (updateMessage) updateMessage += " and ";
      updateMessage += `minimum quantity set to ${updates.min_quantity}`;
    }

    // Include property name in the message if available
    const propertyName = item.properties?.name ? ` at ${item.properties.name}` : "";

    return {
      success: true,
      message: `Successfully updated ${item.name}${propertyName}: ${updateMessage}`,
      action: "updateInventoryItem",
      entityType: "inventory_item",
      entityId: item.id
    };
  } catch (error: any) {
    console.error("Error in updateInventoryItemQuantity:", error);
    return {
      success: false,
      message: `Error updating inventory item: ${error.message}`
    };
  }
}

async function handleCreatePurchaseOrder(supabase: any, userId: string, data: any): Promise<CommandResponse> {
  try {
    // Get items to include in the purchase order
    let itemsToInclude: any[] = [];

    if (data.allLowStock) {
      // Include all low stock items using RPC function
      let propertyId = null;

      // Filter by property if specified
      if (data.property) {
        // First find the property ID by name
        const { data: properties, error: propError } = await supabase
          .from("properties")
          .select("id, name")
          .eq("user_id", userId)
          .ilike("name", `%${data.property}%`)
          .limit(1);

        if (propError) {
          console.error("Error finding property:", propError);
        } else if (properties && properties.length > 0) {
          propertyId = properties[0].id;
        }
      }

      // Use RPC function to get low stock items
      const { data: lowStockItems, error: itemsError } = await supabase
        .rpc('get_low_stock_items', {
          user_id_param: userId,
          property_id_param: propertyId
        });

      if (itemsError) {
        console.error("Error fetching low stock items:", itemsError);
        return {
          success: false,
          message: `Error fetching low stock items: ${itemsError.message}`
        };
      }

      console.log(`Found ${lowStockItems?.length || 0} low stock items for user ${userId}${propertyId ? ` at property ${propertyId}` : ''}`);

      if (!lowStockItems || lowStockItems.length === 0) {
        return {
          success: false,
          message: `No low stock items found${data.property ? ` at property "${data.property}"` : ''}. Check your inventory to make sure items have minimum quantities set.`
        };
      }

      itemsToInclude = (lowStockItems || []).map((item: any) => ({
        inventory_item_id: item.id,
        item_name: item.name,
        quantity: Math.max(item.min_quantity - item.quantity, 1),
        price: item.price,
        amazon_url: item.amazon_url,
        walmart_url: item.walmart_url,
        property_id: item.property_id,
        property_name: item.property_name
      }));
    } else if (data.items && data.items.length > 0) {
      // Include specific items or categories
      for (const itemData of data.items) {
        // Find items by name or category
        let query = supabase
          .from("inventory_items")
          .select(`
            id, name, quantity, min_quantity, property_id, collection,
            price, amazon_url, walmart_url,
            properties:property_id (id, name)
          `)
          .eq("user_id", userId);

        // Check if this is a category search (like "cleaning supplies", "bathroom supplies")
        const categoryKeywords = {
          'cleaning': ['cleaner', 'soap', 'detergent', 'disinfectant', 'bleach', 'spray'],
          'bathroom': ['toilet', 'towel', 'shampoo', 'soap', 'tissue'],
          'kitchen': ['dish', 'plate', 'cup', 'glass', 'utensil', 'knife', 'fork'],
          'bedroom': ['sheet', 'pillow', 'blanket', 'towel'],
          'laundry': ['detergent', 'softener', 'bleach']
        };

        let foundItems = [];
        let isCategory = false;

        // Check if the item name matches a category
        for (const [category, keywords] of Object.entries(categoryKeywords)) {
          if (itemData.name.toLowerCase().includes(category)) {
            isCategory = true;
            // Search for items that match any of the category keywords
            for (const keyword of keywords) {
              const { data: categoryItems, error: categoryError } = await query
                .ilike("name", `%${keyword}%`);

              if (!categoryError && categoryItems) {
                foundItems.push(...categoryItems);
              }
            }
            break;
          }
        }

        // If not a category, search by exact name
        if (!isCategory) {
          const { data: exactItems, error: findError } = await query
            .ilike("name", `%${itemData.name}%`);

          if (!findError && exactItems) {
            foundItems = exactItems;
          }
        }

        // Remove duplicates from foundItems
        const uniqueItems = foundItems.filter((item, index, self) =>
          index === self.findIndex(i => i.id === item.id)
        );

        if (uniqueItems && uniqueItems.length > 0) {
          for (const item of uniqueItems) {

            itemsToInclude.push({
              inventory_item_id: item.id,
              item_name: item.name,
              quantity: itemData.quantity || Math.max(item.min_quantity - item.quantity, 1),
              price: item.price,
              amazon_url: item.amazon_url,
              walmart_url: item.walmart_url,
              property_id: item.property_id,
              property_name: item.properties?.name
            });
          }
        }
      }
    } else {
      return {
        success: false,
        message: "No items specified for the purchase order."
      };
    }

    if (itemsToInclude.length === 0) {
      return {
        success: false,
        message: "No valid items found to include in the purchase order."
      };
    }

    // Group items by property
    const itemsByProperty: {[key: string]: any[]} = {};

    for (const item of itemsToInclude) {
      const propertyId = item.property_id;
      if (!itemsByProperty[propertyId]) {
        itemsByProperty[propertyId] = [];
      }
      itemsByProperty[propertyId].push(item);
    }

    const createdOrders = [];

    // Create purchase orders for each property
    for (const [propertyId, items] of Object.entries(itemsByProperty)) {
      if (items.length === 0) continue;

      // Calculate total price
      const totalPrice = items.reduce((sum, item) => sum + (item.price || 0) * item.quantity, 0);

      // Create the purchase order
      const { data: order, error: orderError } = await supabase
        .from("purchase_orders")
        .insert({
          user_id: userId,
          property_id: propertyId,
          status: "pending",
          total_price: totalPrice > 0 ? totalPrice : null,
          notes: data.notes || ""
        })
        .select()
        .single();

      if (orderError) {
        console.error("Error creating purchase order:", orderError);
        continue;
      }

      // Add items to the purchase order
      const orderItems = items.map((item) => ({
        purchase_order_id: order.id,
        inventory_item_id: item.inventory_item_id,
        item_name: item.item_name,
        quantity: item.quantity,
        price: item.price,
        amazon_url: item.amazon_url,
        walmart_url: item.walmart_url
      }));

      const { error: itemsError } = await supabase
        .from("purchase_order_items")
        .insert(orderItems);

      if (itemsError) {
        console.error("Error adding items to purchase order:", itemsError);
        continue;
      }

      createdOrders.push({
        id: order.id,
        propertyName: items[0].property_name,
        itemCount: items.length
      });
    }

    if (createdOrders.length === 0) {
      return {
        success: false,
        message: "Failed to create any purchase orders."
      };
    }

    let message = "";
    if (createdOrders.length === 1) {
      const order = createdOrders[0];
      message = `Successfully created a purchase order with ${order.itemCount} items for ${order.propertyName}`;
    } else {
      message = `Successfully created ${createdOrders.length} purchase orders for different properties`;
    }

    return {
      success: true,
      message,
      action: "createPurchaseOrder",
      entityType: "purchase_order",
      entityId: createdOrders[0].id
    };
  } catch (error: any) {
    console.error("Error in handleCreatePurchaseOrder:", error);
    return {
      success: false,
      message: `Error creating purchase order: ${error.message}`
    };
  }
}

serve(handler);
