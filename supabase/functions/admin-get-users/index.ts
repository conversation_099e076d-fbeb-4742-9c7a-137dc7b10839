
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.4'
import { corsHeaders } from '../_shared/cors.ts'

// Type definition for Database structure
interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email?: string
          first_name?: string
          last_name?: string
          avatar_url?: string
          role?: string
          is_super_admin?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Functions: {
      is_super_admin: {
        Args: { uid: string }
        Returns: boolean
      }
    }
  }
}

Deno.serve(async (req) => {
  console.log("Admin Get Users function invoked");

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';

    // Create a Supabase client with the service role key
    const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey);

    // Verify that the request is authorized
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      console.error("Missing authorization header");
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        {
          status: 401,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // Verify the user is authenticated and is a super admin
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);

    if (userError || !user) {
      console.error("Unauthorized access attempt:", userError);
      return new Response(
        JSON.stringify({ error: 'Unauthorized', details: userError?.message }),
        {
          status: 401,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        }
      );
    }

    console.log(`User authenticated: ${user.id}`);

    // Check if the user is a super admin
    const { data: isAdmin, error: isAdminError } = await supabase.rpc('is_super_admin', {
      uid: user.id
    });

    if (isAdminError) {
      console.error("Error checking admin status:", isAdminError);
      return new Response(
        JSON.stringify({ error: `Error checking admin status: ${isAdminError.message}` }),
        {
          status: 500,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        }
      );
    }

    if (!isAdmin) {
      console.error("Non-admin access attempt");
      return new Response(
        JSON.stringify({ error: 'Admin access required' }),
        {
          status: 403,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // If the user is a super admin, fetch all profiles using service role
    // Also fetch additional user information from auth.users
    console.log('Fetching all user profiles...');

    // First, get all profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false });

    if (profilesError) {
      console.error('Error fetching profiles:', profilesError);
      return new Response(
        JSON.stringify({ error: profilesError.message }),
        {
          status: 500,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        }
      );
    }

    console.log(`Successfully fetched ${profiles?.length || 0} profiles`);

    // Return the profiles
    return new Response(
      JSON.stringify({ users: profiles }),
      {
        status: 200,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : 'No stack trace';
    console.error('Unexpected error:', errorMessage, errorStack);
    return new Response(
      JSON.stringify({ error: 'Internal Server Error', details: errorMessage }),
      {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );
  }
});
