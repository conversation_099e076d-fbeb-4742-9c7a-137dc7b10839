import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type, Authorization",
  "Access-Control-Max-Age": "86400",
};

const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

const handler = async (req: Request): Promise<Response> => {
  // Handle preflight requests for CORS
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: {
        ...corsHeaders,
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
      },
    });
  }

  try {
    // Parse request body or URL parameters
    let taskId, token;

    // Try URL parameters first
    const url = new URL(req.url);
    const urlTaskId = url.searchParams.get("taskId");
    const urlToken = url.searchParams.get("token");

    if (urlTaskId && urlToken) {
      console.log("Using URL parameters");
      taskId = urlTaskId;
      token = urlToken;
    } else {
      // Try to parse request body
      try {
        const body = await req.json();
        taskId = body.taskId;
        token = body.token;
        console.log("Using request body parameters");
      } catch (parseError) {
        console.error('Error parsing request body:', parseError);
        return new Response(
          JSON.stringify({
            error: "Invalid request",
            message: "Could not parse request body and no URL parameters found",
            details: parseError instanceof Error ? parseError.message : String(parseError),
          }),
          {
            status: 400,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }
    }

    console.log("Verifying token for task:", { taskId, tokenProvided: !!token });

    if (!taskId || !token) {
      return new Response(
        JSON.stringify({
          error: "Missing required parameters",
          message: "Please provide taskId and token parameters",
        }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Initialize Supabase admin client
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Verify token
    const isValid = await verifyToken(taskId, token);
    if (!isValid) {
      return new Response(
        JSON.stringify({
          error: "Invalid token",
          message: "The provided token is not valid for this task",
        }),
        {
          status: 401,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Get task details
    const { data: task, error: taskError } = await supabase
      .from("maintenance_tasks")
      .select("*, provider_id, provider_email")
      .eq("id", taskId)
      .maybeSingle();

    if (taskError) {
      console.error("Error fetching task:", taskError);
      return new Response(
        JSON.stringify({
          error: "Database error",
          message: "Error fetching task details",
          details: taskError.message,
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    if (!task) {
      return new Response(
        JSON.stringify({
          error: "Not found",
          message: "Task not found",
        }),
        {
          status: 404,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Add debug logging
    console.log('Task details:', JSON.stringify(task));

    // Return task details needed for the response handler
    return new Response(
      JSON.stringify({
        taskId: task.id,
        title: task.title,
        status: task.status,
        providerEmail: task.provider_email,
        isValid: true,
      }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );

  } catch (err: unknown) {
    console.error("Unhandled error:", err);
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        message: "An unexpected error occurred",
        details: err instanceof Error ? err.message : String(err),
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
};

async function verifyToken(taskId: string, token: string): Promise<boolean> {
  try {
    if (!taskId || !token) {
      console.error("Missing taskId or token for verification");
      return false;
    }

    console.log("Token verification inputs:", { taskId, tokenLength: token.length });

    const encoder = new TextEncoder();
    const data = encoder.encode(taskId + "maintenance-response-secret");
    const hashBuffer = await crypto.subtle.digest("SHA-256", data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const generatedToken = hashArray.map(b => b.toString(16).padStart(2, "0")).join("").substring(0, 32);

    console.log("Token verification details:", {
      taskId,
      generatedToken,
      generatedLength: generatedToken.length,
      receivedToken: token,
      receivedLength: token.length,
      match: token === generatedToken
    });

    return token === generatedToken;
  } catch (error) {
    console.error("Error verifying token:", error);
    return false;
  }
}

serve(handler);
