// Type declarations for Deno and Edge Function modules
declare module "https://deno.land/std@0.168.0/http/server.ts" {
  export function serve(handler: (req: Request) => Response | Promise<Response>): void;
}

declare module "https://esm.sh/@supabase/supabase-js@2.7.1" {
  export function createClient(supabaseUrl: string, supabaseKey: string): {
    from: (table: string) => {
      select: (columns?: string) => any;
      insert: (data: any) => any;
      update: (data: any) => any;
      delete: () => any;
      eq: (column: string, value: any) => any;
      single: () => any;
    };
  };
}

declare module "https://esm.sh/ical@0.8.0" {
  interface ICalEvent {
    type: string;
    start: Date;
    end: Date;
    summary?: string;
    [key: string]: any;
  }

  interface ICalData {
    [key: string]: ICalEvent;
  }

  const ical: {
    parseICS(data: string): ICalData;
  };
  
  export default ical;
}

declare namespace Deno {
  interface Env {
    get(key: string): string | undefined;
  }
  
  export const env: Env;
}