import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface RegisterServiceProviderRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: string; // Add role parameter
  invitationToken?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get the request body
    let requestBody;
    try {
      requestBody = await req.json();
      console.log('Request body:', JSON.stringify(requestBody));
    } catch (error) {
      console.error('Error parsing request body:', error);
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid JSON in request body'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400
        }
      );
    }

    // Extract and validate required fields
    const { 
      email, 
      password, 
      firstName, 
      lastName, 
      role = 'service_provider', 
      invitationToken 
    } = requestBody as RegisterServiceProviderRequest;

    if (!email) {
      console.error('Missing required field: email');
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Missing required field: email'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400
        }
      );
    }

    if (!password) {
      console.error('Missing required field: password');
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Missing required field: password'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400
        }
      );
    }

    if (!firstName) {
      console.error('Missing required field: firstName');
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Missing required field: firstName'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400
        }
      );
    }

    if (!lastName) {
      console.error('Missing required field: lastName');
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Missing required field: lastName'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400
        }
      );
    }

    console.log(`Registering user with role ${role}: ${email}`)

    // Create the user with email confirmation disabled
    console.log(`Creating user with email ${email} and role ${role}`)
    
    try {
      const { data: userData, error: userError } = await supabaseAdmin.auth.admin.createUser({
        email,
        password,
        email_confirm: true,
        user_metadata: {
          first_name: firstName,
          last_name: lastName,
          role: role, // Use the provided role
        },
      });

      if (userError) {
        console.error('Error creating user:', userError);
        return new Response(
          JSON.stringify({
            success: false,
            error: userError.message
          }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400
          }
        );
      }

      if (!userData?.user) {
        console.error('User creation response did not include user data');
        return new Response(
          JSON.stringify({
            success: false,
            error: 'User creation failed'
          }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400
          }
        );
      }

      console.log('User created with metadata:', userData?.user?.user_metadata);
      console.log(`User created successfully with ID: ${userData.user.id}`);

      // Create the user profile
      try {
        console.log(`Creating user profile for user ID: ${userData.user.id} with role: ${role}`);
        const { data: profileData, error: userProfileError } = await supabaseAdmin
          .from('profiles')
          .upsert({
            id: userData.user.id,
            email,
            first_name: firstName,
            last_name: lastName,
            role: role, // Use the provided role
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select();

        if (userProfileError) {
          console.error('Error creating user profile:', userProfileError);
          // Continue anyway, as this might be a duplicate profile
        } else {
          console.log('User profile created successfully:', profileData);
        }
      } catch (profileError) {
        console.error('Exception creating user profile:', profileError);
        // Continue anyway - don't fail the whole registration
      }

      // Create the service provider profile only if the role is service_provider
      let profileError = null;
      if (role === 'service_provider') {
        try {
          console.log('Creating service provider profile for user:', userData.user.id);
          const result = await supabaseAdmin
            .from('service_providers')
            .insert({
              id: userData.user.id,
              email,
              first_name: firstName,
              last_name: lastName,
              status: 'active',
            });

          profileError = result.error;

          if (profileError) {
            console.error('Error creating service provider profile:', profileError);
          } else {
            console.log('Service provider profile created successfully');
          }
        } catch (err) {
          console.error('Exception creating service provider profile:', err);
          // Continue anyway - don't fail the whole registration
        }
      }

      // Process invitation if provided
      let invitationResult = null;
      if (invitationToken) {
        try {
          console.log(`Processing invitation token: ${invitationToken}`);

          // First, get the invitation details to extract the team_id
          const { data: invitationDetails, error: invitationDetailsError } = await supabaseAdmin
            .from('team_invitations')
            .select('team_id, status')
            .eq('token', invitationToken)
            .maybeSingle();

          if (invitationDetailsError) {
            console.error('Error fetching invitation details:', invitationDetailsError);
            invitationResult = { success: false, error: invitationDetailsError.message };
          } else if (!invitationDetails) {
            console.error('Invitation not found');
            invitationResult = { success: false, error: 'Invitation not found' };
          } else {
            console.log('Found invitation details:', invitationDetails);

            // Call the RPC function to accept the invitation
            const { data: invitationData, error: invitationError } = await supabaseAdmin.rpc(
              'accept_invitation_and_add_member',
              {
                p_token: invitationToken,
                p_user_id: userData.user.id
              }
            );

            if (invitationError) {
              console.error('Error accepting invitation:', invitationError);
              invitationResult = { success: false, error: invitationError.message };

              // If the RPC fails, try to directly add the user to the team
              if (invitationDetails.team_id) {
                console.log('Attempting direct team membership as fallback');
                const { data: teamData, error: teamError } = await supabaseAdmin.rpc(
                  'add_user_to_team',
                  {
                    p_user_id: userData.user.id,
                    p_team_id: invitationDetails.team_id
                  }
                );

                if (teamError) {
                  console.error('Error adding user to team directly:', teamError);
                } else {
                  console.log('Successfully added user to team directly:', teamData);
                  invitationResult = teamData || { success: true, team_id: invitationDetails.team_id };
                }
              }
            } else {
              console.log('Invitation accepted successfully:', invitationData);
              invitationResult = invitationData || { success: true };
            }
          }
        } catch (inviteError) {
          console.error('Exception processing invitation:', inviteError);
          invitationResult = { success: false, error: 'Exception processing invitation' };
        }
      }

      // Return the user data
      return new Response(
        JSON.stringify({
          success: true,
          user: userData.user,
          message: 'User registered successfully',
          invitation: invitationResult
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        }
      );
    } catch (userCreationError) {
      console.error('Error creating user:', userCreationError);
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Error creating user: ' + (userCreationError.message || 'Unknown error')
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400
        }
      );
    }
  } catch (err) {
    console.error('Unexpected error:', err);
    return new Response(
      JSON.stringify({
        success: false,
        error: 'An unexpected error occurred: ' + (err.message || 'Unknown error')
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  }
});
