import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.8" // Updated version

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase URL or service role key')
      return new Response(
        JSON.stringify({ error: 'Server configuration error' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    // Create Supabase client with admin rights
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Verify user authentication using the JWT from the Authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.error('Missing or invalid authorization header')
      return new Response(
        JSON.stringify({ error: 'Missing or invalid authorization' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
      )
    }

    const token = authHeader.split(' ')[1]
    // Cast to any before accessing auth
    const { data: { user }, error: authError } = await (supabase as any).auth.getUser(token)

    if (authError || !user) {
      console.error('Authentication failed:', authError)
      return new Response(
        JSON.stringify({ error: 'Authentication failed', details: authError?.message }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
      )
    }

    // Parse the FormData from the request
    const formData = await req.formData()
    const file = formData.get('file') as File
    const propertyId = formData.get('propertyId') as string
    const userId = formData.get('userId') as string

    // Validate required fields
    if (!file) {
      return new Response(
        JSON.stringify({ error: 'No file uploaded' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Verify the user has access to the property if propertyId is provided
    if (propertyId) {
      const { data: propertyAccess, error: accessError } = await supabase
        .from('properties')
        .select('id')
        .eq('id', propertyId)
        .eq('user_id', userId)
        .single()

      if (accessError || !propertyAccess) {
        console.error('Property access error:', accessError)
        return new Response(
          JSON.stringify({ error: 'Access denied to property' }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 403 }
        )
      }
    }

    // Log received data for debugging
    console.log(`Processing upload - Property ID: ${propertyId}, User ID: ${userId}, File name: ${file.name}, File size: ${file.size}`)

    // Sanitize file name and get extension
    const sanitizedFileName = file.name.replace(/[^\x00-\x7F]/g, '')
    const fileExt = sanitizedFileName.split('.').pop()?.toLowerCase()

    // Validate file type
    const allowedTypes = ['jpg', 'jpeg', 'png', 'webp']
    if (!fileExt || !allowedTypes.includes(fileExt)) {
      return new Response(
        JSON.stringify({ error: 'Invalid file type. Only JPG, PNG and WebP are allowed.' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Create unique path for the file
    const filePath = `${propertyId || 'temp'}/${crypto.randomUUID()}.${fileExt}`

    // Ensure the property-images bucket exists
    // Cast to any before accessing storage
    const { data: buckets } = await (supabase as any).storage.listBuckets()
    // Add explicit type for 'b'
    if (!buckets?.find((b: { name: string }) => b.name === 'property-images')) {
      // Cast to any before accessing storage
      const { error: bucketError } = await (supabase as any).storage.createBucket('property-images', {
        public: true,
        fileSizeLimit: 10485760 // 10MB
      })

      if (bucketError) {
        console.error('Error creating bucket:', bucketError)
        return new Response(
          JSON.stringify({ error: 'Failed to initialize storage' }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
        )
      }
    }

    // Upload to Supabase Storage
    // Cast to any before accessing storage
    const { error: uploadError } = await (supabase as any).storage
      .from('property-images')
      .upload(filePath, file, {
        contentType: file.type,
        upsert: true
      })

    if (uploadError) {
      console.error('Storage upload error:', uploadError)
      return new Response(
        JSON.stringify({ error: 'Failed to upload file to storage', details: uploadError }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    console.log('File uploaded successfully to storage:', filePath)

    // Get public URL
    // Cast to any before accessing storage
    const { data: publicUrlData } = (supabase as any).storage
      .from('property-images')
      .getPublicUrl(filePath)

    // If property ID is provided, update the property record
    if (propertyId && userId) {
      console.log(`Updating property record: ${propertyId} with image URL: ${publicUrlData.publicUrl}`)
      
      const { error: updateError } = await supabase
        .from('properties')
        .update({ image_url: publicUrlData.publicUrl })
        .eq('id', propertyId)
        .eq('user_id', userId)

      if (updateError) {
        console.error('Database update error:', updateError)
        return new Response(
          JSON.stringify({ error: 'Failed to update property with image URL', details: updateError }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
        )
      }
      
      console.log('Property record updated successfully')
    }

    return new Response(
      JSON.stringify({ 
        message: 'Image uploaded successfully',
        url: publicUrlData.publicUrl
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
    )
  } catch (error: any) { // Add explicit type for error
    console.error('Unexpected error:', error)
    return new Response(
      JSON.stringify({ error: 'An unexpected error occurred', details: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
