
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1"
import ical from "https://esm.sh/ical@0.8.0"

const SYNC_BATCH_SIZE = 5; // Number of properties to sync in a batch
const BATCH_DELAY_MS = 5000; // Delay between batches to avoid overloading servers (5 seconds)

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, x-client-application, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? '';
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '';
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase credentials');
      return new Response(
        JSON.stringify({ error: 'Server configuration error' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Parse request body if present
    let forceSyncAll = false;
    let forceFetch = false;
    let propertyId = null;
    
    try {
      const requestData = await req.json();
      if (requestData.forceSyncAll === true) {
        forceSyncAll = true;
      }
      if (requestData.forceFetch === true) {
        forceFetch = true;
      }
      if (requestData.propertyId) {
        propertyId = requestData.propertyId;
        console.log(`Syncing specific property: ${requestData.propertyId}`);
      }
    } catch (e) {
      // No request body or invalid JSON - proceed with default settings
    }

    // Get all properties with iCal URLs
    let properties;
    let fetchError;
    
    try {
      let query = supabase
        .from('properties')
        .select('id, ical_url, user_id, name, last_ical_sync')
        .neq('ical_url', '')
        .order('last_ical_sync', { ascending: true, nullsFirst: true });
        
      if (propertyId) {
        query = query.eq('id', propertyId);
      }
      
      const result = await query.limit(forceSyncAll ? 100 : SYNC_BATCH_SIZE);
        
      properties = result.data;
      fetchError = result.error;
    } catch (e) {
      console.log("Error querying properties:", e);
      return new Response(
        JSON.stringify({ error: 'Failed to fetch properties', details: e.message }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }
      
    if (fetchError) {
      console.error('Error fetching properties:', fetchError);
      return new Response(
        JSON.stringify({ error: 'Failed to fetch properties', details: fetchError }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    if (!properties || properties.length === 0) {
      return new Response(
        JSON.stringify({ message: 'No properties with iCal URLs found to sync' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
      )
    }

    console.log(`Found ${properties.length} properties to sync`);
    
    const results = [];
    
    // Process properties in batches to avoid overloading
    for (let i = 0; i < properties.length; i += SYNC_BATCH_SIZE) {
      const batch = properties.slice(i, i + SYNC_BATCH_SIZE);
      
      // Process each property in the batch concurrently
      const batchPromises = batch.map(async (property) => {
        try {
          console.log(`Processing property: ${property.id}, ${property.name}`);
          
          if (!property.ical_url) {
            return { 
              propertyId: property.id, 
              status: 'skipped', 
              message: 'No iCal URL' 
            };
          }
          
          // Fetch and parse the iCal data
          const response = await fetch(property.ical_url);
          
          if (!response.ok) {
            console.error(`Failed to fetch iCal for property ${property.id}: ${response.status}`);
            return { 
              propertyId: property.id, 
              status: 'error', 
              message: `Failed to fetch iCal: ${response.status}` 
            };
          }
          
          const icalData = await response.text();
          
          if (!icalData || icalData.trim() === '') {
            console.error(`Empty iCal data for property ${property.id}`);
            return { 
              propertyId: property.id, 
              status: 'error', 
              message: 'Empty iCal data received' 
            };
          }
          
          const data = ical.parseICS(icalData);
          if (!data || Object.keys(data).length === 0) {
            console.error(`No events found in iCal data for property ${property.id}`);
            return { 
              propertyId: property.id, 
              status: 'error', 
              message: 'No events found in iCal data' 
            };
          }
          
          console.log(`Parsed ${Object.keys(data).length} events for property ${property.id}`);
          
          // Find if the property is currently occupied and the next upcoming event
          const now = new Date();
          let isOccupied = false;
          let currentCheckout = null;
          let nextEvent = null;
          
          console.log(`Current time: ${now.toISOString()}`);
          
          // Debug: Log all events
          console.log(`All events for property ${property.id}:`);
          for (const key in data) {
            const event = data[key];
            if (event.type === 'VEVENT' && event.start && event.end) {
              console.log(`Event: ${event.summary}, Start: ${event.start.toISOString()}, End: ${event.end.toISOString()}`);
            }
          }
          
          // First check for current occupancy
          for (const key in data) {
            const event = data[key];
            if (event.type === 'VEVENT' && event.start && event.end) {
              // iCal dates are exclusive for the end date (the checkout date)
              // So we need to check if now is >= start and < end
              if (now >= event.start && now < event.end) {
                console.log(`Property ${property.id} is occupied by event: `, {
                  start: event.start.toISOString(),
                  end: event.end.toISOString(),
                  summary: event.summary || 'No summary'
                });
                
                isOccupied = true;
                
                // Format checkout date
                const checkoutDate = new Date(event.end);
                checkoutDate.setDate(checkoutDate.getDate() - 1); // Subtract one day for actual checkout
                currentCheckout = checkoutDate.toLocaleDateString('en-US', { 
                  month: 'short', 
                  day: 'numeric', 
                  year: 'numeric' 
                });
                
                console.log(`Property is occupied, checkout on ${currentCheckout}`);
                break;
              }
            }
          }
          
          console.log(`After checking all events, isOccupied = ${isOccupied}`);
          
          // Then find next booking if not currently occupied
          if (!isOccupied) {
            for (const key in data) {
              const event = data[key];
              if (event.type === 'VEVENT' && event.start && event.start > now) {
                if (!nextEvent || event.start < nextEvent.start) {
                  nextEvent = event;
                }
              }
            }
          }
          
          let nextBooking = null;
          if (nextEvent) {
            const startDate = nextEvent.start;
            const endDate = nextEvent.end;
            
            if (startDate && endDate) {
              const startFormatted = startDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
              const endFormatted = endDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
              nextBooking = `${startFormatted} - ${endFormatted}`;
              console.log(`Next booking: ${nextBooking}`);
            }
          }
          
          // Try to update with all the new fields
          try {
            console.log(`Updating property ${property.id} with:`, {
              next_booking: nextBooking,
              is_occupied: isOccupied,
              current_checkout: currentCheckout,
              last_ical_sync: new Date().toISOString()
            });
            
            const { error: updateError } = await supabase
              .from('properties')
              .update({ 
                next_booking: nextBooking, 
                is_occupied: isOccupied,
                current_checkout: currentCheckout,
                last_ical_sync: new Date().toISOString() 
              })
              .eq('id', property.id);
            
            if (updateError) {
              throw updateError;
            }
          } catch (updateError) {
            console.error(`Failed to update property ${property.id}:`, updateError);
            return { 
              propertyId: property.id, 
              status: 'error', 
              message: `DB update failed: ${updateError.message}` 
            };
          }
          
          return { 
            propertyId: property.id, 
            status: 'success', 
            isOccupied,
            currentCheckout,
            nextBooking: nextBooking || 'No upcoming bookings' 
          };
        } catch (error) {
          console.error(`Error processing property ${property.id}:`, error);
          return { 
            propertyId: property.id, 
            status: 'error', 
            message: error.message 
          };
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      // Wait before processing the next batch to avoid overloading
      if (i + SYNC_BATCH_SIZE < properties.length) {
        await new Promise(resolve => setTimeout(resolve, BATCH_DELAY_MS));
      }
    }
    
    // Queue up the next batch for processing if we limited this run and there are more properties
    if (!forceSyncAll && !forceFetch && properties.length === SYNC_BATCH_SIZE) {
      // Schedule another run after a delay to process more properties
      setTimeout(async () => {
        try {
          await fetch(`${supabaseUrl}/functions/v1/sync-property-calendars`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${Deno.env.get('SUPABASE_ANON_KEY')}`
            },
            body: JSON.stringify({ forceSyncAll: false })
          });
        } catch (e) {
          console.error("Failed to schedule follow-up sync:", e);
        }
      }, BATCH_DELAY_MS);
    }

    return new Response(
      JSON.stringify({ 
        message: `Processed ${results.length} properties`,
        results,
        success: true
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
    )
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(
      JSON.stringify({ error: 'An unexpected error occurred', details: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
