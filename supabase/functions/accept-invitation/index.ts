import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, x-client-application, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
};

interface AcceptInvitationRequest {
  token: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
    
    if (!supabaseUrl || !supabaseKey) {
      console.error('Missing Supabase URL or key');
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Server configuration error'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500
        }
      );
    }
    
    const supabaseAdmin = createClient(supabaseUrl, supabaseKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Get the request body
    let requestBody;
    try {
      requestBody = await req.json();
      console.log('Request body:', JSON.stringify(requestBody));
    } catch (error) {
      console.error('Error parsing request body:', error);
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid JSON in request body'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400
        }
      );
    }

    // Extract and validate required fields
    const { 
      token, 
      email, 
      password, 
      firstName, 
      lastName, 
      role = 'service_provider'
    } = requestBody as AcceptInvitationRequest;

    if (!token || !email || !password || !firstName || !lastName) {
      console.error('Missing required fields');
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Missing required fields'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400
        }
      );
    }

    // First, get the invitation details
    console.log(`Looking up invitation with token: ${token}`);
    const { data: invitationData, error: invitationError } = await supabaseAdmin
      .from('team_invitations')
      .select('id, team_id, email, role, status')
      .eq('token', token)
      .maybeSingle();

    if (invitationError) {
      console.error('Error fetching invitation:', invitationError);
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Error fetching invitation: ' + invitationError.message
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400
        }
      );
    }

    if (!invitationData) {
      console.error('Invitation not found');
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invitation not found'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 404
        }
      );
    }

    console.log('Found invitation:', invitationData);

    // Create the user
    console.log('Creating new user');
    
    try {
      const { data: userData, error: userError } = await supabaseAdmin.auth.admin.createUser({
        email,
        password,
        email_confirm: true,
        user_metadata: {
          first_name: firstName,
          last_name: lastName,
          role: role,
        },
      });

      if (userError) {
        // If the user already exists, we'll get an error
        console.error('Error creating user:', userError);
        
        // Try to sign in with the provided credentials
        console.log('User might already exist, trying to sign in');
        const { data: signInData, error: signInError } = await supabaseAdmin.auth.signInWithPassword({
          email,
          password
        });
        
        if (signInError) {
          console.error('Error signing in:', signInError);
          return new Response(
            JSON.stringify({
              success: false,
              error: 'Could not create or sign in user: ' + signInError.message
            }),
            {
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
              status: 400
            }
          );
        }
        
        if (!signInData?.user) {
          console.error('Sign in response did not include user data');
          return new Response(
            JSON.stringify({
              success: false,
              error: 'Could not retrieve user data'
            }),
            {
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
              status: 400
            }
          );
        }
        
        // Use the existing user's ID
        const userId = signInData.user.id;
        console.log(`Using existing user with ID: ${userId}`);
        
        // Add the user to the team
        console.log(`Adding user ${userId} to team ${invitationData.team_id}`);
        const { error: teamMemberError } = await supabaseAdmin
          .from('team_members')
          .upsert({
            team_id: invitationData.team_id,
            user_id: userId,
            role: invitationData.role || role,
            status: 'active',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
        
        if (teamMemberError) {
          console.error('Error adding user to team:', teamMemberError);
          // Continue anyway - the user might already be in the team
        }
        
        // Update the invitation status
        console.log(`Updating invitation status to accepted for invitation ID: ${invitationData.id}`);
        const { error: updateError } = await supabaseAdmin
          .from('team_invitations')
          .update({
            status: 'accepted',
            accepted_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', invitationData.id);
        
        if (updateError) {
          console.error('Error updating invitation status:', updateError);
          // Continue anyway - the user is already added to the team
        }
        
        return new Response(
          JSON.stringify({
            success: true,
            user: signInData.user,
            isNewUser: false,
            team_id: invitationData.team_id,
            message: 'Invitation accepted successfully'
          }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 200
          }
        );
      }
      
      if (!userData?.user) {
        console.error('User creation response did not include user data');
        return new Response(
          JSON.stringify({
            success: false,
            error: 'User creation failed'
          }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500
          }
        );
      }
      
      const userId = userData.user.id;
      console.log(`Created user with ID: ${userId}`);
      
      // Create the user profile
      console.log(`Creating user profile for user ID: ${userId} with role: ${role}`);
      const { error: userProfileError } = await supabaseAdmin
        .from('profiles')
        .upsert({
          id: userId,
          email,
          first_name: firstName,
          last_name: lastName,
          role: role,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      
      if (userProfileError) {
        console.error('Error creating user profile:', userProfileError);
        // Continue anyway - the profile might already exist
      }
      
      // Create the service provider profile if needed
      if (role === 'service_provider') {
        console.log('Creating service provider profile for user:', userId);
        const { error: spProfileError } = await supabaseAdmin
          .from('service_providers')
          .upsert({
            id: userId,
            email,
            first_name: firstName,
            last_name: lastName,
            status: 'active',
          });
        
        if (spProfileError) {
          console.error('Error creating service provider profile:', spProfileError);
          // Continue anyway - the profile might already exist
        }
      }
      
      // Add the user to the team
      console.log(`Adding user ${userId} to team ${invitationData.team_id}`);
      const { error: teamMemberError } = await supabaseAdmin
        .from('team_members')
        .upsert({
          team_id: invitationData.team_id,
          user_id: userId,
          role: invitationData.role || role,
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      
      if (teamMemberError) {
        console.error('Error adding user to team:', teamMemberError);
        // Continue anyway - the user might already be in the team
      }
      
      // Update the invitation status
      console.log(`Updating invitation status to accepted for invitation ID: ${invitationData.id}`);
      const { error: updateError } = await supabaseAdmin
        .from('team_invitations')
        .update({
          status: 'accepted',
          accepted_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', invitationData.id);
      
      if (updateError) {
        console.error('Error updating invitation status:', updateError);
        // Continue anyway - the user is already added to the team
      }
      
      return new Response(
        JSON.stringify({
          success: true,
          user: userData.user,
          isNewUser: true,
          team_id: invitationData.team_id,
          message: 'Invitation accepted successfully'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        }
      );
    } catch (error) {
      console.error('Unexpected error:', error);
      return new Response(
        JSON.stringify({
          success: false,
          error: 'An unexpected error occurred: ' + (error instanceof Error ? error.message : 'Unknown error')
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500
        }
      );
    }
  } catch (err) {
    console.error('Unexpected error:', err);
    return new Response(
      JSON.stringify({
        success: false,
        error: 'An unexpected error occurred: ' + (err instanceof Error ? err.message : 'Unknown error')
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  }
});
