{"imports": {"std/": "https://deno.land/std@0.190.0/", "supabase": "https://esm.sh/@supabase/supabase-js@2"}, "compilerOptions": {"allowJs": true, "lib": ["deno.window"], "strict": true}, "tasks": {"deploy": "supabase functions deploy --no-verify-jwt"}, "lsp": {"enable": true, "semanticTokens": false}, "lint": {"files": {"include": ["*.ts"]}, "rules": {"tags": ["recommended"]}}, "fmt": {"files": {"include": ["*.ts"]}, "options": {"useTabs": false, "lineWidth": 100, "indentWidth": 2, "singleQuote": true, "proseWrap": "preserve"}}}