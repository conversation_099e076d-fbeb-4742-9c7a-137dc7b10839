import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type, Authorization",
  "Access-Control-Max-Age": "86400",
};

const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

const handler = async (req: Request): Promise<Response> => {
  // Handle preflight requests for CORS
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: {
        ...corsHeaders,
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
      },
    });
  }

  try {
    // Parse parameters from both URL and body
    let taskId: string | null = null;
    let action: string | null = null;
    let token: string | null = null;

    // Try URL parameters first (for GET requests)
    const url = new URL(req.url);
    const urlParams = {
      taskId: url.searchParams.get("taskId"),
      action: url.searchParams.get("action"),
      token: url.searchParams.get("token"),
    };

    // If method is POST, try to get parameters from body
    let bodyParams: { taskId?: string; action?: string; token?: string } = {};
    if (req.method === "POST") {
      try {
        const body = await req.json();
        bodyParams = {
          taskId: body.taskId,
          action: body.action,
          token: body.token,
        };
      } catch (e) {
        console.error("Error parsing request body:", e);
      }
    }

    // Use body params if available, otherwise fall back to URL params
    taskId = bodyParams?.taskId || urlParams.taskId;
    action = bodyParams?.action || urlParams.action;
    token = bodyParams?.token || urlParams.token;

    console.log("Processing request with parameters:", { taskId, action, token });

    if (!taskId || !action || !token) {
      return new Response(
        JSON.stringify({
          error: "Missing required parameters",
          provided: { taskId: !!taskId, action: !!action, token: !!token },
          message: "Please provide taskId, action, and token parameters",
        }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Only allow accept or decline actions
    // Also handle 'reject' for backward compatibility
    if (action !== "accept" && action !== "decline" && action !== "reject") {
      return new Response(
        JSON.stringify({
          error: "Invalid action",
          message: "Action must be either 'accept', 'decline', or 'reject'",
        }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Normalize action - treat 'reject' as 'decline'
    if (action === "reject") {
      action = "decline";
      console.log("Normalized action from 'reject' to 'decline'");
    }

    // Initialize Supabase admin client
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Verify token
    const isValid = await verifyToken(taskId, token);
    if (!isValid) {
      return new Response(
        JSON.stringify({
          error: "Invalid token",
          message: "The provided token is not valid for this task",
        }),
        {
          status: 401,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Get task details
    const { data: task, error: taskError } = await supabase
      .from("maintenance_tasks")
      .select("*")
      .eq("id", taskId)
      .maybeSingle();

    console.log("Task details:", task);

    if (taskError) {
      console.error("Error fetching task:", taskError);
      return new Response(
        JSON.stringify({
          error: "Database error",
          message: "Error fetching task details",
          details: taskError.message,
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    if (!task) {
      return new Response(
        JSON.stringify({
          error: "Not found",
          message: "Task not found",
        }),
        {
          status: 404,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Only allow updates to pending or open tasks
    console.log('Current task status:', task.status);
    if (task.status !== "pending" && task.status !== "open") {
      // Check if the task is already in the state we're trying to set it to
      const requestedStatus = action === "accept" ? "accepted" : "declined";

      if (task.status === requestedStatus) {
        // If the task is already in the requested state, return a success response
        return new Response(
          JSON.stringify({
            success: true,
            message: `Task is already ${task.status}`,
            taskId,
            status: task.status,
          }),
          {
            status: 200,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      } else {
        // If the task is in a different state, return an error
        return new Response(
          JSON.stringify({
            error: "Invalid state",
            message: `Task has already been ${task.status}`,
          }),
          {
            status: 400,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }
    }

    // Update task status
    const newStatus = action === "accept" ? "accepted" : "declined";
    const { error: updateError } = await supabase
      .from("maintenance_tasks")
      .update({
        status: newStatus,
        updated_at: new Date().toISOString(),
      })
      .eq("id", taskId);

    if (updateError) {
      console.error("Error updating task:", updateError);
      return new Response(
        JSON.stringify({
          error: "Database error",
          message: "Failed to update task status",
          details: updateError.message,
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Send notification if task has an owner
    if (task.user_id) {
      try {
        const { data: userProfile } = await supabase
          .from("profiles")
          .select("email")
          .eq("id", task.user_id)
          .single();

        if (userProfile?.email) {
          console.log(`Sending notification email to task owner: ${userProfile.email}`);
          try {
            const { data: emailResult, error: emailError } = await supabase.functions.invoke("send-email", {
              body: {
                to: userProfile.email,
                subject: `Maintenance Task ${newStatus}: ${task.title}`,
                html: `
                  <div style="font-family: Arial, sans-serif;">
                    <h2>Maintenance Task Update</h2>
                    <p>Your maintenance task has been ${newStatus}.</p>
                    <p><strong>Title:</strong> ${task.title}</p>
                    <p><strong>Status:</strong> ${newStatus}</p>
                    ${action === "decline"
                      ? "<p>You'll need to assign this task to another provider.</p>"
                      : "<p>The provider will proceed with the task.</p>"}
                    <p>You can view the full details in your maintenance dashboard.</p>
                  </div>
                `,
                from: "Maintenance Service <<EMAIL>>",
              },
            });

            if (emailError) {
              console.error("Error sending notification email:", emailError);
            } else {
              console.log("Notification email sent successfully:", emailResult);
            }
          } catch (emailInvokeError) {
            console.error("Error invoking send-email function:", emailInvokeError);
          }
        }
      } catch (emailError) {
        // Log but don't fail if email sending fails
        console.error("Error sending notification email:", emailError);
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        message: `Task ${newStatus} successfully`,
        taskId,
        status: newStatus,
      }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );

  } catch (err: unknown) {
    console.error("Unhandled error:", err);
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        message: "An unexpected error occurred",
        details: err instanceof Error ? err.message : String(err),
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
};

async function verifyToken(taskId: string, token: string): Promise<boolean> {
  try {
    if (!taskId || !token) {
      console.error("Missing taskId or token for verification");
      return false;
    }

    const encoder = new TextEncoder();
    const data = encoder.encode(taskId + "maintenance-response-secret");
    const hashBuffer = await crypto.subtle.digest("SHA-256", data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const generatedToken = hashArray.map(b => b.toString(16).padStart(2, "0")).join("").substring(0, 32);

    console.log("Token verification:", {
      generatedLength: generatedToken.length,
      receivedLength: token.length,
      match: token === generatedToken
    });

    return token === generatedToken;
  } catch (error) {
    console.error("Error verifying token:", error);
    return false;
  }
}

serve(handler);
