import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.20.0';
import { corsHeaders } from '../_shared/cors.ts';

// Log the environment variables for debugging
console.log('SUPABASE_URL:', Deno.env.get('SUPABASE_URL'));
console.log('SUPABASE_ANON_KEY:', Deno.env.get('SUPABASE_ANON_KEY')?.substring(0, 5) + '...');
console.log('SUPABASE_SERVICE_ROLE_KEY:', Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')?.substring(0, 5) + '...');

interface ConfirmEmailRequest {
  user_id: string;
  email: string;
}

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the Admin key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get the request body
    const { user_id, email } = await req.json() as ConfirmEmailRequest;

    // Validate required fields
    if (!user_id && !email) {
      return new Response(
        JSON.stringify({ error: 'Either user_id or email is required' }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }

    let userId = user_id;

    // If no user_id is provided but email is, try to find the user by email
    if (!userId && email) {
      console.log(`Looking up user by email: ${email}`);
      
      // List users to find the one with the matching email
      const { data: userList, error: userError } = await supabaseAdmin
        .auth.admin.listUsers();

      if (userError) {
        console.error('Error listing users:', userError);
        return new Response(
          JSON.stringify({ error: `Failed to list users: ${userError.message}` }),
          {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          }
        );
      }

      // Find the user by email
      const user = userList?.users?.find(u => u.email === email);
      
      if (!user) {
        return new Response(
          JSON.stringify({ error: 'User not found with the provided email' }),
          {
            status: 404,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          }
        );
      }
      
      userId = user.id;
      console.log(`Found user with ID: ${userId}`);
    }

    // Confirm the user's email
    console.log(`Confirming email for user: ${userId}`);
    const { data, error } = await supabaseAdmin.auth.admin.updateUserById(
      userId,
      { email_confirm: true }
    );

    if (error) {
      console.error('Error confirming user email:', error);
      return new Response(
        JSON.stringify({ error: `Failed to confirm email: ${error.message}` }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }

    console.log('Email confirmed successfully:', data);
    return new Response(
      JSON.stringify({
        success: true,
        user: data.user
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      }
    );
  } catch (error) {
    console.error('Unexpected error:', error);
    // Provide more detailed error information for debugging
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : '';

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        details: errorMessage,
        stack: errorStack
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      }
    );
  }
});
