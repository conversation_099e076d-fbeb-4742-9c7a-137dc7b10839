import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

const generateToken = () => {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Verify auth
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) throw new Error('No authorization header');

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token);

    if (authError || !user) throw new Error('Invalid token');

    // Generate new token
    const apiToken = generateToken();
    const hashedToken = await crypto.subtle.digest(
      'SHA-256',
      new TextEncoder().encode(apiToken)
    ).then(hash => Array.from(new Uint8Array(hash))
      .map(b => b.toString(16).padStart(2, '0'))
      .join(''));

    // Store hashed token
    const { error: insertError } = await supabaseClient
      .from('extension_api_tokens')
      .insert({
        user_id: user.id,
        token_hash: hashedToken
      });

    if (insertError) throw insertError;

    // Return the unhashed token - it will only be shown once
    return new Response(
      JSON.stringify({ 
        token: apiToken,
        message: 'Token generated successfully. Save this token - it will not be shown again.' 
      }),
      { 
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 200
      }
    );

  } catch (error) {
    // Handle unknown error type
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('Error generating extension token:', error); // Log the full error for debugging
    return new Response(
      JSON.stringify({ 
        error: errorMessage 
      }),
      { 
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json'
        },
        status: 400
      }
    );
  }
});
