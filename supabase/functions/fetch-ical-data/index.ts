import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1"
import ical from "https://esm.sh/ical@0.8.0"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EventItem {
  summary: any;
  start: Date;
  end: Date;
  formatted: string;
  startISODate: string;
  endISODate: string;
}

interface BookingInfo {
  start: Date;
  end: Date;
  formatted: string;
  startISODate?: string;
}

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const requestData = await req.json();
    const { url, propertyId, userId } = requestData;
    
    console.log("Request data:", { url, propertyId, userId });
    
    if (!url) {
      return new Response(
        JSON.stringify({ error: 'No iCal URL provided', success: false }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    console.log(`Fetching iCal data from URL: ${url} for property: ${propertyId}`)
    // Fetch the iCal data
    try {
      const response = await fetch(url);
      if (!response.ok) {
        console.error(`Failed to fetch iCal: ${response.status} ${response.statusText}`)
        return new Response(
          JSON.stringify({ error: `Failed to fetch iCal data: ${response.status} ${response.statusText}`, success: false }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
        )
      }

      const icalData = await response.text();
      if (!icalData || icalData.trim() === '') {
        console.error("Empty iCal data received");
        return new Response(
          JSON.stringify({ error: 'Empty iCal data received from URL', success: false }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
        );
      }
      
      console.log(`iCal data fetched, size: ${icalData.length} bytes`);
      
      try {
        const data = ical.parseICS(icalData);
        console.log(`Parsed iCal events: ${Object.keys(data).length}`);
        
        // Get current date in local time without time part
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        console.log(`Current date for comparison: ${today.toISOString().split('T')[0]}`);
        
        // Parse all events and categorize them
        const allEvents: EventItem[] = [];
        let currentBooking: BookingInfo | null = null;
        let nextBooking: BookingInfo | null = null;
        
        for (const key in data) {
          const event = data[key];
          if (event.type === 'VEVENT' && event.start && event.end) {
            // Clone dates to avoid modifying the original
            const startDate = new Date(event.start);
            // In iCal format, the end date is exclusive, so we need to subtract 1 day
            const endDate = new Date(event.end);
            endDate.setDate(endDate.getDate() - 1);
            
            const startISODate = startDate.toISOString().split('T')[0];
            const endISODate = endDate.toISOString().split('T')[0];
            const todayISODate = today.toISOString().split('T')[0];
            
            console.log(`Event: ${event.summary}, Start: ${startISODate}, End: ${endISODate}, Today: ${todayISODate}`);
            console.log(`Date comparisons: today >= start: ${todayISODate >= startISODate}, today <= end: ${todayISODate <= endISODate}`);
            
            // Format the dates for consistent display
            const startFormatted = formatDateConsistently(startDate);
            const endFormatted = formatDateConsistently(endDate);
            const formattedBooking = `${startFormatted} - ${endFormatted}`;
            
            // Add to all events
            allEvents.push({
              summary: event.summary,
              start: startDate,
              end: endDate,
              formatted: formattedBooking,
              startISODate,
              endISODate
            });
            
            // Check if this is a current booking (today is between start and end, inclusive)
            // Important: Use string comparison for dates to avoid timezone issues
            if (todayISODate >= startISODate && todayISODate <= endISODate) {
              console.log(`CURRENT BOOKING FOUND: ${formattedBooking}`);
              currentBooking = {
                start: startDate,
                end: endDate,
                formatted: formattedBooking
              };
            }
            // Check if this is an upcoming booking (start is after today)
            else if (startISODate > todayISODate && (!nextBooking || startISODate < nextBooking.startISODate)) {
              console.log(`NEXT BOOKING FOUND/UPDATED: ${formattedBooking}`);
              nextBooking = {
                start: startDate,
                end: endDate,
                formatted: formattedBooking,
                startISODate
              };
            }
          }
        }
        
        // Helper function for consistent date formatting
        function formatDateConsistently(date: Date): string {
          const months = ['January', 'February', 'March', 'April', 'May', 'June', 
                          'July', 'August', 'September', 'October', 'November', 'December'];
          const month = months[date.getMonth()];
          const day = date.getDate();
          const year = date.getFullYear();
          return `${month} ${day}, ${year}`;
        }
        
        // Determine what to store in the database
        let currentBookingValue: string | null = null;
        let nextBookingValue: string | null = null;
        let currentCheckout: string | null = null;
        let isOccupied = false;
        let nextCheckInISO: string | null = null;
        let nextCheckInFormatted: string | null = null;
        
        // Process current booking (if exists)
        if (currentBooking) {
          currentBookingValue = currentBooking.formatted;
          currentCheckout = formatDateConsistently(currentBooking.end);
          isOccupied = true;
          console.log(`Using current booking: ${currentBookingValue}, checkout: ${currentCheckout}`);
        }
        
        // Process next booking (if exists) - regardless of whether there's a current booking
        if (nextBooking) {
          // Store the next upcoming booking with individual date components to make parsing easier
          // Explicitly check for startISODate
          nextCheckInISO = nextBooking.startISODate || null;
          nextCheckInFormatted = formatDateConsistently(nextBooking.start);
          nextBookingValue = nextBooking.formatted;
          console.log(`Using next booking: ${nextBookingValue}, check-in: ${nextCheckInFormatted}`);
        }
        
        // If property ID is provided, update the property record with the booking info
        if (propertyId && userId) {
          // Safely access Deno.env with optional chaining
          // @ts-ignore - Deno exists at runtime but TypeScript doesn't know about it
          const supabaseUrl = Deno?.env?.get?.('SUPABASE_URL') ?? '';
          // @ts-ignore - Deno exists at runtime but TypeScript doesn't know about it
          const supabaseServiceKey = Deno?.env?.get?.('SUPABASE_SERVICE_ROLE_KEY') ?? '';
          
          if (!supabaseUrl || !supabaseServiceKey) {
            console.error('Missing Supabase credentials');
            return new Response(
              JSON.stringify({ error: 'Server configuration error', success: false }),
              { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
            )
          }
          
          console.log(`Updating property ${propertyId} with booking info:`, { 
            nextBooking: nextBookingValue,
            isOccupied,
            currentCheckout,
            nextCheckInISO,
            nextCheckInFormatted
          });
          
          const supabase = createClient(supabaseUrl, supabaseServiceKey);
          
          try {
            // Store both the current booking status and next booking information
            const updateData = { 
              next_booking: nextBookingValue,
              is_occupied: isOccupied,
              current_checkout: currentCheckout,
              next_checkin_date: nextCheckInISO, // ISO format date for reliable parsing
              next_checkin_formatted: nextCheckInFormatted, // Human-readable format
              last_ical_sync: new Date().toISOString()
            };
            
            console.log("Update data:", updateData);
            
            // Attempt to update with all fields
            const { error: updateError } = await supabase
              .from('properties')
              .update(updateData)
              .eq('id', propertyId);
              
            if (updateError) {
              throw updateError;
            }
            
            console.log('Successfully updated property with booking data');
          } catch (dbError) {
            console.error('Database update error:', dbError);
            return new Response(
              JSON.stringify({ 
                error: 'Failed to update property with booking data', 
                details: dbError, 
                success: false 
              }),
              { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
            );
          }
        }
        
        return new Response(
          JSON.stringify({ 
            currentBooking: currentBooking ? currentBooking.formatted : null,
            nextBooking: nextBookingValue,
            nextCheckInISO: nextCheckInISO,
            nextCheckInFormatted: nextCheckInFormatted,
            isOccupied: !!currentBooking,
            allEvents: allEvents.length,
            success: true
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
        );
      } catch (parseError) {
        console.error("Error parsing iCal data:", parseError);
        return new Response(
          JSON.stringify({ 
            error: 'Failed to parse iCal data', 
            details: parseError.message,
            success: false
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
        );
      }
    } catch (fetchError) {
      console.error("Error fetching from URL:", fetchError);
      return new Response(
        JSON.stringify({ 
          error: 'Failed to fetch from URL', 
          details: fetchError.message,
          success: false
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      );
    }
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(
      JSON.stringify({ error: 'An unexpected error occurred', details: error.message, success: false }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
