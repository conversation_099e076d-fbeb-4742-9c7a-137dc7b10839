
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";

interface UploadUrlRequest {
  fileName: string;
}

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the service role key
    const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY is not set");
    }
    
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);
    
    // Verify that the user is a super admin
    const authHeader = req.headers.get("Authorization") || "";
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      throw new Error("Missing or invalid authorization header");
    }
    
    const token = authHeader.substring(7);
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      throw new Error("Authentication failed");
    }
    
    // Check if user is a super admin
    const { data: profile, error: profileError } = await supabaseAdmin
      .from("profiles")
      .select("is_super_admin")
      .eq("id", user.id)
      .single();
    
    if (profileError || !profile || !profile.is_super_admin) {
      throw new Error("Only super admins can perform database operations");
    }
    
    // Parse request body
    const requestData: UploadUrlRequest = await req.json();
    
    if (!requestData.fileName) {
      throw new Error("fileName is required");
    }
    
    // Create a temporary bucket for database backups if it doesn't exist
    const { data: buckets } = await supabaseAdmin
      .storage
      .listBuckets();
    
    const databaseBackupsBucket = "database-backups";
    if (!buckets?.find(b => b.name === databaseBackupsBucket)) {
      const { error: bucketError } = await supabaseAdmin
        .storage
        .createBucket(databaseBackupsBucket, {
          public: false,
        });
      
      if (bucketError) {
        throw new Error(`Failed to create storage bucket: ${bucketError.message}`);
      }
    }
    
    // Generate a unique file key
    const timestamp = new Date().getTime();
    const fileKey = `${timestamp}-${requestData.fileName}`;
    
    // Generate a signed URL for uploading
    const { data: uploadData, error: uploadError } = await supabaseAdmin
      .storage
      .from(databaseBackupsBucket)
      .createSignedUploadUrl(fileKey);
    
    if (uploadError) {
      throw new Error(`Failed to create signed upload URL: ${uploadError.message}`);
    }
    
    return new Response(
      JSON.stringify({
        uploadUrl: uploadData.signedUrl,
        fileKey: fileKey,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error in admin-get-upload-url function:", error);
    return new Response(
      JSON.stringify({ error: error.message || "Failed to create upload URL" }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
