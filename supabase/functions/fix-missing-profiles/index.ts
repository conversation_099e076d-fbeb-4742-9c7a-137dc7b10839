// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/deploy_node_server

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get all users from auth.users
    const { data: users, error: usersError } = await supabaseAdmin.auth.admin.listUsers()

    if (usersError) {
      console.error('Error fetching users:', usersError)
      return new Response(
        JSON.stringify({
          success: false,
          error: usersError.message
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500
        }
      )
    }

    // Get all existing profiles
    const { data: profiles, error: profilesError } = await supabaseAdmin
      .from('profiles')
      .select('id')

    if (profilesError) {
      console.error('Error fetching profiles:', profilesError)
      return new Response(
        JSON.stringify({
          success: false,
          error: profilesError.message
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500
        }
      )
    }

    // Create a set of existing profile IDs for quick lookup
    const existingProfileIds = new Set(profiles?.map(profile => profile.id) || [])

    // Find users without profiles
    const usersWithoutProfiles = users.users.filter(user => !existingProfileIds.has(user.id))

    console.log(`Found ${usersWithoutProfiles.length} users without profiles`)

    // Create profiles for users without them
    const results = []
    for (const user of usersWithoutProfiles) {
      try {
        const { error: createError } = await supabaseAdmin
          .from('profiles')
          .insert({
            id: user.id,
            email: user.email,
            first_name: user.user_metadata?.first_name || 'User',
            last_name: user.user_metadata?.last_name || '',
            role: user.user_metadata?.role || 'property_manager',
            is_super_admin: user.user_metadata?.is_super_admin || false,
            created_at: new Date(),
            updated_at: new Date()
          })

        if (createError) {
          console.error(`Error creating profile for user ${user.id}:`, createError)
          results.push({
            user_id: user.id,
            email: user.email,
            success: false,
            error: createError.message
          })
        } else {
          console.log(`Created profile for user ${user.id}`)
          results.push({
            user_id: user.id,
            email: user.email,
            success: true
          })
        }
      } catch (err) {
        console.error(`Exception creating profile for user ${user.id}:`, err)
        results.push({
          user_id: user.id,
          email: user.email,
          success: false,
          error: err.message
        })
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        total_users: users.users.length,
        users_without_profiles: usersWithoutProfiles.length,
        results
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )
  } catch (err) {
    console.error('Unexpected error:', err)
    return new Response(
      JSON.stringify({
        success: false,
        error: err.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})
