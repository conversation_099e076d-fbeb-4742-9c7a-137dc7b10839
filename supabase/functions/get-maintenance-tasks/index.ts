import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get the request body
    const { userId } = await req.json()

    if (!userId) {
      return new Response(
        JSON.stringify({ error: 'User ID is required' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    console.log(`Getting maintenance tasks for user ${userId}`)

    // Get user role to determine which tasks to fetch
    const { data: userProfile, error: profileError } = await supabase
      .from('profiles')
      .select('role, is_super_admin')
      .eq('id', userId)
      .single()

    if (profileError) {
      console.error('Error fetching user profile:', profileError)
      return new Response(
        JSON.stringify({ error: 'Error fetching user profile', details: profileError }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }

    const isAdmin = userProfile?.is_super_admin || userProfile?.role === 'admin'
    const isServiceProvider = userProfile?.role === 'service_provider'

    let tasks = []

    if (isAdmin) {
      // Admins see all tasks
      const { data, error } = await supabase
        .from('maintenance_tasks')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        throw error
      }
      tasks = data
    } else if (isServiceProvider) {
      // Service providers see tasks they created or are assigned to
      const { data: providerData } = await supabase
        .from('service_providers')
        .select('email')
        .eq('id', userId)
        .single()

      const providerEmail = providerData?.email

      let query = supabase.from('maintenance_tasks').select('*')
      if (providerEmail) {
        query = query.or(`user_id.eq.${userId},provider_id.eq.${userId},provider_email.eq.${providerEmail}`)
      } else {
        query = query.or(`user_id.eq.${userId},provider_id.eq.${userId}`)
      }

      const { data, error } = await query.order('created_at', { ascending: false })
      if (error) {
        throw error
      }
      tasks = data
    } else {
      // Get user role
      const { data: userRoleData } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', userId)
        .single()

      const userRole = userRoleData?.role
      console.log(`User role: ${userRole}`)

      // Staff members and regular users see tasks for their properties or team properties
      // First, get all team memberships
      const { data: teamMemberships } = await supabase
        .from('team_members')
        .select('team_id')
        .eq('user_id', userId)
        .eq('status', 'active')

      if (teamMemberships && teamMemberships.length > 0) {
        const teamIds = teamMemberships.map((tm) => tm.team_id)

        // Get all property IDs for these teams
        const { data: teamPropertiesData } = await supabase
          .from('team_properties')
          .select('property_id')
          .in('team_id', teamIds)

        if (teamPropertiesData && teamPropertiesData.length > 0) {
          const propertyIds = teamPropertiesData.map((tp) => tp.property_id)

          // IMPORTANT: For team members, query by property_id directly without team_id filter
          // This is because maintenance tasks might be associated with a different team
          // but for the same properties
          console.log(`Getting tasks for ${propertyIds.length} properties in user's teams`)

          // First try: Query by property_id directly
          const { data: propertyData, error: propertyError } = await supabase
            .from('maintenance_tasks')
            .select('*')
            .in('property_id', propertyIds)
            .order('created_at', { ascending: false })

          if (propertyError) {
            console.error('Error with direct property query:', propertyError)
            // Fallback to OR query
            const { data, error } = await supabase
              .from('maintenance_tasks')
              .select('*')
              .or(`property_id.in.(${propertyIds.join(',')}),user_id.eq.${userId}`)
              .order('created_at', { ascending: false })

            if (error) {
              throw error
            }
            tasks = data
          } else {
            console.log(`Found ${propertyData.length} tasks with direct property query`)
            tasks = propertyData
          }
        } else {
          // No team properties, just get tasks created by this user
          const { data, error } = await supabase
            .from('maintenance_tasks')
            .select('*')
            .eq('user_id', userId)
            .order('created_at', { ascending: false })

          if (error) {
            throw error
          }
          tasks = data
        }
      } else {
        // No team memberships, just get tasks created by this user
        const { data, error } = await supabase
          .from('maintenance_tasks')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false })

        if (error) {
          throw error
        }
        tasks = data
      }
    }

    console.log(`Found ${tasks.length} maintenance tasks`)

    return new Response(
      JSON.stringify(tasks),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
