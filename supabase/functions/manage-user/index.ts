import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4'
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'

// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders,
    })
  }

  try {
    // Get the authorization header from the request
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create a Supabase client with the service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''

    if (!supabaseUrl || !supabaseServiceKey) {
      return new Response(
        JSON.stringify({ error: 'Server configuration error' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Verify the user's token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized', details: authError?.message }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check if the user is a super admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_super_admin, role')
      .eq('id', user.id)
      .single()

    if (profileError) {
      return new Response(
        JSON.stringify({ error: 'Error fetching user profile', details: profileError.message }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const isSuperAdmin = profile?.is_super_admin || profile?.role === 'admin'
    if (!isSuperAdmin) {
      return new Response(
        JSON.stringify({ error: 'Forbidden: Only super admins can manage users' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse the request body
    const requestData = await req.json()
    const { action, userId, userData } = requestData

    if (!action || !userId) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    let result = null
    let error = null

    // Perform the requested action
    switch (action) {
      case 'update':
        if (!userData) {
          return new Response(
            JSON.stringify({ error: 'Missing user data for update' }),
            { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          )
        }

        // Update the user profile in the profiles table
        const { error: profileUpdateError } = await supabase
          .from('profiles')
          .update({
            first_name: userData.first_name,
            last_name: userData.last_name,
            email: userData.email,
            role: userData.role,
            is_super_admin: userData.is_super_admin,
          })
          .eq('id', userId)

        if (profileUpdateError) {
          error = profileUpdateError
          break
        }

        // Update the email in the auth.users table
        const { error: authUpdateError } = await supabase.auth.admin.updateUserById(
          userId,
          { email: userData.email }
        )

        if (authUpdateError) {
          error = authUpdateError
        } else {
          result = { message: 'User updated successfully' }
        }
        break

      case 'reset-password':
        if (!userData || !userData.password) {
          return new Response(
            JSON.stringify({ error: 'Missing password for reset' }),
            { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
          )
        }

        const { error: passwordUpdateError } = await supabase.auth.admin.updateUserById(
          userId,
          { password: userData.password }
        )

        if (passwordUpdateError) {
          error = passwordUpdateError
        } else {
          result = { message: 'Password updated successfully' }
        }
        break

      case 'delete':
        try {
          // First, delete any related records in the database
          // This is necessary to avoid foreign key constraint errors

          // Delete from user_preferences
          await supabase.from('user_preferences').delete().eq('user_id', userId)

          // Delete from user_permissions
          await supabase.from('user_permissions').delete().eq('user_id', userId)

          // Delete from team_members
          await supabase.from('team_members').delete().eq('user_id', userId)

          // Delete from team_invitations
          await supabase.from('team_invitations').delete().eq('user_id', userId)

          // Delete from damage_notes, damage_photos, and damage_invoices first
          await supabase.from('damage_notes').delete().eq('user_id', userId)
          await supabase.from('damage_photos').delete().eq('user_id', userId)
          await supabase.from('damage_invoices').delete().eq('user_id', userId)

          // Get all damage reports by this user
          const { data: damageReports } = await supabase
            .from('damage_reports')
            .select('id')
            .eq('user_id', userId)

          // Delete related records for each damage report
          if (damageReports && damageReports.length > 0) {
            const damageReportIds = damageReports.map((report: { id: string }) => report.id)

            // Delete from damage_notes
            await supabase.from('damage_notes').delete().in('damage_report_id', damageReportIds)

            // Delete from damage_photos
            await supabase.from('damage_photos').delete().in('damage_report_id', damageReportIds)

            // Delete from damage_invoices
            await supabase.from('damage_invoices').delete().in('damage_report_id', damageReportIds)
          }

          // Now delete the damage reports
          await supabase.from('damage_reports').delete().eq('user_id', userId)

          // Get all purchase orders by this user
          const { data: purchaseOrders } = await supabase
            .from('purchase_orders')
            .select('id')
            .eq('user_id', userId)

          // Delete related records for each purchase order
          if (purchaseOrders && purchaseOrders.length > 0) {
            const purchaseOrderIds = purchaseOrders.map((order: { id: string }) => order.id)

            // Delete from purchase_order_items
            await supabase.from('purchase_order_items').delete().in('purchase_order_id', purchaseOrderIds)
          }

          // Now delete the purchase orders
          await supabase.from('purchase_orders').delete().eq('user_id', userId)

          // Delete from maintenance_providers
          await supabase.from('maintenance_providers').delete().eq('user_id', userId)

          // Delete from inventory_items
          await supabase.from('inventory_items').delete().eq('user_id', userId)

          // Delete from maintenance_requests
          await supabase.from('maintenance_requests').delete().eq('user_id', userId)

          // Delete from collections
          await supabase.from('collections').delete().eq('user_id', userId)

          // Delete from properties
          await supabase.from('properties').delete().eq('user_id', userId)

          // Finally, delete from profiles
          await supabase.from('profiles').delete().eq('id', userId)

          // Finally, delete the user from auth.users
          const { error: deleteError } = await supabase.auth.admin.deleteUser(userId)

          if (deleteError) {
            error = deleteError
          } else {
            result = { message: 'User deleted successfully' }
          }
        } catch (deleteError: unknown) {
          console.error('Error in delete operation:', deleteError)
          const errorMessage = deleteError instanceof Error ? deleteError.message : String(deleteError)
          error = { message: `Database error deleting user: ${errorMessage}` }
        }
        break

      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }

    if (error) {
      return new Response(
        JSON.stringify({ error: error.message }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    return new Response(
      JSON.stringify(result),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: errorMessage }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
