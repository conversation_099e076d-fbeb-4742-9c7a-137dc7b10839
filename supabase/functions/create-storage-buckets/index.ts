// Edge function to create required storage buckets

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { ensureStorageBuckets } from "../_shared/ensure-buckets.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, x-client-application, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    console.log('[createStorageBuckets] Edge function test successful')
    
    // Ensure all required buckets exist
    const result = await ensureStorageBuckets()
    
    if (result) {
      return new Response(
        JSON.stringify({ 
          success: true,
          message: 'Storage buckets initialized successfully'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
      )
    } else {
      return new Response(
        JSON.stringify({ 
          success: false,
          message: 'Failed to initialize storage buckets'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }
  } catch (error) {
    console.error('Unexpected error:', error)
    return new Response(
      JSON.stringify({ error: 'An unexpected error occurred', details: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
