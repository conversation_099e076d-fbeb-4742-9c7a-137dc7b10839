import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

interface AutomationRule {
  id: string;
  user_id: string;
  name: string;
  trigger_type: 'check_in' | 'check_out';
  task_type: string;
  time_offset: number;
  property_ids: string[] | null;
  title: string;
  description: string;
  severity: string;
  assigned_to: string | null;
}

interface QueueItem {
  id: string;
  booking_id: string;
  processed: boolean;
  created_at: string;
  processed_at: string | null;
}

serve(async (_req) => {
  try {
    // Create a Supabase client with the service role key
    const supabaseAdmin = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // Get unprocessed queue items
    const { data: queueItems, error: queueError } = await supabaseAdmin
      .from('automation_queue')
      .select('*')
      .eq('processed', false)
      .order('created_at', { ascending: true })
      .limit(50); // Process in batches

    if (queueError) {
      return new Response(
        JSON.stringify({ error: `Error fetching queue: ${queueError.message}` }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }

    if (!queueItems || queueItems.length === 0) {
      return new Response(
        JSON.stringify({ message: "No items in the queue to process" }),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    }

    const results = [];

    // Process each queue item
    for (const item of queueItems) {
      try {
        // Get the booking details
        const { data: booking, error: bookingError } = await supabaseAdmin
          .from('bookings')
          .select('*, properties(*)')
          .eq('id', item.booking_id)
          .single();

        if (bookingError || !booking) {
          console.error(`Error fetching booking ${item.booking_id}:`, bookingError);

          // Mark as processed even if there was an error
          await supabaseAdmin
            .from('automation_queue')
            .update({
              processed: true,
              processed_at: new Date().toISOString()
            })
            .eq('id', item.id);

          results.push({
            queueId: item.id,
            bookingId: item.booking_id,
            success: false,
            error: bookingError ? bookingError.message : 'Booking not found'
          });

          continue;
        }

        // Get automation rules for the booking's user
        const { data: rules, error: rulesError } = await supabaseAdmin
          .from('automation_rules')
          .select('*')
          .eq('user_id', booking.user_id);

        if (rulesError) {
          console.error(`Error fetching rules for user ${booking.user_id}:`, rulesError);

          // Mark as processed even if there was an error
          await supabaseAdmin
            .from('automation_queue')
            .update({
              processed: true,
              processed_at: new Date().toISOString()
            })
            .eq('id', item.id);

          results.push({
            queueId: item.id,
            bookingId: item.booking_id,
            success: false,
            error: rulesError.message
          });

          continue;
        }

        if (!rules || rules.length === 0) {
          // No rules to process, mark as processed
          await supabaseAdmin
            .from('automation_queue')
            .update({
              processed: true,
              processed_at: new Date().toISOString()
            })
            .eq('id', item.id);

          results.push({
            queueId: item.id,
            bookingId: item.booking_id,
            success: true,
            tasksCreated: 0,
            message: 'No automation rules found for this user'
          });

          continue;
        }

        // Process each rule
        const createdTasks = [];

        for (const rule of rules) {
          // Skip if rule is property-specific and doesn't include this property
          if (rule.property_ids && !rule.property_ids.includes(booking.property_id)) {
            continue;
          }

          // Get property check-in/check-out times
          const checkInTime = booking.properties.check_in_time || '15:00:00';
          const checkOutTime = booking.properties.check_out_time || '11:00:00';

          // Calculate task date based on check-in or check-out with property-specific times
          let baseDate;
          if (rule.trigger_type === 'check_in') {
            // Format: YYYY-MM-DDT15:00:00 (using property's check-in time)
            const checkInDateTime = `${booking.check_in_date}T${checkInTime}`;
            baseDate = new Date(checkInDateTime);
          } else {
            // Format: YYYY-MM-DDT11:00:00 (using property's check-out time)
            const checkOutDateTime = `${booking.check_out_date}T${checkOutTime}`;
            baseDate = new Date(checkOutDateTime);
          }

          // Apply time offset (convert hours to milliseconds)
          const taskDate = new Date(baseDate.getTime() + (rule.time_offset * 60 * 60 * 1000));

          // Format the date as YYYY-MM-DD
          const formattedDate = taskDate.toISOString().split('T')[0];

          // Include the time in the task due date
          const hours = taskDate.getHours().toString().padStart(2, '0');
          const minutes = taskDate.getMinutes().toString().padStart(2, '0');
          const seconds = taskDate.getSeconds().toString().padStart(2, '0');
          const formattedDateTime = `${formattedDate} ${hours}:${minutes}:${seconds}`;

          // Replace {property} placeholder in title and description
          const title = rule.title.replace('{property}', booking.properties.name);
          const description = rule.description
            ? rule.description.replace('{property}', booking.properties.name)
            : '';

          // Create the task
          const { data: task, error: taskError } = await supabaseAdmin
            .from('maintenance_tasks')
            .insert({
              title,
              description,
              property_id: booking.property_id,
              property_name: booking.properties.name,
              severity: rule.severity,
              status: 'open',
              due_date: formattedDateTime,
              user_id: booking.user_id,
              assigned_to: rule.assigned_to
            })
            .select()
            .single();

          if (taskError) {
            console.error(`Error creating task for rule ${rule.id}:`, taskError);
            continue;
          }

          createdTasks.push(task);
        }

        // Mark queue item as processed
        await supabaseAdmin
          .from('automation_queue')
          .update({
            processed: true,
            processed_at: new Date().toISOString()
          })
          .eq('id', item.id);

        results.push({
          queueId: item.id,
          bookingId: item.booking_id,
          success: true,
          tasksCreated: createdTasks.length,
          tasks: createdTasks
        });

      } catch (error) {
        console.error(`Error processing queue item ${item.id}:`, error);

        // Mark as processed even if there was an error
        await supabaseAdmin
          .from('automation_queue')
          .update({
            processed: true,
            processed_at: new Date().toISOString()
          })
          .eq('id', item.id);

        results.push({
          queueId: item.id,
          bookingId: item.booking_id,
          success: false,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    return new Response(
      JSON.stringify({
        message: `Processed ${queueItems.length} queue items`,
        results
      }),
      { status: 200, headers: { "Content-Type": "application/json" } }
    );

  } catch (error) {
    return new Response(
      JSON.stringify({ error: `Unexpected error: ${error instanceof Error ? error.message : String(error)}` }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
});
