import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

interface AutomationRule {
  id: string;
  user_id: string;
  name: string;
  trigger_type: 'check_in' | 'check_out';
  task_type: string;
  time_offset: number;
  property_ids: string[] | null;
  title: string;
  description: string;
  severity: string;
  assigned_to: string | null;
}

interface Booking {
  id: string;
  property_id: string;
  check_in_date: string;
  check_out_date: string;
  user_id: string;
}

interface Property {
  id: string;
  name: string;
}

serve(async (req) => {
  try {
    // Get the request body
    const { bookingId } = await req.json();
    
    if (!bookingId) {
      return new Response(
        JSON.stringify({ error: "Missing bookingId parameter" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }
    
    // Create a Supabase client with the service role key
    const supabaseAdmin = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );
    
    // Get the booking details
    const { data: booking, error: bookingError } = await supabaseAdmin
      .from('bookings')
      .select('*, properties(*)')
      .eq('id', bookingId)
      .single();
      
    if (bookingError) {
      return new Response(
        JSON.stringify({ error: `Error fetching booking: ${bookingError.message}` }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
    
    if (!booking) {
      return new Response(
        JSON.stringify({ error: "Booking not found" }),
        { status: 404, headers: { "Content-Type": "application/json" } }
      );
    }
    
    // Get all automation rules for the booking's user
    const { data: rules, error: rulesError } = await supabaseAdmin
      .from('automation_rules')
      .select('*')
      .eq('user_id', booking.user_id);
      
    if (rulesError) {
      return new Response(
        JSON.stringify({ error: `Error fetching automation rules: ${rulesError.message}` }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
    
    if (!rules || rules.length === 0) {
      return new Response(
        JSON.stringify({ message: "No automation rules found for this user" }),
        { status: 200, headers: { "Content-Type": "application/json" } }
      );
    }
    
    // Process each rule
    const createdTasks = [];
    
    for (const rule of rules) {
      // Skip if rule is property-specific and doesn't include this property
      if (rule.property_ids && !rule.property_ids.includes(booking.property_id)) {
        continue;
      }
      
      // Calculate task date based on check-in or check-out
      const baseDate = rule.trigger_type === 'check_in' 
        ? new Date(booking.check_in_date) 
        : new Date(booking.check_out_date);
        
      // Apply time offset (convert hours to milliseconds)
      const taskDate = new Date(baseDate.getTime() + (rule.time_offset * 60 * 60 * 1000));
      
      // Format the date as YYYY-MM-DD
      const formattedDate = taskDate.toISOString().split('T')[0];
      
      // Replace {property} placeholder in title and description
      const title = rule.title.replace('{property}', booking.properties.name);
      const description = rule.description 
        ? rule.description.replace('{property}', booking.properties.name)
        : '';
      
      // Create the task
      const { data: task, error: taskError } = await supabaseAdmin
        .from('maintenance_tasks')
        .insert({
          title,
          description,
          property_id: booking.property_id,
          property_name: booking.properties.name,
          severity: rule.severity,
          status: 'open',
          due_date: formattedDate,
          user_id: booking.user_id,
          assigned_to: rule.assigned_to
        })
        .select()
        .single();
        
      if (taskError) {
        console.error(`Error creating task for rule ${rule.id}:`, taskError);
        continue;
      }
      
      createdTasks.push(task);
    }
    
    return new Response(
      JSON.stringify({ 
        message: `Created ${createdTasks.length} tasks for booking ${bookingId}`,
        tasks: createdTasks
      }),
      { status: 200, headers: { "Content-Type": "application/json" } }
    );
    
  } catch (error) {
    return new Response(
      JSON.stringify({ error: `Unexpected error: ${error.message}` }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
});
