
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { GoogleGenerativeAI } from "https://esm.sh/@google/generative-ai@0.2.1";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

const GEMINI_API_KEY = Deno.env.get("GEMINI_API_KEY") || "AIzaSyD55Kn_94EdiW7czvu8qZ4G6R76vRL563s";
const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "";
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

interface MaintenanceRequest {
  text: string;
  properties?: Array<{id: string, name: string}>;
  providers?: Array<{id: string, name: string}>;
  userId?: string;
  createTasks?: boolean; // If true, create tasks in database; if false, just return task data
}

// Base interface for AI response handlers
interface AiHandler {
  generatePrompt(request: MaintenanceRequest): string;
  parseResponse(responseText: string): unknown;
}

// Handler for maintenance tasks
class MaintenanceTaskHandler implements AiHandler {
  generatePrompt(request: MaintenanceRequest): string {
    const { text, properties = [], providers = [] } = request;
    
    // Include available properties and providers in the prompt
    let propertyContext = "";
    if (properties.length > 0) {
      propertyContext = `Available properties:\n${properties.map(p => `- ${p.name} (ID: ${p.id})`).join('\n')}\n\n`;
    }
    
    let providerContext = "";
    if (providers.length > 0) {
      providerContext = `Available service providers:\n${providers.map(p => `- ${p.name} (ID: ${p.id})`).join('\n')}\n\n`;
    }
    
    return `
      You are a maintenance task analyzer. Your job is to extract potential maintenance tasks from the provided text.
      ${propertyContext}
      ${providerContext}
      For each identified maintenance task, provide the following details in valid JSON format:
      [
        {
          "title": "Brief title of the task",
          "description": "Detailed description of what needs to be done",
          "severity": "low", "medium", "high", or "critical" based on urgency,
          "status": "new" (always set as default),
          "propertyName": "Name of the property if mentioned in the text and matches one of the available properties",
          "propertyId": "ID of the property if it can be identified from the available properties list",
          "dueDate": "A specific due date if mentioned in the format YYYY-MM-DD, or a relative time frame like '2 weeks', '7 days', 'next month'",
          "providerId": "ID of the service provider if one is mentioned and can be matched to the available providers list",
          "providerName": "Name of the service provider if mentioned in the text"
        },
        ...
      ]
      Only return the JSON array, nothing else. Ensure the JSON is valid.
      
      Text to analyze: ${text}
    `;
  }

  parseResponse(responseText: string): unknown {
    // Extract JSON if it's within markdown code blocks or any other formatting
    const jsonMatch = responseText.match(/\[\s*\{.*\}\s*\]/s);
    const jsonText = jsonMatch ? jsonMatch[0] : responseText;
    const parsedResponse = JSON.parse(jsonText);
    
    if (!Array.isArray(parsedResponse)) {
      throw new Error("Response is not an array");
    }
    
    return parsedResponse;
  }
}

// Factory for creating appropriate handlers based on request type
function createHandler(type: string = 'maintenance'): AiHandler {
  switch (type) {
    case 'maintenance':
      return new MaintenanceTaskHandler();
    // Future handlers can be added here
    default:
      return new MaintenanceTaskHandler();
  }
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (!GEMINI_API_KEY) {
      throw new Error("GEMINI_API_KEY is not set");
    }

    const requestData: MaintenanceRequest = await req.json();
    const { text, properties, providers, userId, createTasks = false } = requestData;

    if (!text || text.trim() === "") {
      throw new Error("Text input is required");
    }

    console.log("Processing text input:", text);
    console.log("Properties provided:", properties?.length || 0);
    console.log("Providers provided:", providers?.length || 0);
    console.log("Using API key:", GEMINI_API_KEY.substring(0, 5) + "...");

    // Initialize the Gemini API
    const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({
      model: "gemini-1.5-flash",
      generationConfig: {
        temperature: 1,
        topP: 0.95,
        topK: 40,
        maxOutputTokens: 8192,
      },
    });

    // Get appropriate handler for this request type
    const aiHandler = createHandler();
    
    // Generate prompt using the handler
    const prompt = aiHandler.generatePrompt(requestData);

    console.log("Sending prompt to Gemini API");
    
    // Call the Gemini API
    const result = await model.generateContent(prompt);
    const response = result.response;
    const textResponse = response.text();
    
    console.log("Gemini API response received");
    
    // Process the response to ensure it's valid JSON
    try {
      const parsedResponse = aiHandler.parseResponse(textResponse);

      // If createTasks is true and we have a userId, create the tasks in the database
      if (createTasks && userId && Array.isArray(parsedResponse)) {
        if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
          throw new Error("Supabase environment variables are not set");
        }

        // Initialize Supabase client with service role key for admin access
        const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

        const createdTasks = [];
        const errors = [];

        for (const task of parsedResponse) {
          try {
            // Validate property access if propertyId is provided
            let validPropertyId = null;
            let teamId = null;

            if (task.propertyId) {
              // Check if property exists and user has access
              const { data: propertyData, error: propertyError } = await supabase
                .from('properties')
                .select('id, user_id')
                .eq('id', task.propertyId)
                .limit(1);

              if (!propertyError && propertyData && propertyData.length > 0) {
                const property = propertyData[0];

                // Check if user owns the property or has team access
                if (property.user_id === userId) {
                  validPropertyId = task.propertyId;
                } else {
                  // Check team access
                  const { data: teamPropertyData, error: teamPropertyError } = await supabase
                    .from('team_properties')
                    .select('team_id, team_members!inner(user_id, status)')
                    .eq('property_id', task.propertyId)
                    .eq('team_members.user_id', userId)
                    .eq('team_members.status', 'active')
                    .limit(1);

                  if (!teamPropertyError && teamPropertyData && teamPropertyData.length > 0) {
                    validPropertyId = task.propertyId;
                    teamId = teamPropertyData[0].team_id;
                  }
                }
              }
            }

            // Create the task in the database
            const { data: newTask, error: insertError } = await supabase
              .from("maintenance_tasks")
              .insert({
                user_id: userId,
                title: task.title || 'Untitled Task',
                description: task.description || '',
                property_id: validPropertyId,
                property_name: task.propertyName || 'General',
                severity: task.severity || 'medium',
                status: 'open',
                due_date: task.dueDate === 'No due date' ? null : task.dueDate,
                provider_id: task.providerId || null,
                team_id: teamId
              })
              .select()
              .single();

            if (insertError) {
              console.error("Error creating task:", insertError);
              errors.push(`Failed to create task "${task.title}": ${insertError.message}`);
            } else {
              createdTasks.push(newTask);
            }
          } catch (taskError) {
            console.error("Error processing individual task:", taskError);
            errors.push(`Failed to process task "${task.title}": ${taskError instanceof Error ? taskError.message : 'Unknown error'}`);
          }
        }

        return new Response(JSON.stringify({
          success: true,
          createdTasks,
          errors: errors.length > 0 ? errors : undefined,
          message: `Successfully created ${createdTasks.length} of ${parsedResponse.length} tasks`
        }), {
          status: 200,
          headers: {
            "Content-Type": "application/json",
            ...corsHeaders,
          },
        });
      }

      // Default behavior: just return the parsed task data
      return new Response(JSON.stringify(parsedResponse), {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      });
    } catch (e) {
      console.error("Error parsing Gemini response as JSON:", e);
      console.log("Raw response:", textResponse);
      throw new Error("Failed to parse AI response as JSON");
    }
  } catch (error) {
    console.error("Error in AI maintenance items function:", error);
    return new Response(
      JSON.stringify({ 
        error: error instanceof Error ? error.message : "An error occurred while processing the request"
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

serve(handler);
