
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";

const EMAILIT_API_KEY = Deno.env.get("EMAILIT_API_KEY");

interface EmailData {
  from: string;
  to: string;
  subject: string;
  html?: string;
  text?: string;
  reply_to?: string;
}

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
};

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("Received email request");
    const emailData: EmailData = await req.json();

    // Log details (without sensitive content)
    console.log("Email request details:", {
      from: emailData.from,
      to: emailData.to,
      subject: emailData.subject,
      hasHtml: !!emailData.html,
      hasText: !!emailData.text,
      hasReplyTo: !!emailData.reply_to
    });

    // Validate required fields
    if (!emailData.from || !emailData.to || !emailData.subject) {
      throw new Error("Missing required email fields: from, to, and subject are required");
    }

    // At least one of text or html must be provided
    if (!emailData.html && !emailData.text) {
      throw new Error("Either html or text content must be provided");
    }

    if (!EMAILIT_API_KEY) {
      console.error("EMAILIT_API_KEY environment variable not set");
      throw new Error("Email service not properly configured");
    }

    console.log("Sending email to", emailData.to);

    // Development mode email simulation
    if (Deno.env.get('NODE_ENV') === 'development' || !EMAILIT_API_KEY) {
      console.log("DEVELOPMENT MODE: Email would be sent with this content:", {
        to: emailData.to,
        subject: emailData.subject,
        contentPreview: emailData.html?.substring(0, 100) || emailData.text?.substring(0, 100),
      });

      return new Response(
        JSON.stringify({ id: "mock-email-id", success: true }),
        {
          status: 200,
          headers: {
            "Content-Type": "application/json",
            ...corsHeaders,
          },
        }
      );
    }

    // Real email sending in production
    const response = await fetch("https://api.emailit.com/v1/emails", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${EMAILIT_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        from: emailData.from,
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text,
        reply_to: emailData.reply_to
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error response from Emailit API:", errorData);
      throw new Error(errorData.message || "Failed to send email");
    }

    const result = await response.json();
    console.log("Email sent successfully:", result.id);

    return new Response(JSON.stringify(result), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders,
      },
    });
  } catch (error: any) {
    console.error("Error in send-email function:", error);
    return new Response(
      JSON.stringify({ error: error.message || "An error occurred while sending the email" }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

serve(handler);
