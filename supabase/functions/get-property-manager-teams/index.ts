import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, x-client-application, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get the request body
    const { userId } = await req.json()

    if (!userId) {
      return new Response(
        JSON.stringify({ error: 'User ID is required' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    console.log(`Getting teams for property manager: ${userId}`)

    // First verify the user is a property manager
    const { data: userProfile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single()
    
    if (profileError) {
      console.error('Error fetching user profile:', profileError)
      return new Response(
        JSON.stringify({ error: 'Error fetching user profile', details: profileError }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }
    
    if (userProfile.role !== 'property_manager' && userProfile.role !== 'admin' && userProfile.role !== 'super_admin') {
      return new Response(
        JSON.stringify({ error: 'User is not a property manager or admin' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 403,
        }
      )
    }
    
    // Get teams owned by the user
    const { data: ownedTeams, error: ownedTeamsError } = await supabase
      .from('teams')
      .select('*, team_members:team_members(count)')
      .eq('owner_id', userId)
    
    if (ownedTeamsError) {
      console.error('Error fetching owned teams:', ownedTeamsError)
      return new Response(
        JSON.stringify({ error: 'Error fetching owned teams', details: ownedTeamsError }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }
    
    // Get team memberships
    const { data: teamMemberships, error: teamMembershipsError } = await supabase
      .from('team_members')
      .select('team_id')
      .eq('user_id', userId)
      .eq('status', 'active')
    
    if (teamMembershipsError) {
      console.error('Error fetching team memberships:', teamMembershipsError)
      return new Response(
        JSON.stringify({ error: 'Error fetching team memberships', details: teamMembershipsError }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }
    
    const teamIds = teamMemberships?.map(tm => tm.team_id) || []
    
    // Get teams where the user is a member but not the owner
    let memberTeams = []
    if (teamIds.length > 0) {
      const { data: teams, error: teamsError } = await supabase
        .from('teams')
        .select('*, team_members:team_members(count)')
        .neq('owner_id', userId)
        .in('id', teamIds)
      
      if (teamsError) {
        console.error('Error fetching member teams:', teamsError)
        return new Response(
          JSON.stringify({ error: 'Error fetching member teams', details: teamsError }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
          }
        )
      }
      
      memberTeams = teams || []
    }
    
    // Combine and format teams
    const allTeams = [...(ownedTeams || []), ...memberTeams]
    const teamsWithMemberCount = allTeams.map(team => ({
      ...team,
      member_count: team.team_members?.[0]?.count || 0
    }))
    
    console.log(`Found ${teamsWithMemberCount.length} teams for user ${userId}`)
    
    return new Response(
      JSON.stringify(teamsWithMemberCount),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
