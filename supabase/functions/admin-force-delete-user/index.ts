import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4'
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'

// Define CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders,
    })
  }

  try {
    // Get the authorization header from the request
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create a Supabase client with the service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''

    if (!supabaseUrl || !supabaseServiceKey) {
      return new Response(
        JSON.stringify({ error: 'Server configuration error' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Verify the user's token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized', details: authError?.message }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check if the user is a super admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_super_admin, role')
      .eq('id', user.id)
      .single()

    if (profileError) {
      return new Response(
        JSON.stringify({ error: 'Error fetching user profile', details: profileError.message }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const isSuperAdmin = profile?.is_super_admin || profile?.role === 'admin'
    if (!isSuperAdmin) {
      return new Response(
        JSON.stringify({ error: 'Forbidden: Only super admins can force delete users' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse the request body
    const requestData = await req.json()
    const { userId } = requestData

    if (!userId) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameter: userId' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Force deleting user with ID: ${userId}`)

    try {
      // Delete each related record individually instead of using session_replication_role
      // This approach doesn't require special permissions

      console.log(`Force deleting user data for ID: ${userId}`);

      // Delete from user_preferences
      const { error: prefError } = await supabase.from('user_preferences').delete().eq('user_id', userId);
      if (prefError) console.log('Error deleting user_preferences:', prefError.message);

      // Delete from user_permissions
      const { error: permError } = await supabase.from('user_permissions').delete().eq('user_id', userId);
      if (permError) console.log('Error deleting user_permissions:', permError.message);

      // Delete from team_members
      const { error: teamMemberError } = await supabase.from('team_members').delete().eq('user_id', userId);
      if (teamMemberError) console.log('Error deleting team_members:', teamMemberError.message);

      // Delete from team_invitations
      const { error: inviteError } = await supabase.from('team_invitations').delete().eq('user_id', userId);
      if (inviteError) console.log('Error deleting team_invitations:', inviteError.message);

      // Delete from damage_notes
      const { error: notesError } = await supabase.from('damage_notes').delete().eq('user_id', userId);
      if (notesError) console.log('Error deleting damage_notes:', notesError.message);

      // Delete from damage_photos
      const { error: photosError } = await supabase.from('damage_photos').delete().eq('user_id', userId);
      if (photosError) console.log('Error deleting damage_photos:', photosError.message);

      // Delete from damage_invoices
      const { error: invoicesError } = await supabase.from('damage_invoices').delete().eq('user_id', userId);
      if (invoicesError) console.log('Error deleting damage_invoices:', invoicesError.message);

      // Delete from damage_reports
      const { error: reportsError } = await supabase.from('damage_reports').delete().eq('user_id', userId);
      if (reportsError) console.log('Error deleting damage_reports:', reportsError.message);

      // Get purchase order IDs first
      const { data: purchaseOrders, error: poQueryError } = await supabase
        .from('purchase_orders')
        .select('id')
        .eq('user_id', userId);

      if (poQueryError) {
        console.log('Error querying purchase orders:', poQueryError.message);
      } else if (purchaseOrders && purchaseOrders.length > 0) {
        const poIds = purchaseOrders.map((po: { id: string }) => po.id);

        // Delete from purchase_order_items
        const { error: poItemsError } = await supabase
          .from('purchase_order_items')
          .delete()
          .in('purchase_order_id', poIds);

        if (poItemsError) console.log('Error deleting purchase_order_items:', poItemsError.message);
      }

      // Delete from purchase_orders
      const { error: poError } = await supabase.from('purchase_orders').delete().eq('user_id', userId);
      if (poError) console.log('Error deleting purchase_orders:', poError.message);

      // Delete from maintenance_providers
      const { error: mpError } = await supabase.from('maintenance_providers').delete().eq('user_id', userId);
      if (mpError) console.log('Error deleting maintenance_providers:', mpError.message);

      // Delete from inventory_items
      const { error: invError } = await supabase.from('inventory_items').delete().eq('user_id', userId);
      if (invError) console.log('Error deleting inventory_items:', invError.message);

      // Delete from maintenance_tasks
      const { error: mtError } = await supabase.from('maintenance_tasks').delete().eq('user_id', userId);
      if (mtError) console.log('Error deleting maintenance_tasks:', mtError.message);

      // Delete from collections
      const { error: colError } = await supabase.from('collections').delete().eq('user_id', userId);
      if (colError) console.log('Error deleting collections:', colError.message);

      // Delete from properties
      const { error: propError } = await supabase.from('properties').delete().eq('user_id', userId);
      if (propError) console.log('Error deleting properties:', propError.message);

      // Delete from service_providers
      const { error: spError } = await supabase.from('service_providers').delete().eq('user_id', userId);
      if (spError) console.log('Error deleting service_providers:', spError.message);

      // Delete from profiles
      const { error: profileError } = await supabase.from('profiles').delete().eq('id', userId);
      if (profileError) console.log('Error deleting profile:', profileError.message);

      // Finally, delete the user from auth.users
      console.log('Deleting auth user...');
      const { error: deleteError } = await supabase.auth.admin.deleteUser(userId);

      if (deleteError) {
        console.error('Error deleting auth user:', deleteError);
        // Continue anyway since we've already cleaned up the database
        return new Response(
          JSON.stringify({
            message: 'User data deleted from database, but could not delete auth user',
            details: deleteError.message
          }),
          { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      return new Response(
        JSON.stringify({ message: 'User force deleted successfully' }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    } catch (error: unknown) {
      console.error('Error in force delete operation:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return new Response(
        JSON.stringify({ error: `Database error force deleting user: ${errorMessage}` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: errorMessage }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
