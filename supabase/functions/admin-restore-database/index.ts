
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";

interface RestoreRequest {
  fileKey?: string;
  previewMode?: boolean;
  fileContents?: Record<string, any>;
}

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the service role key
    const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY is not set");
    }
    
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);
    
    // Verify that the user is a super admin
    const authHeader = req.headers.get("Authorization") || "";
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return new Response(
        JSON.stringify({ 
          error: "Missing or invalid authorization header",
          success: false
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 401,
        }
      );
    }
    
    const token = authHeader.substring(7);
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ 
          error: "Authentication failed",
          success: false
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 401,
        }
      );
    }
    
    // Check if user is a super admin
    const { data: profile, error: profileError } = await supabaseAdmin
      .from("profiles")
      .select("is_super_admin")
      .eq("id", user.id)
      .single();
    
    if (profileError || !profile || !profile.is_super_admin) {
      return new Response(
        JSON.stringify({ 
          error: "Only super admins can perform database restores",
          success: false
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 403,
        }
      );
    }
    
    // Parse request body
    const requestData: RestoreRequest = await req.json();
    
    // Set up backup data variable
    let backup: Record<string, any>;
    
    // Handle direct file content mode (for preview)
    if (requestData.fileContents) {
      backup = requestData.fileContents;
    }
    // Handle storage file key mode
    else if (requestData.fileKey) {
      // Download the backup file from storage
      const { data: fileData, error: fileError } = await supabaseAdmin
        .storage
        .from("database-backups")
        .download(requestData.fileKey);
      
      if (fileError) {
        return new Response(
          JSON.stringify({ 
            error: `Failed to download backup file: ${fileError.message}`,
            success: false
          }),
          {
            headers: { ...corsHeaders, "Content-Type": "application/json" },
            status: 500,
          }
        );
      }
      
      // Parse the backup data
      const backupText = await fileData.text();
      try {
        backup = JSON.parse(backupText);
      } catch (error) {
        return new Response(
          JSON.stringify({ 
            error: "Invalid backup file format - couldn't parse JSON",
            success: false
          }),
          {
            headers: { ...corsHeaders, "Content-Type": "application/json" },
            status: 400,
          }
        );
      }
    } else {
      return new Response(
        JSON.stringify({ 
          error: "Either fileKey or fileContents is required",
          success: false
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }
    
    if (!backup.tables || typeof backup.tables !== "object") {
      return new Response(
        JSON.stringify({ 
          error: "Invalid backup format: Missing or invalid 'tables' property",
          success: false
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }
    
    // Check if we're in preview mode
    const previewMode = !!requestData.previewMode;
    
    // Initialize results tracking
    const results: Record<string, any> = {
      tables: {},
      success: true,
      previewMode,
      tablesFound: Object.keys(backup.tables).length,
      hasData: false
    };
    
    // Get list of all tables from the database for better validation
    const { data: availableTables, error: tablesError } = await supabaseAdmin
      .rpc('get_all_tables');
    
    if (tablesError) {
      console.error("Failed to get available tables:", tablesError);
    }
    
    const tableNames = availableTables ? availableTables.map((t: any) => t.tablename) : [];
    console.log("Available tables:", tableNames);
    
    // Basic validation of the backup file structure
    for (const [tableName, tableData] of Object.entries(backup.tables)) {
      // Skip system tables
      if (tableName === "schema_migrations") {
        continue;
      }
      
      results.tables[tableName] = {
        exists: false,
        dataRows: 0,
        columnsMatch: false,
        error: null
      };

      // Check if the table exists in the database
      const tableExists = tableNames.includes(tableName);
      
      // If table doesn't exist in get_all_tables results, double-check with a direct query
      if (!tableExists) {
        try {
          // Try a direct query as a final check
          const { error: queryError } = await supabaseAdmin
            .from(tableName)
            .select('*', { count: 'exact', head: true });
          
          if (queryError) {
            results.tables[tableName].error = `Table doesn't exist or permission denied: ${queryError.message}`;
            results.success = false;
            continue;
          } else {
            // If we got here, the table exists but wasn't in get_all_tables
            results.tables[tableName].exists = true;
          }
        } catch (error) {
          results.tables[tableName].error = `Table doesn't exist or permission denied: ${error instanceof Error ? error.message : 'Unknown error'}`;
          results.success = false;
          continue;
        }
      } else {
        results.tables[tableName].exists = true;
      }
      
      // Check column structure if available
      if (tableData.columns) {
        try {
          const { data: columns, error: columnsError } = await supabaseAdmin
            .rpc('get_table_columns', { table_name: tableName });
          
          if (columnsError) {
            results.tables[tableName].error = `Failed to retrieve column info: ${columnsError.message}`;
            results.success = false;
          } else if (columns) {
            // Get column names from the database
            const dbColumnNames = columns.map((col: any) => col.column_name);
            
            // Get column names from the backup
            const backupColumnNames = tableData.columns.map((col: any) => col.column_name);
            
            // Check if all backup columns exist in the database
            const missingColumns = backupColumnNames.filter(
              (col: string) => !dbColumnNames.includes(col)
            );
            
            if (missingColumns.length > 0) {
              results.tables[tableName].error = `Missing columns in database: ${missingColumns.join(', ')}`;
              results.success = false;
            } else {
              results.tables[tableName].columnsMatch = true;
            }
          }
        } catch (error) {
          results.tables[tableName].error = `Error checking columns: ${error instanceof Error ? error.message : 'Unknown error'}`;
          results.success = false;
        }
      }
      
      // Check data if available
      if (tableData.data && Array.isArray(tableData.data)) {
        results.tables[tableName].dataRows = tableData.data.length;
        if (tableData.data.length > 0) {
          results.hasData = true;
        }
      }
      
      // If we're not in preview mode and everything looks good, perform the restore
      if (!previewMode && results.tables[tableName].exists && 
          results.tables[tableName].columnsMatch) {
        
        try {
          // Clear existing data
          const { error: clearError } = await supabaseAdmin
            .from(tableName)
            .delete()
            .neq("id", "00000000-0000-0000-0000-000000000000"); // Delete all rows
          
          if (clearError) {
            results.tables[tableName].error = `Error clearing table: ${clearError.message}`;
            results.success = false;
            continue;
          }
          
          // Insert the backed up data
          if (tableData.data && tableData.data.length > 0) {
            // Insert data in chunks to avoid exceeding payload limits
            const chunkSize = 1000;
            let insertSuccess = true;
            
            for (let i = 0; i < tableData.data.length; i += chunkSize) {
              const chunk = tableData.data.slice(i, i + chunkSize);
              
              const { error: insertError } = await supabaseAdmin
                .from(tableName)
                .insert(chunk);
              
              if (insertError) {
                results.tables[tableName].error = `Error inserting data: ${insertError.message}`;
                results.success = false;
                insertSuccess = false;
                break;
              }
            }
            
            if (insertSuccess) {
              results.tables[tableName].restored = true;
            }
          } else {
            results.tables[tableName].restored = true; // Table was cleared but had no data to restore
          }
        } catch (error) {
          results.tables[tableName].error = `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`;
          results.success = false;
        }
      }
    }
    
    return new Response(
      JSON.stringify({
        ...results,
        message: previewMode 
          ? "Backup validation completed successfully" 
          : (results.success ? "Database restore completed successfully" : "Database restore completed with errors"),
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error in admin-restore-database function:", error);
    return new Response(
      JSON.stringify({ 
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        success: false
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
