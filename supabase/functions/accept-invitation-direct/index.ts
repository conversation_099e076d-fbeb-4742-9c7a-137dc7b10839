import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.20.0';
import { corsHeaders } from '../_shared/cors.ts';

// Log the environment variables for debugging
console.log('SUPABASE_URL:', Deno.env.get('SUPABASE_URL'));
console.log('SUPABASE_ANON_KEY:', Deno.env.get('SUPABASE_ANON_KEY')?.substring(0, 5) + '...');
console.log('SUPABASE_SERVICE_ROLE_KEY:', Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')?.substring(0, 5) + '...');

interface InvitationRequest {
  token: string;
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  role: string;
}

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Create a Supabase client with the Admin key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get the request body (snake_case) and map to camelCase
    const { token, email, password, first_name: firstName, last_name: lastName, role } = await req.json() as InvitationRequest;

    // Validate required fields
    if (!token || !email || !password || !firstName || !lastName) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }

    console.log(`Processing invitation acceptance for email: ${email}, token: ${token}`);

    // Get the invitation details
    let invitationData;
    try {
      const { data, error: invitationError } = await supabaseAdmin
        .from('team_invitations')
        .select('*')
        .eq('token', token)
        .single();

      if (invitationError || !data) {
        console.error('Error fetching invitation:', invitationError);
        return new Response(
          JSON.stringify({
            error: 'Invalid invitation token',
            details: invitationError?.message || 'No invitation found with this token'
          }),
          {
            status: 400,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          }
        );
      }

      invitationData = data;
      console.log('Invitation found:', JSON.stringify(invitationData));
    } catch (fetchError) {
      console.error('Exception fetching invitation:', fetchError);
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch invitation',
          details: fetchError instanceof Error ? fetchError.message : 'Unknown error'
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }

    // Check if the invitation is already accepted
    if (invitationData.status === 'accepted') {
      return new Response(
        JSON.stringify({ error: 'Invitation already accepted' }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }

    // Check if the invitation is expired
    if (invitationData.expires_at && new Date(invitationData.expires_at) < new Date()) {
      return new Response(
        JSON.stringify({ error: 'Invitation expired' }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }

    // Check if the email matches the invitation
    if (invitationData.email && invitationData.email !== email) {
      return new Response(
        JSON.stringify({ error: 'Email does not match invitation' }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }

    // Check if the user already exists by querying the profiles table
    const { data: existingProfile, error: profileError } = await supabaseAdmin
      .from('profiles')
      // only select id (primary key)
      .select('id')
      .eq('email', email)
      .maybeSingle();

    if (profileError) {
      console.error('Error checking for existing profile:', profileError);
    }

    let userId;

    // If a profile exists, use its id
    if (existingProfile && existingProfile.id) {
      console.log('User already exists in profiles table:', existingProfile.id);
      userId = existingProfile.id;
    } else {
      // Also check if the user exists in the auth system by listing users
      const { data: userList, error: userError } = await supabaseAdmin
        .auth.admin.listUsers();

      if (userError) {
        console.error('Error checking for existing user in auth system:', userError);
      }

      // Find the user by email
      const existingUser = userList?.users?.find(user => user.email === email);

      if (existingUser) {
        console.log('User exists in auth system but not in profiles table:', existingUser.id);
        userId = existingUser.id;
      }

      // If no auth user exists, create one with the given password
      if (!userId) {
        console.log('No existing auth user found, creating new user');
        try {
          const { data: newUser, error: newUserError } = await supabaseAdmin.auth.admin.createUser({
            email,
            password,
            email_confirm: true, // Auto-confirm email
            user_metadata: {
              first_name: firstName,
              last_name: lastName,
              role: role || invitationData.role || 'service_provider'
            }
          });

          if (newUserError || !newUser?.user) {
            console.error('Error creating new auth user:', newUserError);
            return new Response(
              JSON.stringify({ error: `Failed to create user: ${newUserError?.message || 'Unknown error'}` }),
              { status: 500, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
            );
          }

          userId = newUser.user.id;
          console.log(`Successfully created new user with ID: ${userId}`);
        } catch (createUserError) {
          console.error('Exception when creating user:', createUserError);
          return new Response(
            JSON.stringify({
              error: 'Failed to create user due to exception',
              details: createUserError instanceof Error ? createUserError.message : 'Unknown error'
            }),
            { status: 500, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
          );
        }
      }

      // If no profile exists, create one using our new safer function
      if (userId) {
        try {
          console.log(`Creating profile for user ${userId} using create_profile_safely function`);
          const { data: profileResult, error: createProfileError } = await supabaseAdmin.rpc(
            'create_profile_safely',
            {
              p_user_id: userId,
              p_first_name: firstName,
              p_last_name: lastName,
              p_role: role || invitationData.role || 'service_provider',
              p_email: email
            }
          );

          if (createProfileError) {
            console.error('Error creating profile with RPC function:', createProfileError);
            // Don't return an error, just log it and continue
            // The profile might be created by the handle_new_user trigger
          } else {
            console.log('Profile creation result:', profileResult);
          }
        } catch (profileError) {
          console.error('Exception when creating profile with RPC function:', profileError);
          // Don't return an error, just log it and continue
          // The profile might be created by the handle_new_user trigger
        }

        // As a fallback, also try the direct upsert method
        try {
          console.log(`Fallback: Upserting profile for user ${userId} directly`);
          const { error: createProfileError } = await supabaseAdmin
            .from('profiles')
            .upsert(
              {
                id: userId,
                email,
                first_name: firstName,
                last_name: lastName,
                role: role || invitationData.role || 'service_provider',
                is_super_admin: false,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              },
              { onConflict: 'id' }
            );

          if (createProfileError) {
            console.error('Error upserting profile for user (fallback method):', createProfileError);
          } else {
            console.log('Profile upserted successfully with fallback method');
          }
        } catch (profileError) {
          console.error('Exception when upserting profile (fallback method):', profileError);
        }
      }
    }

    // Accept the invitation and add the user to the team
    let acceptData;
    try {
      console.log(`Calling accept_invitation_and_add_member RPC with token: ${token} and userId: ${userId}`);
      const result = await supabaseAdmin.rpc(
        'accept_invitation_and_add_member',
        {
          p_token: token,
          p_user_id: userId
        }
      );

      if (result.error) {
        console.error('Error accepting invitation:', result.error);
        return new Response(
          JSON.stringify({
            error: 'Failed to accept invitation',
            details: result.error.message,
            code: result.error.code
          }),
          {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          }
        );
      }

      acceptData = result.data;
      console.log('RPC function returned:', acceptData);
    } catch (rpcError) {
      console.error('Exception when calling RPC function:', rpcError);
      return new Response(
        JSON.stringify({
          error: 'Exception when accepting invitation',
          details: rpcError instanceof Error ? rpcError.message : 'Unknown error'
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }

    console.log('Invitation accepted successfully:', acceptData);

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        user_id: userId,
        team_id: invitationData.team_id
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      }
    );
  } catch (error) {
    console.error('Unexpected error:', error);
    // Provide more detailed error information for debugging
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : '';

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        details: errorMessage,
        stack: errorStack
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      }
    );
  }
});