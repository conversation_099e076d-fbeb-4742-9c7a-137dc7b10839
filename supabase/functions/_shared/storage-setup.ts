
import { SupabaseClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";

export const ensureBucketExists = async (
  supabase: SupabaseClient,
  bucketName: string,
  isPublic: boolean = true
): Promise<boolean> => {
  try {
    // Check if bucket exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error(`Error listing buckets: ${listError.message}`);
      return false;
    }
    
    const bucketExists = buckets.some(bucket => bucket.name === bucketName);
    
    if (!bucketExists) {
      console.log(`Creating ${bucketName} bucket`);
      const { error: createError } = await supabase.storage.createBucket(bucketName, {
        public: isPublic
      });
      
      if (createError) {
        console.error(`Error creating ${bucketName} bucket: ${createError.message}`);
        return false;
      }
      
      console.log(`Successfully created ${bucketName} bucket`);
    } else {
      console.log(`Bucket ${bucketName} already exists`);
    }
    
    // Ensure correct security policies are set
    if (isPublic) {
      const { error: updateError } = await supabase.storage.updateBucket(bucketName, {
        public: true
      });
      
      if (updateError) {
        console.error(`Error updating ${bucketName} bucket to public: ${updateError.message}`);
      }
    }
    
    return true;
  } catch (error) {
    console.error(`Unexpected error ensuring bucket exists: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return false;
  }
};
