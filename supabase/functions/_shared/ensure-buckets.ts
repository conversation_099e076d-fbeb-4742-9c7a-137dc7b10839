// Utility to ensure required storage buckets exist

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'

export async function ensureStorageBuckets() {
  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase URL or service role key')
      return false
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    // List of required buckets
    const requiredBuckets = [
      { name: 'damage-photos', public: true },
      { name: 'property-images', public: true },
      { name: 'inventory', public: true },
      { name: 'invoices', public: true }
    ]
    
    // Get existing buckets
    const { data: existingBuckets, error: listError } = await supabase.storage.listBuckets()
    
    if (listError) {
      console.error('Error listing buckets:', listError)
      return false
    }
    
    const existingBucketNames = existingBuckets.map(bucket => bucket.name)
    
    // Create missing buckets
    for (const bucket of requiredBuckets) {
      if (!existingBucketNames.includes(bucket.name)) {
        console.log(`Creating missing bucket: ${bucket.name}`)
        const { error: createError } = await supabase.storage.createBucket(bucket.name, {
          public: bucket.public
        })
        
        if (createError) {
          console.error(`Error creating bucket ${bucket.name}:`, createError)
        } else {
          console.log(`Successfully created bucket: ${bucket.name}`)
        }
      } else {
        console.log(`Bucket already exists: ${bucket.name}`)
      }
    }
    
    return true
  } catch (error) {
    console.error('Unexpected error in ensureStorageBuckets:', error)
    return false
  }
}
