
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get environment variables
    const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error("Missing environment variables");
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Server configuration error" 
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Create a Supabase client with the service role key for admin access
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Parse the multipart form data
    const formData = await req.formData();
    const file = formData.get("file") as File;
    const damageReportId = formData.get("damageReportId") as string;
    const invoiceNumber = formData.get("invoiceNumber") as string;
    const providerId = formData.get("providerId") as string;
    const totalAmount = formData.get("totalAmount") as string;
    const status = formData.get("status") as string;
    const userId = formData.get("userId") as string;

    if (!file || !damageReportId || !userId) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Missing required fields" 
        }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    console.log("Received file upload request:", {
      damageReportId,
      invoiceNumber,
      fileName: file.name,
      fileSize: file.size,
      contentType: file.type,
      providerId,
    });

    // Ensure the file is a PDF
    if (file.type !== "application/pdf") {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Only PDF files are supported" 
        }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Check if the bucket exists, create it if it doesn't
    const { data: buckets, error: bucketError } = await supabase.storage.listBuckets();
    
    if (bucketError) {
      console.error("Error listing buckets:", bucketError);
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Failed to check for storage bucket", 
          details: bucketError 
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    const bucketExists = buckets.some(bucket => bucket.name === "invoice-files");
    
    if (!bucketExists) {
      console.log("Creating invoice-files bucket");
      const { error: createBucketError } = await supabase.storage.createBucket("invoice-files", {
        public: true
      });
      
      if (createBucketError) {
        console.error("Error creating bucket:", createBucketError);
        return new Response(
          JSON.stringify({ 
            success: false, 
            error: "Failed to create storage bucket", 
            details: createBucketError 
          }),
          {
            status: 500,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }
      
      console.log("Successfully created invoice-files bucket");
    }

    // Generate a unique file path for the invoice
    const filePath = `${damageReportId}/${crypto.randomUUID()}.pdf`;
    
    // Upload the file to storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from("invoice-files")
      .upload(filePath, file, {
        contentType: "application/pdf",
        upsert: false,
      });

    if (uploadError) {
      console.error("Error uploading file:", uploadError);
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Failed to upload file", 
          details: uploadError 
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Get the public URL for the uploaded file
    const { data: publicUrlData } = supabase.storage
      .from("invoice-files")
      .getPublicUrl(filePath);

    // Insert the invoice record into the database
    const { data: invoice, error: dbError } = await supabase
      .from("damage_invoices")
      .insert({
        damage_report_id: damageReportId,
        provider_id: providerId !== "none" ? providerId : null,
        invoice_number: invoiceNumber,
        total_amount: totalAmount ? parseFloat(totalAmount) : null,
        status: status || "draft",
        file_path: filePath,
        file_name: file.name,
        file_url: publicUrlData.publicUrl,
        user_id: userId,
      })
      .select()
      .single();

    if (dbError) {
      console.error("Error saving invoice data to database:", dbError);
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Failed to save invoice metadata", 
          details: dbError 
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        message: "Invoice uploaded successfully",
        invoice,
      }),
      {
        status: 200,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  } catch (error: any) {
    console.error("Unexpected error:", error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: "An unexpected error occurred", 
        details: error.message 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
});
