
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase URL or service role key')
      return new Response(
        JSON.stringify({ error: 'Server configuration error' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Process form data
    const formData = await req.formData()
    const file = formData.get('file') as File
    const damageReportId = formData.get('damageReportId') as string
    const userId = formData.get('userId') as string
    const caption = formData.get('caption') as string
    
    if (!file || !damageReportId || !userId) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Log received data for debugging
    console.log(`Processing damage photo upload - Report ID: ${damageReportId}, User ID: ${userId}, File name: ${file.name}, File size: ${file.size}`)

    // Sanitize file name to ensure it's safe
    const sanitizedFileName = file.name.replace(/[^\x00-\x7F]/g, '')
    const fileExt = sanitizedFileName.split('.').pop()
    
    // Create unique path for the file
    const filePath = `${damageReportId}/${crypto.randomUUID()}.${fileExt}`

    console.log(`Uploading file to path: ${filePath}`)
    
    // Upload to Supabase Storage
    const { data: storageData, error: uploadError } = await supabase.storage
      .from('damage-photos')
      .upload(filePath, file, {
        contentType: file.type,
        upsert: true
      })

    if (uploadError) {
      console.error('Storage upload error:', uploadError)
      return new Response(
        JSON.stringify({ error: 'Failed to upload file to storage', details: uploadError }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    console.log('File uploaded successfully to storage:', filePath)

    // Create entry in damage_photos table
    const { data: photoData, error: dbError } = await supabase
      .from('damage_photos')
      .insert({
        damage_report_id: damageReportId,
        user_id: userId,
        file_name: sanitizedFileName,
        file_path: filePath,
        caption: caption || null
      })
      .select()
      .single()

    if (dbError) {
      console.error('Database error:', dbError)
      
      // Clean up storage if database insert fails
      await supabase.storage
        .from('damage-photos')
        .remove([filePath])
      
      return new Response(
        JSON.stringify({ error: 'Failed to record photo in database', details: dbError }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    // Get public URL for the uploaded file
    const { data: publicUrlData } = supabase.storage
      .from('damage-photos')
      .getPublicUrl(filePath)

    // Add URL to the photo data
    const photoWithUrl = {
      ...photoData,
      url: publicUrlData.publicUrl
    }

    // Add activity record
    await supabase
      .from('damage_report_activities')
      .insert({
        damage_report_id: damageReportId,
        user_id: userId,
        action: 'upload',
        details: `Uploaded photo: ${sanitizedFileName}`
      })

    console.log('Successfully processed photo upload:', photoWithUrl.id)

    return new Response(
      JSON.stringify({ 
        message: 'Photo uploaded successfully',
        photo: photoWithUrl
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 200 }
    )
  } catch (error) {
    console.error('Unexpected error:', error)
    return new Response(
      JSON.stringify({ error: 'An unexpected error occurred', details: error.message }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
