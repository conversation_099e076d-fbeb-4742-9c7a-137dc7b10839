import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get the request body
    const { userId, teamId } = await req.json()

    if (!userId) {
      return new Response(
        JSON.stringify({ error: 'User ID is required' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      )
    }

    console.log(`Getting team maintenance tasks for user ${userId} in team ${teamId || 'any'}`)

    // First verify the user is a member of the team
    let teamIds = []
    
    if (teamId) {
      // If a specific team ID is provided, verify membership
      const { data: teamMember, error: teamMemberError } = await supabase
        .from('team_members')
        .select('team_id')
        .eq('user_id', userId)
        .eq('team_id', teamId)
        .eq('status', 'active')
        .single()
      
      if (teamMemberError) {
        console.error('Error verifying team membership:', teamMemberError)
        return new Response(
          JSON.stringify({ error: 'Error verifying team membership', details: teamMemberError }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 403,
          }
        )
      }
      
      if (teamMember) {
        teamIds = [teamId]
      } else {
        return new Response(
          JSON.stringify({ error: 'User is not a member of the specified team' }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 403,
          }
        )
      }
    } else {
      // Get all teams the user is a member of
      const { data: teamMemberships, error: teamMembershipsError } = await supabase
        .from('team_members')
        .select('team_id')
        .eq('user_id', userId)
        .eq('status', 'active')
      
      if (teamMembershipsError) {
        console.error('Error fetching team memberships:', teamMembershipsError)
        return new Response(
          JSON.stringify({ error: 'Error fetching team memberships', details: teamMembershipsError }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
          }
        )
      }
      
      if (teamMemberships && teamMemberships.length > 0) {
        teamIds = teamMemberships.map(tm => tm.team_id)
      } else {
        return new Response(
          JSON.stringify({ error: 'User is not a member of any team' }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 403,
          }
        )
      }
    }
    
    console.log(`User is a member of ${teamIds.length} teams:`, teamIds)
    
    // Get all property IDs for these teams
    const { data: teamProperties, error: teamPropertiesError } = await supabase
      .from('team_properties')
      .select('property_id')
      .in('team_id', teamIds)
    
    if (teamPropertiesError) {
      console.error('Error fetching team properties:', teamPropertiesError)
      return new Response(
        JSON.stringify({ error: 'Error fetching team properties', details: teamPropertiesError }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }
    
    if (!teamProperties || teamProperties.length === 0) {
      console.log('No properties found for user\'s teams')
      return new Response(
        JSON.stringify([]),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    }
    
    const propertyIds = teamProperties.map(tp => tp.property_id)
    console.log(`Found ${propertyIds.length} properties for user's teams`)
    
    // Get all maintenance tasks for these properties
    const { data: tasks, error: tasksError } = await supabase
      .from('maintenance_tasks')
      .select('*')
      .in('property_id', propertyIds)
      .order('created_at', { ascending: false })
    
    if (tasksError) {
      console.error('Error fetching maintenance tasks:', tasksError)
      return new Response(
        JSON.stringify({ error: 'Error fetching maintenance tasks', details: tasksError }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      )
    }
    
    console.log(`Found ${tasks?.length || 0} maintenance tasks for team properties`)
    
    return new Response(
      JSON.stringify(tasks || []),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    )
  }
})
