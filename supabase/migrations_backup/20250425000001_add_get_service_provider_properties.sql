-- Create a function to get properties accessible to a service provider
CREATE OR REPLACE FUNCTION public.get_service_provider_properties(p_user_id UUID)
RETURNS TABLE (
    id UUID,
    name TEXT,
    address TEXT,
    city TEXT,
    state TEXT,
    zip TEXT,
    user_id UUID,
    team_id UUID,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
    is_service_provider BOOLEAN;
BEGIN
    -- Check if the user is a service provider
    SELECT EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = p_user_id AND profiles.role = 'service_provider'
    ) INTO is_service_provider;

    -- If not a service provider, return empty result
    IF NOT is_service_provider THEN
        RETURN;
    END IF;
    -- Return properties the service provider has access to via team membership
    RETURN QUERY
    SELECT DISTINCT ON (p.id)
        p.id,
        p.name,
        p.address,
        p.city,
        p.state,
        p.zip,
        p.user_id,
        p.team_id,
        p.created_at,
        p.updated_at
    FROM properties p
    JOIN team_properties tp ON p.id = tp.property_id
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tm.user_id = p_user_id
    AND tm.status = 'active'
    ORDER BY p.id, p.name;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_service_provider_properties(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_service_provider_properties(UUID) TO anon;

-- Add a comment to the function
COMMENT ON FUNCTION public.get_service_provider_properties(UUID) IS 'Gets properties accessible to a service provider, including properties from teams they are members of';
