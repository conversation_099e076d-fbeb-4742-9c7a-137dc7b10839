-- Drop ALL policies on teams table
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'teams'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON teams', policy_record.policyname);
    END LOOP;
END
$$;

-- Create a single, simple policy for teams
CREATE POLICY "teams_simple_policy"
ON teams
FOR ALL
USING (true)
WITH CHECK (true);

-- Enable RLS on teams
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
