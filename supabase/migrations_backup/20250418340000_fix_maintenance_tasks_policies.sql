-- Drop ALL policies on maintenance_tasks table
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'maintenance_tasks'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON maintenance_tasks', policy_record.policyname);
    END LOOP;
END
$$;

-- Create simple policies for maintenance_tasks
CREATE POLICY "maintenance_tasks_viewable_by_creator"
ON maintenance_tasks
FOR SELECT
USING (
  auth.uid() = user_id
);

CREATE POLICY "maintenance_tasks_viewable_by_assigned_provider"
ON maintenance_tasks
FOR SELECT
USING (
  provider_id = auth.uid() OR 
  provider_email = (SELECT email FROM service_providers WHERE id = auth.uid())
);

CREATE POLICY "maintenance_tasks_viewable_by_property_owner"
ON maintenance_tasks
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM properties
    WHERE properties.id = maintenance_tasks.property_id
    AND properties.user_id = auth.uid()
  )
);

CREATE POLICY "maintenance_tasks_viewable_by_admins"
ON maintenance_tasks
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  )
);

-- Enable RLS on maintenance_tasks
ALTER TABLE maintenance_tasks ENABLE ROW LEVEL SECURITY;
