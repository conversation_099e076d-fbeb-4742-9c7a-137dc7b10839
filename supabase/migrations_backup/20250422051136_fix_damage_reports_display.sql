-- Create a view to display damage reports with property information
CREATE OR REPLACE VIEW damage_reports_with_property AS
SELECT
    dr.id,
    dr.title,
    dr.description,
    dr.status,
    dr.property_id,
    p.name AS property_name,
    dr.user_id,
    u.email AS user_email,
    dr.provider_id,
    mp.name AS provider_name,
    dr.created_at,
    dr.updated_at,
    dr.platform,
    dr.team_id,
    p.user_id AS property_owner_id
FROM
    damage_reports dr
JOIN
    properties p ON dr.property_id = p.id
LEFT JOIN
    auth.users u ON dr.user_id = u.id
LEFT JOIN
    maintenance_providers mp ON dr.provider_id = mp.id;

-- Create a function to get damage reports for a user with proper deduplication
CREATE OR REPLACE FUNCTION get_user_damage_reports(p_user_id UUID)
RETURNS TABLE (
    id UUID,
    title TEXT,
    description TEXT,
    status TEXT,
    property_id UUID,
    property_name TEXT,
    user_id UUID,
    user_email TEXT,
    provider_id UUID,
    provider_name TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    platform TEXT,
    team_id UUID,
    property_owner_id UUID,
    source TEXT
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    -- Get user's own reports
    RETURN QUERY
    SELECT
        dr.*,
        'own' AS source
    FROM
        damage_reports_with_property dr
    WHERE
        dr.user_id = p_user_id;

    -- Get reports for properties in teams the user is a member of
    -- but exclude reports already returned (user's own reports)
    RETURN QUERY
    SELECT DISTINCT ON (dr.id)
        dr.*,
        'team' AS source
    FROM
        damage_reports_with_property dr
    JOIN
        team_properties tp ON dr.property_id = tp.property_id
    JOIN
        team_members tm ON tp.team_id = tm.team_id
    WHERE
        tm.user_id = p_user_id
        AND tm.status = 'active'
        AND dr.user_id != p_user_id;

    -- Get reports directly associated with teams the user is a member of
    -- but exclude reports already returned
    RETURN QUERY
    SELECT DISTINCT ON (dr.id)
        dr.*,
        'team_direct' AS source
    FROM
        damage_reports_with_property dr
    JOIN
        team_members tm ON dr.team_id = tm.team_id
    WHERE
        tm.user_id = p_user_id
        AND tm.status = 'active'
        AND dr.user_id != p_user_id
        AND NOT EXISTS (
            SELECT 1 FROM team_properties tp
            WHERE tp.property_id = dr.property_id AND tp.team_id = tm.team_id
        );

    -- For admins, get all other reports
    IF EXISTS (SELECT 1 FROM profiles WHERE id = p_user_id AND (is_super_admin = true OR role = 'admin')) THEN
        RETURN QUERY
        SELECT DISTINCT ON (dr.id)
            dr.*,
            'admin' AS source
        FROM
            damage_reports_with_property dr
        WHERE
            dr.user_id != p_user_id
            AND NOT EXISTS (
                SELECT 1 FROM team_properties tp
                JOIN team_members tm ON tp.team_id = tm.team_id
                WHERE tp.property_id = dr.property_id AND tm.user_id = p_user_id AND tm.status = 'active'
            )
            AND (dr.team_id IS NULL OR NOT EXISTS (
                SELECT 1 FROM team_members tm
                WHERE tm.team_id = dr.team_id AND tm.user_id = p_user_id AND tm.status = 'active'
            ));
    END IF;

    -- For service providers, get reports assigned to them
    IF EXISTS (SELECT 1 FROM profiles WHERE id = p_user_id AND role = 'service_provider') THEN
        RETURN QUERY
        SELECT DISTINCT ON (dr.id)
            dr.*,
            'assigned' AS source
        FROM
            damage_reports_with_property dr
        WHERE
            dr.provider_id = p_user_id
            AND dr.user_id != p_user_id
            AND NOT EXISTS (
                SELECT 1 FROM team_properties tp
                JOIN team_members tm ON tp.team_id = tm.team_id
                WHERE tp.property_id = dr.property_id AND tm.user_id = p_user_id AND tm.status = 'active'
            )
            AND (dr.team_id IS NULL OR NOT EXISTS (
                SELECT 1 FROM team_members tm
                WHERE tm.team_id = dr.team_id AND tm.user_id = p_user_id AND tm.status = 'active'
            ));
    END IF;
END;
$$;

-- Create RLS policy for the view
ALTER VIEW damage_reports_with_property OWNER TO postgres;
GRANT SELECT ON damage_reports_with_property TO authenticated;
GRANT SELECT ON damage_reports_with_property TO anon;

-- Create RLS policy for the function
GRANT EXECUTE ON FUNCTION get_user_damage_reports(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_damage_reports(UUID) TO anon;
