-- Create a function to get all properties a user has access to
CREATE OR REPLACE FUNCTION public.get_user_accessible_properties(
  p_user_id UUID
) RETURNS SETOF properties AS $$
DECLARE
  is_admin BOOLEAN;
BEGIN
  -- Check if the user is a super admin or admin
  SELECT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = p_user_id AND (is_super_admin = true OR role = 'admin')
  ) INTO is_admin;
  
  -- For admins, return all properties
  IF is_admin THEN
    RETURN QUERY
    SELECT * FROM properties;
  ELSE
    -- Return properties the user owns
    RETURN QUERY
    SELECT * FROM properties
    WHERE user_id = p_user_id
    
    UNION
    
    -- Return properties the user has access to via team membership
    SELECT p.* FROM properties p
    JOIN team_properties tp ON p.id = tp.property_id
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tm.user_id = p_user_id
    AND tm.status = 'active';
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
