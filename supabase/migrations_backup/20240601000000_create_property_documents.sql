-- Create property_documents table
CREATE TABLE IF NOT EXISTS public.property_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    is_private BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create property_files table
CREATE TABLE IF NOT EXISTS public.property_files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    filename TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    is_private BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create storage bucket for property files if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
SELECT 'property-files', 'property-files', true
WHERE NOT EXISTS (
    SELECT 1 FROM storage.buckets WHERE id = 'property-files'
);

-- Enable RLS on the new tables
ALTER TABLE public.property_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.property_files ENABLE ROW LEVEL SECURITY;

-- Create function to check if user has access to property documents
CREATE OR REPLACE FUNCTION public.has_property_document_access(p_document_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  v_property_id uuid;
  v_user_id uuid;
  v_is_private boolean;
BEGIN
  -- Get document details
  SELECT property_id, user_id, is_private INTO v_property_id, v_user_id, v_is_private
  FROM property_documents
  WHERE id = p_document_id;

  -- Super admins and admins have access to all documents
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Document creator always has access
  IF v_user_id = auth.uid() THEN
    RETURN TRUE;
  END IF;

  -- If document is private, only creator can access
  IF v_is_private = true THEN
    RETURN FALSE;
  END IF;

  -- Property owner has access to all non-private documents
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE id = v_property_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to non-private documents
  IF EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$function$;

-- Create function to check if user has access to property files
CREATE OR REPLACE FUNCTION public.has_property_file_access(p_file_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  v_property_id uuid;
  v_user_id uuid;
  v_is_private boolean;
BEGIN
  -- Get file details
  SELECT property_id, user_id, is_private INTO v_property_id, v_user_id, v_is_private
  FROM property_files
  WHERE id = p_file_id;

  -- Super admins and admins have access to all files
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- File uploader always has access
  IF v_user_id = auth.uid() THEN
    RETURN TRUE;
  END IF;

  -- If file is private, only uploader can access
  IF v_is_private = true THEN
    RETURN FALSE;
  END IF;

  -- Property owner has access to all non-private files
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE id = v_property_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to non-private files
  IF EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$function$;

-- RLS policies for property_documents
CREATE POLICY "Users can view their own documents"
ON property_documents FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Users can view non-private property documents they have access to"
ON property_documents FOR SELECT
USING (
  is_private = false AND
  has_property_access(property_id)
);

CREATE POLICY "Users can insert documents for properties they have access to"
ON property_documents FOR INSERT
WITH CHECK (
  has_property_access(property_id) AND
  user_id = auth.uid()
);

CREATE POLICY "Users can update their own documents"
ON property_documents FOR UPDATE
USING (user_id = auth.uid());

CREATE POLICY "Property owners can update any document"
ON property_documents FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM properties
    WHERE id = property_documents.property_id AND user_id = auth.uid()
  )
);

CREATE POLICY "Users can delete their own documents"
ON property_documents FOR DELETE
USING (user_id = auth.uid());

CREATE POLICY "Property owners can delete any document"
ON property_documents FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM properties
    WHERE id = property_documents.property_id AND user_id = auth.uid()
  )
);

-- RLS policies for property_files
CREATE POLICY "Users can view their own files"
ON property_files FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Users can view non-private property files they have access to"
ON property_files FOR SELECT
USING (
  is_private = false AND
  has_property_access(property_id)
);

CREATE POLICY "Users can insert files for properties they have access to"
ON property_files FOR INSERT
WITH CHECK (
  has_property_access(property_id) AND
  user_id = auth.uid()
);

CREATE POLICY "Users can update their own files"
ON property_files FOR UPDATE
USING (user_id = auth.uid());

CREATE POLICY "Property owners can update any file"
ON property_files FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM properties
    WHERE id = property_files.property_id AND user_id = auth.uid()
  )
);

CREATE POLICY "Users can delete their own files"
ON property_files FOR DELETE
USING (user_id = auth.uid());

CREATE POLICY "Property owners can delete any file"
ON property_files FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM properties
    WHERE id = property_files.property_id AND user_id = auth.uid()
  )
);

-- Storage policies for property-files bucket
CREATE POLICY "Public Read Access for Property Files"
ON storage.objects FOR SELECT
USING (bucket_id = 'property-files');

CREATE POLICY "Allow Users to Upload Property Files"
ON storage.objects FOR INSERT
WITH CHECK (bucket_id = 'property-files' AND auth.role() = 'authenticated');

CREATE POLICY "Allow Users to Update Property Files"
ON storage.objects FOR UPDATE
USING (bucket_id = 'property-files' AND auth.role() = 'authenticated');

CREATE POLICY "Allow Users to Delete Property Files"
ON storage.objects FOR DELETE
USING (bucket_id = 'property-files' AND auth.role() = 'authenticated');
