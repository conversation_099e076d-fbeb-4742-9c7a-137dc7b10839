-- Enable Row Level Security on teams-related tables
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_invitations ENABLE ROW LEVEL SECURITY;

-- <PERSON>reate helper function to check if a user is a team owner
CREATE OR REPLACE FUNCTION is_team_owner(team_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM teams
    WHERE id = team_id AND owner_id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON><PERSON> helper function to check if a user is a team member
CREATE OR REPLACE FUNCTION is_team_member(team_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = team_id AND user_id = auth.uid() AND status = 'active'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON><PERSON> helper function to check if a user is a super admin
CREATE OR REPLACE FUNCTION is_super_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND is_super_admin = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create helper function to check if a user is an admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create helper function to check if a user is a property manager
CREATE OR REPLACE FUNCTION is_property_manager()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND role = 'property_manager'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create helper function to check if a user has a specific permission for a team
CREATE OR REPLACE FUNCTION has_team_permission(team_id UUID, permission_name TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM user_permissions
    WHERE user_id = auth.uid() 
      AND team_id = team_id 
      AND permission = permission_name::permission_type
      AND enabled = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS Policies for teams table

-- Super Admins can do anything with teams
CREATE POLICY "Super Admins can do anything with teams"
ON teams
USING (is_super_admin())
WITH CHECK (is_super_admin());

-- Admins can do anything with teams
CREATE POLICY "Admins can do anything with teams"
ON teams
USING (is_admin())
WITH CHECK (is_admin());

-- Property Managers can create teams
CREATE POLICY "Property Managers can create teams"
ON teams
FOR INSERT
WITH CHECK (is_property_manager() AND owner_id = auth.uid());

-- Users can view teams they own
CREATE POLICY "Users can view teams they own"
ON teams
FOR SELECT
USING (owner_id = auth.uid());

-- Users can view teams they are members of
CREATE POLICY "Users can view teams they are members of"
ON teams
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = teams.id AND user_id = auth.uid() AND status = 'active'
  )
);

-- Team owners can update their teams
CREATE POLICY "Team owners can update their teams"
ON teams
FOR UPDATE
USING (owner_id = auth.uid())
WITH CHECK (owner_id = auth.uid());

-- Team owners can delete their teams
CREATE POLICY "Team owners can delete their teams"
ON teams
FOR DELETE
USING (owner_id = auth.uid());

-- RLS Policies for team_members table

-- Super Admins can do anything with team members
CREATE POLICY "Super Admins can do anything with team members"
ON team_members
USING (is_super_admin())
WITH CHECK (is_super_admin());

-- Admins can do anything with team members
CREATE POLICY "Admins can do anything with team members"
ON team_members
USING (is_admin())
WITH CHECK (is_admin());

-- Team owners can manage team members
CREATE POLICY "Team owners can manage team members"
ON team_members
USING (is_team_owner(team_id))
WITH CHECK (is_team_owner(team_id));

-- Property Managers with manage_staff permission can manage staff members
CREATE POLICY "Property Managers can manage staff members"
ON team_members
USING (
  is_property_manager() AND 
  is_team_member(team_id) AND 
  has_team_permission(team_id, 'manage_staff')
)
WITH CHECK (
  is_property_manager() AND 
  is_team_member(team_id) AND 
  has_team_permission(team_id, 'manage_staff')
);

-- Property Managers with manage_service_providers permission can manage service providers
CREATE POLICY "Property Managers can manage service providers"
ON team_members
USING (
  is_property_manager() AND 
  is_team_member(team_id) AND 
  has_team_permission(team_id, 'manage_service_providers')
)
WITH CHECK (
  is_property_manager() AND 
  is_team_member(team_id) AND 
  has_team_permission(team_id, 'manage_service_providers')
);

-- Users can view team members in teams they belong to
CREATE POLICY "Users can view team members in their teams"
ON team_members
FOR SELECT
USING (
  is_team_member(team_id) OR user_id = auth.uid()
);

-- RLS Policies for user_permissions table

-- Super Admins can do anything with permissions
CREATE POLICY "Super Admins can do anything with permissions"
ON user_permissions
USING (is_super_admin())
WITH CHECK (is_super_admin());

-- Admins can manage permissions except for Super Admins
CREATE POLICY "Admins can manage permissions except for Super Admins"
ON user_permissions
USING (
  is_admin() AND 
  NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = user_permissions.user_id AND is_super_admin = true
  )
)
WITH CHECK (
  is_admin() AND 
  NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = user_permissions.user_id AND is_super_admin = true
  )
);

-- Property Managers can manage permissions for their team members
CREATE POLICY "Property Managers can manage permissions for their team members"
ON user_permissions
USING (
  is_property_manager() AND 
  team_id IS NOT NULL AND
  is_team_owner(team_id) AND
  NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = user_permissions.user_id AND (role = 'admin' OR is_super_admin = true)
  )
)
WITH CHECK (
  is_property_manager() AND 
  team_id IS NOT NULL AND
  is_team_owner(team_id) AND
  NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE id = user_permissions.user_id AND (role = 'admin' OR is_super_admin = true)
  )
);

-- Users can view their own permissions
CREATE POLICY "Users can view their own permissions"
ON user_permissions
FOR SELECT
USING (user_id = auth.uid());

-- Team members can view permissions for their team
CREATE POLICY "Team members can view permissions for their team"
ON user_permissions
FOR SELECT
USING (
  team_id IS NOT NULL AND
  is_team_member(team_id)
);

-- RLS Policies for team_invitations table

-- Super Admins can do anything with team invitations
CREATE POLICY "Super Admins can do anything with team invitations"
ON team_invitations
USING (is_super_admin())
WITH CHECK (is_super_admin());

-- Admins can do anything with team invitations
CREATE POLICY "Admins can do anything with team invitations"
ON team_invitations
USING (is_admin())
WITH CHECK (is_admin());

-- Team owners can manage invitations for their teams
CREATE POLICY "Team owners can manage invitations for their teams"
ON team_invitations
USING (is_team_owner(team_id))
WITH CHECK (is_team_owner(team_id));

-- Property Managers with manage_staff permission can invite staff
CREATE POLICY "Property Managers can invite staff"
ON team_invitations
USING (
  is_property_manager() AND 
  is_team_member(team_id) AND 
  has_team_permission(team_id, 'manage_staff') AND
  role = 'staff'
)
WITH CHECK (
  is_property_manager() AND 
  is_team_member(team_id) AND 
  has_team_permission(team_id, 'manage_staff') AND
  role = 'staff'
);

-- Property Managers with manage_service_providers permission can invite service providers
CREATE POLICY "Property Managers can invite service providers"
ON team_invitations
USING (
  is_property_manager() AND 
  is_team_member(team_id) AND 
  has_team_permission(team_id, 'manage_service_providers') AND
  role = 'service_provider'
)
WITH CHECK (
  is_property_manager() AND 
  is_team_member(team_id) AND 
  has_team_permission(team_id, 'manage_service_providers') AND
  role = 'service_provider'
);

-- Users can view invitations sent to their email
CREATE POLICY "Users can view invitations sent to their email"
ON team_invitations
FOR SELECT
USING (
  email = (
    SELECT email FROM auth.users WHERE id = auth.uid()
  )
);

-- Create default permissions for new users
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Create a profile for the new user with default role of property_manager
  INSERT INTO profiles (id, email, role, is_super_admin)
  VALUES (
    NEW.id,
    NEW.email,
    'property_manager',
    FALSE
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to handle new users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_user();
