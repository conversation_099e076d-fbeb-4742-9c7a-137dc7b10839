-- Add team_id column to inventory_items table
ALTER TABLE inventory_items ADD COLUMN IF NOT EXISTS team_id UUID REFERENCES teams(id);

-- Create index on team_id for better performance
CREATE INDEX IF NOT EXISTS inventory_items_team_id_idx ON inventory_items(team_id);

-- Update RLS policies for inventory_items to include team-based access
CREATE OR REPLACE FUNCTION public.has_inventory_item_access(p_item_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  v_property_id uuid;
  v_team_id uuid;
BEGIN
  -- Get the property_id and team_id for this item
  SELECT property_id, team_id INTO v_property_id, v_team_id
  FROM inventory_items
  WHERE id = p_item_id;

  -- Super admins and admins have access to all items
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Item creator has access
  IF EXISTS (
    SELECT 1 FROM inventory_items
    WHERE id = p_item_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Direct team access if team_id is set
  IF v_team_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_members
    WHERE team_id = v_team_id AND user_id = auth.uid() AND status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to items for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$function$;

-- Create a trigger to automatically set team_id when property_id is set
CREATE OR REPLACE FUNCTION set_inventory_team_id()
RETURNS TRIGGER AS $$
BEGIN
  -- If property_id is set, find the team_id from team_properties
  IF NEW.property_id IS NOT NULL THEN
    -- Get the first team that has this property
    SELECT team_id INTO NEW.team_id
    FROM team_properties
    WHERE property_id = NEW.property_id
    LIMIT 1;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS set_inventory_team_id_trigger ON inventory_items;
CREATE TRIGGER set_inventory_team_id_trigger
BEFORE INSERT OR UPDATE ON inventory_items
FOR EACH ROW
EXECUTE FUNCTION set_inventory_team_id();
