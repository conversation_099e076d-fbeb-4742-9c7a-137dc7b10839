-- Drop the previous function if it exists
DROP FUNCTION IF EXISTS get_maintenance_tasks_for_user(UUID);

-- Create a simpler version of the function that just returns all tasks for the user
CREATE OR REPLACE FUNCTION get_maintenance_tasks_for_user(p_user_id UUID)
RETURNS SETOF maintenance_tasks
LANGUAGE sql
SECURITY DEFINER
AS $$
    -- Get all tasks for the user
    SELECT * FROM maintenance_tasks
    WHERE
        -- User owns the task
        user_id::text = p_user_id::text
        -- User is assigned to the task
        OR assigned_to::text = p_user_id::text
        -- User is the service provider for the task
        OR provider_id::text = p_user_id::text
        -- User owns the property
        OR property_id IN (
            SELECT id FROM properties WHERE user_id::text = p_user_id::text
        )
        -- User is a team member with access to the property
        OR property_id IN (
            SELECT tp.property_id
            FROM team_properties tp
            JOIN team_members tm ON tp.team_id = tm.team_id
            WHERE tm.user_id::text = p_user_id::text AND tm.status = 'active'
        )
        -- User is a team member with access to the team
        OR team_id IN (
            SELECT team_id
            FROM team_members
            WHERE user_id::text = p_user_id::text AND status = 'active'
        )
        -- User is a team owner
        OR team_id IN (
            SELECT id
            FROM teams
            WHERE owner_id::text = p_user_id::text
        )
    ORDER BY created_at DESC;
$$;
