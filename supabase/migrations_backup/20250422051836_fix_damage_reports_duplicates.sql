-- Fix duplicate damage reports by adding a unique constraint on property_id, title, and user_id

-- First, identify and remove any existing duplicates
DO $$
DECLARE
    duplicate_record RECORD;
BEGIN
    -- Find duplicate records (same property_id and title)
    FOR duplicate_record IN (
        SELECT id, property_id, title, created_at,
               ROW_NUMBER() OVER (PARTITION BY property_id, title ORDER BY created_at DESC) as row_num
        FROM damage_reports
    )
    LOOP
        -- Delete all but the most recent duplicate (keep the newest one)
        IF duplicate_record.row_num > 1 THEN
            DELETE FROM damage_reports WHERE id = duplicate_record.id;
            RAISE NOTICE 'Deleted duplicate damage report: %', duplicate_record.id;
        END IF;
    END LOOP;
END;
$$;

-- Now add a unique constraint to prevent future duplicates
ALTER TABLE damage_reports
ADD CONSTRAINT damage_reports_property_id_title_key UNIQUE (property_id, title);

-- Add an index to improve query performance
CREATE INDEX IF NOT EXISTS damage_reports_property_id_idx ON damage_reports(property_id);
