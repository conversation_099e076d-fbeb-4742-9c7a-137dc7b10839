-- Update existing maintenance tasks with team_id based on their property's team
UPDATE maintenance_tasks mt
SET team_id = tp.team_id
FROM team_properties tp
WHERE mt.property_id = tp.property_id
AND (mt.team_id IS NULL OR mt.team_id != tp.team_id);

-- Add a comment to the maintenance_tasks table to explain the team_id column
COMMENT ON COLUMN maintenance_tasks.team_id IS 'The team that this task belongs to, based on the property. This is used for team-based access control.';

-- Log the update
DO $$
DECLARE
  updated_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO updated_count FROM maintenance_tasks WHERE team_id IS NOT NULL;
  RAISE NOTICE 'Updated % maintenance tasks with team_id', updated_count;
END $$;
