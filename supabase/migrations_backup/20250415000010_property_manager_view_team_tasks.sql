-- Allow property managers to see tasks created by service providers in their team
DO $$
BEGIN
  -- Check if the policy already exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'maintenance_tasks' 
    AND policyname = 'Property managers can view tasks created by team service providers'
  ) THEN
    -- Create the policy if it doesn't exist
    EXECUTE $policy$
    CREATE POLICY "Property managers can view tasks created by team service providers"
    ON maintenance_tasks
    FOR SELECT
    TO public
    USING (
      EXISTS (
        SELECT 1 FROM profiles p
        WHERE p.id = auth.uid() AND p.role = 'property_manager'
        AND EXISTS (
          -- Check if the task creator is a service provider in the property manager's team
          SELECT 1 FROM team_members tm1
          JOIN team_members tm2 ON tm1.team_id = tm2.team_id
          JOIN profiles sp ON tm2.user_id = sp.id
          WHERE tm1.user_id = auth.uid() 
            AND tm1.status = 'active'
            AND tm2.user_id = maintenance_tasks.user_id
            AND tm2.status = 'active'
            AND sp.role = 'service_provider'
        )
      )
    );
    $policy$;
  END IF;
END
$$;
