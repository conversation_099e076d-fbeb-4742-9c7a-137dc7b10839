-- Ensure service providers can update tasks assigned to them
DO $$
BEGIN
  -- Check if the policy already exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'maintenance_tasks' 
    AND policyname = 'Service providers can update tasks assigned to them'
  ) THEN
    -- Create the policy if it doesn't exist
    EXECUTE $policy$
    CREATE POLICY "Service providers can update tasks assigned to them"
    ON maintenance_tasks
    FOR UPDATE
    TO public
    USING (
      (provider_id = auth.uid()) OR 
      (provider_email = (
        SELECT email FROM service_providers
        WHERE id = auth.uid()
      ))
    );
    $policy$;
  END IF;
END
$$;
