-- Create a function to accept invitations and add team members
CREATE OR REPLACE FUNCTION accept_invitation_and_add_member(p_token TEXT, p_user_id UUID)
RETURNS JSONB AS $$
DECLARE
    v_invitation RECORD;
    v_team_id UUID;
    v_role TEXT;
    v_team_member_id UUID;
    v_result JSONB;
BEGIN
    -- Get the invitation details
    SELECT * INTO v_invitation
    FROM team_invitations
    WHERE token = p_token
    AND status = 'pending'
    AND (expires_at IS NULL OR expires_at > NOW());
    
    -- Check if invitation exists and is valid
    IF v_invitation IS NULL THEN
        RETURN jsonb_build_object(
            'success', FALSE,
            'error', 'Invitation not found or has expired'
        );
    END IF;
    
    -- Store team_id and role for later use
    v_team_id := v_invitation.team_id;
    v_role := v_invitation.role;
    
    -- Check if user is already a member of this team
    IF EXISTS (
        SELECT 1 FROM team_members
        WHERE team_id = v_team_id
        AND user_id = p_user_id
        AND status = 'active'
    ) THEN
        -- Update the invitation status to accepted
        UPDATE team_invitations
        SET status = 'accepted'
        WHERE token = p_token;
        
        RETURN jsonb_build_object(
            'success', TRUE,
            'message', 'You are already a member of this team',
            'team_id', v_team_id
        );
    END IF;
    
    -- Begin transaction
    BEGIN
        -- Update the invitation status to accepted
        UPDATE team_invitations
        SET status = 'accepted'
        WHERE token = p_token;
        
        -- Add the user to the team
        INSERT INTO team_members (
            team_id,
            user_id,
            status,
            added_by
        ) VALUES (
            v_team_id,
            p_user_id,
            'active',
            v_invitation.invited_by
        )
        RETURNING id INTO v_team_member_id;
        
        -- Update user profile role if needed
        -- Only update if the user doesn't already have a role or has a lower-priority role
        -- Role priority: super_admin > admin > property_manager > staff > service_provider
        UPDATE profiles
        SET role = v_role
        WHERE id = p_user_id
        AND (
            role IS NULL
            OR (role = 'service_provider' AND v_role IN ('staff', 'property_manager', 'admin', 'super_admin'))
            OR (role = 'staff' AND v_role IN ('property_manager', 'admin', 'super_admin'))
            OR (role = 'property_manager' AND v_role IN ('admin', 'super_admin'))
            OR (role = 'admin' AND v_role = 'super_admin')
        );
        
        -- Add default permissions based on role
        IF v_role = 'property_manager' THEN
            -- Property managers get all basic permissions
            INSERT INTO user_permissions (user_id, team_id, permission, enabled)
            VALUES
                (p_user_id, v_team_id, 'manage_properties', TRUE),
                (p_user_id, v_team_id, 'manage_inventory', TRUE),
                (p_user_id, v_team_id, 'view_inventory', TRUE),
                (p_user_id, v_team_id, 'manage_staff', TRUE),
                (p_user_id, v_team_id, 'manage_service_providers', TRUE),
                (p_user_id, v_team_id, 'manage_purchase_orders', TRUE),
                (p_user_id, v_team_id, 'view_purchase_orders', TRUE),
                (p_user_id, v_team_id, 'manage_damage_reports', TRUE),
                (p_user_id, v_team_id, 'view_damage_reports', TRUE),
                (p_user_id, v_team_id, 'manage_maintenance', TRUE),
                (p_user_id, v_team_id, 'view_maintenance', TRUE),
                (p_user_id, v_team_id, 'view_team', TRUE);
                
        ELSIF v_role = 'staff' THEN
            -- Staff get view permissions and some management permissions
            INSERT INTO user_permissions (user_id, team_id, permission, enabled)
            VALUES
                (p_user_id, v_team_id, 'view_inventory', TRUE),
                (p_user_id, v_team_id, 'view_purchase_orders', TRUE),
                (p_user_id, v_team_id, 'view_damage_reports', TRUE),
                (p_user_id, v_team_id, 'view_maintenance', TRUE),
                (p_user_id, v_team_id, 'view_team', TRUE);
                
        ELSIF v_role = 'service_provider' THEN
            -- Service providers get minimal permissions
            INSERT INTO user_permissions (user_id, team_id, permission, enabled)
            VALUES
                (p_user_id, v_team_id, 'view_damage_reports', TRUE),
                (p_user_id, v_team_id, 'view_maintenance', TRUE);
                
        ELSIF v_role = 'admin' THEN
            -- Admins get all permissions
            INSERT INTO user_permissions (user_id, team_id, permission, enabled)
            VALUES
                (p_user_id, v_team_id, 'manage_properties', TRUE),
                (p_user_id, v_team_id, 'manage_inventory', TRUE),
                (p_user_id, v_team_id, 'view_inventory', TRUE),
                (p_user_id, v_team_id, 'manage_staff', TRUE),
                (p_user_id, v_team_id, 'manage_service_providers', TRUE),
                (p_user_id, v_team_id, 'manage_purchase_orders', TRUE),
                (p_user_id, v_team_id, 'view_purchase_orders', TRUE),
                (p_user_id, v_team_id, 'manage_damage_reports', TRUE),
                (p_user_id, v_team_id, 'view_damage_reports', TRUE),
                (p_user_id, v_team_id, 'manage_maintenance', TRUE),
                (p_user_id, v_team_id, 'view_maintenance', TRUE),
                (p_user_id, v_team_id, 'manage_team', TRUE),
                (p_user_id, v_team_id, 'view_team', TRUE),
                (p_user_id, v_team_id, 'admin', TRUE);
        END IF;
        
        -- Commit transaction
        v_result := jsonb_build_object(
            'success', TRUE,
            'team_id', v_team_id,
            'team_member_id', v_team_member_id
        );
    EXCEPTION WHEN OTHERS THEN
        -- Rollback transaction on error
        RAISE;
        v_result := jsonb_build_object(
            'success', FALSE,
            'error', SQLERRM
        );
    END;
    
    RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
