-- Create a view to make it easier to get team members with their profile information
CREATE OR REPLACE VIEW public.team_members_with_profiles AS
SELECT 
  tm.id,
  tm.team_id,
  tm.user_id,
  tm.added_by,
  tm.status,
  tm.created_at,
  tm.updated_at,
  p.email,
  p.first_name,
  p.last_name,
  p.avatar_url,
  p.role as profile_role,
  p.is_super_admin
FROM 
  team_members tm
JOIN 
  profiles p ON tm.user_id = p.id;

-- Create RLS policy for the view
ALTER VIEW public.team_members_with_profiles OWNER TO postgres;
GRANT SELECT ON public.team_members_with_profiles TO authenticated;
GRANT SELECT ON public.team_members_with_profiles TO anon;
GRANT SELECT ON public.team_members_with_profiles TO service_role;

-- Create a function to get team members with profiles
CREATE OR REPLACE FUNCTION public.get_team_members_with_profiles(p_team_id uuid)
RETURNS SETOF team_members_with_profiles
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT * FROM team_members_with_profiles
  WHERE team_id = p_team_id;
$$;

-- Create a function to get properties for a team member
CREATE OR REPLACE FUNCTION public.get_properties_for_team_member(p_user_id uuid, p_team_id uuid)
RETURNS SETOF properties
LANGUAGE sql
SECURITY DEFINER
AS $$
  -- Get properties owned by the user
  SELECT p.* FROM properties p
  WHERE p.user_id = p_user_id
  
  UNION
  
  -- Get properties in the team
  SELECT p.* FROM properties p
  JOIN team_properties tp ON p.id = tp.property_id
  JOIN team_members tm ON tp.team_id = tm.team_id
  WHERE tm.user_id = p_user_id
  AND tm.team_id = p_team_id
  AND tm.status = 'active';
$$;
