-- Ensure service providers can update tasks they created
DO $$
BEGIN
  -- Check if the policy already exists
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'maintenance_tasks' 
    AND policyname = 'Service providers can update tasks they created'
  ) THEN
    -- Create the policy if it doesn't exist
    EXECUTE $policy$
    CREATE POLICY "Service providers can update tasks they created"
    ON maintenance_tasks
    FOR UPDATE
    TO public
    USING (
      auth.uid() = user_id AND
      EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role = 'service_provider'
      )
    );
    $policy$;
  END IF;
END
$$;
