-- Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Create policy for public read access to inventory files
DROP POLICY IF EXISTS "Public Read Access for Inventory Files" ON storage.objects;
CREATE POLICY "Public Read Access for Inventory Files"
ON storage.objects FOR SELECT
USING (bucket_id = 'inventory');

-- Create policy for authenticated users to upload inventory files
DROP POLICY IF EXISTS "Allow Users to Upload Inventory Files" ON storage.objects;
CREATE POLICY "Allow Users to Upload Inventory Files"
ON storage.objects FOR INSERT
WITH CHECK (bucket_id = 'inventory' AND auth.role() = 'authenticated');

-- Create policy for authenticated users to update their inventory files
DROP POLICY IF EXISTS "Allow Users to Update Inventory Files" ON storage.objects;
CREATE POLICY "Allow Users to Update Inventory Files"
ON storage.objects FOR UPDATE
USING (bucket_id = 'inventory' AND auth.role() = 'authenticated');

-- Create policy for authenticated users to delete their inventory files
DROP POLICY IF EXISTS "Allow Users to Delete Inventory Files" ON storage.objects;
CREATE POLICY "Allow Users to Delete Inventory Files"
ON storage.objects FOR DELETE
USING (bucket_id = 'inventory' AND auth.role() = 'authenticated');

-- Create storage bucket for inventory if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
SELECT 'inventory', 'inventory', true
WHERE NOT EXISTS (
    SELECT 1 FROM storage.buckets WHERE id = 'inventory'
);