-- Fix damage report PDF and invoices

-- 1. Create a function to get damage notes with filtering for private notes
CREATE OR REPLACE FUNCTION get_damage_notes(p_damage_report_id UUID, p_include_private BOOLEAN DEFAULT FALSE)
RETURNS TABLE (
    id UUID,
    damage_report_id UUID,
    content TEXT,
    user_id UUID,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    private BOOLEAN,
    created_by TEXT
) LANGUAGE sql SECURITY DEFINER AS $$
    SELECT
        dn.id,
        dn.damage_report_id,
        dn.content,
        dn.user_id,
        dn.created_at,
        dn.updated_at,
        dn.private,
        dn.created_by
    FROM
        damage_notes dn
    WHERE
        dn.damage_report_id = p_damage_report_id
        AND (p_include_private = TRUE OR dn.private = FALSE OR dn.user_id = auth.uid())
    ORDER BY
        dn.created_at DESC;
$$;

-- <PERSON> execute permissions
GRANT EXECUTE ON FUNCTION get_damage_notes(<PERSON><PERSON><PERSON>, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION get_damage_notes(UUID, BOOLEAN) TO anon;

-- 2. Enable RLS on damage_invoices table
ALTER TABLE damage_invoices ENABLE ROW LEVEL SECURITY;

-- 3. Add RLS policies for damage_invoices
-- Users can view invoices for damage reports they have access to
CREATE POLICY "Users can view damage invoices"
ON damage_invoices
FOR SELECT
TO public
USING (can_access_damage_report(damage_report_id));

-- Users can create invoices for damage reports they have access to
CREATE POLICY "Users can create damage invoices"
ON damage_invoices
FOR INSERT
TO public
WITH CHECK (can_access_damage_report(damage_report_id) AND auth.uid() = user_id);

-- Users can update their own invoices
CREATE POLICY "Users can update their own damage invoices"
ON damage_invoices
FOR UPDATE
TO public
USING (auth.uid() = user_id);

-- Users can delete their own invoices
CREATE POLICY "Users can delete their own damage invoices"
ON damage_invoices
FOR DELETE
TO public
USING (auth.uid() = user_id);

-- 4. Create a function to get damage photos with public URLs
DROP FUNCTION IF EXISTS get_damage_photos(UUID);

CREATE OR REPLACE FUNCTION get_damage_photos(p_damage_report_id UUID)
RETURNS TABLE (
    id UUID,
    damage_report_id UUID,
    file_name TEXT,
    file_path TEXT,
    caption TEXT,
    user_id UUID,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    media_type TEXT,
    public_url TEXT
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN QUERY
  SELECT
    dp.id,
    dp.damage_report_id,
    dp.file_name,
    dp.file_path,
    dp.caption,
    dp.user_id,
    dp.created_at,
    dp.updated_at,
    dp.media_type,
    'https://pwaeknalhosfwuxkpaet.supabase.co/storage/v1/object/public/damage-photos/' || dp.file_path AS public_url
  FROM damage_photos dp
  WHERE dp.damage_report_id = p_damage_report_id
  ORDER BY dp.created_at DESC;

  RETURN;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_damage_photos(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_damage_photos(UUID) TO anon;
