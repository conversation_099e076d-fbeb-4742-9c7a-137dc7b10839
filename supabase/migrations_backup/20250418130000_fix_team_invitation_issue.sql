-- Function to manually fix the team invitation issue
CREATE OR REPLACE FUNCTION public.fix_team_invitation(
  p_email text,
  p_team_id uuid
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id uuid;
  v_invitation_id uuid;
  v_invitation_token text;
  v_invited_by uuid;
  v_team_name text;
BEGIN
  -- Get the user ID from the email
  SELECT id INTO v_user_id
  FROM profiles
  WHERE email = p_email;
  
  IF v_user_id IS NULL THEN
    RETURN jsonb_build_object('success', false, 'error', 'User not found with email: ' || p_email);
  END IF;
  
  -- Get the team name
  SELECT name, owner_id INTO v_team_name, v_invited_by
  FROM teams
  WHERE id = p_team_id;
  
  IF v_team_name IS NULL THEN
    RETURN jsonb_build_object('success', false, 'error', 'Team not found with ID: ' || p_team_id);
  END IF;
  
  -- Check if there's a pending invitation
  SELECT id, token INTO v_invitation_id, v_invitation_token
  FROM team_invitations
  WHERE email = p_email AND team_id = p_team_id AND status = 'pending';
  
  -- If there's no pending invitation, create one
  IF v_invitation_id IS NULL THEN
    v_invitation_token := encode(gen_random_bytes(16), 'hex');
    
    INSERT INTO team_invitations (
      team_id,
      email,
      invited_by,
      role,
      token,
      status,
      created_at,
      updated_at,
      expires_at
    ) VALUES (
      p_team_id,
      p_email,
      v_invited_by,
      'property_manager',
      v_invitation_token,
      'pending',
      NOW(),
      NOW(),
      NOW() + INTERVAL '7 days'
    )
    RETURNING id, token INTO v_invitation_id, v_invitation_token;
  END IF;
  
  -- Add the user to the team
  INSERT INTO team_members (team_id, user_id, added_by, status, created_at, updated_at)
  VALUES (p_team_id, v_user_id, v_invited_by, 'active', NOW(), NOW())
  ON CONFLICT (team_id, user_id) 
  DO UPDATE SET status = 'active', updated_at = NOW();
  
  -- Update the invitation status to accepted
  UPDATE team_invitations
  SET status = 'accepted', updated_at = NOW()
  WHERE id = v_invitation_id;
  
  RETURN jsonb_build_object(
    'success', true, 
    'message', 'User ' || p_email || ' has been added to team ' || v_team_name,
    'user_id', v_user_id,
    'team_id', p_team_id,
    'invitation_id', v_invitation_id
  );
END;
$$;
