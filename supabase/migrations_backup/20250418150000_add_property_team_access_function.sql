-- Create a function to get a property if the user is a team member
CREATE OR REPLACE FUNCTION public.get_property_if_team_member(
  p_property_id uuid,
  p_user_id uuid
)
RETURNS SETOF properties
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the user is a super admin or admin
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = p_user_id AND (is_super_admin = true OR role = 'admin')
  ) THEN
    -- Return the property directly for admins
    RETURN QUERY
    SELECT * FROM properties
    WHERE id = p_property_id;
    RETURN;
  END IF;

  -- Check if the user owns the property
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE id = p_property_id AND user_id = p_user_id
  ) THEN
    -- Return the property if the user owns it
    RETURN QUERY
    SELECT * FROM properties
    WHERE id = p_property_id AND user_id = p_user_id;
    RETURN;
  END IF;

  -- Check if the user is a member of a team that has access to the property
  IF EXISTS (
    SELECT 1 FROM team_members tm
    JOIN team_properties tp ON tm.team_id = tp.team_id
    WHERE tm.user_id = p_user_id
    AND tp.property_id = p_property_id
    AND tm.status = 'active'
  ) THEN
    -- Return the property if the user is in a team with access
    RETURN QUERY
    SELECT p.* FROM properties p
    JOIN team_properties tp ON p.id = tp.property_id
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE p.id = p_property_id
    AND tm.user_id = p_user_id
    AND tm.status = 'active';
    RETURN;
  END IF;

  -- If none of the above conditions are met, return an empty result
  RETURN;
END;
$$;
