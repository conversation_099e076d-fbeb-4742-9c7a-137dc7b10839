-- Drop ALL policies on user_permissions table
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'user_permissions'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON user_permissions', policy_record.policyname);
    END LOOP;
END
$$;

-- Create a single, simple policy for user_permissions
CREATE POLICY "user_permissions_simple_policy"
ON user_permissions
FOR ALL
USING (true)
WITH CHECK (true);

-- Enable RLS on user_permissions
ALTER TABLE user_permissions ENABLE ROW LEVEL SECURITY;
