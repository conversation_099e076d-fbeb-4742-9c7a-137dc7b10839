-- Drop existing policies for teams table
DROP POLICY IF EXISTS "Teams are viewable by team members" ON teams;
DROP POLICY IF EXISTS "Teams are viewable by team owners" ON teams;
DROP POLICY IF EXISTS "Teams are editable by team owners" ON teams;

-- Create new policies with improved access control
CREATE POLICY "Teams are viewable by team members"
ON teams
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM team_members
    WHERE team_members.team_id = teams.id
    AND team_members.user_id = auth.uid()
    AND team_members.status = 'active'
  )
);

CREATE POLICY "Teams are viewable by admins"
ON teams
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  )
);

CREATE POLICY "Teams are editable by team owners"
ON teams
FOR ALL
USING (
  teams.owner_id = auth.uid()
)
WITH CHECK (
  teams.owner_id = auth.uid()
);
