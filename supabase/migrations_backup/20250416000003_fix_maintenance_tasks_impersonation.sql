-- Create a function to check if a user has access to a maintenance task during impersonation
CREATE OR REPLACE FUNCTION public.has_maintenance_task_access_impersonation(p_task_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_property_id uuid;
  v_provider_id uuid;
  v_provider_email text;
  v_user_id uuid;
BEGIN
  -- Get the task details
  SELECT property_id, provider_id, provider_email, user_id INTO v_property_id, v_provider_id, v_provider_email, v_user_id
  FROM maintenance_tasks
  WHERE id = p_task_id;

  -- Super admins and admins have access to all tasks
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Task creator has access
  IF v_user_id = auth.uid() THEN
    RETURN TRUE;
  END IF;

  -- Provider assigned to the task has access
  IF v_provider_id = auth.uid() THEN
    RETURN TRUE;
  END IF;

  -- Provider with matching email has access
  IF v_provider_email IS NOT NULL AND EXISTS (
    SELECT 1 FROM service_providers
    WHERE id = auth.uid() AND email = v_provider_email
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to tasks for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;

-- Update the has_maintenance_task_access function to use has_user_access for impersonation
CREATE OR REPLACE FUNCTION public.has_maintenance_task_access(p_task_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id uuid;
BEGIN
  -- Get the user_id for this task
  SELECT user_id INTO v_user_id
  FROM maintenance_tasks
  WHERE id = p_task_id;

  -- Super admins and admins have access to all tasks
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    -- If the admin is impersonating a user, check if that user has access
    IF v_user_id IS NOT NULL AND has_user_access(v_user_id) THEN
      RETURN TRUE;
    END IF;
    
    -- Otherwise, use the regular access check
    RETURN has_maintenance_task_access_impersonation(p_task_id);
  END IF;

  -- For non-admins, use the regular access check
  RETURN has_maintenance_task_access_impersonation(p_task_id);
END;
$$;

COMMENT ON FUNCTION public.has_maintenance_task_access IS 'Determines if the current user has access to a maintenance task. Handles impersonation for super admins and admins.';
