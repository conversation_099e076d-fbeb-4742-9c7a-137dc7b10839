-- Fix duplicate damage reports by adding a trigger that prevents duplicates

-- First, clean up any existing duplicates
DO $$
DECLARE
    duplicate_record RECORD;
BEGIN
    -- Find duplicate records (same property_id and title)
    FOR duplicate_record IN (
        SELECT id, property_id, title, created_at,
               ROW_NUMBER() OVER (PARTITION BY property_id, title ORDER BY created_at DESC) as row_num
        FROM damage_reports
    )
    LOOP
        -- Delete all but the most recent duplicate (keep the newest one)
        IF duplicate_record.row_num > 1 THEN
            DELETE FROM damage_reports WHERE id = duplicate_record.id;
            RAISE NOTICE 'Deleted duplicate damage report: %', duplicate_record.id;
        END IF;
    END LOOP;
END;
$$;

-- Create a function to check for duplicates
CREATE OR REPLACE FUNCTION check_damage_report_duplicate()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if there's already a damage report with the same property_id and title
    IF EXISTS (
        SELECT 1 FROM damage_reports
        WHERE property_id = NEW.property_id
        AND title = NEW.title
        AND id != NEW.id
    ) THEN
        RAISE EXCEPTION 'A damage report with the same title already exists for this property';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS check_damage_report_duplicate_trigger ON damage_reports;
CREATE TRIGGER check_damage_report_duplicate_trigger
BEFORE INSERT OR UPDATE ON damage_reports
FOR EACH ROW
EXECUTE FUNCTION check_damage_report_duplicate();

-- Create a function to get damage reports for a property
CREATE OR REPLACE FUNCTION get_property_damage_reports(p_property_id UUID)
RETURNS TABLE (
    id UUID,
    title TEXT,
    description TEXT,
    status TEXT,
    property_id UUID,
    property_name TEXT,
    user_id UUID,
    user_email VARCHAR(255),
    provider_id UUID,
    provider_name TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    platform TEXT
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    RETURN QUERY
    SELECT
        dr.id,
        dr.title,
        dr.description,
        dr.status,
        dr.property_id,
        p.name AS property_name,
        dr.user_id,
        u.email AS user_email,
        dr.provider_id,
        mp.name AS provider_name,
        dr.created_at,
        dr.updated_at,
        dr.platform
    FROM
        damage_reports dr
    JOIN
        properties p ON dr.property_id = p.id
    LEFT JOIN
        auth.users u ON dr.user_id = u.id
    LEFT JOIN
        maintenance_providers mp ON dr.provider_id = mp.id
    WHERE
        dr.property_id = p_property_id
    ORDER BY
        dr.created_at DESC;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_property_damage_reports(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_property_damage_reports(UUID) TO anon;
