-- One-time sync to update existing inventory items with team_id values
-- This will set the team_id for all inventory items based on their property's team assignment

-- First, create a temporary function to handle the sync
CREATE OR REPLACE FUNCTION temp_sync_inventory_team_ids()
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
    r RECORD;
BEGIN
    -- Log start of sync
    RAISE NOTICE 'Starting inventory team_id sync...';
    
    -- For each property that belongs to a team
    FOR r IN 
        SELECT DISTINCT tp.property_id, tp.team_id 
        FROM team_properties tp
    LOOP
        -- Update all inventory items for this property to have the team_id
        UPDATE inventory_items
        SET team_id = r.team_id
        WHERE property_id = r.property_id
        AND (team_id IS NULL OR team_id != r.team_id);
        
        -- Log each property update
        RAISE NOTICE 'Updated inventory items for property % with team %', r.property_id, r.team_id;
    END LOOP;
    
    -- Log completion
    RAISE NOTICE 'Inventory team_id sync completed';
END;
$$;

-- Execute the sync function
SELECT temp_sync_inventory_team_ids();

-- Drop the temporary function
DROP FUNCTION temp_sync_inventory_team_ids();

-- Also sync purchase orders to ensure they have the correct team_id
-- First add team_id column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'purchase_orders' AND column_name = 'team_id'
    ) THEN
        ALTER TABLE purchase_orders ADD COLUMN team_id UUID REFERENCES teams(id);
        CREATE INDEX IF NOT EXISTS purchase_orders_team_id_idx ON purchase_orders(team_id);
    END IF;
END
$$;

-- Update purchase orders with team_id based on their property's team
UPDATE purchase_orders po
SET team_id = tp.team_id
FROM team_properties tp
WHERE po.property_id = tp.property_id
AND (po.team_id IS NULL OR po.team_id != tp.team_id);

-- Also sync damage_reports to ensure they have the correct team_id
-- First add team_id column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'damage_reports' AND column_name = 'team_id'
    ) THEN
        ALTER TABLE damage_reports ADD COLUMN team_id UUID REFERENCES teams(id);
        CREATE INDEX IF NOT EXISTS damage_reports_team_id_idx ON damage_reports(team_id);
    END IF;
END
$$;

-- Update damage reports with team_id based on their property's team
UPDATE damage_reports dr
SET team_id = tp.team_id
FROM team_properties tp
WHERE dr.property_id = tp.property_id
AND (dr.team_id IS NULL OR dr.team_id != tp.team_id);

-- Also sync maintenance_tasks to ensure they have the correct team_id
-- First add team_id column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name = 'maintenance_tasks' AND column_name = 'team_id'
    ) THEN
        ALTER TABLE maintenance_tasks ADD COLUMN team_id UUID REFERENCES teams(id);
        CREATE INDEX IF NOT EXISTS maintenance_tasks_team_id_idx ON maintenance_tasks(team_id);
    END IF;
END
$$;

-- Update maintenance tasks with team_id based on their property's team
UPDATE maintenance_tasks mt
SET team_id = tp.team_id
FROM team_properties tp
WHERE mt.property_id = tp.property_id
AND (mt.team_id IS NULL OR mt.team_id != tp.team_id);

-- Create triggers to automatically set team_id for new records in these tables

-- For purchase_orders
CREATE OR REPLACE FUNCTION set_purchase_order_team_id()
RETURNS TRIGGER AS $$
BEGIN
  -- If property_id is set, find the team_id from team_properties
  IF NEW.property_id IS NOT NULL THEN
    -- Get the first team that has this property
    SELECT team_id INTO NEW.team_id
    FROM team_properties
    WHERE property_id = NEW.property_id
    LIMIT 1;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger for purchase_orders
DROP TRIGGER IF EXISTS set_purchase_order_team_id_trigger ON purchase_orders;
CREATE TRIGGER set_purchase_order_team_id_trigger
BEFORE INSERT OR UPDATE ON purchase_orders
FOR EACH ROW
EXECUTE FUNCTION set_purchase_order_team_id();

-- For damage_reports
CREATE OR REPLACE FUNCTION set_damage_report_team_id()
RETURNS TRIGGER AS $$
BEGIN
  -- If property_id is set, find the team_id from team_properties
  IF NEW.property_id IS NOT NULL THEN
    -- Get the first team that has this property
    SELECT team_id INTO NEW.team_id
    FROM team_properties
    WHERE property_id = NEW.property_id
    LIMIT 1;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger for damage_reports
DROP TRIGGER IF EXISTS set_damage_report_team_id_trigger ON damage_reports;
CREATE TRIGGER set_damage_report_team_id_trigger
BEFORE INSERT OR UPDATE ON damage_reports
FOR EACH ROW
EXECUTE FUNCTION set_damage_report_team_id();

-- For maintenance_tasks
CREATE OR REPLACE FUNCTION set_maintenance_task_team_id()
RETURNS TRIGGER AS $$
BEGIN
  -- If property_id is set, find the team_id from team_properties
  IF NEW.property_id IS NOT NULL THEN
    -- Get the first team that has this property
    SELECT team_id INTO NEW.team_id
    FROM team_properties
    WHERE property_id = NEW.property_id
    LIMIT 1;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger for maintenance_tasks
DROP TRIGGER IF EXISTS set_maintenance_task_team_id_trigger ON maintenance_tasks;
CREATE TRIGGER set_maintenance_task_team_id_trigger
BEFORE INSERT OR UPDATE ON maintenance_tasks
FOR EACH ROW
EXECUTE FUNCTION set_maintenance_task_team_id();
