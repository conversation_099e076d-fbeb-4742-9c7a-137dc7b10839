-- Fix 1: Modify the set_inventory_team_id trigger to also run on UPDATE of team_properties

-- First, create a function to update inventory items when a property is added to a team
CREATE OR REPLACE FUNCTION public.update_inventory_team_id_on_team_property_change()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  -- When a property is added to a team, update all inventory items for that property
  IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND OLD.team_id != NEW.team_id) THEN
    UPDATE inventory_items
    SET team_id = NEW.team_id
    WHERE property_id = NEW.property_id
    AND (team_id IS NULL OR team_id != NEW.team_id);

    RAISE NOTICE 'Updated inventory items for property % with team %', NEW.property_id, NEW.team_id;
  END IF;

  -- When a property is removed from a team, clear the team_id for inventory items
  IF TG_OP = 'DELETE' THEN
    UPDATE inventory_items
    SET team_id = NULL
    WHERE property_id = OLD.property_id
    AND team_id = OLD.team_id;

    RAISE NOTICE 'Cleared team_id for inventory items of property % from team %', OLD.property_id, OLD.team_id;
  END IF;

  RETURN NULL; -- for AFTER triggers
END;
$$;

-- Create the trigger on team_properties table
DROP TRIGGER IF EXISTS update_inventory_team_id_trigger ON team_properties;
CREATE TRIGGER update_inventory_team_id_trigger
AFTER INSERT OR UPDATE OR DELETE ON team_properties
FOR EACH ROW
EXECUTE FUNCTION update_inventory_team_id_on_team_property_change();

-- Fix 2: Add a function to sync all inventory items when a property is added to a team
CREATE OR REPLACE FUNCTION public.sync_inventory_team_ids()
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
    r RECORD;
BEGIN
    -- Log start of sync
    RAISE NOTICE 'Starting inventory team_id sync...';

    -- For each property that belongs to a team
    FOR r IN
        SELECT DISTINCT tp.property_id, tp.team_id
        FROM team_properties tp
    LOOP
        -- Update all inventory items for this property to have the team_id
        UPDATE inventory_items
        SET team_id = r.team_id
        WHERE property_id = r.property_id
        AND (team_id IS NULL OR team_id != r.team_id);

        -- Log each property update
        RAISE NOTICE 'Updated inventory items for property % with team %', r.property_id, r.team_id;
    END LOOP;

    -- Log completion
    RAISE NOTICE 'Inventory team_id sync completed';
END;
$$;

-- Fix 3: Implement a check to prevent duplicate property names within the same account or team

-- Create a function to check for duplicate property names
CREATE OR REPLACE FUNCTION public.check_duplicate_property_name()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  -- Check for duplicate names within the same user account
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE name = NEW.name
    AND user_id = NEW.user_id
    AND id != NEW.id
  ) THEN
    RAISE EXCEPTION 'A property with the name "%" already exists in your account', NEW.name;
  END IF;

  RETURN NEW;
END;
$$;

-- Create the trigger on properties table
DROP TRIGGER IF EXISTS check_duplicate_property_name_trigger ON properties;
CREATE TRIGGER check_duplicate_property_name_trigger
BEFORE INSERT OR UPDATE ON properties
FOR EACH ROW
EXECUTE FUNCTION check_duplicate_property_name();

-- Create a function to check for duplicate property names within a team
CREATE OR REPLACE FUNCTION public.check_team_duplicate_property_name()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
  v_property_name TEXT;
  v_team_name TEXT;
  v_duplicate_count INTEGER;
BEGIN
  -- Get the property name
  SELECT name INTO v_property_name FROM properties WHERE id = NEW.property_id;

  -- Get the team name
  SELECT name INTO v_team_name FROM teams WHERE id = NEW.team_id;

  -- Count properties with the same name in this team
  SELECT COUNT(*) INTO v_duplicate_count
  FROM team_properties tp
  JOIN properties p ON tp.property_id = p.id
  WHERE tp.team_id = NEW.team_id
  AND p.name = v_property_name
  AND tp.property_id != NEW.property_id;

  -- If there's a duplicate, raise a warning (not an exception to avoid breaking existing functionality)
  IF v_duplicate_count > 0 THEN
    RAISE WARNING 'Warning: Adding property "%" to team "%" will create a duplicate name (% existing properties with this name)',
      v_property_name, v_team_name, v_duplicate_count;
  END IF;

  RETURN NEW;
END;
$$;

-- Create the trigger on team_properties table
DROP TRIGGER IF EXISTS check_team_duplicate_property_name_trigger ON team_properties;
CREATE TRIGGER check_team_duplicate_property_name_trigger
BEFORE INSERT ON team_properties
FOR EACH ROW
EXECUTE FUNCTION check_team_duplicate_property_name();

-- Run the sync function once to update all existing inventory items
SELECT sync_inventory_team_ids();
