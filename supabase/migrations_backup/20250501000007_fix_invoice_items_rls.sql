-- Enable RLS on invoice_items table
ALTER TABLE invoice_items ENABLE ROW LEVEL SECURITY;

-- Create a function to check if a user has access to an invoice item
CREATE OR REPLACE FUNCTION public.has_invoice_item_access(p_item_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  v_invoice_id uuid;
  v_damage_report_id uuid;
  v_user_id uuid;
BEGIN
  -- Get the invoice_id for this item
  SELECT invoice_id INTO v_invoice_id
  FROM invoice_items
  WHERE id = p_item_id;

  -- Get the damage_report_id and user_id for this invoice
  SELECT damage_report_id, user_id INTO v_damage_report_id, v_user_id
  FROM damage_invoices
  WHERE id = v_invoice_id;

  -- Super admins and admins have access to all items
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Invoice creator has access
  IF v_user_id = auth.uid() THEN
    RETURN TRUE;
  END IF;

  -- Check if user can access the damage report
  RETURN can_access_damage_report(v_damage_report_id);
END;
$function$;

-- Add RLS policy for users to view invoice items
CREATE POLICY "Users can view invoice items"
ON invoice_items
FOR SELECT
TO public
USING (
  has_invoice_item_access(id)
);

-- Add RLS policy for users to insert invoice items
CREATE POLICY "Users can insert invoice items"
ON invoice_items
FOR INSERT
TO public
WITH CHECK (
  EXISTS (
    SELECT 1 FROM damage_invoices di
    WHERE di.id = invoice_items.invoice_id
    AND (
      -- User created the invoice
      di.user_id = auth.uid()
      OR
      -- User can access the damage report and is a service provider assigned to it
      (
        can_access_damage_report(di.damage_report_id)
        AND EXISTS (
          SELECT 1 FROM damage_reports dr
          WHERE dr.id = di.damage_report_id
          AND dr.provider_id = auth.uid()
        )
      )
    )
  )
);

-- Add RLS policy for users to update invoice items
CREATE POLICY "Users can update invoice items"
ON invoice_items
FOR UPDATE
TO public
USING (
  EXISTS (
    SELECT 1 FROM damage_invoices di
    WHERE di.id = invoice_items.invoice_id
    AND (
      -- User created the invoice
      di.user_id = auth.uid()
      OR
      -- User can access the damage report and is a service provider assigned to it
      (
        can_access_damage_report(di.damage_report_id)
        AND EXISTS (
          SELECT 1 FROM damage_reports dr
          WHERE dr.id = di.damage_report_id
          AND dr.provider_id = auth.uid()
        )
      )
    )
  )
);

-- Add RLS policy for users to delete invoice items
CREATE POLICY "Users can delete invoice items"
ON invoice_items
FOR DELETE
TO public
USING (
  EXISTS (
    SELECT 1 FROM damage_invoices di
    WHERE di.id = invoice_items.invoice_id
    AND (
      -- User created the invoice
      di.user_id = auth.uid()
      OR
      -- User can access the damage report and is a service provider assigned to it
      (
        can_access_damage_report(di.damage_report_id)
        AND EXISTS (
          SELECT 1 FROM damage_reports dr
          WHERE dr.id = di.damage_report_id
          AND dr.provider_id = auth.uid()
        )
      )
    )
  )
);
