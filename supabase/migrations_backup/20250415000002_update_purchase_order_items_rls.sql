-- Create a function to check if a user has access to a purchase order item
CREATE OR REPLACE FUNCTION public.has_purchase_order_item_access(p_item_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  v_order_id uuid;
  v_property_id uuid;
BEGIN
  -- Get the purchase_order_id for this item
  SELECT purchase_order_id INTO v_order_id
  FROM purchase_order_items
  WHERE id = p_item_id;

  -- Get the property_id for this order
  SELECT property_id INTO v_property_id
  FROM purchase_orders
  WHERE id = v_order_id;

  -- Super admins and admins have access to all items
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Order creator has access
  IF EXISTS (
    SELECT 1 FROM purchase_orders
    WHERE id = v_order_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to items for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$function$;

-- Add RLS policy for team members to view purchase order items
CREATE POLICY "Team members can view purchase order items for their properties"
ON purchase_order_items
FOR SELECT
TO public
USING (
  has_purchase_order_item_access(id)
);

-- Add RLS policy for team members to update purchase order items
CREATE POLICY "Team members can update purchase order items for their properties"
ON purchase_order_items
FOR UPDATE
TO public
USING (
  has_purchase_order_item_access(id)
);

-- Add RLS policy for team members to insert purchase order items
CREATE POLICY "Team members can insert purchase order items for their properties"
ON purchase_order_items
FOR INSERT
TO public
WITH CHECK (
  EXISTS (
    SELECT 1 FROM purchase_orders po
    JOIN team_properties tp ON po.property_id = tp.property_id
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE po.id = purchase_order_items.purchase_order_id
    AND tm.user_id = auth.uid()
    AND tm.status = 'active'
  )
);

-- Add RLS policy for team members to delete purchase order items
CREATE POLICY "Team members can delete purchase order items for their properties"
ON purchase_order_items
FOR DELETE
TO public
USING (
  has_purchase_order_item_access(id)
);

-- Update the has_purchase_order_access function to include property managers
CREATE OR REPLACE FUNCTION public.has_purchase_order_access(p_order_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
DECLARE
  v_property_id uuid;
  v_user_id uuid;
BEGIN
  -- Get the property_id and user_id for this order
  SELECT property_id, user_id INTO v_property_id, v_user_id
  FROM purchase_orders
  WHERE id = p_order_id;

  -- Super admins and admins have access to all orders
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Order creator has access
  IF v_user_id = auth.uid() THEN
    RETURN TRUE;
  END IF;

  -- Property managers have access to orders for properties they own
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE id = v_property_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to orders for properties in their teams
  IF v_property_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = v_property_id AND tm.user_id = auth.uid() AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$function$;

-- Add RLS policy for team members to insert purchase orders
CREATE POLICY "Team members can insert purchase orders for their properties"
ON purchase_orders
FOR INSERT
TO public
WITH CHECK (
  property_id IN (
    SELECT tp.property_id
    FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tm.user_id = auth.uid() AND tm.status = 'active'
  )
);

-- Add RLS policy for team members to delete purchase orders
CREATE POLICY "Team members can delete purchase orders for their properties"
ON purchase_orders
FOR DELETE
TO public
USING (
  has_purchase_order_access(id)
);
