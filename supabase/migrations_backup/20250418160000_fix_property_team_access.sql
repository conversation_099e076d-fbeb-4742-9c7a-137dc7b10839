-- Create a more robust function to check if a user can access a property
CREATE OR REPLACE FUNCTION public.has_property_access(p_property_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Super admins and admins have access to all properties
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Property managers have access to properties they own
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE id = p_property_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to properties in their teams
  IF EXISTS (
    SELECT 1 FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tp.property_id = p_property_id
    AND tm.user_id = auth.uid()
    AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;

-- Create a function to get a property with all its details
CREATE OR REPLACE FUNCTION public.get_property_details(p_property_id uuid)
RETURNS SETOF properties
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the user has access to the property
  IF NOT has_property_access(p_property_id) THEN
    RAISE EXCEPTION 'You do not have access to this property';
  END IF;

  -- Return the property details
  RETURN QUERY
  SELECT * FROM properties
  WHERE id = p_property_id;
END;
$$;
