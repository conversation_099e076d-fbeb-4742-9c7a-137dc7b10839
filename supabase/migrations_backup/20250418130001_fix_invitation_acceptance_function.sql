-- Fix the accept_invitation_and_add_member function to handle email mismatches
CREATE OR REPLACE FUNCTION public.accept_invitation_and_add_member(p_token text, p_user_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_team_id UUID;
    v_email TEXT;
    v_status TEXT;
    v_invited_by UUID;
    v_role TEXT;
    v_result JSONB;
    v_user_email TEXT;
BEGIN
    -- Get the invitation details
    SELECT team_id, email, status, invited_by, role INTO v_team_id, v_email, v_status, v_invited_by, v_role
    FROM team_invitations
    WHERE token = p_token;
    
    -- Check if invitation exists
    IF v_team_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'error', 'Invitation not found');
    END IF;
    
    -- Get the user's email
    SELECT email INTO v_user_email
    FROM profiles
    WHERE id = p_user_id;
    
    -- Log the emails for debugging
    RAISE NOTICE 'Invitation email: %, User email: %', v_email, v_user_email;
    
    -- If invitation is already accepted, just add the user to the team
    IF v_status = 'accepted' THEN
        -- Add the user to the team using the add_user_to_team function
        DECLARE
            team_result JSONB;
        BEGIN
            team_result := add_user_to_team(p_user_id, v_team_id, v_invited_by);
            
            -- If the user is a service provider, add default permissions
            IF EXISTS (SELECT 1 FROM profiles WHERE id = p_user_id AND role = 'service_provider') THEN
                PERFORM add_service_provider_default_permissions(p_user_id, v_team_id);
            END IF;
            
            RETURN team_result;
        END;
    END IF;
    
    -- If invitation is not pending, return error
    IF v_status != 'pending' THEN
        RETURN jsonb_build_object('success', false, 'error', 'Invitation is not pending or accepted');
    END IF;
    
    -- Update the invitation status
    UPDATE team_invitations
    SET status = 'accepted', updated_at = NOW()
    WHERE token = p_token;
    
    -- Update the user's profile with the invited email if it's different
    -- This helps with email mismatch issues
    IF v_user_email IS DISTINCT FROM v_email THEN
        RAISE NOTICE 'Updating user profile email from % to %', v_user_email, v_email;
        
        -- Only update if the user doesn't already have an email
        IF v_user_email IS NULL OR v_user_email = '' THEN
            UPDATE profiles
            SET email = v_email, updated_at = NOW()
            WHERE id = p_user_id;
        END IF;
    END IF;
    
    -- Add the user to the team using the add_user_to_team function
    DECLARE
        team_result JSONB;
    BEGIN
        team_result := add_user_to_team(p_user_id, v_team_id, v_invited_by);
        
        -- If the user is a service provider, add default permissions
        IF v_role = 'service_provider' OR EXISTS (SELECT 1 FROM profiles WHERE id = p_user_id AND role = 'service_provider') THEN
            PERFORM add_service_provider_default_permissions(p_user_id, v_team_id);
        END IF;
        
        RETURN team_result;
    END;
    
EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object('success', false, 'error', 'Unexpected error: ' || SQLERRM);
END;
$$;
