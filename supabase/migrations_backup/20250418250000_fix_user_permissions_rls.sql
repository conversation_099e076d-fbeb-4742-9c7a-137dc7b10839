-- Drop existing policies for user_permissions table
DROP POLICY IF EXISTS "User permissions are viewable by team members" ON user_permissions;
DROP POLICY IF EXISTS "User permissions are viewable by team owners" ON user_permissions;
DROP POLICY IF EXISTS "User permissions are editable by team owners" ON user_permissions;

-- Create new policies with improved access control
CREATE POLICY "User permissions are viewable by the user"
ON user_permissions
FOR SELECT
USING (
  auth.uid() = user_id
);

CREATE POLICY "User permissions are viewable by team owners"
ON user_permissions
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM teams
    WHERE teams.id = user_permissions.team_id
    AND teams.owner_id = auth.uid()
  )
);

CREATE POLICY "User permissions are viewable by admins"
ON user_permissions
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  )
);

CREATE POLICY "User permissions are editable by team owners"
ON user_permissions
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM teams
    WHERE teams.id = user_permissions.team_id
    AND teams.owner_id = auth.uid()
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM teams
    WHERE teams.id = user_permissions.team_id
    AND teams.owner_id = auth.uid()
  )
);
