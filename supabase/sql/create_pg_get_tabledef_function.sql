
-- This function retrieves the table definition in SQL format for a given table
CREATE OR REPLACE FUNCTION public.pg_get_tabledef(
  in_schema VARCHAR,
  in_table VARCHAR,
  _verbose BOOLEAN DEFAULT FALSE
)
RETURNS TEXT
LANGUAGE plpgsql
VOLATILE
AS $$
DECLARE
  v_table_ddl TEXT := '';
  v_table_oid INT;
  v_colrec RECORD;
  v_constraintrec RECORD;
  v_indexrec RECORD;
  v_rec RECORD;
  v_tablespace TEXT;
  v_temp TEXT;
  constraintarr TEXT[] := '{}';
  bSkip BOOLEAN;
BEGIN
  -- Get the table OID
  SELECT c.oid
  INTO v_table_oid
  FROM pg_catalog.pg_class c
  LEFT JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
  WHERE c.relkind in ('r','p')
    AND c.relname = in_table
    AND n.nspname = in_schema;
  
  IF (v_table_oid IS NULL) THEN
    RAISE EXCEPTION 'Table %.% does not exist', in_schema, in_table;
  END IF;
  
  -- Get tablespace if not default
  SELECT tablespace 
  INTO v_temp 
  FROM pg_tables 
  WHERE schemaname = in_schema 
    AND tablename = in_table 
    AND tablespace IS NOT NULL;
  
  IF v_temp IS NULL THEN 
    v_tablespace := '';
  ELSE
    v_tablespace := ' TABLESPACE ' || v_temp;
  END IF;
  
  -- Start building the CREATE TABLE statement
  v_table_ddl := 'CREATE TABLE ' || in_schema || '.' || in_table || ' (' || E'\n';
  
  -- Add columns
  FOR v_colrec IN 
    SELECT 
      c.column_name,
      CASE 
        WHEN c.data_type = 'USER-DEFINED' THEN 
          c.udt_schema || '.' || c.udt_name
        ELSE 
          c.data_type
      END as data_type,
      c.character_maximum_length,
      c.is_nullable,
      c.column_default,
      c.numeric_precision,
      c.numeric_scale
    FROM information_schema.columns c
    WHERE table_schema = in_schema
      AND table_name = in_table
    ORDER BY ordinal_position 
  LOOP
    v_table_ddl := v_table_ddl || '  ' || v_colrec.column_name || ' ' || v_colrec.data_type;
    
    -- Add length for character types
    IF v_colrec.character_maximum_length IS NOT NULL THEN
      v_table_ddl := v_table_ddl || '(' || v_colrec.character_maximum_length || ')';
    ELSIF v_colrec.numeric_precision > 0 AND v_colrec.numeric_scale > 0 THEN
      v_table_ddl := v_table_ddl || '(' || v_colrec.numeric_precision || ',' || v_colrec.numeric_scale || ')';
    END IF;
    
    -- Add nullability
    IF v_colrec.is_nullable = 'NO' THEN
      v_table_ddl := v_table_ddl || ' NOT NULL';
    END IF;
    
    -- Add default value
    IF v_colrec.column_default IS NOT NULL THEN
      v_table_ddl := v_table_ddl || ' DEFAULT ' || v_colrec.column_default;
    END IF;
    
    v_table_ddl := v_table_ddl || ',' || E'\n';
  END LOOP;
  
  -- Add constraints
  FOR v_constraintrec IN 
    SELECT 
      con.conname as constraint_name,
      con.contype as constraint_type,
      pg_get_constraintdef(con.oid) as constraint_definition
    FROM pg_catalog.pg_constraint con
    JOIN pg_catalog.pg_class rel ON rel.oid = con.conrelid
    JOIN pg_catalog.pg_namespace nsp ON nsp.oid = connamespace
    WHERE nsp.nspname = in_schema
      AND rel.relname = in_table
    ORDER BY con.contype
  LOOP
    v_table_ddl := v_table_ddl || '  CONSTRAINT ' || v_constraintrec.constraint_name || ' ' 
                || v_constraintrec.constraint_definition || ',' || E'\n';
    
    constraintarr := constraintarr || v_constraintrec.constraint_name::text;
  END LOOP;
  
  -- Remove trailing comma
  IF substring(v_table_ddl, length(v_table_ddl) - 1, 1) = ',' THEN
    v_table_ddl := substr(v_table_ddl, 0, length(v_table_ddl) - 1) || E'\n';
  END IF;
  
  -- Close the CREATE TABLE statement
  v_table_ddl := v_table_ddl || ')' || v_tablespace || ';';
  
  -- Add indexes that aren't part of constraints
  FOR v_indexrec IN 
    SELECT indexdef, indexname
    FROM pg_indexes 
    WHERE (schemaname, tablename) = (in_schema, in_table)
  LOOP
    bSkip := FALSE;
    
    -- Skip indexes that are part of constraints
    FOREACH v_temp IN ARRAY constraintarr 
    LOOP
      IF v_temp = v_indexrec.indexname THEN
        bSkip := TRUE;
        EXIT;
      END IF;
    END LOOP;
    
    IF NOT bSkip THEN
      v_table_ddl := v_table_ddl || E'\n\n' || v_indexrec.indexdef || ';';
    END IF;
  END LOOP;
  
  -- Add comments if any
  FOR v_rec IN 
    SELECT 
      'COMMENT ON COLUMN ' || in_schema || '.' || in_table || '.' || a.attname || 
      ' IS ' || quote_literal(d.description) || ';' as comment_sql
    FROM pg_description d
    JOIN pg_class c ON c.oid = d.objoid
    JOIN pg_attribute a ON c.oid = a.attrelid AND a.attnum = d.objsubid
    JOIN pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = in_schema
      AND c.relname = in_table
      AND d.objsubid > 0
  LOOP
    v_table_ddl := v_table_ddl || E'\n\n' || v_rec.comment_sql;
  END LOOP;
  
  RETURN v_table_ddl;
END;
$$;
