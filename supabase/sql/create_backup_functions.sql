
-- Function to get all table names in the public schema
CREATE OR REPLACE FUNCTION public.get_all_tables() 
RETURNS TABLE (tablename text) 
LANGUAGE SQL SECURITY DEFINER 
SET search_path = public
AS $$
  SELECT tablename::text FROM pg_tables WHERE schemaname = 'public';
$$;

-- Function to get column information for a specific table
CREATE OR REPLACE FUNCTION public.get_table_columns(table_name text) 
RETURNS TABLE (
  column_name text,
  data_type text,
  is_nullable boolean,
  column_default text
) 
LANGUAGE SQL SECURITY DEFINER 
SET search_path = public
AS $$
  SELECT 
    column_name::text,
    data_type::text,
    (is_nullable = 'YES') as is_nullable,
    column_default::text
  FROM information_schema.columns
  WHERE table_schema = 'public' AND table_name = $1;
$$;

-- Function to get table structure for a specific table
CREATE OR REPLACE FUNCTION public.get_table_structure(table_name text) 
RETURNS TABLE (
  column_name text,
  data_type text,
  is_nullable boolean,
  column_default text
) 
LANGUAGE SQL SECURITY DEFINER 
SET search_path = public
AS $$
  SELECT 
    column_name::text,
    data_type::text,
    (is_nullable = 'YES') as is_nullable,
    column_default::text
  FROM information_schema.columns
  WHERE table_schema = 'public' AND table_name = $1;
$$;
