
-- Create or ensure invoice-files bucket exists with public access
INSERT INTO storage.buckets (id, name, public)
VALUES ('invoice-files', 'invoice-files', true)
ON CONFLICT (id) DO UPDATE 
SET public = true;

-- Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Create policy for public read access to invoice files
CREATE POLICY "Public Read Access for Invoice Files" 
ON storage.objects FOR SELECT 
USING (bucket_id = 'invoice-files');

-- Create policy for authenticated users to upload invoice files
CREATE POLICY "Allow Users to Upload Invoice Files" 
ON storage.objects FOR INSERT 
WITH CHECK (bucket_id = 'invoice-files' AND auth.role() = 'authenticated');

-- Create policy for authenticated users to update their invoice files
CREATE POLICY "Allow Users to Update Invoice Files" 
ON storage.objects FOR UPDATE 
USING (bucket_id = 'invoice-files' AND auth.role() = 'authenticated');

-- Create policy for authenticated users to delete their invoice files
CREATE POLICY "Allow Users to Delete Invoice Files" 
ON storage.objects FOR DELETE 
USING (bucket_id = 'invoice-files' AND auth.role() = 'authenticated');
