-- This seed file will be executed when running `supabase db reset`
-- It contains additional data for testing purposes

-- Create a sample service provider
INSERT INTO service_providers (id, first_name, last_name, email, phone, specialty)
VALUES (
  '33333333-3333-3333-3333-333333333333',
  '<PERSON>',
  'Doe',
  '<EMAIL>',
  '************',
  'Plumbing'
);

-- Create a sample maintenance task
INSERT INTO maintenance_tasks (id, user_id, property_id, property_name, title, description, status, severity, provider_id)
VALUES (
  '44444444-4444-4444-4444-444444444444',
  '00000000-0000-0000-0000-000000000000',
  '11111111-1111-1111-1111-111111111111',
  'Sample Property',
  'Fix leaky faucet',
  'The kitchen faucet is leaking and needs to be repaired.',
  'open',
  'medium',
  '33333333-3333-3333-3333-333333333333'
);

-- Create a sample damage report
INSERT INTO damage_reports (id, user_id, property_id, title, description, status, provider_id)
VALUES (
  '55555555-5555-5555-5555-555555555555',
  '00000000-0000-0000-0000-000000000000',
  '11111111-1111-1111-1111-111111111111',
  'Broken window',
  'The living room window is broken and needs to be replaced.',
  'open',
  '33333333-3333-3333-3333-333333333333'
);

-- Create a sample team
INSERT INTO teams (id, name, owner_id)
VALUES (
  '66666666-6666-6666-6666-666666666666',
  'Sample Team',
  '00000000-0000-0000-0000-000000000000'
);

-- Add the admin user to the team
INSERT INTO team_members (team_id, user_id, status)
VALUES (
  '66666666-6666-6666-6666-666666666666',
  '00000000-0000-0000-0000-000000000000',
  'active'
);

-- Add permissions for the admin user
INSERT INTO user_permissions (user_id, team_id, permission, enabled)
VALUES
  ('00000000-0000-0000-0000-000000000000', '66666666-6666-6666-6666-666666666666', 'manage_properties', TRUE),
  ('00000000-0000-0000-0000-000000000000', '66666666-6666-6666-6666-666666666666', 'manage_inventory', TRUE),
  ('00000000-0000-0000-0000-000000000000', '66666666-6666-6666-6666-666666666666', 'view_inventory', TRUE),
  ('00000000-0000-0000-0000-000000000000', '66666666-6666-6666-6666-666666666666', 'manage_purchase_orders', TRUE),
  ('00000000-0000-0000-0000-000000000000', '66666666-6666-6666-6666-666666666666', 'view_purchase_orders', TRUE),
  ('00000000-0000-0000-0000-000000000000', '66666666-6666-6666-6666-666666666666', 'manage_damage_reports', TRUE),
  ('00000000-0000-0000-0000-000000000000', '66666666-6666-6666-6666-666666666666', 'view_damage_reports', TRUE),
  ('00000000-0000-0000-0000-000000000000', '66666666-6666-6666-6666-666666666666', 'manage_maintenance', TRUE),
  ('00000000-0000-0000-0000-000000000000', '66666666-6666-6666-6666-666666666666', 'view_maintenance', TRUE),
  ('00000000-0000-0000-0000-000000000000', '66666666-6666-6666-6666-666666666666', 'manage_team', TRUE),
  ('00000000-0000-0000-0000-000000000000', '66666666-6666-6666-6666-666666666666', 'view_team', TRUE);

-- Create a sample purchase order
INSERT INTO purchase_orders (id, user_id, property_id, status, total_price, notes)
VALUES (
  '77777777-7777-7777-7777-777777777777',
  '00000000-0000-0000-0000-000000000000',
  '11111111-1111-1111-1111-111111111111',
  'draft',
  99.99,
  'Sample purchase order for testing'
);