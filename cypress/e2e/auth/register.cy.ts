
describe('Registration Page', () => {
  beforeEach(() => {
    cy.visit('/register');
  });

  it('should display registration form with all fields', () => {
    cy.get('h1').contains('Create an account');
    cy.get('input[name="email"]').should('be.visible');
    cy.get('input[name="password"]').should('be.visible');
    cy.get('input[name="firstName"]').should('be.visible');
    cy.get('input[name="lastName"]').should('be.visible');
    cy.get('button[type="submit"]').should('be.visible');
  });

  it('should show validation errors for empty fields', () => {
    cy.get('button[type="submit"]').click();
    cy.contains('Email is required').should('be.visible');
    cy.contains('Password is required').should('be.visible');
    cy.contains('First name is required').should('be.visible');
    cy.contains('Last name is required').should('be.visible');
  });

  it('should navigate to login page when "Sign in" is clicked', () => {
    cy.contains('Sign in').click();
    cy.url().should('include', '/login');
  });

  it('should register with valid information and redirect to dashboard', () => {
    cy.intercept('POST', '*/auth/v1/signup*').as('signupRequest');
    
    cy.get('input[name="firstName"]').type('Test');
    cy.get('input[name="lastName"]').type('User');
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('Password123!');
    cy.get('button[type="submit"]').click();
    
    cy.wait('@signupRequest');
    cy.contains('Registration successful').should('be.visible');
  });

  it('should show error message with invalid registration data', () => {
    cy.intercept('POST', '*/auth/v1/signup*', {
      statusCode: 400,
      body: { error: 'Email already in use' }
    }).as('failedSignupRequest');
    
    cy.get('input[name="firstName"]').type('Test');
    cy.get('input[name="lastName"]').type('User');
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('Password123!');
    cy.get('button[type="submit"]').click();
    
    cy.wait('@failedSignupRequest');
    cy.contains('Email already in use').should('be.visible');
  });
});
