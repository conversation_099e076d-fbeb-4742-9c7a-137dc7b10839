
describe('Login Page', () => {
  beforeEach(() => {
    cy.visit('/login');
  });

  it('should display login form with all fields', () => {
    cy.get('h1').contains('Sign In');
    cy.get('input[name="email"]').should('be.visible');
    cy.get('input[name="password"]').should('be.visible');
    cy.get('button[type="submit"]').should('be.visible');
  });

  it('should show validation errors for empty fields', () => {
    cy.get('button[type="submit"]').click();
    cy.contains('Email is required').should('be.visible');
    cy.contains('Password is required').should('be.visible');
  });

  it('should navigate to signup page when "Sign up" is clicked', () => {
    cy.contains('Sign up').click();
    cy.url().should('include', '/register');
  });

  it('should navigate to forgot password page when "Forgot password" is clicked', () => {
    cy.contains('Forgot password').click();
    cy.url().should('include', '/forgot-password');
  });

  it('should login with valid credentials and redirect to dashboard', () => {
    cy.intercept('POST', '*/auth/v1/token*').as('loginRequest');
    
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('password123');
    cy.get('button[type="submit"]').click();
    
    cy.wait('@loginRequest');
    cy.url().should('include', '/dashboard');
  });

  it('should show error message with invalid credentials', () => {
    cy.intercept('POST', '*/auth/v1/token*', {
      statusCode: 400,
      body: { error: 'Invalid login credentials' }
    }).as('failedLoginRequest');
    
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('wrongpassword');
    cy.get('button[type="submit"]').click();
    
    cy.wait('@failedLoginRequest');
    cy.contains('Invalid login credentials').should('be.visible');
  });
});
