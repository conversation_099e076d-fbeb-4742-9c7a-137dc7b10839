
describe('Property Management', () => {
  beforeEach(() => {
    cy.login();
    cy.visit('/properties');
  });

  it('should display property list', () => {
    cy.get('[data-cy="property-card"]').should('have.length.at.least', 1);
  });

  it('should open property details when clicking on a property', () => {
    cy.get('[data-cy="property-card"]').first().click();
    cy.get('[data-cy="property-details-dialog"]').should('be.visible');
    cy.get('[data-cy="property-details-dialog"] button[aria-label="Close"]').click();
  });

  it('should filter properties', () => {
    cy.get('[data-cy="property-filters-button"]').click();
    cy.get('[data-cy="property-filter-status"]').select('active');
    cy.get('[data-cy="apply-filters-button"]').click();
    
    // Verify filtered results
    cy.get('[data-cy="property-card"]').should('have.length.at.least', 1);
    cy.get('[data-cy="property-status"]').each(($el) => {
      expect($el.text()).to.include('Active');
    });
  });

  it('should add a new property', () => {
    const newProperty = {
      name: 'Test Property',
      address: '123 Test St',
      city: 'Testville',
      state: 'TS',
      zip_code: '12345',
      bedrooms: '3',
      bathrooms: '2',
      square_feet: '1500',
      property_type: 'house'
    };
    
    cy.get('[data-cy="add-property-btn"]').click();
    
    // Fill out property form
    cy.get('input[name="name"]').type(newProperty.name);
    cy.get('input[name="address"]').type(newProperty.address);
    cy.get('input[name="city"]').type(newProperty.city);
    cy.get('input[name="state"]').type(newProperty.state);
    cy.get('input[name="zip_code"]').type(newProperty.zip_code);
    cy.get('input[name="bedrooms"]').type(newProperty.bedrooms);
    cy.get('input[name="bathrooms"]').type(newProperty.bathrooms);
    cy.get('input[name="square_feet"]').type(newProperty.square_feet);
    cy.get('select[name="property_type"]').select(newProperty.property_type);
    
    cy.get('button[type="submit"]').click();
    
    // Verify property was added
    cy.contains(newProperty.name).should('be.visible');
    cy.contains(newProperty.address).should('be.visible');
  });

  it('should edit a property', () => {
    cy.get('[data-cy="property-card"]').first().click();
    cy.get('[data-cy="edit-property-button"]').click();
    
    const updatedName = 'Updated Property Name';
    cy.get('input[name="name"]').clear().type(updatedName);
    
    cy.get('button[type="submit"]').click();
    
    // Verify property was updated
    cy.contains(updatedName).should('be.visible');
  });
});
