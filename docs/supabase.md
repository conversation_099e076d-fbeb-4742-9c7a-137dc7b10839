# StayFu Supabase Configuration

This document provides a comprehensive overview of the StayFu Supabase configuration, including functions, RLS policies, and edge functions. This documentation should be maintained and updated as the configuration evolves to ensure that we don't break existing functionality or rebuild components that already exist.

## Recent Updates

### Authentication Error Handling Fix (June 2023, Updated May 2025)

We've fixed issues with the authentication error handling:

1. **Fixed Sign Out Error Handling**: Resolved the "Auth session missing" error (403 Forbidden) that occurred when trying to sign out without an active session. The fix includes:
   - Checking for a valid session before attempting to sign out
   - Trying local scope signOut first, which is less likely to cause 403 errors
   - Only attempting global scope signOut if local scope succeeds
   - Gracefully handling 403 Forbidden errors by returning a mock success response
   - Ensuring the auth state is properly reset even if the sign out API call fails
   - Special handling in the customFetch function to handle 403 errors specifically for logout requests

2. **Improved Password Reset Flow**: Enhanced the password reset flow to handle session errors gracefully:
   - Added session validation before attempting to sign out during password reset
   - Improved error handling in the forgot password and reset password flows
   - Ensured the user experience remains smooth even if API calls fail

### Team Permissions Management Fix (May 2023)

We've fixed issues with the team permissions management functionality:

1. **Fixed Infinite Loop in Permission Management**: Resolved an issue where the PermissionManagement component would enter an infinite loop when loading team members and permissions.

2. **Optimized Permission Management Hook**: Enhanced the usePermissionManagement hook with backup and restore logic, throttling to prevent excessive API calls, and better error handling.

3. **Fixed Team Owner Detection**: Improved the isTeamOwner check to properly identify team owners.

4. **RLS Policy Simplification**: Simplified the RLS policies for user_permissions to prevent recursion issues that were causing permission management problems.

## Database Functions

### User and Profile Management

#### create_profile
Creates a user profile.
```sql
create_profile(id uuid, email text, first_name text, last_name text, role user_role)
```

#### create_profile_if_not_exists
Creates a user profile if it doesn't already exist.
```sql
create_profile_if_not_exists(id uuid, email text, first_name text, last_name text, role user_role)
```

#### ensure_profile_exists
Ensures a profile exists for a user.
```sql
ensure_profile_exists(user_id uuid)
```

#### get_profile
Gets a user's profile.
```sql
get_profile(user_id uuid)
```

#### get_profile_by_id
Gets a user's profile by ID.
```sql
get_profile_by_id(p_id uuid)
```

#### get_profile_by_id_or_email
Gets a user's profile by ID or email.
```sql
get_profile_by_id_or_email(p_id uuid, p_email text)
```

#### create_service_provider_profile
Creates a service provider profile.
```sql
create_service_provider_profile(p_id uuid, p_email text, p_first_name text, p_last_name text, p_status text)
```

#### register_service_provider
Registers a service provider.
```sql
register_service_provider(p_id uuid, p_email text, p_first_name text, p_last_name text)
```

#### get_user_role
Gets a user's role.
```sql
get_user_role(user_id uuid)
```

#### is_super_admin
Checks if a user is a super admin.
```sql
is_super_admin(user_id uuid)
```

### Team Management

#### create_team_invitation
Creates a team invitation.
```sql
create_team_invitation(p_team_id uuid, p_email text, p_role user_role, p_invited_by uuid)
```

#### accept_invitation_and_add_member
Accepts a team invitation and adds the user as a team member. Enhanced with better error handling and profile creation if needed.
```sql
accept_invitation_and_add_member(p_token text, p_user_id uuid)
```

This function has been improved to:
1. Create a profile for the user if it doesn't exist
2. Handle errors gracefully with detailed error messages
3. Return the team name in addition to the team ID
4. Add more logging for debugging purposes
5. Handle email mismatches between invitation and user profile

#### accept_invitation_direct
Accepts a team invitation and creates a new user if needed, all in one step. Handles type conversion between text and user_role enum types.

The function properly handles the role parameter by:
1. Directly using the user_role type for internal variables
2. Safely casting text inputs to user_role with error handling
3. Defaulting to 'service_provider' if any casting errors occur
4. Avoiding COALESCE with mixed types to prevent type errors
5. Using the correct column names for the profiles table (id instead of user_id)

```sql
accept_invitation_direct(p_token text, p_email text, p_first_name text, p_last_name text, p_password text, p_role text DEFAULT 'service_provider')
```

#### accept_team_invitation
Accepts a team invitation.
```sql
accept_team_invitation(p_token text)
```

#### accept_team_invitation_safe
Safely accepts a team invitation.
```sql
accept_team_invitation_safe(p_token text)
```

#### add_user_to_team
Adds a user to a team. Enhanced with better error handling and team name retrieval.
```sql
add_user_to_team(p_user_id uuid, p_team_id uuid, p_added_by uuid DEFAULT NULL)
```

This function has been improved to:
1. Handle errors gracefully with detailed error messages
2. Return the team name in addition to the team ID
3. Add more logging for debugging purposes
4. Handle the case where the team owner is not found

#### add_service_provider_default_permissions
Adds default permissions for a service provider.
```sql
add_service_provider_default_permissions(p_user_id uuid, p_team_id uuid)
```

#### set_default_permissions
Sets default permissions for a user. Enhanced with better error handling to prevent registration failures.
```sql
set_default_permissions()
```

This function has been improved to:
1. Handle errors gracefully to prevent registration failures
2. Add more logging for debugging purposes
3. Check if the permission_type enum exists before attempting to insert permissions
4. Wrap the entire function in exception handling to ensure it never blocks the trigger
5. Create a default team for property managers if they don't have one

#### setup_team_member_permissions
Sets up permissions for a team member.
```sql
setup_team_member_permissions(p_user_id uuid, p_team_id uuid, p_role user_role)
```

#### get_team_members_with_profiles
Gets team members with their profiles.
```sql
get_team_members_with_profiles(p_team_id uuid)
```

#### get_team_name_by_id
Gets a team's name by ID.
```sql
get_team_name_by_id(p_team_id uuid)
```

#### get_user_teams
Gets teams a user belongs to.
```sql
get_user_teams(p_user_id uuid)
```

#### get_user_team_memberships
Gets a user's team memberships.
```sql
get_user_team_memberships(p_user_id uuid)
```

#### get_user_team_ids
Gets IDs of teams a user belongs to.
```sql
get_user_team_ids(p_user_id uuid)
```

#### get_user_team_access
Gets a user's team access.
```sql
get_user_team_access(p_user_id uuid)
```

#### is_team_member
Checks if a user is a team member.
```sql
is_team_member(p_user_id uuid, p_team_id uuid)
```

#### is_team_owner
Checks if a user is a team owner.
```sql
is_team_owner(p_user_id uuid, p_team_id uuid)
```

#### user_is_team_member
Checks if a user is a team member.
```sql
user_is_team_member(p_user_id uuid, p_team_id uuid)
```

#### user_is_team_owner
Checks if a user is a team owner.
```sql
user_is_team_owner(p_user_id uuid, p_team_id uuid)
```

#### user_owns_team
Checks if a user owns a team.
```sql
user_owns_team(p_user_id uuid, p_team_id uuid)
```

#### user_can_access_team
Checks if a user can access a team.
```sql
user_can_access_team(p_user_id uuid, p_team_id uuid)
```

#### user_can_access_team_members
Checks if a user can access team members.
```sql
user_can_access_team_members(p_user_id uuid, p_team_id uuid)
```

#### user_can_access_team_properties
Checks if a user can access team properties.
```sql
user_can_access_team_properties(p_user_id uuid, p_team_id uuid)
```

#### debug_team_access
Debugs team access.
```sql
debug_team_access(p_user_id uuid, p_team_id uuid)
```

### Property Management

#### add_property_to_team_direct
Adds a property to a team directly.
```sql
add_property_to_team_direct(p_property_id uuid, p_team_id uuid)
```

#### add_property_to_team_safe
Safely adds a property to a team.
```sql
add_property_to_team_safe(p_property_id uuid, p_team_id uuid, p_user_id uuid)
```

#### remove_property_from_team_direct
Removes a property from a team directly.
```sql
remove_property_from_team_direct(p_property_id uuid, p_team_id uuid)
```

#### remove_property_from_team_safe
Safely removes a property from a team.
```sql
remove_property_from_team_safe(p_property_id uuid, p_team_id uuid, p_user_id uuid)
```

#### get_property_details
Gets property details.
```sql
get_property_details(p_property_id uuid)
```

#### get_property_details_simple
Gets simple property details.
```sql
get_property_details_simple(p_property_id uuid)
```

#### get_property_if_team_member
Gets a property if the user is a team member.
```sql
get_property_if_team_member(p_property_id uuid, p_user_id uuid)
```

#### get_property_with_team_access
Gets a property with team access.
```sql
get_property_with_team_access(p_property_id uuid, p_user_id uuid)
```

#### get_properties_for_team_member
Gets properties for a team member.
```sql
get_properties_for_team_member(p_user_id uuid)
```

#### get_team_properties
Gets properties for a team.
```sql
get_team_properties(p_team_id uuid)
```

#### get_user_properties
Gets properties for a user.
```sql
get_user_properties(p_user_id uuid)
```

#### get_user_accessible_properties
Gets properties accessible to a user.
```sql
get_user_accessible_properties(p_user_id uuid)
```

#### get_unique_user_properties
Gets unique properties for a user.
```sql
get_unique_user_properties(p_user_id uuid)
```

#### get_service_provider_properties
Gets properties accessible to a service provider, including properties from teams they are members of.
```sql
get_service_provider_properties(p_user_id uuid)
```

#### get_user_role_properties
Routes to the appropriate properties function based on user role. This function determines whether to use get_service_provider_properties or get_user_properties based on the user's role.
```sql
get_user_role_properties(p_user_id uuid)
```

#### check_duplicate_property_name
Checks for duplicate property names.
```sql
check_duplicate_property_name(p_name text, p_user_id uuid)
```

#### check_team_duplicate_property_name
Checks for duplicate property names in a team.
```sql
check_team_duplicate_property_name(p_name text, p_team_id uuid)
```

#### prevent_duplicate_property_names_in_team
Prevents duplicate property names in a team.
```sql
prevent_duplicate_property_names_in_team()
```

#### check_property_in_team
Checks if a property is in a team.
```sql
check_property_in_team(p_property_id uuid, p_team_id uuid)
```

#### is_property_in_team
Checks if a property is in a team.
```sql
is_property_in_team(p_property_id uuid, p_team_id uuid)
```

#### property_belongs_to_team
Checks if a property belongs to a team.
```sql
property_belongs_to_team(p_property_id uuid, p_team_id uuid)
```

#### ensure_team_property_access
Ensures team property access.
```sql
ensure_team_property_access()
```

#### delete_property_cascade
Deletes a property and all related records.
```sql
delete_property_cascade(p_property_id uuid)
```

#### has_property_document_access
Checks if a user has access to a property document.
```sql
has_property_document_access(p_document_id uuid)
```

#### has_property_file_access
Checks if a user has access to a property file.
```sql
has_property_file_access(p_file_id uuid)
```

### Damage Reports

#### get_damage_reports_with_property
Gets damage reports with property information.
```sql
get_damage_reports_with_property(p_user_id uuid)
```

#### get_user_damage_reports
Gets damage reports for a user.
```sql
get_user_damage_reports(p_user_id uuid)
```

#### get_user_damage_reports_simple
Gets simple damage reports for a user.
```sql
get_user_damage_reports_simple(p_user_id uuid)
```

#### get_property_damage_reports
Gets damage reports for a property.
```sql
get_property_damage_reports(p_property_id uuid)
```

#### get_team_damage_reports
Gets damage reports for a team.
```sql
get_team_damage_reports(p_team_id uuid)
```

#### get_all_damage_reports
Gets all damage reports.
```sql
get_all_damage_reports()
```

#### get_damage_notes
Gets notes for a damage report.
```sql
get_damage_notes(p_damage_report_id uuid)
```

#### get_damage_photos
Gets photos for a damage report.
```sql
get_damage_photos(p_damage_report_id uuid)
```

#### get_user_damage_photos
Gets damage photos for a user.
```sql
get_user_damage_photos(p_user_id uuid)
```

#### get_damage_photo_url
Gets the URL for a damage photo.
```sql
get_damage_photo_url(p_file_path text)
```

#### check_damage_report_duplicate
Checks for duplicate damage reports.
```sql
check_damage_report_duplicate(p_property_id uuid, p_title text, p_description text)
```

#### set_damage_report_team_id
Sets the team ID for a damage report.
```sql
set_damage_report_team_id()
```

### Maintenance

#### get_maintenance_tasks_for_user
Gets maintenance tasks for a user.
```sql
get_maintenance_tasks_for_user(p_user_id uuid)
```

#### get_user_maintenance_tasks
Gets maintenance tasks for a user.
```sql
get_user_maintenance_tasks(p_user_id uuid)
```

#### get_maintenance_tasks_for_team_member
Gets maintenance tasks for a team member.
```sql
get_maintenance_tasks_for_team_member(p_user_id uuid)
```

#### get_tasks_for_properties_or_user
Gets tasks for properties or a user.
```sql
get_tasks_for_properties_or_user(p_user_id uuid)
```

#### set_maintenance_task_team_id
Sets the team ID for a maintenance task.
```sql
set_maintenance_task_team_id()
```

#### create_maintenance_provider_for_service_provider
Creates a maintenance provider for a service provider. Enhanced with better error handling to prevent registration failures.
```sql
create_maintenance_provider_for_service_provider()
```

This function has been improved to:
1. Handle errors gracefully to prevent registration failures
2. Add more logging for debugging purposes
3. Check if a provider record already exists before creating a new one
4. Wrap the entire function in exception handling to ensure it never blocks the trigger

#### create_maintenance_provider_for_team_member
Creates a maintenance provider for a team member.
```sql
create_maintenance_provider_for_team_member(p_user_id uuid, p_team_id uuid)
```

#### get_providers
Gets service providers from all teams the user is a member of, ensuring service providers are synced between all members of the team.
```sql
get_providers()
```

This function has been improved to:
1. Return maintenance providers from all teams the user is a member of
2. Ensure service providers are synchronized between all team members
3. Handle different access levels based on user role (admin vs. regular user)
4. Return both maintenance providers and service provider accounts in a consistent format

**Important Note**: The maintenance_providers table does not have a team_id column. Team access is handled through this function by joining with team_members to determine access. Do not attempt to filter maintenance_providers directly with team_id as it will result in a 400 Bad Request error. Always use the get_providers() RPC function to retrieve providers with proper team access control.

### Inventory

#### set_inventory_team_id
Sets the team ID for inventory items.
```sql
set_inventory_team_id()
```

#### sync_inventory_team_ids
Syncs team IDs for inventory items.
```sql
sync_inventory_team_ids()
```

#### update_inventory_team_id_on_team_property_change
Updates inventory team IDs when team properties change.
```sql
update_inventory_team_id_on_team_property_change()
```

### Purchase Orders

#### set_purchase_order_team_id
Sets the team ID for purchase orders.
```sql
set_purchase_order_team_id()
```

### Permissions and Access Control

#### has_permission
Checks if a user has a permission.
```sql
has_permission(p_user_id uuid, p_permission permission_type)
```

#### user_has_permission
Checks if a user has a permission.
```sql
user_has_permission(p_user_id uuid, p_permission permission_type)
```

#### user_has_team_permission
Checks if a user has a team permission.
```sql
user_has_team_permission(p_user_id uuid, p_team_id uuid, p_permission permission_type)
```

#### can_manage_invitations
Checks if a user can manage invitations.
```sql
can_manage_invitations(p_team_id uuid)
```

#### can_manage_invitations_safe
Safely checks if a user can manage invitations.
```sql
can_manage_invitations_safe(p_team_id uuid)
```

#### can_manage_permissions_safe
Safely checks if a user can manage permissions.
```sql
can_manage_permissions_safe(p_team_id uuid)
```

#### can_manage_team_permissions
Checks if a user can manage team permissions.
```sql
can_manage_team_permissions(p_team_id uuid)
```

#### can_manage_team_members
Checks if a user can manage team members.
```sql
can_manage_team_members(p_team_id uuid)
```

#### can_manage_staff
Checks if a user can manage staff.
```sql
can_manage_staff(p_team_id uuid)
```

#### can_manage_service_providers
Checks if a user can manage service providers.
```sql
can_manage_service_providers(p_team_id uuid)
```

#### can_create_maintenance_task
Checks if a user can create maintenance tasks.
```sql
can_create_maintenance_task(p_property_id uuid)
```

#### has_user_access
Checks if a user has access to another user.
```sql
has_user_access(p_target_user_id uuid)
```

#### has_property_access
Checks if a user has access to a property.
```sql
has_property_access(p_property_id uuid)
```

#### has_damage_report_access
Checks if a user has access to a damage report.
```sql
has_damage_report_access(p_damage_report_id uuid)
```

#### can_access_damage_report
Checks if a user can access a damage report.
```sql
can_access_damage_report(p_damage_report_id uuid)
```

#### has_damage_note_access
Checks if a user has access to a damage note.
```sql
has_damage_note_access(p_damage_note_id uuid)
```

#### has_damage_photo_access
Checks if a user has access to a damage photo.
```sql
has_damage_photo_access(p_damage_photo_id uuid)
```

#### has_inventory_item_access
Checks if a user has access to an inventory item.
```sql
has_inventory_item_access(p_inventory_item_id uuid)
```

#### has_maintenance_task_access
Checks if a user has access to a maintenance task.
```sql
has_maintenance_task_access(p_maintenance_task_id uuid)
```

#### has_maintenance_task_access_impersonation
Checks if a user has access to a maintenance task with impersonation.
```sql
has_maintenance_task_access_impersonation(p_maintenance_task_id uuid)
```

#### has_maintenance_provider_access
Checks if a user has access to a maintenance provider.
```sql
has_maintenance_provider_access(p_maintenance_provider_id uuid)
```

#### has_purchase_order_access
Checks if a user has access to a purchase order.
```sql
has_purchase_order_access(p_purchase_order_id uuid)
```

#### has_purchase_order_item_access
Checks if a user has access to a purchase order item.
```sql
has_purchase_order_item_access(p_purchase_order_item_id uuid)
```

#### has_invoice_item_access
Checks if a user has access to an invoice item.
```sql
has_invoice_item_access(p_item_id uuid)
```

#### has_service_provider_access
Checks if a user has access to a service provider.
```sql
has_service_provider_access(p_service_provider_id uuid)
```

#### is_service_provider_in_team
Checks if a service provider is in a team.
```sql
is_service_provider_in_team(p_service_provider_id uuid, p_team_id uuid)
```

### Automation

#### handle_new_user
Handles a new user.
```sql
handle_new_user()
```

#### handle_booking_automation
Handles booking automation.
```sql
handle_booking_automation()
```

### Utility Functions

#### update_modified_column
Updates the modified column.
```sql
update_modified_column()
```

#### set_storage_policy
Sets storage policy.
```sql
set_storage_policy()
```

#### update_extension_token_last_used
Updates the last used timestamp for an extension token.
```sql
update_extension_token_last_used(p_token_hash text)
```

#### create_user_preferences
Creates or updates user preferences with SECURITY DEFINER to bypass RLS.
```sql
create_user_preferences(p_user_id uuid, p_onboarding_state jsonb)
```

#### update_user_preferences
Updates user preferences with SECURITY DEFINER to bypass RLS.
```sql
update_user_preferences(p_user_id uuid, p_onboarding_state jsonb)
```

#### delete_team_cascade
Deletes a team and all related records.
```sql
delete_team_cascade(p_team_id uuid)
```

#### fix_team_invitation
Fixes a team invitation.
```sql
fix_team_invitation(p_invitation_id uuid)
```

#### ensure_all_users_have_profiles
Ensures all users have profiles.
```sql
ensure_all_users_have_profiles()
```

#### scheduled_ensure_profiles
Scheduled function to ensure all users have profiles.
```sql
scheduled_ensure_profiles()
```

## RLS Policies

> **Note**: To avoid infinite recursion issues in RLS policies, we've simplified the policies to use `true` for both USING and WITH CHECK clauses. This allows all authenticated users to access the data, and we rely on RPC functions with SECURITY DEFINER to enforce access control. This approach is more efficient and avoids the complex policy chains that were causing recursion errors.

### profiles
- Users can view their own profiles
- Users can update their own profiles
- Authenticated users can read basic profile info of other users
- Service role can manage all profiles
- Service role can create profiles
- Service role can update profiles

### teams
- All authenticated users can access teams (simplified policy)
- Access control is enforced through RPC functions

### team_members
- All authenticated users can access team members (simplified policy)
- Access control is enforced through RPC functions

### properties
- All authenticated users can access properties (simplified policy)
- Access control is enforced through RPC functions

### team_properties
- All authenticated users can access team properties (simplified policy)
- Access control is enforced through RPC functions

### user_permissions
- Users can view their own permissions
- Team owners can view permissions for their team members
- Team owners can update permissions for their team members
- Users with manage_team permission can manage team permissions

### team_invitations
- Users can view invitations sent to their email
- Team owners can view invitations for their teams
- Team owners can create invitations for their teams
- Team owners can update invitations for their teams
- Team owners can delete invitations for their teams
- Users with manage_staff or manage_service_providers permission can manage invitations

### inventory_items
- Users can view inventory items for properties they own
- Users can view inventory items for teams they are members of
- Users can create inventory items for properties they own
- Users can update inventory items they created
- Users can delete inventory items they created
- Team members with manage_inventory permission can manage team inventory items
- Super admins and admins can view and manage all inventory items
- Service providers can view inventory items for teams they are members of

### damage_reports
- Users can view damage reports they created
- Users can view damage reports for properties they own
- Users can view damage reports for teams they are members of
- Users can create damage reports
- Users can update damage reports they created
- Users can delete damage reports they created
- Team members with manage_damage_reports permission can manage team damage reports
- Super admins and admins can view and manage all damage reports
- Service providers can view damage reports for teams they are members of

### damage_photos
- Users can view photos for damage reports they can access
- Users can add photos to damage reports they created
- Users can update photos they added
- Users can delete photos they added

### damage_notes
- Users can view notes for damage reports they can access
- Users can add notes to damage reports they can access
- Users can update notes they created
- Users can delete notes they created

### damage_invoices
- Users can view invoices for damage reports they can access
- Users can create invoices for damage reports they created
- Users can update invoices they created
- Users can delete invoices they created

### invoice_items
- Users can view invoice items for invoices they can access
- Users can create invoice items for invoices they created
- Users can update invoice items they created
- Users can delete invoice items they created
- Service providers can manage invoice items for damage reports assigned to them

### maintenance_tasks
- Users can view tasks assigned to them
- Users can view tasks for properties they own
- Users can view tasks for teams they are members of
- Users can create tasks for properties they own
- Users can update tasks they created
- Users can delete tasks they created
- Team members with manage_maintenance permission can manage team maintenance tasks
- Super admins and admins can view and manage all maintenance tasks
- Service providers can view and update tasks assigned to them
- Service providers can view tasks for teams they are members of

### maintenance_providers
- Users can view providers they created
- Users can view providers for teams they are members of (via the get_providers() RPC function)
- Users can create providers
- Users can update providers they created
- Users can delete providers they created
- Service providers can view their own provider record

**Important Note**: The maintenance_providers table does not have a team_id column. Team access is handled through the get_providers() RPC function, not through direct RLS policies. Do not attempt to filter maintenance_providers directly with team_id as it will result in a 400 Bad Request error.

### purchase_orders
- Users can view orders they created
- Users can view orders for properties they own
- Users can view orders for teams they are members of
- Users can create orders for properties they own
- Users can update orders they created
- Users can delete orders they created
- Team members with manage_purchase_orders permission can manage team purchase orders
- Super admins and admins can view and manage all purchase orders
- Service providers can view purchase orders for teams they are members of

### purchase_order_items
- Users can view items for orders they can access
- Users can add items to orders they created
- Users can update items they added
- Users can delete items they added

### user_settings
- Users can view their own settings
- Users can update their own settings

### user_preferences
- Users can manage their own preferences (view, create, update, delete)
- Admins and super admins can manage all user preferences

### extension_api_tokens
- Users can view their own tokens
- Users can create tokens
- Users can update their own tokens
- Users can delete their own tokens

### property_documents
- Users can view their own documents
- Users can view non-private documents for properties they have access to
- Users can create documents for properties they have access to
- Users can update their own documents
- Property owners can update any document for their properties
- Users can delete their own documents
- Property owners can delete any document for their properties

### property_files
- Users can view their own files
- Users can view non-private files for properties they have access to
- Users can upload files for properties they have access to
- Users can update their own files
- Property owners can update any file for their properties
- Users can delete their own files
- Property owners can delete any file for their properties

## Edge Functions

### accept-invitation-direct
Accepts a team invitation directly, creating a user if needed. This function is critical for the team invitation flow, especially for service providers. Enhanced with better error handling and profile creation.

- **Parameters**:
  - `token`: The invitation token
  - `email`: The email address
  - `password`: The password (for new users)
  - `firstName`: The first name (for new users)
  - `lastName`: The last name (for new users)
  - `role`: The user role (for new users)
- **Returns**: JSON object with success status, team ID, and team name
- **Process**:
  1. Validates the invitation token with better error handling
  2. Checks if the user already exists in the profiles table
  3. If not, checks if the user exists in the auth system
  4. If the user doesn't exist at all, creates a new user with email confirmation
  5. Creates a profile for the user if it doesn't exist, including the required `is_super_admin` field
  6. Calls the `accept_invitation_and_add_member` function to add the user to the team
  7. For service providers, calls the `add_service_provider_default_permissions` function
  8. Returns detailed error messages for better debugging
  9. Includes the team name in the response for better user experience

### get-user-by-id
Gets a user by ID (for impersonation).
- **Parameters**:
  - `userId`: The user ID
- **Returns**: JSON object with user details

## Important Notes

1. Many functions use the `auth.uid()` function to get the current user's ID.
2. RLS policies are set up to ensure users can only access data they are authorized to see based on their role and team memberships.
3. Edge functions are used for operations that require service role access or complex logic.
4. The `add_user_to_team` function is critical for team management and is used by the invitation acceptance process.
5. The `set_default_permissions` function is used to set up default permissions for users based on their role.
6. The `has_permission` and related functions are used throughout the application to check if users have the necessary permissions.
7. The `handle_new_user` trigger function is used to automatically create profiles for new users.
8. The `update_modified_column` trigger function is used to automatically update the `updated_at` timestamp when records are modified.

## Avoiding Infinite Recursion in RLS Policies

Infinite recursion is a common issue with complex RLS policies in Supabase. This occurs when policies reference other tables that have their own policies, creating circular dependencies. Here are strategies to avoid this problem:

1. **Use SECURITY DEFINER Functions**: Create database functions with `SECURITY DEFINER` that bypass RLS policies and return only the data the user should access. This approach is more efficient and avoids policy chains.

2. **Simplify RLS Policies**: For tables that are frequently accessed together, consider using simpler policies (even `true` policies) and enforce access control through your application logic or RPC functions.

3. **Avoid Nested Queries in Policies**: When a policy for Table A includes a subquery that selects from Table B, and Table B's policy includes a subquery that selects from Table A, you'll get infinite recursion. Break this cycle by:
   - Using junction tables with simpler policies
   - Creating helper functions that don't trigger RLS
   - Using materialized views for complex access patterns

4. **Error Identification**: When you see the error `infinite recursion detected in policy for relation "X"`, examine all policies that reference table X and look for circular references.

5. **Testing Strategy**: Always test RLS policies with different user roles to ensure they work correctly without causing recursion issues.

6. **Proper WITH CHECK Clauses**: Always include both USING and WITH CHECK clauses in your policies to ensure they work correctly for all operations. Missing WITH CHECK clauses can cause permission errors on INSERT and UPDATE operations.

Example solution for team_members and properties tables:
```sql
-- Instead of complex nested policies, use simple policies
CREATE POLICY simple_policy ON team_members USING (true) WITH CHECK (true);
CREATE POLICY simple_policy ON properties USING (true) WITH CHECK (true);

-- Then create secure functions to enforce access control
CREATE FUNCTION get_accessible_properties(p_user_id UUID)
RETURNS SETOF properties
LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN QUERY
  SELECT p.* FROM properties p
  WHERE p.user_id = p_user_id
  OR p.id IN (
    SELECT tp.property_id FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tm.user_id = p_user_id AND tm.status = 'active'
  );
END;
$$;
```

7. **Client-Side Fallbacks**: When using RPC functions that might fail due to recursion issues, implement client-side fallbacks that use simpler queries or alternative approaches to fetch the data. This is especially important for service providers accessing properties through team memberships.
9. **Team Invitation Flow**: The team invitation process involves several components:
   - Creating an invitation record in the `team_invitations` table
   - Sending an email with a link containing the invitation token
   - When the user clicks the link, they are directed to the Auth page with the token
   - The Auth page attempts to use the `accept_invitation_direct` edge function to handle the invitation and user registration in one step
   - If the edge function fails, it falls back to using the `acceptInvitationWithRegistration` client-side function
   - The client-side function has a robust fallback mechanism that tries multiple approaches:
     - First, it tries to use the edge function
     - If that fails, it tries to register the user directly and then accept the invitation
     - If the user already exists, it tries to sign in and then accept the invitation
     - If the user exists but the password is incorrect, it provides a clear error message
   - For service providers, the `add_service_provider_default_permissions` function is called to set up appropriate permissions
   - The entire flow has been enhanced with better error handling and user experience improvements
   - The Auth page now supports both `invitation_token` and `invitation` URL parameters for backward compatibility
   - The Auth page now automatically fetches invitation details to pre-fill the registration form
   - The invitation flow now handles edge cases like existing users with incorrect passwords
   - The `handle_new_user` trigger function has been improved to better handle errors during profile creation
   - The invitation details are now stored in localStorage for better persistence across page navigations
   - The edge function now continues even if profile creation fails, as the profile might be created by the trigger
   - The registration process now includes the `emailRedirectTo` option to redirect users back to the invitation page after email confirmation
