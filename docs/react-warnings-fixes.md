# React Warnings Fixes

This document outlines the fixes implemented to address React warnings in the StayFu application.

## Overview of Issues

The application was experiencing the following warning:

```
Warning: Cannot update a component (`ForwardRef`) while rendering a different component (`ForwardRef`). To locate the bad setState() call inside `ForwardRef`, follow the stack trace as described in https://reactjs.org/link/setstate-in-render
```

This warning occurs when a component tries to update state during rendering, which is not allowed in React. The warning was specifically related to the Sonner toast library being used in provider components.

## Implemented Fixes

### 1. Added useCallback for Toast Functions

Modified the `OnboardingContext.tsx` file to use `useCallback` for toast functions:

```javascript
// Create a memoized toast function to avoid calling during render
const showErrorToast = useCallback((message: string) => {
  toast.error(message);
}, []);

const showSuccessToast = useCallback((message: string) => {
  toast.success(message);
}, []);
```

### 2. Delayed Toast Calls with setTimeout

Wrapped all toast calls in setTimeout to ensure they don't happen during rendering:

```javascript
// Before:
toast.error('Failed to load user preferences');

// After:
setTimeout(() => {
  showErrorToast('Failed to load user preferences');
}, 0);
```

### 3. Updated All Toast Calls in OnboardingContext

Updated all toast calls in the following functions:
- `loadOnboardingState`
- `markTutorialSeen`
- `resetTutorials`
- `restartTutorial`

## Affected Files

The following files were modified:

1. `src/contexts/OnboardingContext.tsx`

## Testing

These changes should be tested by:

1. Logging in to the application
2. Navigating between different views
3. Checking the browser console for any React warnings
4. Testing the tutorial reset and restart functionality
5. Verifying that toast notifications still appear correctly

## Future Considerations

1. Consider applying similar fixes to other components that use toast notifications
2. Consider using a more React-friendly toast library that doesn't cause these warnings
3. Implement a global toast service that handles all toast notifications in a way that doesn't cause React warnings
