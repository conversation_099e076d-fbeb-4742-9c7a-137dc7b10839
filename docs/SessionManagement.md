# Session Management in StayFu

## Overview

StayFu uses Supabase Auth for authentication and session management. This document outlines the session management strategy to ensure users remain logged in for appropriate periods and data remains fresh.

## Important Note on Routing

StayFu uses **HashRouter** throughout the application. All navigation and redirects related to authentication (login, logout, session expiration) must use hash-based URLs (e.g., `/#/login` instead of `/login`). This is critical for proper session management and authentication flows.

## Session Duration

- **Session Validity**: Sessions remain valid for at least 48 hours to prevent users from being timed out after page refreshes
- **Session Refresh**: Sessions are automatically refreshed when they have less than 12 hours remaining
- **Idle Timeout**: Users are only logged out after 48 hours of inactivity (configurable in App.tsx)
- **Warning Time**: Users receive a warning 30 minutes before being logged out due to inactivity

## Session Storage

- Sessions are stored in both localStorage and sessionStorage for redundancy
- The primary storage key is `stayfu_auth_token`
- Session expiry times are also stored for debugging purposes

## Session Checks

- Sessions are checked when:
  - The application loads
  - The page becomes visible after being hidden
  - Every 60 minutes via a background interval
  - After page refreshes

## Data Refresh Strategy

- Data is refreshed:
  - When the user manually triggers a refresh
  - When the page becomes visible after being hidden for more than 5 minutes
  - Every 5 minutes via a background interval (configurable in App.tsx)

## Data Refresh

The application uses several mechanisms to ensure data is fresh:
1. Automatic data refreshing when tabs become active
2. React Query cache invalidation on specific triggers
3. Manual refresh buttons on each page

## Troubleshooting

If users experience session issues:
1. Check browser console for session-related errors
2. Verify that localStorage contains a valid `stayfu_auth_token`
3. Check if the session has expired by examining `stayfu_session_expires`
4. Try refreshing the page or clearing your browser cache

## Implementation Details

The session management system is implemented across several files:
- `src/components/auth/RequireAuth.tsx`: Handles session validation for protected routes
- `src/components/auth/SessionManager.tsx`: Manages session refresh and idle timeout
- `src/utils/sessionUtils.ts`: Provides utility functions for session management
- `src/contexts/AuthContext.tsx`: Manages the global authentication state
