# StayFu Data Loading Architecture

This document explains the simplified data loading architecture in the StayFu application, focusing on how data is loaded, cached, and refreshed throughout the application.

## Overview

The StayFu application uses React Query as its primary data fetching and caching library. The architecture has been simplified to rely on React Query's built-in functionality for data refreshing, with all focus-related functionality intentionally disabled to prevent issues with tab focus changes.

## Key Components

### 1. React Query Configuration

The application uses React Query with the following configuration:

```tsx
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: true, // Enable refetch on window focus for data refreshing after idle
      refetchOnMount: 'always', // Always refetch when component mounts
      refetchOnReconnect: true, // Refetch when reconnecting
      retry: 3, // Retry attempts
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
      staleTime: 0, // 0 seconds - always consider data stale to ensure fresh data
      gcTime: 10 * 60 * 1000, // 10 minutes - how long to keep data in cache
      networkMode: 'always', // Always try to fetch from network
    },
    mutations: {
      retry: 2,
      networkMode: 'always', // Always try to perform mutations
    },
  },
});
```

This configuration ensures that:
- Data is automatically refetched when a window regains focus (key for refreshing after idle)
- Data is always refetched when a component mounts
- Data is always considered stale (ready for refresh)
- Network requests are always attempted, even when offline

### 2. Network Status Handler (Visibility Change Handler Removed)

The application no longer uses visibility change handlers. Instead, it only handles network status changes:

```tsx
export const setupGlobalVisibilityChangeHandler = () => {
  if (typeof document === 'undefined') return;

  // Handle online event
  const handleOnline = () => {
    console.log('[cacheUtils] Network is now online, refreshing data...');

    try {
      // Get the global query client
      const queryClient = getGlobalQueryClient();

      // Invalidate all queries
      queryClient.invalidateQueries();

      // Force refetch all queries
      queryClient.refetchQueries({
        type: 'all',
        stale: true
      });

      console.log('[cacheUtils] Forced refetch of all queries due to network reconnection');
    } catch (error) {
      console.error('[cacheUtils] Error refreshing data on network reconnection:', error);
    }
  };

  // Add event listeners - only for network events, not visibility
  window.addEventListener('online', handleOnline);

  // Return cleanup function
  return () => {
    window.removeEventListener('online', handleOnline);
  };
};
```

### 3. Network Status Handling

The application handles network status changes to refresh data when the network connection is restored:

```tsx
// Handle online event
const handleOnline = () => {
  console.log('[cacheUtils] Network is now online, refreshing data...');

  try {
    // Get the global query client
    const queryClient = getGlobalQueryClient();

    // Invalidate all queries
    queryClient.invalidateQueries();

    // Force refetch all queries
    queryClient.refetchQueries({
      type: 'all',
      stale: true
    });

    console.log('[cacheUtils] Forced refetch of all queries due to network reconnection');
  } catch (error) {
    console.error('[cacheUtils] Error refreshing data on network reconnection:', error);
  }
};
```

### 4. Simplified Global Data Refresh Context

The application provides a simplified context for refreshing data:

```tsx
export const GlobalDataRefreshProvider: React.FC<GlobalDataRefreshProviderProps> = ({ children }) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const queryClient = useQueryClient();

  // Function to refresh all data
  const refreshAllData = useCallback(async () => {
    if (isRefreshing) {
      console.log('[GlobalDataRefreshContext] Already refreshing, skipping duplicate refresh');
      return;
    }

    try {
      setIsRefreshing(true);
      console.log('[GlobalDataRefreshContext] Refreshing all data...');

      // Invalidate all queries
      await queryClient.invalidateQueries();

      // Force refetch all queries
      await queryClient.refetchQueries({
        type: 'all',
        stale: true
      });

      console.log('[GlobalDataRefreshContext] All data refreshed successfully');
    } catch (error) {
      console.error('[GlobalDataRefreshContext] Error refreshing data:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [isRefreshing, queryClient]);

  // Function to refresh data for a specific route
  const refreshRouteData = useCallback(async (route: string) => {
    // Implementation details...
  }, [isRefreshing, queryClient, refreshAllData]);

  return (
    <GlobalDataRefreshContext.Provider value={{ refreshAllData, refreshRouteData, isRefreshing }}>
      {children}
    </GlobalDataRefreshContext.Provider>
  );
};
```

## Standardized Data Loading Pattern

We've standardized the data loading pattern across all views to ensure consistent behavior. The following pages now use standardized hooks:

- Properties (`usePropertiesQueryV2`)
- Maintenance (`useMaintenanceTasksQueryV2`)
- Maintenance Automation (`useTaskAutomationQueryV2`)
- Inventory (`useInventoryQueryV2`)
- Damages (`useDamageReportsQueryV2`)
- Teams (`useTeamManagementQueryV2`)
- Settings/Appearance (`useAppearanceSettingsQueryV2`)

### 1. Consistent React Query Hooks

All views now use React Query hooks with a consistent pattern:

```tsx
export const useStandardizedDataQuery = () => {
  const [retryCount, setRetryCount] = useState(0);
  const queryClient = useQueryClient();

  // Function to retry data fetching manually
  const retryFetch = useCallback(async () => {
    setRetryCount(prev => prev + 1);
    await queryClient.invalidateQueries({ queryKey: ['dataKey'] });
    await queryClient.refetchQueries({ queryKey: ['dataKey'] });
  }, [queryClient]);

  // Fetch data using React Query
  const {
    data = [],
    isLoading,
    error,
    isError
  } = useQuery({
    queryKey: ['dataKey', retryCount], // Include retryCount to force refetch
    queryFn: async () => {
      // Data fetching logic
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000),
    staleTime: 1000 * 60 * 5, // 5 minutes - consistent across all views
    refetchOnWindowFocus: true, // Enable refetch on window focus for data refreshing after idle
    refetchOnMount: true
  });

  return {
    data,
    loading: isLoading,
    error,
    isError,
    fetchData: retryFetch
  };
};
```

### 2. Simplified Page Components

All page components now follow a consistent pattern:

```tsx
const PageComponent = () => {
  // Use the standardized data query hook
  const { data, loading, error, fetchData } = useStandardizedDataQuery();

  // Simple refresh function
  const handleRefresh = useCallback(async () => {
    if (isRefreshing) return;
    setIsRefreshing(true);

    try {
      await fetchData();
    } catch (err) {
      console.error('Error refreshing data:', err);
    } finally {
      setIsRefreshing(false);
    }
  }, [fetchData, isRefreshing]);

  // Minimal event handling - no visibility change events
  useEffect(() => {
    const handleDataRefreshed = (event: CustomEvent) => {
      if (!isRefreshing && !loading) {
        fetchData();
      }
    };

    // Only listen for data refresh events, not visibility changes
    window.addEventListener('stayfu-data-refreshed', handleDataRefreshed as EventListener);

    return () => {
      window.removeEventListener('stayfu-data-refreshed', handleDataRefreshed as EventListener);
    };
  }, [fetchData, isRefreshing, loading]);

  // Rest of the component...
};
```

## Data Flow

1. **Initial Load**: When a component mounts, it fetches data using React Query hooks.
2. **Network Status Change**: When the network connection is restored, all data is refreshed.
3. **Route Change**: When the route changes, route-specific data is refreshed.
4. **Manual Refresh**: Users can manually refresh data by clicking refresh buttons or navigating between pages.

Note: Tab focus change events are intentionally ignored to prevent focus-related issues.

## Best Practices

1. **Use React Query Hooks**: Always use React Query hooks for data fetching to ensure consistent caching and refreshing behavior.
2. **Avoid Local Caching**: Avoid implementing custom caching mechanisms that might conflict with React Query's caching.
3. **Handle Loading States**: Always handle loading states to provide a good user experience during data fetching.
4. **Handle Errors**: Always handle errors to provide meaningful feedback to users when data fetching fails.
5. **Use Suspense (Future)**: Consider using React Suspense for data fetching when it becomes stable in React.

## Troubleshooting

If data is not loading correctly:

1. Check if the component is using React Query hooks correctly.
2. Check if the network status handlers are working correctly.
3. Check if there are any errors in the console.
4. Check if the data is being fetched from the correct endpoint.
5. Check if the data is being transformed correctly.
6. Check if the data is being cached correctly.
7. Verify that no visibility change handlers are being used (they should all be removed).

## Recent Improvements to Prevent Data Disappearing

We've implemented several key improvements to prevent data from disappearing when navigating between pages:

### 1. Updated React Query Configuration

```tsx
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false, // Changed from true to false to prevent data disappearing
      refetchOnMount: true, // Changed from 'always' to true to prevent data clearing during navigation
      refetchOnReconnect: true, // Refetch when reconnecting
      retry: 3, // Retry attempts
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
      staleTime: 1000 * 60 * 5, // 5 minutes - increased from 0 to prevent excessive refetching
      gcTime: 30 * 60 * 1000, // 30 minutes - increased from 10 minutes
      networkMode: 'always', // Always try to fetch from network
      keepPreviousData: true, // Keep previous data while fetching new data
    },
  },
});
```

### 2. Preserving Previous Data in Hooks

We've updated our data hooks to preserve previous data during loading states:

```tsx
// Keep track of previous data to prevent data disappearing during loading
const [previousData, setPreviousData] = useState<DataType[]>([]);

// Fetch data using React Query
const {
  data = [],
  isLoading,
  error,
  isError
} = useQuery({
  queryKey: ['dataKey', retryCount],
  queryFn: async () => {
    try {
      // Data fetching logic...

      // If no data found, return previous data if available
      if (!fetchedData || fetchedData.length === 0) {
        return previousData.length > 0 ? previousData : [];
      }

      // Format data...
      const formattedData = formatData(fetchedData);

      // Update previous data for future use
      setPreviousData(formattedData);

      return formattedData;
    } catch (err) {
      // Return previous data on error to prevent data disappearing
      if (previousData.length > 0) {
        return previousData;
      }
      throw err;
    }
  },
  keepPreviousData: true, // Keep previous data while fetching new data
  placeholderData: previousData.length > 0 ? previousData : undefined // Use previous data as placeholder
});
```

### 3. Improved Loading States in UI Components

We've updated our UI components to handle loading states better:

```tsx
// If loading and we have no data, show skeletons
if (loading && filteredData.length === 0) {
  return <LoadingSkeletons />;
}

// If we have data, show it even during loading to prevent disappearing data
return (
  <div>
    {/* Show a subtle loading indicator when refreshing data but we have existing data */}
    {loading && filteredData.length > 0 && (
      <div className="loading-indicator">Refreshing data...</div>
    )}

    {filteredData.map(item => (
      <DataItem key={item.id} item={item} />
    ))}
  </div>
);
```

## Conclusion

The StayFu application now uses a robust, standardized data loading architecture that ensures data is always preserved during navigation and loading states. By using React Query with optimized configuration, implementing data preservation strategies, and improving loading state handling, the application provides a reliable and responsive user experience without data disappearing issues.

### Key Improvements

1. **Standardized Hooks**: All major views now use standardized React Query hooks with consistent patterns
2. **Simplified Event Handling**: Removed complex event handling in favor of simple, direct data fetching
3. **Removed Toast Notifications**: Eliminated unnecessary toast notifications for data refreshes
4. **Consistent Configuration**: All hooks use the same staleTime, retry logic, and refresh mechanisms
5. **Removed All Focus-Related Logic**: Completely removed all visibility change handlers and focus-related functionality
6. **Disabled refetchOnWindowFocus**: Set refetchOnWindowFocus to false in all React Query configurations
7. **Data Preservation**: Implemented strategies to preserve data during loading states and navigation
8. **Improved Loading States**: Enhanced UI components to handle loading states better and prevent data disappearing

These improvements make the codebase more maintainable, reduce complexity, and ensure consistent behavior across all views, with data always visible to users regardless of loading or navigation state.
