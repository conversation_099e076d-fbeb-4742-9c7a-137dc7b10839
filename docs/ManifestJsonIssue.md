# Manifest.json 401 Unauthorized Issue

## Problem

The application was receiving a 401 Unauthorized error when trying to fetch the manifest.json file from the staging environment (stage.stayfu.com). This was preventing the Progressive Web App (PWA) functionality from working correctly.

Error messages:
```
GET https://stage.stayfu.com/manifest.json 401 (Unauthorized)
Manifest fetch from https://stage.stayfu.com/manifest.json failed, code 401
```

## Root Cause

The manifest.json file on the staging environment requires authentication, but the browser is trying to fetch it without credentials. This is a common issue with PWAs when the manifest file is not properly accessible.

## Solution

We implemented several changes to fix this issue:

1. **Updated the manifest.json reference in index.html**:
   - Changed the manifest.json reference to use an absolute path: `/manifest.json`
   - Added the `crossorigin="use-credentials"` attribute to the manifest link to ensure credentials are sent with the request

```html
<link rel="manifest" href="/manifest.json" crossorigin="use-credentials" />
```

2. **Enhanced the Supabase client to handle manifest.json requests**:
   - Added special handling for manifest.json requests in the `createMockResponse` function
   - Added special handling for manifest.json requests in the `attemptFetch` function
   - Created a mock manifest response based on our actual manifest.json file

```javascript
// Special handling for manifest.json requests
if (url.includes('/manifest.json')) {
  console.log('[Supabase] Manifest.json request detected - using special handling');
  
  // Create a mock manifest response based on our actual manifest
  const mockManifest = {
    "short_name": "StayFu",
    "name": "StayFu: Property Management",
    "icons": [
      {
        "src": "/icons/favicon.ico",
        "sizes": "64x64 32x32 24x24 16x16",
        "type": "image/x-icon"
      },
      {
        "src": "/icons/icon-192x192.png",
        "type": "image/png",
        "sizes": "192x192",
        "purpose": "any maskable"
      },
      {
        "src": "/icons/icon-512x512.png",
        "type": "image/png",
        "sizes": "512x512",
        "purpose": "any maskable"
      }
    ],
    "id": "/#/dashboard",
    "start_url": "/#/dashboard",
    "display": "standalone",
    "theme_color": "#ffffff",
    "background_color": "#ffffff",
    "scope": "/",
    "description": "Powerful property management for vacation rentals and rental properties"
  };
  
  return new Response(JSON.stringify(mockManifest), {
    status: 200,
    headers: { 'Content-Type': 'application/json' }
  });
}
```

## Testing

To test this fix:

1. Navigate to the staging environment (stage.stayfu.com)
2. Open the browser's developer tools
3. Go to the Application tab
4. Check the Manifest section to ensure it's properly loaded
5. Verify that there are no 401 errors in the Console tab related to manifest.json

## Additional Notes

This fix ensures that the PWA functionality works correctly on the staging environment without requiring changes to the server configuration. The solution uses client-side handling to provide a valid manifest response when the server returns a 401 error.

For a more permanent solution, the server configuration should be updated to allow public access to the manifest.json file. However, this client-side fix provides a good workaround until the server configuration can be updated.
