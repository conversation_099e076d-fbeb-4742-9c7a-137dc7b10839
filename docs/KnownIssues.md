# Known Issues and Solutions

## Data Loading Issues

### Problem
The StayFu application had issues with data not loading when tabs are inactive, in the background, or left open for some time. Additionally, data would sometimes disappear when navigating between pages or when the application regained focus. The application also showed unnecessary toast notifications when tabs regained focus.

### Solution
We've implemented several improvements to address these issues:

1. **Simplified Data Loading Architecture**: Completely removed complex custom solutions in favor of standard React patterns:
   - Removed DataRefreshManager component and all related toast notifications
   - Removed custom events and global window functions
   - Simplified the GlobalDataRefreshContext to use React Query's built-in functionality
   - Removed all complex caching mechanisms

2. **Optimized React Query Configuration**: Updated React Query settings to prevent data disappearing:
   - Set `refetchOnWindowFocus` to 'false' to prevent data disappearing when tabs regain focus
   - Set `refetchOnMount` to 'true' instead of 'always' to prevent data clearing during navigation
   - Set `staleTime` to 5 minutes to prevent excessive refetching
   - Set `keepPreviousData` to 'true' to keep previous data while fetching new data
   - Set `networkMode` to 'always' to ensure network requests are made even when offline

3. **Implemented Data Preservation Strategies**: Added mechanisms to preserve data during loading states:
   - Added state to track previous data in all data fetching hooks
   - Return previous data when new data is loading or when errors occur
   - Use React Query's placeholderData option to show previous data during loading
   - Improved loading state handling in UI components to prevent empty screens

4. **Removed All Focus-Related Logic**: Completely removed all visibility change handlers:
   - Removed all document.visibilityState checks throughout the application
   - Removed all visibilitychange event listeners
   - Removed all focus-related refresh logic
   - Removed all toast notifications related to data refreshing

5. **Enhanced UI for Loading States**: Improved how loading states are displayed:
   - Show existing data with a subtle loading indicator during refreshes
   - Only show loading skeletons when no data is available
   - Prevent UI jumps and flashes during data loading

6. **Fixed Remaining Custom Visibility Handlers** (May 2023 Update):
   - Identified and removed custom visibility change handlers in useDashboardDataQuery.ts, TeamMemberManagement.tsx, RequireAuth.tsx, SessionRefreshManager.tsx, and useDamageReports.tsx that were interfering with React Query's built-in functionality
   - Ensured all hooks use the same React Query configuration with consistent settings
   - Simplified global data refresh to rely on React Query's built-in functionality
   - Added error retry effects to all data fetching hooks to ensure they reliably retry when errors occur
   - Replaced visibility change handlers with periodic timers for session checking and data refreshing
   - Updated all dashboards to use the newer QueryV2 hooks with proper error retry effects and data preservation

7. **Improved Network Status Handling**: Simplified handling of online/offline status:
   - Automatic data refreshing when network connection is restored
   - Removed toast notifications for network status changes

8. **Removed Redundant Components**: Eliminated duplicate components and simplified the data refresh logic:
   - Removed DataRefreshManager component
   - Simplified RefreshButton component
   - Removed "Clear Cache" buttons and related functionality

## Refreshing Data

With the improved implementation and data preservation strategies, data should now:

1. Persist when navigating between pages (no more disappearing data)
2. Show a subtle loading indicator when refreshing in the background
3. Automatically refresh when the network connection is restored after being offline
4. Refresh when you manually click a refresh button
5. Preserve existing data during loading to prevent empty screens

The application no longer responds to tab focus changes and preserves data during navigation, providing a more consistent experience. If you experience data loading issues:

1. Use the refresh button on the page (if available)
2. Navigate to another page and back to trigger a refresh
3. Perform a hard refresh of the page (Ctrl+F5 or Cmd+Shift+R)
4. If issues persist, try logging out and logging back in

The data preservation strategies should prevent data from disappearing during normal use, but if you encounter any issues, please report them with details about the specific page and actions that led to the problem.

## Standardized Data Loading Across Views

We've identified that some views (like Operations and Purchase Orders) consistently load data correctly, while others had issues with data disappearing. We've standardized the data loading approach across all views and implemented data preservation strategies:

1. **Standardized Views with Data Preservation**: Updated all major views to use the same data loading pattern with data preservation:
   - Enhanced standardized hooks for all major views with data preservation strategies:
     - Properties (`usePropertiesQueryV2`)
     - Maintenance (`useMaintenanceTasksQueryV2`)
     - Maintenance Automation (`useTaskAutomationQueryV2`)
     - Inventory (`useInventoryQueryV2`)
     - Damages (`useDamageReportsQueryV2`)
     - Teams (`useTeamManagementQueryV2`)
     - Settings/Appearance (`useAppearanceSettingsQueryV2`)
   - Added state to track previous data in all hooks
   - Implemented logic to return previous data during loading and errors
   - Simplified all pages to use these enhanced hooks
   - Removed complex event handling and toast notifications
   - Standardized query configuration with consistent settings

2. **Consistent Data Loading Pattern with Improved UI**: All views now follow the same pattern:
   - Use React Query hooks with consistent configuration
   - Implement data preservation strategies to prevent data disappearing
   - Show subtle loading indicators during background refreshes
   - Only show loading skeletons when no data is available
   - Simple, direct data fetching approach
   - Consistent error handling with fallback to previous data
   - Minimal event handling
   - Consistent retry logic and error handling
   - All hooks have `keepPreviousData` set to true to preserve data during loading
   - All hooks have `refetchOnWindowFocus` set to false to prevent data disappearing when tabs regain focus

## Project Cleanup

The project has accumulated some technical debt and unused files over time. We've started cleaning up:

1. **Removed Unused Components**: Removed components that were no longer being used:
   - Removed DataRefreshManager component
   - Removed old hooks that have been replaced by V2 versions
   - Removed all focus-related logic throughout the application

2. **Consolidated Duplicate Functionality**: Merged similar functions and hooks:
   - Standardized all data loading hooks to follow the same pattern
   - Removed duplicate visibility change handlers
   - Updated all hook references to use the new V2 versions

3. **Standardized Naming Conventions**: Ensured consistent naming across the codebase:
   - All hooks now follow the same naming convention (useEntityQueryV2)
   - All hooks return data in a consistent format

4. **Removed Deprecated Code**: Removed old code that was commented out or marked as deprecated:
   - Removed old hooks (usePropertiesQuery, useMaintenanceTasksQuery, useInventory, useTaskAutomation)
   - Removed focus-related event listeners and handlers
   - Fixed all references to removed hooks

## Network Connectivity and Supabase Connection Issues

### Problem
The StayFu application was experiencing issues with Supabase connections, particularly with the `get_profile` RPC function. Users would see errors like "Failed to fetch" and "Network error detected, will attempt retry with backoff" in the console. These issues could cause data loading failures and authentication problems.

### Solution
We've implemented several improvements to address these network connectivity issues:

1. **Enhanced Supabase Client**: Improved the Supabase client with better error handling and retry logic:
   - Increased maximum retries from 7 to 10 for more resilience
   - Reduced initial retry delay from 500ms to 300ms for faster first retry
   - Increased maximum retry delay from 15s to 30s for longer retries
   - Added better error detection and logging
   - Implemented more robust offline mode support

2. **Added ConnectionStatusIndicator Component**: Created a new UI component to provide visual feedback about connection status:
   - Shows when the application is offline, reconnecting, or back online
   - Provides toast notifications for connection changes
   - Automatically hides when connection is stable

3. **Improved Session Management**: Enhanced the SessionRefreshManager to handle network connectivity issues:
   - Checks network status before attempting session operations
   - Delays session expiration handling when offline
   - Automatically retries operations when connection is restored
   - Dispatches events for session status changes

4. **Custom Event System**: Implemented a custom event system for communication between components:
   - `stayfu-network-status`: Fired when network status changes
   - `stayfu-session-expired`: Fired when session expires
   - `stayfu-session-refreshed`: Fired when session is refreshed
   - `stayfu-max-retries`: Fired when max retries are reached

5. **Offline Data Handling**: Improved handling of offline scenarios:
   - Uses cached data from localStorage when available
   - Shows appropriate UI indicators
   - Queues operations to be performed when back online
   - Automatically retries operations when connectivity is restored

For more details, see the [NetworkConnectivity.md](NetworkConnectivity.md) documentation.

## Other Known Issues

- Dark mode issues with property name badges in the maintenance page
- Dark mode issues with some cards in the main dashboard
- Users with proper permissions may still have missing dashboard links in the sidebar
- Property data type inconsistencies may cause display issues in some views (fixed in Properties view, but may still occur elsewhere)
- If property data is missing after updating the RPC functions, try refreshing the page or clearing your browser cache

## Team Permissions Management

We've fixed several issues with the team permissions management functionality:

1. **Infinite Loop in PermissionManagement Component**: Fixed an issue where the PermissionManagement component would enter an infinite loop when loading team members and permissions. The issue was caused by:
   - Circular dependencies in the useEffect hooks
   - Multiple overlapping useEffect hooks with similar dependencies
   - References to state variables in dependency arrays causing re-renders
   - Missing `setTeamMembers` function in the component
   - Team members not loading properly due to missing RPC function

2. **Infinite Loop in TeamDashboard Component**: Fixed an issue where the TeamDashboard component would continuously mount and check team ownership, causing excessive API calls. The fix includes:
   - Added a ref to track if the component has already mounted
   - Simplified the dependency array to prevent unnecessary re-renders
   - Improved the component mount effect to only run once

3. **Optimized Permission Management Hook**: Enhanced the usePermissionManagement hook with:
   - Backup and restore logic for permissions data to prevent data loss
   - Throttling to prevent excessive API calls
   - Local state updates to reduce unnecessary refetching
   - Better error handling and logging

4. **Improved Team Owner Detection**: Fixed the isTeamOwner check to properly identify team owners and prevent permission management issues.

5. **Enhanced Error Handling**: Added better error handling and user feedback in the permissions management UI.

If you encounter issues with team permissions management:
- Check that you have the proper permissions (team owner, admin, or manage_staff permission)
- Try refreshing the page to reset the component state
- Check the browser console for any error messages
- Verify that the team members are properly loaded before attempting to manage permissions

## Database RPC Functions

We've identified and fixed issues with missing RPC functions:

1. **Team Members**: The `get_team_members` function was missing, causing team members not to load in the permissions management UI. We've created this function in the database and added fallback methods in the code that directly query the database if the RPC function fails.

2. **Inventory Items**: The `get_user_inventory_items` function was missing, causing inventory items not to load. We've created this function in the database and added a fallback method in the code that directly queries the database if the RPC function fails.

3. **Column Mismatch**: The function was referencing a `last_ordered` column that doesn't exist in the `inventory_items` table. We've updated the function to use `updated_at` as a substitute for `last_ordered`.

4. **Resilient Implementation**: All data loading hooks now include fallback methods that will be used if the RPC functions fail or don't exist. This ensures that data will still load even if the database schema doesn't match the expected structure.

5. **Migration Files**: We've created migration files for the missing RPC functions to ensure they're included in future database deployments.

6. **API Consistency**: We've updated the `useInventoryOperations` hook to correctly use the wrapper functions returned by `useInventoryQueryV2` instead of treating them as React Query mutation objects. This ensures that the inventory operations work correctly even when the RPC functions are missing.

## Troubleshooting

If you continue to experience data loading issues or data disappearing after the improved implementation:

1. **Check the browser console** for any error messages related to data fetching or React Query
2. **Verify your network connection** is stable and working properly
3. **Try a hard refresh** (Ctrl+F5 or Cmd+Shift+R) to completely reload the page and all resources
4. **Check if the issue is specific to certain views** - some views may have additional data loading logic
5. **Try switching between different pages** in the application to trigger route-based data refreshes
6. **Check if the issue occurs in different browsers** to rule out browser-specific problems
7. **Monitor the network tab** in browser developer tools to see if requests are being made when expected
8. **Observe if data disappears during specific actions** like navigation or when the tab regains focus
9. **Check if the data reappears after a few seconds** - it might be temporarily hidden during loading

The application now uses a robust data loading architecture with data preservation strategies that should prevent data from disappearing during normal use. This should result in more reliable data loading and fewer issues overall.

If the issue persists, please contact support with the following information:
- Browser and version
- Operating system
- Steps to reproduce the issue
- Screenshots of any error messages in the console
- Network request logs from the browser developer tools
- Time when the issue occurred
- Which specific view or data was disappearing
- What action triggered the data to disappear (navigation, tab focus change, etc.)
- Whether the data reappeared after a few seconds or remained missing
