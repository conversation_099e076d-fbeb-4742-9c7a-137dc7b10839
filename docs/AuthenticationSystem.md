# Authentication System in StayFu

## Overview

StayFu uses Supabase Auth for authentication and session management. This document outlines the authentication system, including session management, login/logout flows, and password reset functionality.

## Important Note on Routing

StayFu uses **HashRouter** throughout the application. All authentication-related navigation and redirects MUST use hash-based URLs (e.g., `/#/login` instead of `/login`). This is critical for proper authentication flows, session management, and consistent user experience.

## Session Duration

- **JWT Expiry**: 72 hours (259200 seconds) - configured in `supabase/config.toml`
- **Session Validity**: Sessions remain valid for 72 hours to prevent users from being timed out after page refreshes
- **Session Refresh**: Sessions are automatically refreshed when they have less than 24 hours remaining
- **Idle Timeout**: Users are only logged out after 72 hours of inactivity (configurable in `App.tsx`)
- **Warning Time**: Users receive a warning 30 minutes before being logged out due to inactivity

## Session Storage

- Sessions are stored in both localStorage and sessionStorage for redundancy
- The primary storage key is `stayfu_auth_token`
- Session expiry times are also stored for debugging purposes

## Session Checks

- Sessions are checked when:
  - The application loads
  - The page becomes visible after being hidden
  - Every 60 minutes via a background interval
  - After page refreshes

## Authentication Flows

### Login Flow

1. User enters email and password on the login page
2. The application attempts to authenticate with Supabase Auth
3. If successful, the user is redirected to:
   - The dashboard (default)
   - The page they were trying to access before being redirected to login
   - A pending invitation page if they have an invitation

### Logout Flow

1. User can manually log out by clicking the logout button
2. User is automatically logged out after 72 hours of inactivity
3. User is automatically logged out if their session expires

### Password Reset Flow

1. User clicks "Forgot Password" on the login page
2. User enters their email address
3. A password reset email is sent to the user
4. User clicks the link in the email to reset their password
5. User enters a new password and confirms it
6. User is redirected to the login page to log in with their new password

## Error Handling

- Login errors are displayed to the user with clear, user-friendly messages
- Network errors and timeouts are handled gracefully
- Session expiration redirects the user to the login page with an appropriate message

## Implementation Details

The authentication system is implemented across several files:

- `src/components/auth/RequireAuth.tsx`: Handles session validation for protected routes
- `src/components/auth/SessionManager.tsx`: Manages session refresh and idle timeout
- `src/components/auth/IdleTimeoutManager.tsx`: Handles user inactivity and logout
- `src/components/auth/SessionRefreshManager.tsx`: Manages session refresh
- `src/utils/sessionUtils.ts`: Provides utility functions for session management
- `src/contexts/AuthContext.tsx`: Manages the global authentication state
- `src/pages/Login.tsx`: Handles user login
- `src/pages/ForgotPassword.tsx`: Handles password reset requests
- `src/pages/ResetPassword.tsx`: Handles password reset

## Hash Routing

All authentication-related routes use hash routing (`/#/`) to ensure compatibility with the application's routing system. This includes:

- Login: `/#/login`
- Forgot Password: `/#/forgot-password`
- Reset Password: `/#/reset-password`

## Troubleshooting

If users experience authentication issues:

1. Check browser console for authentication-related errors
2. Verify that localStorage contains a valid `stayfu_auth_token`
3. Check if the session has expired by examining `stayfu_session_expires`
4. Try refreshing the page or clearing browser cache
5. Ensure all URLs in emails use hash routing (`/#/`)
