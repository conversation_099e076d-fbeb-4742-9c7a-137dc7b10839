# Data Loading Fixes - June 17, 2024

This document outlines the fixes implemented to address data loading issues when pages lose focus or when navigating between different views in the StayFu application.

## Overview of Issues

The application was experiencing several issues related to data loading:

1. Data would disappear or fail to load when the application regained focus after being in the background
2. Some pages would load data correctly when returning from out-of-focus, while others wouldn't
3. Specific routes like `/#/properties/[id]`, `/#/maintenance`, `/#/damages`, `/#/teams`, and `/#/settings/appearance` were having issues

## Root Causes Identified

1. **Inconsistent React Query Configuration**: Different parts of the application had different React Query configurations, with some setting `refetchOnWindowFocus` to `true` and others to `false`.

2. **Incomplete Route Query Key Mapping**: The `routeQueryKeyMap` in `useNavigationRefresh.ts` was missing some important query keys for certain routes.

3. **Special Case Handling Issues**: The special case handling for property detail pages was mapping them to `/properties` instead of a more specific route.

4. **Visibility Change Handlers**: Some pages had visibility change handlers that might conflict with React Query's built-in window focus handling.

## Implemented Fixes

### 1. Enhanced Navigation Refresh Mechanism

Updated the `useNavigationRefresh.ts` file to:

- Add more comprehensive query key mappings for all routes
- Improve special case handling for property detail pages
- Add debug logging to help identify which queries are failing to refresh
- Use a utility function to force refresh all queries

```javascript
// Before:
const routeQueryKeyMap: Record<string, string[]> = {
  '/properties': ['properties'],
  // ...
};

// After:
const routeQueryKeyMap: Record<string, string[]> = {
  '/properties': ['properties', 'propertiesV2'],
  '/properties/detail': ['propertyDetail', 'propertyDetailV2', 'properties', 'propertiesV2'],
  // ...
};
```

### 2. Added Debug Utilities

Created a new `debugUtils.ts` file with functions to:

- Debug the React Query cache for specific query keys
- Force refresh all queries for a specific route
- Check if a query exists in the cache

```typescript
export const debugQueryCache = (
  queryClient: QueryClient,
  queryKey: string | unknown[]
): {
  exists: boolean;
  isStale: boolean;
  lastUpdated: string | null;
  data: any;
  queryCount: number;
} => {
  // Implementation details...
};

export const forceRefreshQueries = async (
  queryClient: QueryClient,
  queryKeys: string[]
): Promise<void> => {
  // Implementation details...
};
```

### 3. Updated TeamDashboard Component

Modified the `TeamDashboard.tsx` file to:

- Use the enhanced navigation refresh mechanism
- Add more debug logging
- Simplify the refresh logic to be more consistent

```typescript
// Before:
refreshRouteData('/teams');

// After:
console.log('[TeamDashboard] Calling refreshRouteData for /teams');
await refreshRouteData('/teams');
console.log('[TeamDashboard] Finished refreshRouteData for /teams');
```

### 4. Simplified Manual Refresh Logic

Updated the `handleRefresh` function in `TeamDashboard.tsx` to:

- First fetch data directly to ensure we have the latest
- Then use the enhanced navigation refresh to refresh all team-related queries
- Add more debug logging

```typescript
// Before:
// First invalidate all relevant queries
console.log('[TeamDashboard] Invalidating all team-related queries');
queryClient.invalidateQueries({ queryKey: ['teamsV2'] });
queryClient.invalidateQueries({ queryKey: ['teamMembersV2'] });
// ...

// After:
// First fetch the data directly to ensure we have the latest
console.log('[TeamDashboard] Fetching teams and invitations directly');
await Promise.all([
  fetchTeams(0),
  fetchPendingInvitations(true)
]);

// Then use our enhanced navigation refresh to refresh all team-related queries
console.log('[TeamDashboard] Using enhanced navigation refresh for /teams');
await refreshRouteData('/teams');
```

## Testing

These changes should be tested by:

1. Opening different dashboard views and checking if data loads correctly
2. Switching between tabs or minimizing the browser, then returning to the application
3. Navigating between different views and ensuring data persists
4. Checking all dashboard views to ensure they load data properly

## Best Practices for Future Development

1. **Consistent React Query Configuration**: Ensure all QueryClient configurations use the same settings for `refetchOnWindowFocus`.

2. **Comprehensive Query Key Mapping**: Keep the `routeQueryKeyMap` in `useNavigationRefresh.ts` up to date with all necessary query keys for all routes.

3. **Debug Logging**: Use the debug utilities to help identify which queries are failing to refresh.

4. **Avoid Custom Visibility Change Handlers**: Rely on React Query's built-in window focus handling instead of custom visibility change handlers.

5. **Use the Enhanced Navigation Refresh**: Use the `refreshRouteData` function from `useNavigationRefresh.ts` to refresh data when navigating between views.

## Affected Files

1. `src/hooks/useNavigationRefresh.ts`
2. `src/utils/debugUtils.ts` (new file)
3. `src/pages/TeamDashboard.tsx`
