# StayFu Image Handling

This document outlines the image handling strategies used in the StayFu application.

## Overview

StayFu uses Supabase Storage for storing and serving images. Images are used in various parts of the application, including property cards, inventory items, and damage reports. This document focuses on how images are handled, particularly in relation to error handling and performance optimization.

## Image Storage Structure

Images are stored in several Supabase Storage buckets:

1. `property-images` - For property images
2. `inventory` - For inventory item images
3. `damage-photos` - For damage report photos

## Image URL Handling

### URL Formation

Image URLs in the database can be stored in several formats:

1. Full URLs (starting with `http://` or `https://`)
2. Relative paths (e.g., `property-images/123.jpg`)

When displaying images, the application ensures URLs are properly formatted:

```javascript
// If the URL is not empty but doesn't start with http/https, add the Supabase URL prefix
if (imageUrl && !imageUrl.startsWith('http')) {
  imageUrl = `https://pwaeknalhosfwuxkpaet.supabase.co/storage/v1/object/public/${imageUrl}`;
}
```

### Cache Busting

To prevent caching issues, especially after image updates, cache-busting parameters are added to image URLs:

```javascript
// Add cache-busting parameter
if (imageUrl) {
  const separator = imageUrl.includes('?') ? '&' : '?';
  imageUrl = `${imageUrl}${separator}_t=${Date.now()}`;
}
```

## Image Existence Checking

Before displaying images, the application checks if they exist to prevent broken image placeholders:

```javascript
export const checkImageExists = async (url: string): Promise<boolean> => {
  if (!url) return false;

  try {
    // Use AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000);
    
    try {
      const response = await fetch(url, { 
        method: 'HEAD',
        signal: controller.signal,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
      
      clearTimeout(timeoutId);
      return response.ok;
    } catch (fetchError) {
      clearTimeout(timeoutId);
      
      // For network errors, try an alternative approach with Image object
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        img.src = url;
        
        setTimeout(() => {
          img.src = '';
          resolve(false);
        }, 3000);
      });
    }
  } catch (error) {
    return false;
  }
};
```

## Image Loading Strategies

### Lazy Loading

Images are lazy-loaded to improve performance:

```html
<img src={imageUrl} alt={name} loading="lazy" />
```

### Progressive Loading

For larger images, a loading indicator is shown while the image is being checked and loaded:

```jsx
{imageExists === null && (
  <div className="absolute inset-0 flex items-center justify-center bg-muted/50 rounded-t-md z-10">
    <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
  </div>
)}
```

### Error Handling

When images fail to load, the application tries several fallback strategies:

1. Retry with cache-busting parameter
2. Use a placeholder image

```jsx
onError={(e) => {
  console.error("Image failed to load:", imageUrl);
  
  // Try to load the image with a cache-busting parameter
  const cacheBustUrl = imageUrl + (imageUrl.includes('?') ? '&' : '?') + 'cb=' + new Date().getTime();
  
  // Set a fallback image if the retry fails
  e.currentTarget.onerror = () => {
    e.currentTarget.src = "/placeholder.svg";
    e.currentTarget.onerror = null; // Prevent infinite loop
  };
  
  // Try the cache-busting URL
  e.currentTarget.src = cacheBustUrl;
}}
```

## Performance Considerations

### Timeouts

All image operations have timeouts to prevent hanging requests:

- Image existence checking: 3 seconds
- Image loading: 3 seconds
- Image uploads: 15 seconds

### Staggered Loading

To prevent too many simultaneous requests, image checks are staggered with small random delays:

```javascript
// Add a small delay to prevent too many simultaneous requests
await new Promise(resolve => setTimeout(resolve, Math.random() * 500));
```

## Troubleshooting

If images are not loading correctly:

1. Check the browser console for errors
2. Verify the image URL is correctly formatted
3. Ensure the image exists in the Supabase storage bucket
4. Try clearing the browser cache
5. Check network connectivity

## Recent Improvements

Recent improvements to image handling include:

1. Added timeouts to prevent hanging requests
2. Improved error handling with multiple fallback strategies
3. Added loading indicators for better user experience
4. Implemented cache-busting to prevent stale images
5. Added cleanup to prevent memory leaks
