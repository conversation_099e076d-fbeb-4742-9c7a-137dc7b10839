# Service Worker Fixes

This document outlines the fixes implemented to address issues with the Service Worker in the StayFu application.

## Overview of Issues

The application was experiencing the following errors related to the Service Worker:

1. Service Worker registration error:
```
Uncaught (in promise) TypeError: Failed to update a ServiceWorker for scope ('http://localhost:8080/') with script ('http://localhost:8080/service-worker.js'): An unknown error occurred when fetching the script.
```

2. Network error in the Service Worker:
```
Uncaught (in promise) Error: Network error
```

3. WebSocket connection issues with Vite's HMR:
```
WebSocket connection to 'ws://localhost:8080/?token=1Wj_5PF9kjLS' failed
[vite] failed to connect to websocket.
```

These errors indicate issues with the Service Worker registration, network requests handling, and development server configuration.

## Implemented Fixes

### 1. Improved Service Worker Registration Process

Modified the service worker registration process in `src/utils/serviceWorkerRegistration.ts` to:

1. Unregister any existing service workers before registering a new one
2. Add a small delay between unregistration and registration
3. Improve error handling during registration
4. Add more detailed logging

```javascript
export function register() {
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      // Ensure we're using the correct base URL for the service worker
      const baseUrl = window.location.origin;
      const swUrl = `${baseUrl}/service-worker.js`;

      // First, unregister any existing service workers to avoid conflicts
      navigator.serviceWorker.getRegistrations().then(registrations => {
        for (let registration of registrations) {
          registration.unregister().then(() => {
            console.log('Service Worker unregistered successfully');
          }).catch(error => {
            console.error('Error unregistering Service Worker:', error);
          });
        }

        // After unregistering, register the service worker with explicit scope and update options
        setTimeout(() => {
          navigator.serviceWorker
            .register(swUrl, {
              scope: '/',
              updateViaCache: 'none' // Always check for updates
            })
            .then(registration => {
              console.log('Service Worker registered with scope:', registration.scope);
              // ... rest of the code
            })
            .catch(error => {
              console.error('Error during service worker registration:', error);
            });
        }, 1000); // Add a small delay to ensure unregistration completes
      }).catch(error => {
        console.error('Error getting Service Worker registrations:', error);

        // If we can't get registrations, try registering anyway
        navigator.serviceWorker
          .register(swUrl, {
            scope: '/',
            updateViaCache: 'none' // Always check for updates
          })
          .then(registration => {
            // ... rest of the code
          })
          .catch(error => {
            console.error('Error during service worker registration:', error);
          });
      });
    });
  }
}
```

### 2. Enhanced Service Worker Caching

Modified the service worker file (`public/service-worker.js`) to:

1. Increment the cache version to force a clean installation
2. Use a more robust approach to cache assets
3. Add better error handling during caching

```javascript
// Service Worker for StayFu PWA
const CACHE_NAME = 'stayfu-cache-v7'; // Increment the cache version
const APP_SHELL_CACHE = 'stayfu-app-shell-v7';
const DYNAMIC_CACHE = 'stayfu-dynamic-v7';
const API_CACHE = 'stayfu-api-v7';

// Install event - cache the app shell
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Installing Service Worker');

  event.waitUntil(
    Promise.all([
      // Cache app shell assets
      caches.open(APP_SHELL_CACHE)
        .then((cache) => {
          console.log('[Service Worker] Caching App Shell');
          // Use a more robust approach to cache assets
          return Promise.all(
            APP_SHELL_ASSETS.map(url => {
              return fetch(url, { cache: 'no-cache' })
                .then(response => {
                  if (!response || response.status !== 200) {
                    console.error(`[Service Worker] Failed to cache: ${url}`);
                    return;
                  }
                  return cache.put(url, response);
                })
                .catch(error => {
                  console.error(`[Service Worker] Error caching ${url}:`, error);
                });
            })
          );
        })
        .catch(error => {
          console.error('[Service Worker] Error during app shell caching:', error);
        }),
      // Skip waiting to activate immediately
      self.skipWaiting()
    ])
  );
});
```

## Testing

These changes should be tested by:

1. Clearing the browser cache and service workers
2. Reloading the application
3. Checking the browser console for any service worker errors
4. Verifying that the service worker is registered and active
5. Testing offline functionality

### 3. Enhanced Error Handling in Service Worker

Modified the service worker to handle network errors more gracefully:

```javascript
.catch((error) => {
  console.log(`[Service Worker] Network fetch error for ${event.request.url}:`, error);

  // If it's an image request, return a fallback image
  if (event.request.url.match(/\.(jpg|jpeg|png|gif|svg)$/)) {
    return caches.match('/icons/logo.png')
      .then(response => {
        if (response) {
          return response;
        }
        // If even the fallback image is not available, return a simple transparent image
        return new Response(
          new Blob([
            'R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'
          ], { type: 'image/gif' }),
          { status: 200 }
        );
      });
  }

  // For JS/CSS resources, try to return an empty but valid response
  if (event.request.url.match(/\.(js|css)$/)) {
    const contentType = event.request.url.endsWith('.js') ? 'application/javascript' : 'text/css';
    return new Response('/* Empty fallback */', {
      status: 200,
      headers: { 'Content-Type': contentType }
    });
  }

  // For other resources, return a generic error response
  return new Response('Network error occurred', {
    status: 503,
    headers: { 'Content-Type': 'text/plain' }
  });
});
```

### 4. Fixed Vite HMR WebSocket Connection

Updated the Vite configuration to fix WebSocket connection issues:

```javascript
server: {
  host: "::", // Allows access from network (including extension)
  port: 8080,
  // Remove proxy if it exists, as we are using middleware directly
  hmr: {
    // Explicitly set the HMR protocol to ws
    protocol: 'ws',
    // Use the same host and port as the server
    host: 'localhost',
    port: 8080,
    // Add a timeout for WebSocket connections
    timeout: 5000,
    // Enable overlay for errors
    overlay: true,
  },
  // Add CORS headers to allow service worker to fetch resources
  cors: true,
},
```

## Future Considerations

1. Consider implementing a more robust service worker update strategy
2. Add a mechanism to automatically recover from service worker errors
3. Implement better monitoring and logging for service worker issues
4. Consider using Workbox for a more robust service worker implementation
5. Implement a fallback strategy for browsers that don't support service workers
6. Add better debugging tools for service worker issues
