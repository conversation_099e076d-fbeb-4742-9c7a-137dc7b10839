# Network Connectivity Handling in StayFu

## Overview

StayFu has been enhanced with robust network connectivity handling to ensure a smooth user experience even when network conditions are poor or intermittent. This document outlines the strategies implemented to handle network issues.

## Key Components

### 1. Supabase Client Enhancements

The Supabase client has been enhanced with improved error handling and retry logic:

- **Increased Retry Attempts**: Maximum retries increased to 10 with exponential backoff
- **Improved Error Detection**: Better detection of network errors and session expiration
- **Offline Mode Support**: Ability to use cached data when offline
- **Event Dispatching**: Custom events for network status changes and authentication issues

### 2. ConnectionStatusIndicator Component

A new UI component has been added to provide visual feedback about the connection status:

- **Location**: Fixed position at the bottom right of the screen
- **States**:
  - Connected (green): Shows briefly when connection is restored
  - Reconnecting (yellow): Shows when attempting to reconnect
  - Offline (red): Shows when the application is offline
- **Toast Notifications**: User-friendly notifications for connection changes

### 3. Session Management Improvements

The SessionRefreshManager has been updated to handle network connectivity issues:

- **Network Awareness**: Checks network status before attempting session operations
- **Delayed Session Expiration**: Postpones session expiration handling when offline
- **Event-Based Refresh**: Triggers session checks when network is restored
- **Custom Events**: Dispatches events for session status changes

## Custom Events

The following custom events are used for communication between components:

| Event Name | Description | Payload |
|------------|-------------|---------|
| `stayfu-network-status` | Fired when network status changes | `{ online: boolean, timestamp: string }` |
| `stayfu-session-expired` | Fired when session expires | `{ timestamp: string }` |
| `stayfu-session-refreshed` | Fired when session is refreshed | `{ timestamp: string }` |
| `stayfu-max-retries` | Fired when max retries are reached | `{ url: string, method: string, error: string, time: string, isOffline: boolean }` |
| `stayfu-fetch-error` | Fired when a fetch error occurs | `{ url: string, error: string, time: string }` |

## Retry Strategy

The application uses an exponential backoff strategy for retries:

1. Initial retry delay: 300ms
2. Maximum retry delay: 30 seconds
3. Exponential growth with jitter: `delay = min(initialDelay * 1.5^retryCount * (0.9 + random * 0.2), maxDelay)`
4. Maximum retries: 10

## Offline Data Handling

When offline, the application:

1. Uses cached data from localStorage when available
2. Shows appropriate UI indicators
3. Queues operations to be performed when back online
4. Automatically retries operations when connectivity is restored

## User Experience Improvements

- **Toast Notifications**: User-friendly notifications for connection changes
- **Visual Indicators**: Clear visual feedback about connection status
- **Graceful Degradation**: Continues to function with limited capabilities when offline
- **Automatic Recovery**: Seamlessly recovers when connection is restored

## Implementation Details

### Supabase Client

The Supabase client has been enhanced with:

```typescript
// Network status event dispatching
window.dispatchEvent(new CustomEvent('stayfu-network-status', { 
  detail: { online: false, timestamp: new Date().toISOString() } 
}));

// Session expired event dispatching
window.dispatchEvent(new CustomEvent('stayfu-session-expired', { 
  detail: { timestamp: new Date().toISOString() } 
}));

// Max retries event dispatching
window.dispatchEvent(new CustomEvent('stayfu-max-retries', {
  detail: {
    url,
    method: args[1]?.method || 'GET',
    error: err.message,
    time: new Date().toISOString(),
    isOffline: isOffline
  }
}));
```

### ConnectionStatusIndicator

A new component that listens for network events and provides visual feedback:

```tsx
// Event listeners
window.addEventListener('online', handleOnlineStatusChange);
window.addEventListener('offline', handleOnlineStatusChange);
window.addEventListener('stayfu-network-status', handleSupabaseNetworkStatus);
window.addEventListener('stayfu-max-retries', handleMaxRetries);
```

### SessionRefreshManager

Enhanced to handle network connectivity issues:

```typescript
// Check if we're online first
if (typeof navigator !== 'undefined' && 'onLine' in navigator && !navigator.onLine) {
  console.log(`[SessionRefreshManager] Network is offline, skipping session check`);
  setIsRefreshing(false);
  return;
}
```

## Troubleshooting

If you encounter network-related issues:

1. Check your internet connection
2. Look for the ConnectionStatusIndicator to see the current status
3. Check the browser console for detailed error messages
4. Try refreshing the page if the application seems stuck
5. Clear browser cache and cookies if persistent issues occur
