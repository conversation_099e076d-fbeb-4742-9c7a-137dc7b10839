# Property Documents and Files

This document outlines the property documents and files feature in StayFu, which allows property managers and team members to attach files and create rich text documents for properties.

## Overview

The property documents and files feature provides the following capabilities:

1. **Rich Text Documents**: Create, edit, and view formatted text documents with embedded images and links
2. **File Attachments**: Upload, download, and manage files of various types (PDF, DOC, XLS, TXT, images, etc.)
3. **Privacy Controls**: Set documents and files as private or shared with team members
4. **Pagination**: Navigate through large numbers of documents and files
5. **Multiple File Upload**: Upload multiple files at once with optional metadata

## Database Schema

### property_documents

Stores rich text documents for properties.

- `id` (uuid, PK): Document ID
- `property_id` (uuid): Property ID
- `user_id` (uuid): User ID who created the document
- `title` (text): Document title
- `content` (text): Rich text content
- `is_private` (boolean): Whether the document is private to the creator
- `created_at` (timestamp): When the document was created
- `updated_at` (timestamp): When the document was last updated

### property_files

Stores file attachments for properties.

- `id` (uuid, PK): File ID
- `property_id` (uuid): Property ID
- `user_id` (uuid): User ID who uploaded the file
- `filename` (text): Original filename
- `display_name` (text): User-friendly display name for the file
- `caption` (text): Optional description of the file
- `file_path` (text): Path in storage
- `file_type` (text): MIME type
- `file_size` (integer): Size in bytes
- `is_private` (boolean): Whether the file is private to the uploader
- `created_at` (timestamp): When the file was uploaded
- `updated_at` (timestamp): When the file was last updated

## Storage

Files are stored in the `property-files` storage bucket in Supabase. The file path format is:

```
{property_id}/{timestamp}-{random_string}-{filename}
```

## Access Control

### RLS Policies for property_documents

- Users can view their own documents
- Users can view non-private documents for properties they have access to
- Users can create documents for properties they have access to
- Users can update their own documents
- Property owners can update any document for their properties
- Users can delete their own documents
- Property owners can delete any document for their properties

### RLS Policies for property_files

- Users can view their own files
- Users can view non-private files for properties they have access to
- Users can upload files for properties they have access to
- Users can update their own files
- Property owners can update any file for their properties
- Users can delete their own files
- Property owners can delete any file for their properties

### Access Control Functions

- `has_property_document_access(p_document_id uuid)`: Checks if a user has access to a property document
- `has_property_file_access(p_file_id uuid)`: Checks if a user has access to a property file

## UI Components

### PropertyDocumentsTab

Main tab component that contains both documents and files sections. Features:

- Tab navigation between documents and files
- Add document/file buttons
- Pagination controls
- Loading states

### Document Components

1. **PropertyDocumentsList**: Displays a list of documents with:
   - Document title
   - Privacy indicator (private/shared)
   - Creation/update information
   - View/edit/delete buttons

2. **AddDocumentDialog**: Dialog for creating and editing documents with:
   - Title field
   - Rich text editor (React Quill)
   - Privacy toggle

3. **ViewDocumentDialog**: Dialog for viewing documents with:
   - Document title and metadata
   - Rendered rich text content

### File Components

1. **PropertyFilesList**: Displays a list of files with:
   - File name and type
   - File size
   - Privacy indicator (private/shared)
   - Preview/download/edit/delete buttons

2. **AddFileDialog**: Dialog for uploading files with:
   - File selection (drag & drop or browse)
   - Multiple file upload support
   - Optional display name and caption fields
   - Privacy toggle
   - Upload progress indicator

3. **EditFileDialog**: Dialog for editing file properties with:
   - Display name field
   - Caption field
   - Privacy toggle

4. **FilePreviewDialog**: Dialog for previewing files with:
   - File metadata
   - Preview for supported file types (images, PDFs, text)
   - Download button

## Feature Details

### Rich Text Documents

- Uses React Quill for rich text editing
- Supports text formatting, lists, links, and embedded images
- Content is stored as HTML in the database

### File Uploads

- Supports single or multiple file uploads
- Validates file types and sizes
- Shows upload progress for each file
- Generates unique file paths to prevent conflicts

### File Preview

- Previews images directly in the browser
- Embeds PDFs using an iframe
- Shows text content for text files
- Provides download option for all file types

### Pagination

- Limits the number of items per page (default: 10)
- Shows navigation controls for moving between pages
- Displays total item count

### Privacy Controls

- Private documents/files are only visible to the creator
- Shared documents/files are visible to all team members with access to the property
- Property owners can see and manage all documents/files

## Usage Examples

### Creating a Document

1. Navigate to a property detail page
2. Click on the "Documents" tab
3. Click "New Document"
4. Enter a title and content
5. Toggle privacy setting if needed
6. Click "Save Document"

### Uploading Files

1. Navigate to a property detail page
2. Click on the "Documents" tab
3. Switch to the "Files" tab
4. Click "Upload File"
5. Select one or more files
6. For single files, optionally enter a display name and caption
7. Toggle privacy setting if needed
8. Click "Upload File" or "Upload X Files"

### Editing File Properties

1. Navigate to a property detail page
2. Click on the "Documents" tab
3. Switch to the "Files" tab
4. Find the file and click the edit button
5. Update the display name, caption, or privacy setting
6. Click "Save Changes"

## Implementation Notes

1. The rich text editor uses React Quill, an open-source alternative to TinyMCE
2. File uploads are processed sequentially to prevent overwhelming the server
3. File previews use browser capabilities for supported file types
4. The UI is responsive and works on mobile devices
5. All components follow the StayFu UI style guide
