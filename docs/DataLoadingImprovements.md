# StayFu Data Loading and Authentication Improvements

This document outlines the standardized approach to data loading, authentication, session management, and cache handling in the StayFu application. These improvements ensure consistent behavior across all pages and components.

## Important Note on Routing

StayFu uses **HashRouter** throughout the application. All navigation must use hash-based URLs (e.g., `/#/dashboard` instead of `/dashboard`). This is critical for proper navigation, data loading, and session management. When implementing any navigation or redirection, always ensure you're using the hash-based format.

## Recent Updates

### Standardized Data Loading and Authentication (July 2023)

We've implemented a comprehensive standardization of data loading, authentication, session management, and cache handling throughout the application:

1. **Standardized React Query Configuration**: Ensured consistent React Query settings across the application:
   - Set staleTime to 5 minutes to prevent excessive refetching
   - Set cacheTime to 10 minutes to ensure data remains available
   - Used refetchOnMount: true for better performance
   - Added refetchOnReconnect: true to refresh data when network connectivity is restored
   - These changes ensure that data is properly cached and reused when switching between dashboards

2. **Standardized Hooks for Data Loading**: Created standardized hooks for data loading:
   - `useStandardQuery`: A standardized hook for data fetching using React Query
   - Consistent error handling and retry logic
   - Standardized toast notifications for loading, success, and error states
   - Automatic retry on error with exponential backoff

3. **Standardized Visibility Change Handling**: Created a standardized hook for visibility change handling:
   - `useVisibilityChange`: A standardized hook for handling visibility changes
   - Debounced visibility change events to prevent multiple rapid calls
   - Consistent logging and error handling
   - Manual trigger function for testing and debugging

4. **Standardized Session Management**: Created a standardized hook for session management:
   - `useSessionManagement`: A standardized hook for session management
   - Consistent session refresh logic with retry mechanism
   - Proper handling of session expiration
   - Automatic token refresh and profile update

5. **Standardized Authentication Error Handling**: Created a standardized utility for authentication error handling:
   - `handleAuthError`: A standardized function for handling authentication errors
   - Consistent error messages for different error types
   - Proper handling of session expiration and network errors
   - Optional toast notifications and redirects

## Best Practices for Data Loading and Authentication

To maintain reliable data loading and authentication in the StayFu application, follow these best practices:

1. **Use the Standardized Query Hook**: Always use the standardized `useStandardQuery` hook for data fetching:
   ```typescript
   const { data, isLoading, error, refetch } = useStandardQuery(
     'uniqueKey',
     async () => {
       // Fetch data here
       return await supabase.from('table').select('*');
     },
     {
       showLoadingToast: false,
       showErrorToast: true,
       showSuccessToast: false,
       errorMessage: 'Failed to load data'
     }
   );
   ```

2. **Use the Standardized Visibility Change Hook**: Always use the standardized `useVisibilityChange` hook to handle visibility changes:
   ```typescript
   const { isVisible, triggerVisibilityAction } = useVisibilityChange(
     () => {
       // Refresh data when page becomes visible
       if (!isLoading) {
         refetch();
       }
     },
     undefined,
     { debounceTime: 500, logEvents: false }
   );
   ```

3. **Use the Standardized Session Management Hook**: Always use the standardized `useSessionManagement` hook for session management:
   ```typescript
   const { isRefreshing, sessionValid, refreshSession } = useSessionManagement({
     refreshInterval: 24 * 60 * 60 * 1000, // 24 hours
     showToasts: true,
     redirectOnExpire: true
   });
   ```

4. **Use the Standardized Authentication Error Handler**: Always use the standardized `handleAuthError` function for handling authentication errors:
   ```typescript
   try {
     // Authentication operation
     await supabase.auth.signIn({ email, password });
   } catch (error) {
     handleAuthError(error, {
       showToast: true,
       redirectToLogin: false,
       logError: true
     });
   }
   ```

5. **Avoid Direct DOM Manipulation**: Don't rely on direct DOM manipulation or complex DOM queries. Instead, use React state and props to manage component state.

6. **Use Consistent Event Handling**: Use the standardized event names and handlers across the application:
   - 'visibilitychange' for page visibility changes (handled by useVisibilityChange)
   - 'online' and 'offline' for network status changes
   - 'beforeunload' for page unload events (handled by useSessionManagement)

## Conclusion

The standardization of data loading, authentication, session management, and cache handling has significantly enhanced the reliability and consistency of the StayFu application. By implementing standardized hooks and utilities, we've ensured that:

1. **Data Loading is Consistent**: All components use the same approach to data fetching, with consistent caching, refetching, and error handling.

2. **Authentication is Reliable**: Session management is handled consistently across the application, with proper token refresh and error handling.

3. **Visibility Changes are Properly Handled**: The application responds consistently when regaining focus, ensuring data is always up-to-date.

4. **Error Handling is Standardized**: Authentication errors are handled consistently, with proper user feedback and recovery mechanisms.

These improvements have resolved the issues with dashboards not loading data properly after switching between views or when the application loses focus. The standardized approach ensures a consistent user experience across all parts of the application and makes future maintenance and feature development more straightforward.

## Implementation Status

The standardized hooks and utilities have been implemented and are available for use throughout the application:

- `useStandardQuery`: Available in `src/hooks/useStandardQuery.ts`
- `useVisibilityChange`: Available in `src/hooks/useVisibilityChange.ts` (with aggressive refresh settings)
- `useSessionManagement`: Available in `src/hooks/useSessionManagement.ts`
- `handleAuthError`: Available in `src/utils/authErrorHandler.ts`

The following components have been updated to use the standardized hooks:

- `SessionManager`: Updated to use `useSessionManagement` and `useVisibilityChange`
- `Dashboard`: Updated to use `useVisibilityChange` with aggressive refresh settings
- `Maintenance`: Updated to use `useVisibilityChange` with aggressive refresh settings
- `TeamDashboard`: Updated to use `useVisibilityChange` with aggressive refresh settings
- `Operations`: Updated to use `useVisibilityChange` with aggressive refresh settings

All dashboard pages now use the same standardized approach to:
1. Handle visibility changes (when the app comes back from background)
2. Manage authentication sessions
3. Handle authentication errors
4. Load and refresh data

## Recent Optimizations (July 2023)

We've made the following optimizations to ensure data loads reliably when switching between pages or when the app regains focus:

1. **More Aggressive React Query Settings**:
   - Changed `refetchOnWindowFocus` from `true` to `'always'` to ensure data is always refreshed when the window regains focus
   - Changed `refetchOnMount` from `true` to `'always'` to ensure data is always refreshed when components mount
   - Reduced `staleTime` from 5 minutes to 2 minutes to ensure data is refreshed more frequently

2. **Improved Visibility Change Handling**:
   - Reduced debounce time from 500ms to 100ms for faster response
   - Removed processing lock to ensure visibility changes are always processed
   - Added manual trigger function to force refresh when needed

3. **Enhanced Navigation Tracking**:
   - Added hash change listener to handle hash router navigation
   - Integrated visibility change hook with navigation tracking
   - Added immediate refresh when returning to dashboard

4. **Improved Session Management**:
   - Added regular interval to refresh session every hour
   - Added immediate session refresh when component mounts
   - Added query invalidation when session is refreshed

These optimizations ensure that data is always fresh and up-to-date, even when switching between pages or when the app regains focus after being in the background.
