# Navigation Refresh Fixes - May 10, 2023

This document describes the changes made to fix navigation refresh issues in the StayFu application.

## Problem

The application had persistent issues with data loading when navigating between different pages, particularly:
1. Data not refreshing properly when navigating to the Damages dashboard
2. Console logs showing "[VerticalSidebar] Navigating to /damages with data refresh" but data not loading
3. Inconsistent data loading across different views

## Root Causes

1. **Insufficiently Aggressive Data Refreshing**: The `useNavigationRefresh` hook was using `refetchType: 'active'` which only refreshes active queries, not all queries.

2. **Too Short Refresh Cooldown**: The 2-second cooldown between refreshes was too short for some routes, leading to skipped refreshes.

3. **Missing Route Mappings**: The route mapping for '/damages' didn't include all necessary query keys and didn't handle damage report routes with IDs.

4. **Duplicate Refresh Events**: The VerticalSidebar was both calling `refreshRouteData` and dispatching a custom event, which could lead to duplicate refreshes or race conditions.

## Implemented Fixes

### 1. More Aggressive Data Refreshing

Updated the query invalidation in useNavigationRefresh.ts:

```javascript
// Before:
queryClient.invalidateQueries({
  queryKey: [key],
  refetchType: 'active', // Only refetch active queries
  exact: false
});

// After:
queryClient.invalidateQueries({
  queryKey: [key],
  refetchType: 'all', // Refetch all queries, not just active ones
  exact: false
});

// Also explicitly refetch the query to ensure data is loaded
queryClient.refetchQueries({
  queryKey: [key],
  exact: false,
  type: 'all'
});
```

### 2. Increased Refresh Cooldown

Increased the cooldown between refreshes to prevent too frequent refreshes:

```javascript
// Before:
const REFRESH_COOLDOWN = 2000; // 2 seconds cooldown

// After:
const REFRESH_COOLDOWN = 5000; // 5 seconds cooldown
```

### 3. Updated Route Mappings

Added more comprehensive route mappings for damage reports:

```javascript
// Before:
'/damages': ['damageReports', 'properties'],

// After:
'/damages': ['damageReports', 'damageReportsV2', 'properties'],
```

Also added special case handling for damage report routes with IDs:

```javascript
// Special case for damage reports with IDs
else if (cleanRoute.match(/^\/damages\/[a-zA-Z0-9-]+$/)) {
  baseRoute = '/damages';
  console.log(`[useNavigationRefresh] Special case: mapping ${cleanRoute} to ${baseRoute}`);
}
```

### 4. Eliminated Duplicate Refresh Events

Removed the custom event dispatch from VerticalSidebar.tsx to prevent duplicate refreshes:

```javascript
// Before:
// Dispatch a custom event to notify that navigation has occurred
window.dispatchEvent(new CustomEvent('stayfu-navigation-occurred', {
  detail: {
    from: location.pathname,
    to: path
  }
}));

// After:
// We no longer need to dispatch a custom event here as refreshRouteData already handles this
// This prevents duplicate refreshes
```

## Files Modified

1. `src/hooks/useNavigationRefresh.ts` - Updated query invalidation, increased cooldown, added route mappings
2. `src/components/layout/VerticalSidebar.tsx` - Removed duplicate event dispatch

## Testing

The changes should be tested by:
1. Navigating between different pages, especially to and from the Damages dashboard
2. Checking that data is loaded correctly on each page
3. Verifying that the console logs show proper data refreshing
4. Testing navigation to specific damage report pages with IDs

## Conclusion

These changes fix the navigation refresh issues by ensuring more aggressive data refreshing, preventing duplicate refreshes, and handling all route types correctly. The key insight was that the data refreshing needed to be more aggressive and comprehensive, especially for routes with IDs.
