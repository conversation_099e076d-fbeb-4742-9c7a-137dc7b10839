# Settings and Teams Fixes - May 11, 2023

This document describes the changes made to fix data loading issues in the Settings/Appearance and Teams views of the StayFu application.

## Problem

The application had persistent issues with data loading in two specific views:
1. `/settings/appearance` - Settings were not loading reliably when navigating to this page
2. `/teams` - Team data was not loading reliably when navigating to this page

These issues resulted in:
- Empty data displays
- Stalled loading animations
- Inconsistent data across different views

## Root Causes

1. **Missing Route Mappings**: The `/settings` and `/settings/appearance` routes were missing from the routeQueryKeyMap in useNavigationRefresh.ts.

2. **Ineffective Visibility Change Handling**: The TeamDashboard component was using visibility change handlers that were not effectively refreshing data.

3. **Missing React Query Integration**: The AppearanceSettings component was not using React Query for data fetching and relied on prop-based data passing.

## Implemented Fixes

### 1. Updated Route Mappings in useNavigationRefresh.ts

Added settings routes to the routeQueryKeyMap:

```javascript
'/settings': ['userSettings', 'appearanceSettingsV2', 'notificationSettings', 'accountSettings'],
'/settings/appearance': ['appearanceSettingsV2', 'userSettings'],
'/settings/notifications': ['notificationSettings', 'userSettings'],
'/settings/account': ['accountSettings', 'userSettings'],
```

Also added special case handling for settings routes:

```javascript
// Special case for settings routes
else if (cleanRoute.startsWith('/settings/')) {
  // Map specific settings routes to their base routes
  if (cleanRoute.startsWith('/settings/appearance')) {
    baseRoute = '/settings/appearance';
  } else if (cleanRoute.startsWith('/settings/notifications')) {
    baseRoute = '/settings/notifications';
  } else if (cleanRoute.startsWith('/settings/account')) {
    baseRoute = '/settings/account';
  } else {
    baseRoute = '/settings';
  }
  console.log(`[useNavigationRefresh] Special case: mapping ${cleanRoute} to ${baseRoute}`);
}
```

### 2. Fixed TeamDashboard Component

Replaced visibility change handlers with a periodic refresh timer:

```javascript
// Set up a periodic refresh timer instead of using visibility change events
useEffect(() => {
  // Skip if no user ID
  if (!authState.user?.id) return;

  console.log('[TeamDashboard] Setting up periodic refresh timer');

  // Function to refresh all team data
  const refreshAllTeamData = async () => {
    try {
      console.log('[TeamDashboard] Periodic refresh triggered');

      // First invalidate all relevant queries
      console.log('[TeamDashboard] Invalidating all team-related queries');
      queryClient.invalidateQueries({ queryKey: ['teamsV2'] });
      queryClient.invalidateQueries({ queryKey: ['teamMembersV2'] });
      // ... more invalidations ...

      // Also invalidate legacy query keys for backward compatibility
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      queryClient.invalidateQueries({ queryKey: ['teamMembers'] });
      // ... more invalidations ...

      // Then refetch all the queries we invalidated
      await Promise.all([
        queryClient.refetchQueries({ queryKey: ['teamsV2'], type: 'all' }),
        // ... more refetches ...
      ]);

      // Also directly fetch teams to ensure we have the latest data
      console.log('[TeamDashboard] Directly fetching teams data');
      await fetchTeams(0);

      // ... more refreshes ...

      console.log('[TeamDashboard] Periodic data refresh complete');
    } catch (error) {
      console.error('[TeamDashboard] Error in periodic refresh:', error);
    }
  };

  // Initial refresh after a short delay
  const initialRefreshTimeout = setTimeout(() => {
    refreshAllTeamData();
  }, 2000);

  // Set up periodic refresh every 30 seconds
  const refreshInterval = setInterval(() => {
    // Only refresh if the document is visible
    if (document.visibilityState === 'visible') {
      refreshAllTeamData();
    } else {
      console.log('[TeamDashboard] Document not visible, skipping periodic refresh');
    }
  }, 30000); // 30 seconds

  // Clean up
  return () => {
    clearTimeout(initialRefreshTimeout);
    clearInterval(refreshInterval);
  };
}, [
  authState.user?.id,
  queryClient,
  fetchTeams,
  fetchPendingInvitations,
  pendingInvitations.length,
  invitationsLoadedRef
]);
```

### 3. Created a New React Query Hook for Appearance Settings

Created a new hook `useAppearanceSettingsQuery` that uses React Query for data fetching:

```javascript
export const useAppearanceSettingsQuery = (): AppearanceSettingsData => {
  const { authState } = useAuth();
  const userId = authState?.user?.id;
  const queryClient = useQueryClient();
  const [retryCount, setRetryCount] = useState(0);

  // Function to retry data fetching manually
  const refreshSettings = useCallback(async () => {
    console.log('[useAppearanceSettingsQuery] Manual refresh triggered');
    setRetryCount(prev => prev + 1);
    await queryClient.invalidateQueries({ queryKey: ['appearanceSettings'] });
    await queryClient.refetchQueries({ queryKey: ['appearanceSettings'] });
  }, [queryClient]);

  // Fetch appearance settings
  const { 
    data: settings = DEFAULT_SETTINGS,
    isLoading,
    error,
    isError
  } = useQuery({
    queryKey: ['appearanceSettings', retryCount],
    queryFn: async () => {
      // ... fetch settings from Supabase ...
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000),
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    enabled: !!userId
  });

  // ... mutation for updating settings ...

  // Set up a periodic refresh timer
  useEffect(() => {
    // ... periodic refresh logic ...
  }, [userId, refreshSettings]);

  return {
    settings,
    loading: isLoading,
    error: error ? (error as Error).message : null,
    isError,
    updateSetting,
    refreshSettings
  };
};
```

### 4. Updated AppearanceSettings Component to Use React Query

Updated the AppearanceSettings component to use the new hook:

```javascript
const AppearanceSettings = ({ settings: propSettings, handleToggleSetting }: AppearanceSettingsProps) => {
  // Use a ref to track if a change is being processed to avoid loops
  const processingChange = useRef(false);
  const { refreshRouteData } = useNavigationRefresh();
  
  // Use our React Query hook for appearance settings
  const { 
    settings: querySettings, 
    loading, 
    updateSetting,
    refreshSettings
  } = useAppearanceSettingsQuery();
  
  // Refresh data when component mounts
  useEffect(() => {
    console.log('[AppearanceSettings] Component mounted, refreshing data');
    refreshSettings();
    refreshRouteData('/settings/appearance');
  }, [refreshSettings, refreshRouteData]);
  
  // Set up a periodic refresh timer
  useEffect(() => {
    // ... periodic refresh logic ...
  }, [refreshSettings]);
  
  // ... rest of component ...
};
```

## Files Modified

1. `src/hooks/useNavigationRefresh.ts` - Updated route mappings and special case handling
2. `src/pages/TeamDashboard.tsx` - Replaced visibility change handlers with periodic refresh timer
3. `src/hooks/useAppearanceSettingsQuery.ts` - Created new React Query hook for appearance settings
4. `src/components/settings/AppearanceSettings.tsx` - Updated to use React Query

## Testing

The changes should be tested by:
1. Navigating to the Settings/Appearance page and verifying that settings load correctly
2. Navigating to the Teams page and verifying that team data loads correctly
3. Switching between tabs and windows and verifying that data persists
4. Putting the application in the background and bringing it back to the foreground to verify that data refreshes correctly

## Conclusion

These changes fix the data loading issues in the Settings/Appearance and Teams views by ensuring proper route mappings, using periodic refresh timers instead of visibility change handlers, and integrating React Query for data fetching. The key insight was that these views needed more aggressive and reliable data refreshing mechanisms.
