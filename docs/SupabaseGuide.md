# Supabase Guide for StayFu

This document outlines the Supabase setup, functions, and policies used in the StayFu application.

## Database Functions

### `get_user_role_properties`

This function returns all properties that a user has access to, based on their role.

```sql
CREATE OR REPLACE FUNCTION public.get_user_role_properties(p_user_id uuid)
 RETURNS SETOF properties
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    user_role TEXT;
BEGIN
    -- Get the user's role
    SELECT profiles.role INTO user_role
    FROM profiles
    WHERE profiles.id = p_user_id;

    -- Route to the appropriate function based on role
    IF user_role = 'service_provider' THEN
        RETURN QUERY
        SELECT * FROM get_service_provider_properties(p_user_id);
    ELSE
        -- For all other roles (property_manager, admin, etc.), use get_user_properties
        RETURN QUERY
        SELECT * FROM get_user_properties(p_user_id);
    END IF;
END;
$function$;
```

### `get_user_properties`

This function returns all properties that a user has access to, including their own properties and team properties.

```sql
CREATE OR REPLACE FUNCTION public.get_user_properties(p_user_id uuid)
 RETURNS SETOF properties
 LANGUAGE sql
 SECURITY DEFINER
AS $function$
    SELECT DISTINCT ON (p.id)
        p.*
    FROM
        properties p
    WHERE
        -- User's own properties
        p.user_id = p_user_id
        -- Admin access
        OR EXISTS (SELECT 1 FROM profiles prf WHERE prf.id = p_user_id AND (prf.is_super_admin = true OR prf.role = 'admin'))
        -- Team member access
        OR EXISTS (
            SELECT 1 FROM team_properties tp
            JOIN team_members tm ON tp.team_id = tm.team_id
            WHERE tp.property_id = p.id AND tm.user_id = p_user_id AND tm.status = 'active'
        )
    ORDER BY
        p.id, p.name;
$function$;
```

### `get_service_provider_properties`

This function returns all properties that a service provider has access to through team memberships.

```sql
CREATE OR REPLACE FUNCTION public.get_service_provider_properties(p_user_id uuid)
 RETURNS SETOF properties
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    is_service_provider BOOLEAN;
BEGIN
    -- Check if the user is a service provider
    SELECT EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = p_user_id AND profiles.role = 'service_provider'
    ) INTO is_service_provider;

    -- If not a service provider, return empty result
    IF NOT is_service_provider THEN
        RETURN;
    END IF;

    -- Return properties the service provider has access to via team membership
    RETURN QUERY
    SELECT DISTINCT ON (p.id)
        p.*
    FROM properties p
    JOIN team_properties tp ON p.id = tp.property_id
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tm.user_id = p_user_id
    AND tm.status = 'active'
    ORDER BY p.id, p.name;
END;
$function$;
```

### `process_invitation_acceptance`

This function handles the invitation acceptance process in a single transaction, making it more reliable.

```sql
CREATE OR REPLACE FUNCTION public.process_invitation_acceptance(p_token TEXT, p_user_id UUID)
 RETURNS JSON
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  v_invitation RECORD;
  v_team_id UUID;
  v_team_name TEXT;
  v_role TEXT;
  v_invited_by UUID;
  v_user_exists BOOLEAN;
  v_result JSON;
BEGIN
  -- Check if the invitation exists and is valid
  SELECT * INTO v_invitation
  FROM team_invitations
  WHERE token = p_token
    AND status = 'pending'
    AND expires_at > NOW();

  IF v_invitation IS NULL THEN
    RETURN json_build_object(
      'success', FALSE,
      'error', 'Invitation not found or expired'
    );
  END IF;

  -- Store invitation details
  v_team_id := v_invitation.team_id;
  v_team_name := v_invitation.team_name;
  v_role := v_invitation.role;
  v_invited_by := v_invitation.invited_by;

  -- Check if the user exists
  SELECT EXISTS(SELECT 1 FROM auth.users WHERE id = p_user_id) INTO v_user_exists;

  IF NOT v_user_exists THEN
    RETURN json_build_object(
      'success', FALSE,
      'error', 'User not found'
    );
  END IF;

  -- Check if the user is already a member of the team
  IF EXISTS(SELECT 1 FROM team_members WHERE team_id = v_team_id AND user_id = p_user_id) THEN
    -- Update the invitation status
    UPDATE team_invitations
    SET status = 'accepted', updated_at = NOW()
    WHERE token = p_token;

    RETURN json_build_object(
      'success', TRUE,
      'message', 'User is already a member of the team',
      'team_id', v_team_id,
      'team_name', v_team_name
    );
  END IF;

  -- Begin transaction
  BEGIN
    -- Add the user to the team
    INSERT INTO team_members (team_id, user_id, added_by, status)
    VALUES (v_team_id, p_user_id, v_invited_by, 'active');

    -- Update the invitation status
    UPDATE team_invitations
    SET status = 'accepted', updated_at = NOW()
    WHERE token = p_token;

    -- If the role is service_provider, add default permissions
    IF v_role = 'service_provider' THEN
      -- Call the function to add default permissions for service providers
      PERFORM add_service_provider_default_permissions(p_user_id, v_team_id);
    END IF;

    -- Update the user's role if needed
    UPDATE profiles
    SET role = v_role::user_role
    WHERE id = p_user_id AND role != v_role::user_role;

    -- Commit transaction
    v_result := json_build_object(
      'success', TRUE,
      'message', 'Invitation accepted successfully',
      'team_id', v_team_id,
      'team_name', v_team_name,
      'role', v_role
    );
  EXCEPTION
    WHEN OTHERS THEN
      -- Rollback transaction
      RAISE LOG 'Error in process_invitation_acceptance: %', SQLERRM;
      v_result := json_build_object(
        'success', FALSE,
        'error', SQLERRM
      );
  END;

  RETURN v_result;
END;
$function$;
```

### `create_profile_safely`

This function creates a profile for a user in a safe way, with error handling and validation.

```sql
CREATE OR REPLACE FUNCTION public.create_profile_safely(p_user_id UUID, p_first_name TEXT, p_last_name TEXT, p_role TEXT, p_email TEXT)
 RETURNS BOOLEAN
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  v_profile_exists BOOLEAN;
  v_role_value TEXT;
BEGIN
  -- Check if the profile already exists
  SELECT EXISTS(SELECT 1 FROM public.profiles WHERE id = p_user_id) INTO v_profile_exists;

  IF v_profile_exists THEN
    RAISE LOG 'Profile already exists for user %', p_user_id;
    RETURN TRUE;
  END IF;

  -- Validate and set default values
  v_role_value := COALESCE(p_role, 'property_manager');

  -- Validate role value
  IF v_role_value NOT IN ('property_manager', 'service_provider', 'staff', 'admin', 'super_admin') THEN
    v_role_value := 'property_manager';
  END IF;

  -- Create the profile with a BEGIN/EXCEPTION block to catch errors
  BEGIN
    INSERT INTO public.profiles (id, first_name, last_name, role, email, created_at, updated_at)
    VALUES (
      p_user_id,
      COALESCE(p_first_name, ''),
      COALESCE(p_last_name, ''),
      v_role_value::user_role,
      COALESCE(p_email, ''),
      NOW(),
      NOW()
    );

    RAISE LOG 'Profile created successfully for user %', p_user_id;
    RETURN TRUE;
  EXCEPTION
    WHEN OTHERS THEN
      RAISE LOG 'Error creating profile for user %: %', p_user_id, SQLERRM;
      RETURN FALSE;
  END;
END;
$function$;
```

### `ensure_profile_exists`

This trigger function creates a profile for a new user when they are created in the auth.users table.

```sql
CREATE OR REPLACE FUNCTION public.ensure_profile_exists()
 RETURNS TRIGGER
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  v_first_name TEXT;
  v_last_name TEXT;
  v_role TEXT;
  v_email TEXT;
  v_profile_exists BOOLEAN;
BEGIN
  -- Check if the profile already exists
  SELECT EXISTS(SELECT 1 FROM public.profiles WHERE id = NEW.id) INTO v_profile_exists;

  IF v_profile_exists THEN
    RAISE LOG 'Profile already exists for user %', NEW.id;
    RETURN NEW;
  END IF;

  -- Extract user metadata
  v_first_name := COALESCE(NEW.raw_user_meta_data->>'first_name', '');
  v_last_name := COALESCE(NEW.raw_user_meta_data->>'last_name', '');
  v_role := COALESCE(NEW.raw_user_meta_data->>'role', 'property_manager');
  v_email := COALESCE(NEW.email, '');

  -- Log the values for debugging
  RAISE LOG 'Creating profile for user % with first_name=%, last_name=%, role=%, email=%',
    NEW.id, v_first_name, v_last_name, v_role, v_email;

  -- Create the profile with a BEGIN/EXCEPTION block to catch errors
  BEGIN
    INSERT INTO public.profiles (id, first_name, last_name, role, email, created_at, updated_at)
    VALUES (
      NEW.id,
      v_first_name,
      v_last_name,
      v_role::user_role,
      v_email,
      NOW(),
      NOW()
    );

    RAISE LOG 'Profile created successfully for user %', NEW.id;
  EXCEPTION
    WHEN OTHERS THEN
      RAISE LOG 'Error creating profile for user %: %', NEW.id, SQLERRM;
      -- Don't raise an exception, just log the error and continue
      -- This prevents the trigger from failing and allows the user to be created
  END;

  RETURN NEW;
END;
$function$;
```

## Edge Functions

### `accept-invitation-direct`

This edge function handles the invitation acceptance process for new users. It has been updated to use the `create_profile_safely` function to create profiles in a more reliable way.

Key improvements:
- Uses the `create_profile_safely` function to create profiles with better error handling
- Implements a fallback mechanism to ensure profiles are created even if the RPC function fails
- Provides more detailed error messages for debugging

## Client-Side Functions

### `processInvitationAcceptance`

This function handles the invitation acceptance process on the client side.

```typescript
export const processInvitationAcceptance = async (
  token: string,
  userId: string
): Promise<{
  success: boolean;
  error?: string;
  team_id?: string;
  team_name?: string;
  role?: string;
}> => {
  try {
    console.log('Processing invitation acceptance for user:', userId);

    // Call the new RPC function to process the invitation
    const { data, error } = await supabase.rpc(
      'process_invitation_acceptance',
      {
        p_token: token,
        p_user_id: userId
      }
    );

    if (error) {
      console.error('Error processing invitation:', error);
      return { success: false, error: error.message };
    }

    console.log('Invitation processed successfully:', data);
    return {
      success: data.success,
      error: data.error,
      team_id: data.team_id,
      team_name: data.team_name,
      role: data.role
    };
  } catch (error) {
    console.error('Exception in processInvitationAcceptance:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
};
```

### `registerUser`

This function handles user registration.

```typescript
export const registerUser = async (
  email: string,
  password: string,
  firstName: string,
  lastName: string,
  role: string = 'service_provider'
): Promise<{
  success: boolean;
  error?: string;
  user?: any;
  session?: any;
}> => {
  try {
    console.log('Registering new user:', email);

    // Register the user
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          first_name: firstName,
          last_name: lastName,
          role: role
        }
      }
    });

    if (error) {
      console.error('Registration failed:', error);
      return { success: false, error: error.message };
    }

    if (!data?.user) {
      return { success: false, error: 'Failed to create user account' };
    }

    console.log('User registered successfully:', data.user.id);
    return {
      success: true,
      user: data.user,
      session: data.session
    };
  } catch (error) {
    console.error('Exception in registerUser:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
};
```

### `signInUser`

This function handles user sign-in.

```typescript
export const signInUser = async (
  email: string,
  password: string
): Promise<{
  success: boolean;
  error?: string;
  user?: any;
  session?: any;
}> => {
  try {
    console.log('Signing in user:', email);

    // Sign in the user
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      console.error('Sign in failed:', error);
      return { success: false, error: error.message };
    }

    if (!data?.user) {
      return { success: false, error: 'Failed to sign in' };
    }

    console.log('User signed in successfully:', data.user.id);
    return {
      success: true,
      user: data.user,
      session: data.session
    };
  } catch (error) {
    console.error('Exception in signInUser:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
};
```

## Row Level Security (RLS) Policies

The following RLS policies have been implemented to ensure proper data access control:

### `has_property_access` Function

This function checks if a user has access to a property:

```sql
CREATE OR REPLACE FUNCTION public.has_property_access(p_property_id UUID) RETURNS BOOLEAN AS $$
BEGIN
  -- Super admins and admins have access to all properties
  IF EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  ) THEN
    RETURN TRUE;
  END IF;

  -- Property owners have access to their own properties
  IF EXISTS (
    SELECT 1 FROM properties
    WHERE id = p_property_id AND user_id = auth.uid()
  ) THEN
    RETURN TRUE;
  END IF;

  -- Team members have access to team properties
  IF EXISTS (
    SELECT 1 FROM team_members tm
    JOIN team_properties tp ON tm.team_id = tp.team_id
    WHERE tm.user_id = auth.uid()
    AND tp.property_id = p_property_id
    AND tm.status = 'active'
  ) THEN
    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Maintenance Tasks Policy

```sql
CREATE POLICY maintenance_tasks_policy ON maintenance_tasks
FOR ALL
TO public
USING (
  -- User is the creator of the task
  (user_id::text = auth.uid()::text) OR
  -- User is assigned to the task
  (assigned_to = auth.uid()::text) OR
  -- User is the provider for the task
  (provider_id::text = auth.uid()::text) OR
  -- User owns the property
  (property_id IN (SELECT id FROM properties WHERE user_id::text = auth.uid()::text)) OR
  -- User is a member of a team that has access to the property
  (property_id IN (
    SELECT tp.property_id
    FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tm.user_id::text = auth.uid()::text AND tm.status = 'active'
  )) OR
  -- User is a member of the team associated with the task
  (team_id IN (
    SELECT team_id
    FROM team_members
    WHERE user_id::text = auth.uid()::text AND status = 'active'
  )) OR
  -- User owns the team
  (team_id IN (
    SELECT id
    FROM teams
    WHERE owner_id::text = auth.uid()::text
  )) OR
  -- User is a super admin or admin
  (EXISTS (
    SELECT 1
    FROM profiles
    WHERE id::text = auth.uid()::text AND (is_super_admin OR role = ANY(ARRAY['super_admin'::user_role, 'admin'::user_role]))
  ))
);
```

### Inventory Items Policy

```sql
CREATE POLICY inventory_items_policy ON inventory_items
FOR ALL
TO public
USING (
  -- User is the creator of the item
  (user_id::text = auth.uid()::text) OR
  -- User owns the property
  (property_id IN (SELECT id FROM properties WHERE user_id::text = auth.uid()::text)) OR
  -- User is a member of a team that has access to the property
  (property_id IN (
    SELECT tp.property_id
    FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tm.user_id::text = auth.uid()::text AND tm.status = 'active'
  )) OR
  -- User is a super admin or admin
  (EXISTS (
    SELECT 1
    FROM profiles
    WHERE id::text = auth.uid()::text AND (is_super_admin OR role = ANY(ARRAY['super_admin'::user_role, 'admin'::user_role]))
  ))
);
```

### Damage Reports Policy

```sql
CREATE POLICY damage_reports_policy ON damage_reports
FOR ALL
TO public
USING (
  -- User is the creator of the report
  (user_id::text = auth.uid()::text) OR
  -- User owns the property
  (property_id IN (SELECT id FROM properties WHERE user_id::text = auth.uid()::text)) OR
  -- User is a member of a team that has access to the property
  (property_id IN (
    SELECT tp.property_id
    FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tm.user_id::text = auth.uid()::text AND tm.status = 'active'
  )) OR
  -- User is a super admin or admin
  (EXISTS (
    SELECT 1
    FROM profiles
    WHERE id::text = auth.uid()::text AND (is_super_admin OR role = ANY(ARRAY['super_admin'::user_role, 'admin'::user_role]))
  ))
);
```

### Purchase Orders Policy

```sql
CREATE POLICY purchase_orders_policy ON purchase_orders
FOR ALL
TO public
USING (
  -- User is the creator of the order
  (user_id::text = auth.uid()::text) OR
  -- User owns the property
  (property_id IN (SELECT id FROM properties WHERE user_id::text = auth.uid()::text)) OR
  -- User is a member of a team that has access to the property
  (property_id IN (
    SELECT tp.property_id
    FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tm.user_id::text = auth.uid()::text AND tm.status = 'active'
  )) OR
  -- User is a super admin or admin
  (EXISTS (
    SELECT 1
    FROM profiles
    WHERE id::text = auth.uid()::text AND (is_super_admin OR role = ANY(ARRAY['super_admin'::user_role, 'admin'::user_role]))
  ))
);
```

### Collections Policy

```sql
CREATE POLICY collections_policy ON collections
FOR ALL
TO public
USING (
  -- User is the creator of the collection
  (user_id::text = auth.uid()::text) OR
  -- User is a member of a team
  (EXISTS (
    SELECT 1
    FROM team_members tm
    WHERE tm.user_id::text = auth.uid()::text AND tm.status = 'active'
  )) OR
  -- User is a super admin or admin
  (EXISTS (
    SELECT 1
    FROM profiles
    WHERE id::text = auth.uid()::text AND (is_super_admin OR role = ANY(ARRAY['super_admin'::user_role, 'admin'::user_role]))
  ))
);
```

### Properties Policy

```sql
CREATE POLICY properties_policy ON properties
FOR ALL
TO public
USING (
  -- User is the owner of the property
  (user_id::text = auth.uid()::text) OR
  -- User is a member of a team that has access to the property
  (id IN (
    SELECT tp.property_id
    FROM team_properties tp
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tm.user_id::text = auth.uid()::text AND tm.status = 'active'
  )) OR
  -- User is a super admin or admin
  (EXISTS (
    SELECT 1
    FROM profiles
    WHERE id::text = auth.uid()::text AND (is_super_admin OR role = ANY(ARRAY['super_admin'::user_role, 'admin'::user_role]))
  ))
);
```

## Invitation Flow

The invitation flow has been completely redesigned to be more robust and reliable:

1. **Separation of Concerns**: The user registration/login process is now separated from the invitation acceptance process, making each step more focused and robust.

2. **Improved Error Handling**: Better error handling has been added throughout the process, making it more resilient to edge cases.

3. **Fallback Mechanism**: A fallback mechanism has been implemented that tries the new approach first and falls back to the old approach if needed, ensuring backward compatibility.

4. **Transaction-Based Processing**: The invitation acceptance process is now handled in a single database transaction, reducing the chance of database errors.

This new approach should fix the "Database error saving new user" error and make the invitation flow more robust and user-friendly.
