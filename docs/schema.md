# StayFu Database Schema

This document provides a comprehensive overview of the StayFu database schema, including tables, relationships, and custom types. This documentation should be maintained and updated as the schema evolves to ensure that we don't break existing functionality or rebuild components that already exist.

## Tables

### Core Tables

#### profiles
Stores user profile information.
- `id` (uuid, PK): User ID, references auth.users
- `email` (text): User's email address
- `first_name` (text): User's first name
- `last_name` (text): User's last name
- `role` (user_role): User's role in the system (super_admin, admin, property_manager, staff, service_provider)
- `is_super_admin` (boolean): Flag indicating if the user is a super admin
- `created_at` (timestamp): When the profile was created
- `updated_at` (timestamp): When the profile was last updated

**RLS Policies**:
- Users can view their own profiles
- Users can update their own profiles
- Authenticated users can read basic profile info of other users
- Service role can manage all profiles
- Service role can create profiles
- Service role can update profiles

#### teams
Represents property management teams.
- `id` (uuid, PK): Team ID
- `name` (text): Team name
- `owner_id` (uuid): User ID of the team owner
- `created_at` (timestamp): When the team was created
- `updated_at` (timestamp): When the team was last updated

#### team_members
Represents membership of users in teams.
- `id` (uuid, PK): Team member ID
- `team_id` (uuid): Team ID
- `user_id` (uuid): User ID
- `status` (text): Membership status (active, inactive, pending)
- `added_by` (uuid): User ID of who added this member
- `created_at` (timestamp): When the team member was added
- `updated_at` (timestamp): When the team member was last updated

#### properties
Stores property information.
- `id` (uuid, PK): Property ID
- `name` (text): Property name
- `address` (text): Property address
- `city` (text): Property city
- `state` (text): Property state
- `zip` (text): Property ZIP code
- `user_id` (uuid): User ID of the property owner
- `created_at` (timestamp): When the property was created
- `updated_at` (timestamp): When the property was last updated

#### team_properties
Links teams to properties they manage.
- `id` (uuid, PK): Team property ID
- `team_id` (uuid): Team ID
- `property_id` (uuid): Property ID
- `created_at` (timestamp): When the property was added to the team
- `updated_at` (timestamp): When the team property was last updated

### Permissions and Invitations

#### user_permissions
Stores user permissions for specific teams.
- `id` (uuid, PK): Permission ID
- `user_id` (uuid): User ID
- `team_id` (uuid): Team ID
- `permission` (permission_type): Permission type
- `enabled` (boolean): Whether the permission is enabled
- `created_at` (timestamp): When the permission was created
- `updated_at` (timestamp): When the permission was last updated

#### team_invitations
Stores invitations to join teams.
- `id` (uuid, PK): Invitation ID
- `team_id` (uuid): Team ID
- `email` (text): Email address of the invitee
- `role` (text): Role to assign to the invitee
- `status` (text): Invitation status (pending, accepted, declined)
- `token` (uuid): Unique token for the invitation
- `invited_by` (uuid): User ID of who sent the invitation
- `expires_at` (timestamp): When the invitation expires
- `created_at` (timestamp): When the invitation was created
- `updated_at` (timestamp): When the invitation was last updated
- `team_name` (text): Name of the team (denormalized for convenience)

**Invitation Flow**:
The invitation flow has been enhanced with better error handling and user experience improvements:
1. When a user clicks an invitation link, they are directed to the Auth page with the token
2. The Auth page attempts to use the `accept-invitation-direct` edge function to handle the invitation and user registration in one step
3. If the edge function fails, it falls back to using the `acceptInvitationWithRegistration` client-side function
4. The client-side function has a robust fallback mechanism that tries multiple approaches:
   - First, it tries to use the edge function
   - If that fails, it tries to register the user directly and then accept the invitation
   - If the user already exists, it tries to sign in and then accept the invitation
5. For service providers, the `add_service_provider_default_permissions` function is called to set up appropriate permissions

### Inventory Management

#### inventory_items
Stores inventory items for properties.
- `id` (uuid, PK): Item ID
- `property_id` (uuid): Property ID
- `team_id` (uuid): Team ID
- `name` (text): Item name
- `description` (text): Item description
- `quantity` (integer): Item quantity
- `user_id` (uuid): User ID of who added the item
- `created_at` (timestamp): When the item was created
- `updated_at` (timestamp): When the item was last updated

### Damage Reports

#### damage_reports
Stores damage reports for properties.
- `id` (uuid, PK): Report ID
- `property_id` (uuid): Property ID
- `team_id` (uuid): Team ID
- `user_id` (uuid): User ID of who created the report
- `title` (text): Report title
- `description` (text): Report description
- `status` (text): Report status
- `created_at` (timestamp): When the report was created
- `updated_at` (timestamp): When the report was last updated

#### damage_photos
Stores photos for damage reports.
- `id` (uuid, PK): Photo ID
- `damage_report_id` (uuid): Damage report ID
- `user_id` (uuid): User ID of who uploaded the photo
- `url` (text): Photo URL
- `created_at` (timestamp): When the photo was uploaded
- `updated_at` (timestamp): When the photo was last updated

#### damage_notes
Stores notes for damage reports.
- `id` (uuid, PK): Note ID
- `damage_report_id` (uuid): Damage report ID
- `user_id` (uuid): User ID of who added the note
- `content` (text): Note content
- `created_at` (timestamp): When the note was created
- `updated_at` (timestamp): When the note was last updated

#### damage_invoices
Stores invoices for damage repairs.
- `id` (uuid, PK): Invoice ID
- `damage_report_id` (uuid): Damage report ID
- `user_id` (uuid): User ID of who created the invoice
- `amount` (numeric): Invoice amount
- `status` (text): Invoice status
- `created_at` (timestamp): When the invoice was created
- `updated_at` (timestamp): When the invoice was last updated

### Maintenance

#### maintenance_tasks
Stores maintenance tasks for properties.
- `id` (uuid, PK): Task ID
- `property_id` (uuid): Property ID
- `team_id` (uuid): Team ID
- `user_id` (uuid): User ID of who created the task
- `assigned_to` (text): User ID or email of who is assigned to the task
- `provider_id` (uuid): Service provider ID
- `title` (text): Task title
- `description` (text): Task description
- `status` (text): Task status
- `priority` (text): Task priority
- `due_date` (timestamp): When the task is due
- `created_at` (timestamp): When the task was created
- `updated_at` (timestamp): When the task was last updated

#### maintenance_providers
Stores service providers for maintenance tasks.
- `id` (uuid, PK): Provider ID
- `user_id` (uuid): User ID of the provider
- `name` (text): Provider name
- `email` (text): Provider email
- `phone` (text): Provider phone
- `specialty` (text): Type of service provided
- `notes` (text): Additional notes about the provider
- `created_at` (timestamp): When the provider was created
- `updated_at` (timestamp): When the provider was last updated

Note: The maintenance_providers table does not have a team_id column. Team access is handled through the get_providers() RPC function, which joins with team_members to determine access.

### Purchase Orders

#### purchase_orders
Stores purchase orders for properties.
- `id` (uuid, PK): Order ID
- `property_id` (uuid): Property ID
- `team_id` (uuid): Team ID
- `user_id` (uuid): User ID of who created the order
- `vendor` (text): Vendor name
- `status` (po_status): Order status (pending, ordered, delivered, archived)
- `total` (numeric): Order total
- `created_at` (timestamp): When the order was created
- `updated_at` (timestamp): When the order was last updated

#### purchase_order_items
Stores items in purchase orders.
- `id` (uuid, PK): Item ID
- `purchase_order_id` (uuid): Purchase order ID
- `name` (text): Item name
- `description` (text): Item description
- `quantity` (integer): Item quantity
- `price` (numeric): Item price
- `created_at` (timestamp): When the item was created
- `updated_at` (timestamp): When the item was last updated

### Property Documents and Files

#### property_documents
Stores rich text documents for properties.
- `id` (uuid, PK): Document ID
- `property_id` (uuid): Property ID
- `user_id` (uuid): User ID who created the document
- `title` (text): Document title
- `content` (text): Rich text content
- `is_private` (boolean): Whether the document is private to the creator
- `created_at` (timestamp): When the document was created
- `updated_at` (timestamp): When the document was last updated

#### property_files
Stores file attachments for properties.
- `id` (uuid, PK): File ID
- `property_id` (uuid): Property ID
- `user_id` (uuid): User ID who uploaded the file
- `filename` (text): Original filename
- `display_name` (text): User-friendly display name for the file
- `caption` (text): Optional description of the file
- `file_path` (text): Path in storage
- `file_type` (text): MIME type
- `file_size` (integer): Size in bytes
- `is_private` (boolean): Whether the file is private to the uploader
- `created_at` (timestamp): When the file was uploaded
- `updated_at` (timestamp): When the file was last updated

### User Settings and Preferences

#### user_settings
Stores user settings.
- `id` (uuid, PK): Setting ID
- `user_id` (uuid): User ID
- `setting_key` (text): Setting key
- `setting_value` (text): Setting value
- `created_at` (timestamp): When the setting was created
- `updated_at` (timestamp): When the setting was last updated

#### user_preferences
Stores user preferences.
- `id` (uuid, PK): Preference ID
- `user_id` (uuid): User ID
- `preference_key` (text): Preference key
- `preference_value` (text): Preference value
- `created_at` (timestamp): When the preference was created
- `updated_at` (timestamp): When the preference was last updated

#### extension_api_tokens
Stores API tokens for browser extensions.
- `id` (uuid, PK): Token ID
- `user_id` (uuid): User ID
- `token` (text): API token
- `name` (text): Token name
- `last_used` (timestamp): When the token was last used
- `created_at` (timestamp): When the token was created
- `updated_at` (timestamp): When the token was last updated

## Custom Types

### user_role
Defines the roles users can have in the system.
- `super_admin`: Super administrator with full system access
- `admin`: Administrator with extensive access
- `property_manager`: Manager of properties
- `staff`: Staff member with limited access
- `service_provider`: External service provider

### permission_type
Defines the types of permissions users can have.
- `manage_properties`: Permission to manage properties
- `manage_inventory`: Permission to manage inventory
- `view_inventory`: Permission to view inventory
- `manage_staff`: Permission to manage staff
- `manage_service_providers`: Permission to manage service providers
- `manage_purchase_orders`: Permission to manage purchase orders
- `view_purchase_orders`: Permission to view purchase orders
- `manage_damage_reports`: Permission to manage damage reports
- `view_damage_reports`: Permission to view damage reports
- `manage_maintenance`: Permission to manage maintenance tasks
- `view_maintenance`: Permission to view maintenance tasks
- `manage_team`: Permission to manage team settings
- `view_team`: Permission to view team information
- `admin`: Administrative permission
- `submit_damage_reports`: Permission to submit damage reports
- `create_maintenance_task`: Permission to create maintenance tasks

### po_status
Defines the statuses a purchase order can have.
- `pending`: Order is pending
- `ordered`: Order has been placed
- `delivered`: Order has been delivered
- `archived`: Order has been archived

## Relationships

1. **profiles** to **teams**:
   - One-to-many: A user can own multiple teams (owner_id)

2. **profiles** to **team_members**:
   - One-to-many: A user can be a member of multiple teams (user_id)

3. **teams** to **team_members**:
   - One-to-many: A team can have multiple members (team_id)

4. **profiles** to **properties**:
   - One-to-many: A user can own multiple properties (user_id)

5. **teams** to **team_properties**:
   - One-to-many: A team can manage multiple properties (team_id)

6. **properties** to **team_properties**:
   - One-to-many: A property can be managed by multiple teams (property_id)

7. **profiles** to **user_permissions**:
   - One-to-many: A user can have multiple permissions (user_id)

8. **teams** to **user_permissions**:
   - One-to-many: A team can have multiple user permissions (team_id)

9. **teams** to **team_invitations**:
   - One-to-many: A team can have multiple invitations (team_id)

10. **profiles** to **team_invitations**:
    - One-to-many: A user can send multiple invitations (invited_by)

11. **properties** to **inventory_items**:
    - One-to-many: A property can have multiple inventory items (property_id)

12. **teams** to **inventory_items**:
    - One-to-many: A team can have multiple inventory items (team_id)

13. **properties** to **damage_reports**:
    - One-to-many: A property can have multiple damage reports (property_id)

14. **teams** to **damage_reports**:
    - One-to-many: A team can have multiple damage reports (team_id)

15. **damage_reports** to **damage_photos**:
    - One-to-many: A damage report can have multiple photos (damage_report_id)

16. **damage_reports** to **damage_notes**:
    - One-to-many: A damage report can have multiple notes (damage_report_id)

17. **damage_reports** to **damage_invoices**:
    - One-to-many: A damage report can have multiple invoices (damage_report_id)

18. **properties** to **maintenance_tasks**:
    - One-to-many: A property can have multiple maintenance tasks (property_id)

19. **teams** to **maintenance_tasks**:
    - One-to-many: A team can have multiple maintenance tasks (team_id)

20. **profiles** to **maintenance_providers**:
    - One-to-one: A service provider user can have one provider record (user_id)

21. **properties** to **purchase_orders**:
    - One-to-many: A property can have multiple purchase orders (property_id)

22. **teams** to **purchase_orders**:
    - One-to-many: A team can have multiple purchase orders (team_id)

23. **purchase_orders** to **purchase_order_items**:
    - One-to-many: A purchase order can have multiple items (purchase_order_id)

24. **profiles** to **user_settings**:
    - One-to-many: A user can have multiple settings (user_id)

25. **profiles** to **user_preferences**:
    - One-to-many: A user can have multiple preferences (user_id)

26. **profiles** to **extension_api_tokens**:
    - One-to-many: A user can have multiple API tokens (user_id)

27. **properties** to **property_documents**:
    - One-to-many: A property can have multiple documents (property_id)

28. **profiles** to **property_documents**:
    - One-to-many: A user can create multiple documents (user_id)

29. **properties** to **property_files**:
    - One-to-many: A property can have multiple files (property_id)

30. **profiles** to **property_files**:
    - One-to-many: A user can upload multiple files (user_id)

## Important Notes

1. Most tables include `created_at` and `updated_at` timestamps for tracking when records are created and modified.
2. Many tables include a `user_id` field to track which user created or owns the record.
3. Team-based tables often include both `team_id` and `property_id` to support the team-property management structure.
4. The RLS policies are set up to ensure users can only access data they are authorized to see based on their role and team memberships.
