# StayFu Debugging Utilities

This document describes the debugging utilities available in the StayFu application to help diagnose issues with data loading, session management, and visibility state changes.

## Background

The StayFu application has experienced issues with data not loading when:
- The browser tab is inactive for a period of time
- The application is left idle for a while
- The user navigates away and then returns to the application

To help diagnose and fix these issues, we've added extensive debugging utilities that track and log various events and state changes.

## Available Debug Utilities

The following debug utilities are available in the browser console:

### Session Management

- `checkSessionStatus()` - Checks the current session status and logs details to the console
- `getSessionDebugInfo()` - Returns detailed information about the current session, including expiration time, visibility state, and debug data
- `clearAllDebugData()` - Clears all debug data from localStorage
- `downloadAllDebugData()` - Downloads all debug data as a JSON file

### Data Refresh Management

- `refreshAllData()` - Manually triggers a refresh of all application data
- `getDataRefreshDebugInfo()` - Returns detailed information about data refresh status, including last activity time, visibility state, and route data
- `clearDataRefreshDebugInfo()` - Clears data refresh debug information from localStorage

## Debug Data Stored in localStorage

The application stores various debug data in localStorage to help diagnose issues:

- `stayfu_session_checks` - Records of session validity checks
- `stayfu_last_token_refresh` - Information about the last token refresh
- `stayfu_session_expired` - Information about session expiration events
- `stayfu_last_session_refresh` - Information about the last session refresh
- `stayfu_token_refreshed` - Information about token refresh events
- `stayfu_data_refreshes` - Records of data refresh events
- `stayfu_data_visibility_changes` - Records of visibility state changes
- `stayfu_route_changes` - Records of route changes
- `stayfu_user_activities` - Records of user activity events
- `stayfu_auth_events` - Records of authentication state changes
- `stayfu_dashboard_visibility_changes` - Records of visibility state changes in the dashboard
- `stayfu_dashboard_refreshes` - Records of dashboard data refresh events

## How to Use the Debug Utilities

1. Open the browser console (F12 or Ctrl+Shift+I)
2. Call any of the debug utilities listed above
3. Examine the returned data or console logs
4. Use `downloadAllDebugData()` to save the debug data for further analysis

## Diagnosing Common Issues

### Data Not Loading After Tab Inactivity

1. Open the application in a browser tab
2. Navigate to a page with data
3. Switch to another tab for a few minutes
4. Return to the StayFu tab
5. If data doesn't load, check the console for errors
6. Call `getDataRefreshDebugInfo()` to see if visibility changes were detected
7. Call `getSessionDebugInfo()` to check if the session is still valid

### Session Expiration Issues

1. Call `checkSessionStatus()` to see the current session status
2. Call `getSessionDebugInfo()` to get detailed information about the session
3. Check the `stayfu_session_checks` data for any failed session checks
4. Check the `stayfu_session_expired` data for any session expiration events

## Implementing Fixes

Based on the debug data, the following fixes have been implemented:

1. Enhanced visibility change detection to trigger data refreshes when the tab becomes active
2. Improved session management to prevent unnecessary session refreshes
3. Added automatic data refresh when the tab becomes visible after being inactive
   - Dashboard data refreshes after 30 seconds of inactivity
   - Global data refreshes after 5 minutes of inactivity
4. Updated React Query configuration to better handle background tabs:
   - Enabled `refetchOnWindowFocus` to refresh data when the window regains focus after idle
   - Enabled `refetchOnReconnect` to refresh data when the network reconnects
   - Increased retry attempts and added exponential backoff
   - Set staleTime to 0 to always consider data stale and ready for refresh
5. Enhanced Supabase client configuration:
   - Increased token refresh time before expiry
   - Added automatic data refresh when token is refreshed
   - Added more robust error handling and recovery mechanisms
6. Added comprehensive logging and debugging utilities to track visibility changes, data refreshes, and session state

## Future Improvements

- Add a visual indicator when data is being refreshed
- Implement a more sophisticated data staleness detection mechanism
- Add user-configurable refresh intervals
- Implement a more robust offline mode
