# Task Automation

This document describes the task automation feature, which allows property managers to automatically create maintenance tasks based on booking events.

## Overview

The task automation system allows users to define rules that automatically generate maintenance tasks when bookings are created or updated. For example, a user can create a rule to automatically schedule a cleaning task 2 hours after a guest checks out.

## Components

The task automation system consists of the following components:

1. **Automation Rules**: User-defined rules that specify when and what kind of tasks should be created.
2. **Automation Queue**: A database table that stores bookings that need to be processed.
3. **Queue Processor**: A function that processes the queue and creates tasks based on the rules.
4. **Database Trigger**: A trigger that adds bookings to the queue when they are created or updated.
5. **Scheduled Function**: A function that runs periodically to process the queue.

## Database Schema

### Automation Rules Table

```sql
CREATE TABLE automation_rules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  name TEXT NOT NULL,
  trigger_type TEXT NOT NULL,
  task_type TEXT NOT NULL,
  time_offset INTEGER NOT NULL,
  property_ids JSONB,
  title TEXT NOT NULL,
  description TEXT,
  severity TEXT NOT NULL,
  assigned_to UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Automation Queue Table

```sql
CREATE TABLE automation_queue (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  booking_id UUID NOT NULL REFERENCES bookings(id),
  processed BOOLEAN DEFAULT FALSE,
  manual_trigger BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE
);
```

## How It Works

1. When a booking is created or updated, a database trigger adds it to the automation queue.
2. The queue processor runs periodically (via a scheduled function) to process the queue.
3. For each booking in the queue, the processor:
   - Retrieves all automation rules for the booking's user
   - For each rule, checks if it applies to the booking's property
   - If it applies, calculates the task date based on the rule's trigger type and time offset
   - Creates a maintenance task with the specified properties

## User Interface

The task automation UI allows users to:

1. Create, edit, and delete automation rules
2. Manually trigger the automation process for all bookings
3. View the status of automation rules

## API Endpoints

The following API endpoints are available for task automation:

- `POST /api/automation/process-queue`: Triggers processing of the automation queue
- `POST /api/automation/queue-all-bookings`: Queues all upcoming bookings for processing

## Scheduled Processing

The automation queue is processed automatically by a scheduled function that runs every hour. This ensures that tasks are created even if the user is not actively using the application.

## Future Improvements

Potential future improvements to the task automation system include:

1. More trigger types (e.g., before check-in, on booking creation)
2. More task types (e.g., inventory check, guest communication)
3. More complex rules (e.g., conditional rules based on booking properties)
4. Task templates with predefined checklists
5. Email notifications when tasks are created
