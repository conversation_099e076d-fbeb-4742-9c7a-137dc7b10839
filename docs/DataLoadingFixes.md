# Data Loading Fixes

This document outlines the fixes implemented to address data loading issues in the StayFu application, particularly focusing on making all dashboard views load data reliably when switching between views or when the application loses focus.

## Problem

Only the Dashboard, Properties, and Damage Reports views were loading data reliably when:
1. Switching between different views
2. The application loses focus and then regains it

Other views (Maintenance, Teams, Inventory, Task Automation) were experiencing issues where data would disappear or fail to load properly.

## Teams Page Specific Issues

The Teams page had several specific issues:
1. "Failed to load team members" error when switching between teams
2. Team owner status incorrectly showing as false for accounts that own teams
3. Inconsistent data loading when switching between tabs
4. Issues with the `useNavigationRefresh` hook not properly handling hash-based routes
5. React Query not properly invalidating and refetching team members data
6. Missing fallback mechanisms for direct database queries when React Query fails
7. Circular dependencies in the team members loading logic
8. Team members data flashing on and off on the screen
9. Multiple useEffect hooks causing duplicate data fetches
10. Missing `keepPreviousData` option in React Query configuration
11. Permissions management tab flashing on and off when selecting users
12. Loading states causing UI flashing during permission operations
13. Inefficient permission data loading causing unnecessary re-renders
14. `useNavigationRefresh` hook reloading over and over causing infinite loops
15. Missing debouncing and throttling in data refresh mechanisms
16. Aggressive query invalidation and refetching causing performance issues
17. Permissions tab on teams dashboard loading over and over and flashing
18. Inefficient caching in permission management causing duplicate fetches
19. Team ownership checks triggering unnecessary database queries
20. Team members being fetched repeatedly causing infinite loops
21. `useNavigationRefresh` hook repeatedly refreshing the `/teams` route
22. Aggressive query invalidation in team members loading
23. Missing `setTeamMembers` function in PermissionManagement component causing errors
24. TeamDashboard component continuously mounting and checking team ownership

## Task Automation Page Specific Issues

The Task Automation page had several specific issues:
1. Missing from the navigation refresh map
2. No feedback when processing automation rules
3. Inconsistent data loading
4. Path mismatch between the route map (`/task-automation`) and the actual path in the navigation (`/maintenance/automation`)

## Root Causes Identified

1. **Inconsistent React Query Configuration**: Some views had different settings for `refetchOnWindowFocus` and other React Query options.

2. **Custom Visibility Change Handlers**: Some components were using custom visibility change handlers that conflicted with React Query's built-in functionality.

3. **Custom Event System**: The TeamDashboard was using a custom event system (`stayfuTabChange`) to trigger data loading, which was unreliable and conflicted with React Query.

4. **Data Clearing During Refetching**: Some hooks were clearing data during refetching, causing data to disappear temporarily.

5. **Multiple QueryClient Instances**: Different parts of the application were creating their own QueryClient instances with different configurations.

## Implemented Fixes

### 1. Standardized React Query Configuration

Ensured all data fetching hooks use consistent React Query settings:
- `refetchOnWindowFocus: true`
- `refetchOnMount: true`
- `retry: 3`
- `retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000)`
- `staleTime: 1000 * 60 * 5` (5 minutes)
- `keepPreviousData: true`

### 2. Removed Custom Visibility Change Handlers

- Removed custom visibility change handlers from `useMaintenanceTasksRPC.tsx`
- Removed references to `triggerVisibilityAction` in `Dashboard.tsx`
- Removed visibility change handlers from `useDashboardData.tsx`
- Removed visibility change handlers from `IdleTimeoutManager.tsx`
- Removed visibility state tracking from `SessionRefreshManager.tsx`
- Replaced custom visibility handling with React Query's built-in functionality

### 3. Removed Custom Event System

- Removed the custom `stayfuTabChange` event system from `TeamDashboard.tsx`
- Removed event listeners from `TeamPropertiesManager.tsx`, `TeamMemberManagement.tsx`, and `SentInvitationsDisplay.tsx`
- Replaced with React Query's built-in data fetching and caching

### 4. Ensured Data Preservation

- Made sure all hooks preserve previous data during loading states
- Used `keepPreviousData: true` in React Query configuration
- Implemented placeholder data for loading states

### 5. Standardized QueryClient Configuration

- Updated all QueryClient instances to use the same configuration
- Fixed `AppProviders.tsx` to use `refetchOnWindowFocus: true`
- Updated `QueryProvider.tsx` to include `keepPreviousData: true`
- Standardized `cacheUtils.ts` QueryClient configuration

### 6. Fixed Teams Page Specific Issues

- Fixed the "Failed to load team members" error by ensuring direct data fetching before using React Query
- Fixed the team owner status detection with better error handling and debugging
- Added fallback mechanisms to fetch team data directly from the database when needed
- Improved error handling and user feedback
- Fixed the `useNavigationRefresh` hook to properly handle hash-based routes
- Added direct database query to check team ownership status
- Added multiple fallback timers to ensure team members are loaded
- Implemented direct database queries as a last resort when React Query fails
- Fixed circular dependencies in the team members loading logic
- Added proper error retry effects to automatically retry failed queries
- Improved the query invalidation and refetching logic to ensure fresh data
- Added detailed logging to help diagnose issues
- Fixed team members data flashing by consolidating useEffect hooks and using a single data loading function
- Added `keepPreviousData: true` to React Query configuration to prevent data flashing
- Improved hash-based route handling in `useNavigationRefresh` and `GlobalDataRefreshContext`
- Added special case handling for teams with IDs in route normalization
- Prevented duplicate data fetches when changing teams or tabs
- Fixed permissions management tab flashing by optimizing loading states
- Improved permission operations to update UI immediately before database operations
- Added optimistic updates for permission operations to prevent UI flashing
- Implemented better error handling with state rollback for failed permission operations
- Added relative positioning to permission cards container for proper loading overlay
- Improved toast notifications for better user feedback
- Fixed `useNavigationRefresh` hook reloading over and over by adding debouncing and throttling
- Added cooldown period between refreshes to prevent infinite loops
- Implemented a refresh cache to track recent refreshes and prevent duplicates
- Added isRefreshing flag to prevent concurrent refreshes
- Made query invalidation less aggressive by using `refetchType: 'active'` instead of `'all'`
- Added timestamp to events to help track and prevent duplicate event handling
- Fixed permissions tab on teams dashboard loading over and over by implementing proper caching
- Added permissionsLoadedRef to track which permissions have already been loaded
- Implemented a cache for permissions data with TTL to prevent duplicate fetches
- Optimized team ownership checks to use cached results and prevent unnecessary database queries
- Added stable rendering approach for permissions display to prevent flashing
- Used refs to track loading state to prevent flashing when loading states change rapidly
- Added min-height to permissions container to prevent layout shifts
- Implemented proper cache updates for all permission operations (add, remove, update)
- Blacklisted the `/teams` route from automatic refreshes but kept permissions loading working
- Added team members caching to prevent duplicate fetches
- Removed aggressive query invalidation and refetching in team members loading
- Implemented a team-specific initial load tracking system
- Added isLoadingTeamMembersRef to prevent concurrent team member loads
- Removed fallback timers that were causing duplicate loads
- Added REFRESH_BLACKLIST to prevent problematic routes from being refreshed
- Reduced permissions cache TTL to ensure permissions load properly
- Improved permissions loading for initial user selection
- Removed teamPermissionsV2 from automatic refresh to prevent infinite loops
- Added force loading of permissions for initial user selection

### 7. Fixed Task Automation Page Specific Issues

- Added the Task Automation route to the navigation refresh map
- Improved the `processAllBookings` function to provide better user feedback
- Added proper error handling and toast notifications
- Ensured consistent data loading with React Query's built-in functionality
- Fixed path mismatch between the route map and the actual path in the navigation
- Added special case handling for `/maintenance/automation` path in the `useNavigationRefresh` hook

## Files Modified

1. `src/providers/AppProviders.tsx`
   - Changed `refetchOnWindowFocus` from `false` to `true`

2. `src/providers/QueryProvider.tsx`
   - Added `keepPreviousData: true`

3. `src/utils/cacheUtils.ts`
   - Standardized QueryClient configuration
   - Changed `refetchOnMount` from `'always'` to `true`
   - Updated `staleTime` and `gcTime` to be consistent

4. `src/hooks/useMaintenanceTasksQueryV2.ts`
   - Changed `refetchOnWindowFocus` from `false` to `true`

5. `src/hooks/useMaintenanceTasksRPC.tsx`
   - Removed custom visibility change handler
   - Removed visibility state tracking

6. `src/hooks/useDashboardData.tsx`
   - Removed visibility change handler
   - Removed visibility state tracking
   - Removed references to `document.visibilityState`

7. `src/pages/Dashboard.tsx`
   - Removed reference to `triggerVisibilityAction`
   - Used direct `refreshData()` call instead

8. `src/pages/TeamDashboard.tsx`
   - Removed custom `stayfuTabChange` event dispatching
   - Simplified data loading logic
   - Added direct data fetching on component mount
   - Added the `useNavigationRefresh` hook
   - Fixed team owner status detection
   - Improved team selection logic to prevent duplicate fetches
   - Added check to only take action when changing to a different team
   - Added custom event dispatch for team changes
   - Improved tab change handler to prevent duplicate fetches
   - Added check to only take action when changing to a different tab
   - Added custom event dispatch for tab changes
   - Fixed route refreshing on tab change

9. `src/components/teams/TeamPropertiesManager.tsx`
   - Removed custom event listener for tab changes

10. `src/components/teams/TeamMemberManagement.tsx`
    - Removed custom event listener for tab changes
    - Fixed the `useQueryClient` usage
    - Improved error handling
    - Fixed team owner status detection
    - Added direct data fetching before using React Query
    - Consolidated multiple useEffect hooks into a single one to prevent duplicate fetches
    - Implemented a single data loading function with proper state tracking
    - Fixed circular dependencies in the team members loading logic
    - Added detailed logging to help diagnose issues
    - Improved toast notifications for better user feedback
    - Added isMounted flag to prevent state updates after component unmount
    - Added isLoading flag to prevent duplicate data fetches
    - Fixed team members data flashing issue

11. `src/components/teams/PermissionManagement.tsx`
    - Removed custom event listener for tab changes
    - Fixed the `useQueryClient` usage
    - Improved error handling
    - Fixed team owner status detection with fallback to direct database query
    - Added direct data fetching before using React Query
    - Consolidated multiple useEffect hooks into a single one to prevent duplicate fetches
    - Implemented a single data loading function with proper state tracking
    - Fixed circular dependencies in the team members loading logic
    - Added detailed logging to help diagnose issues
    - Added proper error retry effects
    - Added isMounted flag to prevent state updates after component unmount
    - Added isLoading flag to prevent duplicate data fetches
    - Fixed team members data flashing issue
    - Fixed permissions management tab flashing by optimizing loading states
    - Added permissionsLoadingRef to track permission loading state
    - Improved permission loading logic to prevent unnecessary fetches
    - Added relative positioning to permission cards container for proper loading overlay
    - Improved loading overlay with better styling and z-index
    - Only show loading spinner when no permissions data is available
    - Added team members caching to prevent duplicate fetches
    - Implemented a team-specific initial load tracking system
    - Added isLoadingTeamMembersRef to prevent concurrent team member loads
    - Removed fallback timers that were causing duplicate loads
    - Added teamMembersCacheRef to cache team members data
    - Added teamOwnershipRef to cache team ownership status
    - Added stable rendering approach for permissions display
    - Added min-height to permissions container to prevent layout shifts
    - Used refs to track loading state to prevent flashing

12. `src/components/teams/SentInvitationsDisplay.tsx`
    - Removed custom event listener for tab changes

13. `src/components/auth/IdleTimeoutManager.tsx`
    - Removed visibility change handler

14. `src/components/auth/SessionRefreshManager.tsx`
    - Removed visibility state tracking
    - Removed references to `document.visibilityState`

15. `src/integrations/supabase/client.ts`
    - Removed references to `document.visibilityState`

16. `src/utils/sessionUtils.ts`
    - Removed references to `document.visibilityState`

17. `src/hooks/useNavigationRefresh.ts`
    - Added Task Automation route to the query key map
    - Updated Teams route query keys to use V2 versions
    - Fixed hash-based route handling
    - Added special case handling for `/maintenance/automation` path
    - Improved route normalization and path extraction
    - Added dispatch of both `stayfu-navigation-refresh` and `stayfu-data-refreshed` events
    - Added special case handling for teams with IDs in route normalization
    - Added special case handling for properties with IDs in route normalization
    - Improved handling of hash router format (e.g., '/#/properties' or '#/properties')
    - Added debouncing and throttling to prevent infinite loops
    - Implemented a refresh cache to track recent refreshes
    - Added isRefreshingRef to prevent concurrent refreshes
    - Made query invalidation less aggressive with `refetchType: 'active'`
    - Added timestamp to events to help track and prevent duplicate event handling
    - Added cooldown period between refreshes for the same route
    - Added shouldDispatchEvent check to prevent event loops
    - Added REFRESH_BLACKLIST to completely prevent refreshing problematic routes
    - Completely blacklisted the `/teams` route from automatic refreshes
    - Fixed type issue with baseRoute variable

18. `src/contexts/GlobalDataRefreshContext.tsx`
    - Added Task Automation route to the query key map
    - Updated Teams route query keys to use V2 versions
    - Fixed hash-based route handling
    - Added special case handling for `/maintenance/automation` path
    - Improved route normalization and path extraction
    - Added dispatch of `stayfu-data-refreshed` event
    - Added special case handling for teams with IDs in route normalization
    - Added special case handling for properties with IDs in route normalization
    - Improved handling of hash router format (e.g., '/#/properties' or '#/properties')
    - Added refresh cache to prevent duplicate refreshes
    - Added cooldown period between refreshes for the same route
    - Made query invalidation less aggressive with `refetchType: 'active'`
    - Added timestamp to events to help track and prevent duplicate event handling
    - Fixed type issue with baseRoute variable
    - Added REFRESH_BLACKLIST to completely prevent refreshing problematic routes
    - Completely blacklisted the `/teams` route from automatic refreshes

19. `src/pages/TaskAutomation.tsx`
    - Added the `useNavigationRefresh` hook
    - Added data refreshing on component mount
    - Fixed path to use `/maintenance/automation` instead of `/task-automation`
    - Added dispatch of `stayfu-page-loaded` event

20. `src/components/automation/AutomationRules.tsx`
    - Improved the `processAllBookings` function
    - Added proper error handling and toast notifications
    - Updated toast implementation to use the correct format

21. `src/hooks/useTeamManagementQueryV2.ts`
    - Added RPC function fallback for better performance and reliability
    - Improved error handling and debugging
    - Added proper error retry effects to automatically retry failed queries
    - Fixed the `retryFetchTeamMembers` and `retryFetchInvitations` functions
    - Improved the query invalidation and refetching logic
    - Added detailed logging to help diagnose issues
    - Added `keepPreviousData: true` to all queries to prevent data flashing
    - Added `refetchInterval: false` to disable automatic refetching
    - Added `refetchOnReconnect: true` to refetch when reconnecting
    - Increased `cacheTime` to 10 minutes for better performance
    - Used more reliable query with explicit joins for team members
    - Improved error handling in the query functions

22. `src/hooks/usePermissionManagement.ts`
    - Optimized loading states to prevent UI flashing
    - Added optimistic updates for permission operations
    - Implemented better error handling with state rollback for failed operations
    - Added JSON comparison to prevent unnecessary state updates
    - Improved toast notifications with better formatting
    - Added temporary permission IDs for optimistic UI updates
    - Only set loading to true when no permissions data is available
    - Added detailed logging for permission operations
    - Improved error recovery with backup data restoration
    - Implemented a cache for permissions data with TTL to prevent duplicate fetches
    - Added permissionsCacheRef to cache permissions data
    - Updated all permission operations to update the cache
    - Added proper cache restoration for failed operations
    - Removed permissions.length dependency from useEffect to prevent infinite loops
    - Added cache validation to prevent unnecessary database queries

## Testing

After implementing these changes, all dashboard views should now load data reliably when:
1. Switching between different views
2. The application loses focus and then regains it

## Best Practices for Future Development

1. **Use React Query's Built-in Functionality**: Always rely on React Query's built-in functionality for data fetching, caching, and refreshing.

2. **Avoid Custom Event Systems**: Don't create custom event systems for data loading or refreshing.

3. **Preserve Data During Loading**: Always preserve previous data during loading states to prevent data from disappearing.

4. **Consistent Configuration**: Use consistent React Query configuration across all components.

5. **Simplify Data Loading Logic**: Keep data loading logic simple and consistent across all components.

6. **Avoid Multiple QueryClient Instances**: Use a single QueryClient instance with consistent configuration throughout the application.

7. **Avoid Custom Visibility Change Handlers**: Let React Query handle visibility changes with its built-in `refetchOnWindowFocus` option.
