# StayFu Chrome Extension Documentation

## Overview

The StayFu Chrome Extension allows users to import products from Amazon directly into their StayFu inventory. This document provides information about the extension's configuration, troubleshooting, and usage.

## Configuration

Extension id=bklaonfklmbjhnmijonmbegdkfamcnfi

### Routing Configuration

The StayFu application uses **HashRouter** for all navigation. The extension is configured to work with hash-based URLs:

- When the extension redirects to the application, it uses hash-based URLs (e.g., `/#/inventory`)
- When the extension opens the application in a new tab, it uses hash-based URLs
- All deep links from the extension to specific pages in the application use hash-based URLs

### Domain Configuration

The extension is configured to work with the following domains:

- `https://stayfu.com` (Production)
- `https://stage.stayfu.com` (Staging)
- `https://*.stayfu.com` (Any subdomain)
- `https://*.stayfuse.com` (Legacy domain pattern)
- `http://localhost:*` (Local development)
- `http://127.0.0.1:*` (Local development)

### API Endpoints

The extension communicates with the StayFu application through the following API endpoints:

- `/api/extension-status` - Checks if the extension API is available
- `/api/extension/validate-token` - Validates the extension API token
- `/api/extension/properties` - Gets the list of properties the user has access to
- `/api/extension/inventory` - Imports inventory data
- `/api/extension/purchase-order` - Creates a purchase order

## Troubleshooting

### Connection Issues

If the extension fails to connect to the StayFu application:

1. Verify the URL in the extension settings is correct (should be `https://stayfu.com` or `https://stage.stayfu.com`)
2. Check that the API token is valid
3. Ensure the domain is spelled correctly (common mistake: using "stayfuse.com" instead of "stayfu.com")
4. Check browser console for any CORS or network errors

### API Endpoint Issues

The extension now tries multiple API endpoint patterns to handle different server configurations:

1. `/api/extension-status` - Standard endpoint
2. `/api/v1/extension-status` - Versioned API endpoint
3. `/extension-status` - Root API endpoint

If the extension reports "Connected to website, but API endpoints may not be accessible", it means:
- The domain is reachable
- None of the API endpoints returned a valid response
- The server might be configured differently or the API might be behind a proxy

### HTML Response Handling

If the API endpoint returns HTML instead of JSON (common with some server configurations):

1. The extension will detect the HTML response
2. It will try alternative API endpoints
3. If all endpoints return HTML, it will report a successful connection to the website but note that API endpoints may not be accessible

### CORS Issues

The extension implements fallback mechanisms for CORS issues:

1. The extension first pings the domain with a simple HEAD request to check if it's reachable
2. If the domain is reachable but API endpoints have CORS restrictions, the extension will report "Connected to website, but API endpoints may not be accessible"
3. All API requests now use proper timeout handling to prevent hanging requests

### Domain Correction

The extension automatically corrects common domain typos:

- If a user enters "stayfuse.com" instead of "stayfu.com", the extension will automatically correct it

## Development Notes

### Building the Extension

1. Navigate to the `chrome` directory
2. Run `npm install` to install dependencies
3. Run `npm run build` to build the extension
4. Load the unpacked extension in Chrome from the `chrome/dist` directory

### Testing

To test the extension with different environments:

1. Open the extension settings
2. Enter the URL of the environment you want to test (e.g., `https://stage.stayfu.com`)
3. Enter your API token
4. Click "Save Settings"
5. Click "Test Connection" to verify the connection

## Version History

- 2.0.1 - Current version with improved error handling and domain validation
- 2.0.0 - Major rewrite for Manifest V3 compatibility
- 1.0.0 - Initial release
