# StayFu Data Refresh Strategy

This document explains the data refresh strategy in the StayFu application, focusing on how data is refreshed after idle periods or when tabs regain focus.

## Overview

StayFu uses React Query as its primary data fetching and caching library. The application is configured to automatically refresh data when:

1. A tab regains focus after being in the background
2. The network connection is restored after being offline
3. A component mounts or remounts
4. The user manually triggers a refresh

## Important Update (May 2023)

We've fixed issues with data loading by ensuring that:

1. React Query's built-in `refetchOnWindowFocus` functionality is enabled and working correctly
2. Custom visibility change handlers that were interfering with React Query have been removed
3. All hooks consistently use the same React Query configuration

## Key Configuration

The global React Query configuration is set up to ensure data is always fresh:

```tsx
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: true, // Enable refetch on window focus for data refreshing after idle
      refetchOnMount: 'always', // Always refetch when component mounts
      refetchOnReconnect: true, // Refetch when reconnecting
      retry: 3, // Retry attempts
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
      staleTime: 0, // 0 seconds - always consider data stale to ensure fresh data
      gcTime: 10 * 60 * 1000, // 10 minutes - how long to keep data in cache
      networkMode: 'always', // Always try to fetch from network
    },
  },
});
```

## Standardized Hooks

All data fetching hooks in the application follow a consistent pattern:

```tsx
export const useStandardizedHook = () => {
  const { authState } = useAuth();
  const userId = authState.user?.id;
  const queryClient = useQueryClient();
  const [retryCount, setRetryCount] = useState(0);

  // Function to retry data fetching manually
  const retryFetch = useCallback(async () => {
    console.log('[useStandardizedHook] Manual refresh triggered');
    setRetryCount(prev => prev + 1);
    await queryClient.invalidateQueries({ queryKey: ['dataKey'] });
    await queryClient.refetchQueries({ queryKey: ['dataKey'] });
  }, [queryClient]);

  // Fetch data using React Query
  const {
    data = [],
    isLoading,
    error,
    isError
  } = useQuery({
    queryKey: ['dataKey', retryCount], // Include retryCount to force refetch
    queryFn: async () => {
      // Data fetching logic
    },
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000),
    staleTime: 1000 * 60 * 5, // 5 minutes - consistent across all views
    refetchOnWindowFocus: true, // Enable refetch on window focus for data refreshing after idle
    refetchOnMount: true,
    enabled: !!userId
  });

  return {
    data,
    loading: isLoading,
    error: error ? String(error) : null,
    isError,
    fetchData: retryFetch
  };
};
```

## Working vs. Non-Working Views

We've identified that some views (like Inventory and Purchase Orders) consistently load data correctly after idle or loss of focus, while others may have issues. The key difference is:

- **Working Views**: Have `refetchOnWindowFocus: true` in their hooks
- **Non-Working Views**: May have inconsistent settings or override the global configuration

To ensure all views work correctly, we've standardized the configuration across all hooks to use `refetchOnWindowFocus: true`.

## Troubleshooting

If a view is not refreshing data correctly after idle or loss of focus:

1. **Check the hook configuration**: Ensure the hook has `refetchOnWindowFocus: true`
2. **Check for overrides**: Ensure the hook is not overriding the global configuration
3. **Check for custom event handlers**: Ensure there are no custom visibility change handlers that might interfere with React Query's built-in functionality
4. **Check for stale time**: Ensure the staleTime is set to a reasonable value (0 or 5 minutes)

## Testing Data Refresh

To test if data refresh is working correctly:

1. Open the application in a browser tab
2. Navigate to the view you want to test
3. Switch to another tab or minimize the browser for a few minutes
4. Return to the StayFu tab
5. Check if the data is refreshed automatically

You should see log messages in the console indicating that data is being refreshed when the tab regains focus.

## Common Issues and Solutions

### Data not refreshing after idle

**Issue**: Data is not refreshed when returning to a tab after it has been inactive.

**Solution**:
- Ensure the hook has `refetchOnWindowFocus: true`
- Ensure there are no custom visibility change handlers that might interfere
- Try manually refreshing the data to see if that works

### Inconsistent behavior across views

**Issue**: Some views refresh data correctly, while others don't.

**Solution**:
- Standardize the hook configuration across all views
- Ensure all hooks follow the same pattern
- Remove any custom visibility change handlers

### Excessive data refreshing

**Issue**: Data is refreshed too frequently, causing performance issues.

**Solution**:
- Increase the staleTime to reduce the frequency of refreshes
- Use a more targeted approach to invalidate queries
- Consider using optimistic updates for mutations

## Recent Fixes

We've identified and fixed several issues that were causing data loading problems:

1. **Removed Custom Visibility Change Handlers**: We found that several components and hooks were using custom visibility change handlers that were interfering with React Query's built-in functionality. We've removed these custom handlers from:
   - useDashboardDataQuery.ts
   - TeamMemberManagement.tsx
   - RequireAuth.tsx
   - SessionRefreshManager.tsx
   - useDamageReports.tsx
   - Other components that were using custom visibility change handlers

2. **Standardized React Query Configuration**: We've ensured that all hooks use the same React Query configuration with `refetchOnWindowFocus: true`.

3. **Simplified Global Data Refresh**: We've simplified the global data refresh mechanism to rely on React Query's built-in functionality rather than custom event handlers.

4. **Added Error Retry Effects**: We've added error retry effects to all data fetching hooks to ensure they reliably retry when errors occur, similar to the approach used in the Purchase Order dashboard (which was working correctly).

5. **Replaced Visibility Change Handlers with Timers**: We've replaced visibility change handlers with periodic timers for session checking and data refreshing, which prevents conflicts with React Query's built-in functionality.

## Best Practices

1. **Use standardized hooks**: All views should use the standardized hooks with consistent configuration
2. **Avoid custom visibility change handlers**: Let React Query handle visibility changes
3. **Use the global data refresh context**: For manual refreshes, use the global data refresh context
4. **Include retryCount in queryKey**: This allows for manual refreshes by incrementing the retryCount
5. **Set appropriate staleTime**: Use a consistent staleTime across all hooks (0 or 5 minutes)
6. **Enable refetchOnWindowFocus**: This is key for data refreshing after idle
