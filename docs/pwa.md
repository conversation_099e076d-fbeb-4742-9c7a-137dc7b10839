# StayFu PWA Configuration

This document outlines the Progressive Web App (PWA) configuration for StayFu.

## Overview

StayFu is configured as a Progressive Web App (PWA) to provide an app-like experience on both desktop and mobile devices. The PWA configuration allows users to install StayFu on their devices and use it offline.

## Key Files

1. **manifest.json** - Located in `/public/manifest.json`
   - Defines the app's name, icons, colors, and behavior when installed
   - Uses relative paths for all assets to prevent cross-origin issues

2. **service-worker.js** - Located in `/public/service-worker.js`
   - Handles caching strategies for different types of requests
   - Provides offline functionality
   - Special handling for manifest.json to prevent 401 errors

3. **serviceWorkerRegistration.ts** - Located in `/src/utils/serviceWorkerRegistration.ts`
   - Registers the service worker
   - Handles service worker updates
   - Provides utility functions for interacting with the service worker

4. **InstallPWAPrompt.tsx** - Located in `/src/components/pwa/InstallPWAPrompt.tsx`
   - Provides UI for prompting users to install the PWA
   - Different UI for mobile and desktop devices

## Caching Strategies

The service worker uses different caching strategies for different types of requests:

1. **App Shell (HTML, CSS, JS)** - Cache first, then network
   - Provides fast loading of the application shell
   - Updates the cache in the background

2. **API Requests** - Network first, then cache
   - Ensures data is fresh
   - Falls back to cached data when offline

3. **Assets (images, fonts)** - Cache first, then network
   - Provides fast loading of assets
   - Updates the cache in the background

4. **manifest.json** - Special handling
   - Ensures the manifest is always available
   - Prevents 401 errors when accessing from different origins

## Path Configuration

All paths in the manifest.json and HTML files are relative to prevent cross-origin issues. This is important because:

1. The app uses HashRouter, so all navigation is through hash-based URLs
2. The app may be accessed from different domains (localhost, stage.stayfu.com, etc.)
3. Relative paths ensure assets are loaded from the same origin as the app

## Authentication and Session Management

The service worker is configured to:

1. Skip caching of authentication requests to ensure they always go to the network
2. Handle 401 errors by redirecting to the login page
3. Maintain session state even when offline

## Troubleshooting

If you encounter issues with the PWA:

1. **401 Unauthorized errors for manifest.json**
   - This can happen if the manifest is being requested from a different origin
   - The service worker has special handling to intercept these requests and serve the local manifest

2. **Service worker not updating**
   - The service worker is configured to check for updates on each page load
   - You can manually check for updates using the `checkForUpdates()` function

3. **Offline functionality not working**
   - Ensure the service worker is registered and active
   - Check the browser console for any service worker errors

## Development Notes

When developing PWA features:

1. Always test on both desktop and mobile devices
2. Test with different network conditions (online, offline, slow)
3. Test the installation process
4. Ensure all assets are properly cached
5. Use relative paths for all assets in the manifest and HTML

## Recent Changes

Recent changes to fix PWA issues:

1. Updated manifest.json to use relative paths for all assets
2. Added special handling in the service worker for manifest.json requests
3. Updated the service worker to handle cross-origin requests better
4. Updated the service worker registration to use the correct base URL
5. Updated HTML to use relative paths for all assets
6. Incremented cache version to ensure clean caching
