# Routing System in StayFu

## Overview

StayFu uses **HashRouter** from React Router for all navigation throughout the application. This document outlines the routing system, including navigation patterns, route configuration, and best practices.

## HashRouter vs BrowserRouter

StayFu uses **HashRouter** instead of <PERSON>rows<PERSON><PERSON><PERSON><PERSON> for several important reasons:

1. **Compatibility with Static Hosting**: HashRouter works better with static hosting environments where server-side routing is not available.
2. **Simplified Deployment**: No need for server-side configuration to handle client-side routes.
3. **Consistent Behavior**: Ensures consistent behavior across different environments (development, staging, production).
4. **Bookmarking and Sharing**: URLs with hash fragments can be bookmarked and shared without server-side configuration.

## URL Format

All URLs in StayFu must use the hash-based format:

```
/#/path
```

Examples:
- Dashboard: `/#/dashboard`
- Properties: `/#/properties`
- Maintenance: `/#/maintenance`
- Login: `/#/login`

## Navigation Methods

### 1. React Router's `navigate` Function

When using React Router's `navigate` function, the hash is automatically handled:

```tsx
import { useNavigate } from 'react-router-dom';

const navigate = useNavigate();
navigate('/dashboard'); // This will navigate to /#/dashboard
```

### 2. Link Component

When using React Router's `Link` component, the hash is automatically handled:

```tsx
import { Link } from 'react-router-dom';

<Link to="/dashboard">Dashboard</Link> // This will link to /#/dashboard
```

### 3. Direct URL Navigation

When using direct URL navigation (e.g., `window.location.href`), you must include the hash:

```tsx
window.location.href = '/#/dashboard';
```

## Route Configuration

Routes are configured in `src/App.tsx` using React Router's `Routes` and `Route` components:

```tsx
<Routes>
  <Route path="/" element={<Navigate to="/dashboard" replace />} />
  <Route path="/login" element={<Login />} />
  <Route path="/dashboard" element={<RequireAuth><Dashboard /></RequireAuth>} />
  <Route path="/properties" element={<RequireAuth><Properties /></RequireAuth>} />
  {/* Other routes */}
</Routes>
```

## Protected Routes

Protected routes are wrapped in the `RequireAuth` component, which checks if the user is authenticated:

```tsx
<Route path="/dashboard" element={<RequireAuth><Dashboard /></RequireAuth>} />
```

If the user is not authenticated, they are redirected to the login page with a return URL:

```
/#/login?returnUrl=%2Fdashboard
```

## Navigation Events

When navigating between routes, the following events are triggered:

1. **Route Change**: The route changes in the URL
2. **Data Refresh**: Data for the new route is refreshed
3. **Component Mount**: The new component is mounted
4. **Visibility Change**: If the page was hidden, visibility change events are triggered

## Best Practices

1. **Always Use Hash-Based URLs**: All URLs in the application must use the hash-based format (`/#/path`).
2. **Use React Router's Navigation**: Prefer React Router's `navigate` function and `Link` component over direct URL manipulation.
3. **Include Hash in Direct Navigation**: When using direct URL navigation, always include the hash (`/#/`).
4. **Handle Route Parameters Consistently**: When extracting route parameters, be consistent in how you handle the hash.
5. **Test Navigation Thoroughly**: Test navigation flows thoroughly, especially when implementing new features.

## Common Issues and Solutions

### 1. Navigation Not Working

If navigation is not working properly:

- Check if you're using the correct URL format (`/#/path`)
- Verify that you're using React Router's navigation functions
- Check for any errors in the console

### 2. Route Parameters Not Working

If route parameters are not working properly:

- Check if you're extracting parameters correctly
- Verify that the route is configured correctly in `App.tsx`
- Check for any errors in the console

### 3. Redirects Not Working

If redirects are not working properly:

- Check if you're using the correct URL format (`/#/path`)
- Verify that you're handling the redirect correctly
- Check for any errors in the console

## Conclusion

Using HashRouter consistently throughout the StayFu application ensures reliable navigation, consistent behavior across environments, and simplified deployment. By following the best practices outlined in this document, you can ensure that navigation works correctly throughout the application.
