# StayFu UI Style Guide

This document outlines the standardized approach for UI components across the StayFu application.

## Button Standards

### Button Variants

We use the following button variants consistently throughout the application:

1. **Primary (default)** - Blue background, white text
   - Use for primary actions (Save, Submit, Create, etc.)
   - Example: `<Button>Save Changes</Button>`

2. **Secondary** - Light gray background, dark text
   - Use for secondary actions that don't require emphasis
   - Example: `<Button variant="secondary">View Details</Button>`

3. **Outline** - Transparent with border
   - Use for less important actions or in tight spaces
   - Example: `<Button variant="outline">Cancel</Button>`

4. **Destructive** - Red background, white text
   - Use for destructive actions (Delete, Remove, etc.)
   - Example: `<Button variant="destructive">Delete</Button>`

5. **Ghost** - Transparent background
   - Use for subtle actions, especially in tables or cards
   - Example: `<Button variant="ghost">Edit</Button>`

6. **Link** - Appears as a text link
   - Use for navigation or very subtle actions
   - Example: `<Button variant="link">Learn more</Button>`

### Button Sizes

1. **Default** - Standard size for most buttons
   - Example: `<Button>Submit</Button>`

2. **Small (sm)** - Smaller buttons for tight spaces or secondary actions
   - Example: `<Button size="sm">Filter</Button>`

3. **Large (lg)** - Larger buttons for emphasis or primary page actions
   - Example: `<Button size="lg">Create New</Button>`

4. **Icon** - Square button for icon-only buttons
   - Example: `<Button size="icon"><Plus /></Button>`

### Button Positioning

1. **Page Headers**
   - Primary action buttons should be positioned on the right
   - Filter/view toggles should be positioned on the left
   - Use `<PageHeaderButtons>` component

2. **Forms**
   - Submit button should be on the right
   - Cancel button should be to the left of the submit button
   - Use `<FormButtons>` component

3. **Dialogs/Modals**
   - Cancel button should be on the left
   - Submit/Confirm button should be on the right
   - Use `<ButtonGroup position="right">` component

4. **Data Tables**
   - Action buttons should be in the rightmost column
   - Use `<DataTableButtons>` component for consistent spacing

### Button Groups

Use the `<ButtonGroup>` component to ensure consistent spacing and alignment:

```tsx
<ButtonGroup position="right">
  <Button variant="outline">Cancel</Button>
  <Button>Save</Button>
</ButtonGroup>
```

## Filter Standards

### Search Bars

Use the `<SearchBar>` component for consistent search functionality:

```tsx
<SearchBar
  value={searchQuery}
  onChange={setSearchQuery}
  placeholder="Search properties..."
/>
```

### Filter Dropdowns

Use the `<FilterDropdown>` component for simple filter options:

```tsx
<FilterBar>
  <FilterDropdown
    label="Status"
    value={statusFilter}
    onChange={setStatusFilter}
  >
    <FilterOption value="all">All Statuses</FilterOption>
    <FilterOption value="active">Active</FilterOption>
    <FilterOption value="inactive">Inactive</FilterOption>
  </FilterDropdown>
</FilterBar>
```

### Filter Panels

For more complex filtering needs, use the `<FilterPanel>` component:

```tsx
<FilterPanel>
  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
    {/* Filter controls go here */}
  </div>
</FilterPanel>
```

### Active Filters

Display active filters with the `<ActiveFilters>` component:

```tsx
<ActiveFilters
  filters={[
    { id: 'status', label: 'Status', value: 'Active' },
    { id: 'property', label: 'Property', value: 'Beach House' }
  ]}
  onRemove={(id) => handleRemoveFilter(id)}
  onClear={() => handleClearFilters()}
/>
```

## Page Layout Standards

### Page Headers

Page headers should follow this structure:

```tsx
<div className="flex items-center justify-between mb-6">
  <div>
    <h1 className="text-2xl font-bold">Page Title</h1>
    <p className="text-muted-foreground">Page description or subtitle</p>
  </div>
  <PageHeaderButtons>
    <Button variant="outline" size="sm" onClick={toggleFilters}>
      <Filter className="h-4 w-4 mr-2" />
      Filters
    </Button>
    <Button size="sm" onClick={handleAddNew}>
      <Plus className="h-4 w-4 mr-2" />
      Add New
    </Button>
  </PageHeaderButtons>
</div>
```

### Filter Sections

Filter sections should appear below the page header:

```tsx
{showFilters && (
  <FilterPanel>
    {/* Filter controls */}
  </FilterPanel>
)}

<ActiveFilters
  filters={activeFilters}
  onRemove={handleRemoveFilter}
  onClear={handleClearFilters}
/>
```

### Content Sections

Content sections should be clearly separated:

```tsx
<div className="space-y-6">
  <div className="rounded-lg border bg-card p-6">
    <h2 className="text-lg font-medium mb-4">Section Title</h2>
    {/* Section content */}
  </div>
</div>
```

## Color Standards

Use these color tokens consistently throughout the application:

- **Primary**: Blue (#4f46e5) - Main brand color, used for primary buttons and key UI elements
- **Secondary**: Light gray (#f9fafb) - Used for secondary buttons and backgrounds
- **Destructive**: Red (#ef4444) - Used for destructive actions and error states
- **Success**: Green (#22c55e) - Used for success states and indicators
- **Warning**: Yellow (#f59e0b) - Used for warning states and indicators
- **Info**: Blue (#3b82f6) - Used for informational states and indicators
- **Muted**: Gray (#71717a) - Used for less important text and UI elements

### Dark Mode

The application supports both light and dark modes, with light mode being the default. When implementing UI components, ensure they work well in both modes:

1. **Use Tailwind's dark mode classes**
   - Example: `<div className="bg-white dark:bg-gray-800 text-black dark:text-white">`
   - Use `dark:` prefix for dark mode specific styles

2. **Use CSS variables for colors**
   - Example: `<div className="bg-card text-card-foreground">`
   - These variables automatically adjust based on the current theme

3. **Testing dark mode**
   - Always test components in both light and dark modes
   - Toggle dark mode in the settings panel to verify appearance

4. **Color contrast**
   - Ensure sufficient contrast in both modes
   - Text should have at least 4.5:1 contrast ratio with its background

## Responsive Design Standards

All UI components should be responsive and follow these guidelines:

1. **Mobile-first approach**
   - Design for mobile first, then enhance for larger screens
   - Use responsive utilities like `sm:`, `md:`, `lg:` prefixes

2. **Grid layouts**
   - Use grid layouts for responsive content
   - Example: `<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">`

3. **Stack on mobile**
   - Button groups and filter bars should stack vertically on mobile
   - Use `flex-col` for mobile and `sm:flex-row` for larger screens

4. **Hide less important elements**
   - Hide less important UI elements on smaller screens
   - Use `hidden sm:block` to show elements only on larger screens

## Form Standards

### Form Layout

Forms should follow this structure:

```tsx
<form onSubmit={handleSubmit} className="space-y-6">
  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div className="space-y-2">
      <Label htmlFor="firstName">First Name</Label>
      <Input id="firstName" value={firstName} onChange={handleFirstNameChange} />
    </div>
    <div className="space-y-2">
      <Label htmlFor="lastName">Last Name</Label>
      <Input id="lastName" value={lastName} onChange={handleLastNameChange} />
    </div>
  </div>

  <FormButtons
    onCancel={handleCancel}
    onSubmit={handleSubmit}
    loading={isSubmitting}
    submitLabel="Save Changes"
  />
</form>
```

### Form Validation

Use consistent form validation:

```tsx
<div className="space-y-2">
  <Label htmlFor="email">Email</Label>
  <Input
    id="email"
    value={email}
    onChange={handleEmailChange}
    className={errors.email ? "border-destructive" : ""}
  />
  {errors.email && (
    <p className="text-sm text-destructive">{errors.email}</p>
  )}
</div>
```

## Table Standards

### Data Tables

Data tables should follow this structure:

```tsx
<Table>
  <TableHeader>
    <TableRow>
      <TableHead>Name</TableHead>
      <TableHead>Status</TableHead>
      <TableHead className="text-right">Actions</TableHead>
    </TableRow>
  </TableHeader>
  <TableBody>
    {items.map((item) => (
      <TableRow key={item.id}>
        <TableCell>{item.name}</TableCell>
        <TableCell>{item.status}</TableCell>
        <TableCell className="text-right">
          <DataTableButtons>
            <Button variant="ghost" size="sm" onClick={() => handleEdit(item.id)}>
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
            <Button variant="ghost" size="sm" className="text-destructive" onClick={() => handleDelete(item.id)}>
              <Trash className="h-4 w-4 mr-1" />
              Delete
            </Button>
          </DataTableButtons>
        </TableCell>
      </TableRow>
    ))}
  </TableBody>
</Table>
```

### Empty States

Tables should have consistent empty states:

```tsx
{items.length === 0 ? (
  <div className="text-center p-6">
    <p className="text-muted-foreground">No items found</p>
    <Button className="mt-4" onClick={handleAddNew}>
      <Plus className="h-4 w-4 mr-2" />
      Add New
    </Button>
  </div>
) : (
  <Table>
    {/* Table content */}
  </Table>
)}
```

## Card Standards

### Card Layout

Cards should follow this structure:

```tsx
<Card>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Card description or subtitle</CardDescription>
  </CardHeader>
  <CardContent>
    {/* Card content */}
  </CardContent>
  <CardFooter>
    {/* Card footer */}
  </CardFooter>
</Card>
```

### Card Grids

Cards in grids should follow this structure:

```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {items.map((item) => (
    <Card key={item.id}>
      {/* Card content */}
    </Card>
  ))}
</div>
```

## Dialog Standards

### Dialog Layout

Dialogs should follow this structure:

```tsx
<Dialog open={isOpen} onOpenChange={setIsOpen}>
  <DialogTrigger asChild>
    <Button>Open Dialog</Button>
  </DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Dialog Title</DialogTitle>
      <DialogDescription>Dialog description or subtitle</DialogDescription>
    </DialogHeader>
    <div className="py-4">
      {/* Dialog content */}
    </div>
    <DialogFooter>
      <Button variant="outline" onClick={() => setIsOpen(false)}>
        Cancel
      </Button>
      <Button onClick={handleConfirm}>
        Confirm
      </Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

## Toast Standards

### Toast Usage

Use toasts for feedback:

```tsx
// Success toast
toast.success("Operation completed successfully");

// Error toast
toast.error("An error occurred");

// Info toast
toast.info("Information message");

// Warning toast
toast.warning("Warning message");
```

## Icon Standards

### Icon Usage

Use icons consistently:

```tsx
// Button with icon
<Button>
  <Plus className="h-4 w-4 mr-2" />
  Add New
</Button>

// Icon only button
<Button size="icon">
  <Plus className="h-4 w-4" />
</Button>

// Icon in text
<p>
  <Info className="h-4 w-4 inline mr-1" />
  Informational text
</p>
```

## Loading States

### Loading Indicators

Use consistent loading indicators:

```tsx
// Loading button
<Button disabled={isLoading}>
  {isLoading ? (
    <>
      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
      Loading...
    </>
  ) : (
    "Submit"
  )}
</Button>

// Loading page
{isLoading ? (
  <div className="flex items-center justify-center h-64">
    <Loader2 className="h-8 w-8 animate-spin text-primary" />
  </div>
) : (
  // Page content
)}
```

## URL Standards

### URL Format

All links in emails throughout the StayFu app must use hashed URLs (/#/) since the application uses HashRouter.

Example:
```
https://stayfu.com/#/teams/123
```

Not:
```
https://stayfu.com/teams/123
```
