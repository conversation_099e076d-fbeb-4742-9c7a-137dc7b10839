# Row Level Security (RLS) Policies

This document outlines the Row Level Security (RLS) policies used in the StayFu application.

## Common Issues

### Infinite Recursion

One common issue with RLS policies is infinite recursion, which can occur when:

1. A policy for table A references table B
2. A policy for table B references table A
3. This creates a circular dependency that can cause infinite recursion

For example, if:
- `properties` policy checks `team_properties` and `team_members`
- `team_properties` policy checks `team_members`
- `team_members` policy checks `properties`

This creates a circular dependency that can cause infinite recursion.

### Simplified Policies

To avoid infinite recursion, we've implemented simplified policies for the following tables:

#### Properties

```sql
CREATE POLICY "Properties are viewable by owners directly"
ON properties
FOR SELECT
USING (
  auth.uid() = user_id
);

CREATE POLICY "Properties are viewable by team members directly"
ON properties
FOR SELECT
USING (
  -- Simplified policy that doesn't reference team_members to avoid recursion
  EXISTS (
    SELECT 1 FROM team_properties tp
    WHERE tp.property_id = properties.id
  )
);

CREATE POLICY "Properties are viewable by admins directly"
ON properties
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE id = auth.uid() AND (is_super_admin = true OR role = 'admin')
  )
);

CREATE POLICY "Properties are editable by owners directly"
ON properties
FOR UPDATE
USING (
  auth.uid() = user_id
)
WITH CHECK (
  auth.uid() = user_id
);
```

#### Team Members

**Important Note: RLS is currently DISABLED for the team_members table to prevent infinite recursion.**

The previous approach of using RLS policies for team_members was causing infinite recursion errors. After troubleshooting, we found that disabling RLS for this table was the most effective solution.

```sql
-- RLS is disabled for team_members
ALTER TABLE team_members DISABLE ROW LEVEL SECURITY;
```

If RLS needs to be re-enabled in the future, consider using a simplified approach:

```sql
-- Example of a simplified policy (NOT CURRENTLY USED)
CREATE POLICY "Team members are viewable by everyone"
ON team_members
FOR SELECT
USING (true);

-- For INSERT/UPDATE/DELETE operations, be more restrictive
CREATE POLICY "Team members are editable by team owners and admins"
ON team_members
FOR ALL
USING (
  -- User is the owner of the team
  EXISTS (
    SELECT 1 FROM teams
    WHERE teams.id = team_members.team_id
    AND teams.owner_id = auth.uid()
  )
  -- Or user is an admin
  OR EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND (profiles.is_super_admin = true OR profiles.role = 'admin')
  )
);
```

#### Team Properties

```sql
-- For SELECT operations, allow everyone to view team properties
-- This prevents infinite recursion when checking permissions
CREATE POLICY "Team properties are viewable by everyone"
ON team_properties
FOR SELECT
USING (true);

-- For INSERT/UPDATE/DELETE operations, be more restrictive
CREATE POLICY "Team properties are editable by team owners and admins"
ON team_properties
FOR ALL
USING (
  -- User is the owner of the team
  EXISTS (
    SELECT 1 FROM teams
    WHERE teams.id = team_properties.team_id
    AND teams.owner_id = auth.uid()
  )
  -- Or user is an admin
  OR EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND (profiles.is_super_admin = true OR profiles.role = 'admin')
  )
);
```

#### Teams

```sql
CREATE POLICY "Teams are viewable by team members"
ON teams
FOR SELECT
USING (
  -- User is a member of the team
  EXISTS (
    SELECT 1 FROM team_members tm
    WHERE tm.team_id = teams.id
    AND tm.user_id = auth.uid()
    AND tm.status = 'active'
  )
  -- Or user is the owner of the team
  OR teams.owner_id = auth.uid()
  -- Or user is an admin
  OR EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND (profiles.is_super_admin = true OR profiles.role = 'admin')
  )
);
```

## Functions

The application uses several functions to retrieve properties based on user roles:

### get_user_role_properties

This function routes to the appropriate function based on the user's role:

```sql
CREATE OR REPLACE FUNCTION public.get_user_role_properties(p_user_id UUID)
RETURNS TABLE (
    id UUID,
    name TEXT,
    address TEXT,
    city TEXT,
    state TEXT,
    zip TEXT,
    user_id UUID,
    team_id UUID,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
    user_role TEXT;
BEGIN
    -- Get the user's role
    SELECT profiles.role INTO user_role
    FROM profiles
    WHERE profiles.id = p_user_id;

    -- Route to the appropriate function based on role
    IF user_role = 'service_provider' THEN
        RETURN QUERY
        SELECT * FROM get_service_provider_properties(p_user_id);
    ELSE
        -- For all other roles (property_manager, admin, etc.), use get_user_properties
        RETURN QUERY
        SELECT * FROM get_user_properties(p_user_id);
    END IF;
END;
$$;
```

### get_service_provider_properties

This function retrieves properties for service providers:

```sql
CREATE OR REPLACE FUNCTION public.get_service_provider_properties(p_user_id UUID)
RETURNS SETOF properties
LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
    is_service_provider BOOLEAN;
BEGIN
    -- Check if the user is a service provider
    SELECT EXISTS (
        SELECT 1 FROM profiles
        WHERE profiles.id = p_user_id AND profiles.role = 'service_provider'
    ) INTO is_service_provider;

    -- If not a service provider, return empty result
    IF NOT is_service_provider THEN
        RETURN;
    END IF;

    -- Return properties the service provider has access to via team membership
    RETURN QUERY
    SELECT DISTINCT ON (p.id)
        p.*
    FROM properties p
    JOIN team_properties tp ON p.id = tp.property_id
    JOIN team_members tm ON tp.team_id = tm.team_id
    WHERE tm.user_id = p_user_id
    AND tm.status = 'active'
    ORDER BY p.id, p.name;
END;
$$;
```

### get_user_properties

This function retrieves properties for non-service-provider users:

```sql
CREATE OR REPLACE FUNCTION public.get_user_properties(p_user_id UUID)
RETURNS SETOF properties
LANGUAGE sql SECURITY DEFINER AS $$
    SELECT DISTINCT ON (p.id)
        p.*
    FROM
        properties p
    WHERE
        -- User's own properties
        p.user_id = p_user_id
        -- Admin access
        OR EXISTS (SELECT 1 FROM profiles prf WHERE prf.id = p_user_id AND (prf.is_super_admin = true OR prf.role = 'admin'))
        -- Team member access
        OR EXISTS (
            SELECT 1 FROM team_properties tp
            JOIN team_members tm ON tp.team_id = tm.team_id
            WHERE tp.property_id = p.id AND tm.user_id = p_user_id AND tm.status = 'active'
        )
    ORDER BY
        p.id, p.name;
$$;
```

## Troubleshooting

If you encounter issues with RLS policies, consider the following steps:

1. **Check for circular dependencies between policies**
   - This is the most common cause of "infinite recursion" errors
   - Look for tables that reference each other in their policies

2. **Disable RLS for problematic tables**
   - Sometimes the simplest solution is to disable RLS for tables that cause recursion
   - This is the approach we took with the `team_members` table

3. **Simplify policies to avoid complex joins**
   - Avoid joining multiple tables in policies when possible
   - We simplified the properties policy to avoid joining with team_members

4. **Use SECURITY DEFINER functions to bypass RLS when necessary**
   - Functions with SECURITY DEFINER can access tables without triggering RLS
   - This can help break circular dependencies

5. **Enable logging to identify the source of recursion**
   - PostgreSQL logs can help identify which policies are causing recursion

6. **Consider using a more direct approach with fewer joins**
   - Each join in a policy increases the complexity and potential for issues

Remember that RLS policies are evaluated for every row, so complex policies can significantly impact performance.

## User Permissions RLS

We've simplified the RLS policies for the `user_permissions` table to prevent infinite recursion issues:

```sql
-- Drop ALL policies on user_permissions table
DO $$
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN
        SELECT policyname
        FROM pg_policies
        WHERE tablename = 'user_permissions'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON user_permissions', policy_record.policyname);
    END LOOP;
END
$$;

-- Create a single, simple policy for user_permissions
CREATE POLICY "user_permissions_simple_policy"
ON user_permissions
FOR ALL
USING (true)
WITH CHECK (true);

-- Enable RLS on user_permissions
ALTER TABLE user_permissions ENABLE ROW LEVEL SECURITY;
```

This simplified approach allows all authenticated users to view and modify user permissions, relying on application-level authorization to control access. This prevents the infinite recursion issues that were occurring with more complex policies.

## Current Status

As of the latest update:
- RLS is **DISABLED** for the `team_members` table to prevent infinite recursion
- The properties policy has been simplified to avoid referencing `team_members`
- The `user_permissions` table has a simplified policy that allows all operations
- All other tables have their original RLS policies intact

This configuration allows the application to function correctly while maintaining appropriate security controls. The application-level authorization in the React components ensures that only users with appropriate permissions can modify data.
