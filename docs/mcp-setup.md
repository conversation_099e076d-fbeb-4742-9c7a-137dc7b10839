# StayFu MCP (Model Context Protocol) Setup Guide

This document provides a comprehensive guide for setting up and maintaining the Model Context Protocol (MCP) servers used in the StayFu project. MCP servers enable AI assistants to interact with various tools and services, enhancing their capabilities for development and debugging.

## Table of Contents

1. [Overview of MCP Servers](#overview-of-mcp-servers)
2. [Installation and Configuration](#installation-and-configuration)
3. [Browser Tools MCP Setup](#browser-tools-mcp-setup)
4. [Troubleshooting](#troubleshooting)
5. [Maintenance](#maintenance)

## Overview of MCP Servers

The StayFu project uses the following MCP servers:

| MCP Server | Description | Command | Status |
|------------|-------------|---------|--------|
| Fetch MCP | Fetches content from websites in various formats (HTML, Markdown, TXT, JSON) | `node /home/<USER>/Documents/Cline/MCP/fetch-mcp/dist/index.js` | Working |
| Brave Search MCP | Performs web searches using the Brave Search API | `npx -y @modelcontextprotocol/server-brave-search` | Working (Rate limits apply) |
| Browser Tools MCP | Monitors browser logs, takes screenshots, and runs audits | `npx @agentdeskai/browser-tools-mcp@latest` | Working |
| Puppeteer MCP | Automates browser interactions | `xvfb-run -a npx -y @modelcontextprotocol/server-puppeteer` | Working |
| Memory MCP | Provides memory capabilities for AI assistants | `node /home/<USER>/Documents/Cline/MCP/memory-server/build/index.js` | Working |
| Browser Tools 2 | Alternative version of Browser Tools MCP | `npx @agentdeskai/browser-tools-mcp@1.2.0` | Working |
| Browser Tools Server | Server component for Browser Tools MCP | `./launch-browser-tools-mcp.sh` or `npx @agentdeskai/browser-tools-server@1.2.0 --port 3040 --user-data-dir="$HOME/.config/stayfu-browser-tools"` | Working |
| ChromeDriver MCP | Automates browser interactions using Selenium WebDriver | `node /home/<USER>/Documents/Cline/MCP/chromedriver-mcp/chromedriver-mcp.js` | Not yet implemented |

## Installation and Configuration

### Prerequisites

- Node.js (v16 or higher)
- npm (v7 or higher)
- Chrome or Chromium browser
- xvfb (for headless browser automation on Linux)

### General Setup

1. Ensure Node.js and npm are installed:
   ```bash
   node --version
   npm --version
   ```

2. Install global dependencies:
   ```bash
   npm install -g @agentdeskai/browser-tools-mcp@latest @agentdeskai/browser-tools-server@latest @modelcontextprotocol/server-brave-search @modelcontextprotocol/server-puppeteer
   ```

3. Configure your AI assistant (Claude, GPT, etc.) to use these MCP servers.

## ChromeDriver MCP Setup

ChromeDriver MCP provides more robust browser automation capabilities using Selenium WebDriver, which can be particularly useful for client-side rendered applications like StayFu.

### Step 1: Install Required Dependencies

```bash
# Install ChromeDriver
npm install -g chromedriver

# Create a new directory for our custom MCP
mkdir -p ~/Documents/Cline/MCP/chromedriver-mcp
cd ~/Documents/Cline/MCP/chromedriver-mcp

# Initialize a new Node.js project
npm init -y

# Install required dependencies
npm install selenium-webdriver chromedriver express cors body-parser
```

### Step 2: Create the MCP Server

Create a file named `chromedriver-mcp.js` with the following content:

```javascript
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const { Builder, By, Key, until } = require('selenium-webdriver');
const chrome = require('selenium-webdriver/chrome');

const app = express();
const port = 3040;

// Enable CORS and JSON parsing
app.use(cors());
app.use(bodyParser.json());

// Store the driver instance
let driver = null;

// Initialize the driver
async function initializeDriver() {
  if (!driver) {
    console.log('Initializing ChromeDriver...');

    const options = new chrome.Options();
    // Add any Chrome options here
    // options.addArguments('--headless');

    driver = await new Builder()
      .forBrowser('chrome')
      .setChromeOptions(options)
      .build();

    console.log('ChromeDriver initialized successfully');
  }
  return driver;
}

// Endpoint to navigate to a URL
app.post('/navigate', async (req, res) => {
  try {
    const { url } = req.body;
    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    const driver = await initializeDriver();
    await driver.get(url);

    return res.json({ success: true, message: `Navigated to ${url}` });
  } catch (error) {
    console.error('Error navigating:', error);
    return res.status(500).json({ error: error.message });
  }
});

// Endpoint to get the page source
app.get('/source', async (req, res) => {
  try {
    const driver = await initializeDriver();
    const source = await driver.getPageSource();

    return res.json({ source });
  } catch (error) {
    console.error('Error getting page source:', error);
    return res.status(500).json({ error: error.message });
  }
});

// Endpoint to find and click an element
app.post('/click', async (req, res) => {
  try {
    const { selector, type = 'css' } = req.body;
    if (!selector) {
      return res.status(400).json({ error: 'Selector is required' });
    }

    const driver = await initializeDriver();

    let element;
    if (type === 'css') {
      element = await driver.findElement(By.css(selector));
    } else if (type === 'xpath') {
      element = await driver.findElement(By.xpath(selector));
    } else if (type === 'id') {
      element = await driver.findElement(By.id(selector));
    } else {
      return res.status(400).json({ error: 'Invalid selector type' });
    }

    await element.click();

    return res.json({ success: true, message: `Clicked element with ${type} selector: ${selector}` });
  } catch (error) {
    console.error('Error clicking element:', error);
    return res.status(500).json({ error: error.message });
  }
});

// Endpoint to fill a form field
app.post('/fill', async (req, res) => {
  try {
    const { selector, value, type = 'css' } = req.body;
    if (!selector || value === undefined) {
      return res.status(400).json({ error: 'Selector and value are required' });
    }

    const driver = await initializeDriver();

    let element;
    if (type === 'css') {
      element = await driver.findElement(By.css(selector));
    } else if (type === 'xpath') {
      element = await driver.findElement(By.xpath(selector));
    } else if (type === 'id') {
      element = await driver.findElement(By.id(selector));
    } else {
      return res.status(400).json({ error: 'Invalid selector type' });
    }

    await element.clear();
    await element.sendKeys(value);

    return res.json({ success: true, message: `Filled element with ${type} selector: ${selector}` });
  } catch (error) {
    console.error('Error filling element:', error);
    return res.status(500).json({ error: error.message });
  }
});

// Endpoint to take a screenshot
app.get('/screenshot', async (req, res) => {
  try {
    const driver = await initializeDriver();
    const screenshot = await driver.takeScreenshot();

    return res.json({ screenshot });
  } catch (error) {
    console.error('Error taking screenshot:', error);
    return res.status(500).json({ error: error.message });
  }
});

// Endpoint to execute JavaScript
app.post('/execute', async (req, res) => {
  try {
    const { script } = req.body;
    if (!script) {
      return res.status(400).json({ error: 'Script is required' });
    }

    const driver = await initializeDriver();
    const result = await driver.executeScript(script);

    return res.json({ result });
  } catch (error) {
    console.error('Error executing script:', error);
    return res.status(500).json({ error: error.message });
  }
});

// Endpoint to close the driver
app.post('/close', async (req, res) => {
  try {
    if (driver) {
      await driver.quit();
      driver = null;
      console.log('ChromeDriver closed successfully');
    }

    return res.json({ success: true, message: 'ChromeDriver closed successfully' });
  } catch (error) {
    console.error('Error closing driver:', error);
    return res.status(500).json({ error: error.message });
  }
});

// Start the server
app.listen(port, () => {
  console.log(`ChromeDriver MCP server running at http://localhost:${port}`);
});

// Handle process termination
process.on('SIGINT', async () => {
  if (driver) {
    console.log('Closing ChromeDriver...');
    await driver.quit();
  }
  process.exit();
});
```

### Step 3: Create the MCP Client

Create a file named `chromedriver-mcp-client.js` with the following content:

```javascript
#!/usr/bin/env node

const http = require('http');
const https = require('https');
const url = require('url');

// MCP server URL
const MCP_SERVER_URL = 'http://localhost:3040';

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0];
const params = {};

// Parse parameters
for (let i = 1; i < args.length; i += 2) {
  if (args[i].startsWith('--')) {
    const key = args[i].slice(2);
    const value = args[i + 1];
    params[key] = value;
  }
}

// Helper function to make HTTP requests
async function makeRequest(endpoint, method, data = null) {
  return new Promise((resolve, reject) => {
    const parsedUrl = url.parse(`${MCP_SERVER_URL}${endpoint}`);

    const options = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port,
      path: parsedUrl.path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve(parsedData);
        } catch (error) {
          resolve(responseData);
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Execute the command
async function executeCommand() {
  try {
    switch (command) {
      case 'navigate':
        if (!params.url) {
          console.error('URL is required for navigate command');
          process.exit(1);
        }
        const navigateResult = await makeRequest('/navigate', 'POST', { url: params.url });
        console.log(JSON.stringify(navigateResult));
        break;

      case 'source':
        const sourceResult = await makeRequest('/source', 'GET');
        console.log(JSON.stringify(sourceResult));
        break;

      case 'click':
        if (!params.selector) {
          console.error('Selector is required for click command');
          process.exit(1);
        }
        const clickResult = await makeRequest('/click', 'POST', {
          selector: params.selector,
          type: params.type || 'css'
        });
        console.log(JSON.stringify(clickResult));
        break;

      case 'fill':
        if (!params.selector || !params.value) {
          console.error('Selector and value are required for fill command');
          process.exit(1);
        }
        const fillResult = await makeRequest('/fill', 'POST', {
          selector: params.selector,
          value: params.value,
          type: params.type || 'css'
        });
        console.log(JSON.stringify(fillResult));
        break;

      case 'screenshot':
        const screenshotResult = await makeRequest('/screenshot', 'GET');
        console.log(JSON.stringify(screenshotResult));
        break;

      case 'execute':
        if (!params.script) {
          console.error('Script is required for execute command');
          process.exit(1);
        }
        const executeResult = await makeRequest('/execute', 'POST', { script: params.script });
        console.log(JSON.stringify(executeResult));
        break;

      case 'close':
        const closeResult = await makeRequest('/close', 'POST');
        console.log(JSON.stringify(closeResult));
        break;

      default:
        console.error(`Unknown command: ${command}`);
        process.exit(1);
    }
  } catch (error) {
    console.error('Error executing command:', error);
    process.exit(1);
  }
}

executeCommand();
```

### Step 4: Make the Client Executable

```bash
chmod +x chromedriver-mcp-client.js
```

### Step 5: Start the MCP Server

```bash
node chromedriver-mcp.js
```

### Step 6: Using the ChromeDriver MCP

You can use the ChromeDriver MCP with the following commands:

```bash
# Navigate to a URL
./chromedriver-mcp-client.js navigate --url http://localhost:8080

# Get the page source
./chromedriver-mcp-client.js source

# Click an element
./chromedriver-mcp-client.js click --selector "a[href='/#/login']"

# Fill a form field
./chromedriver-mcp-client.js fill --selector "input[type='email']" --value "<EMAIL>"

# Take a screenshot
./chromedriver-mcp-client.js screenshot

# Execute JavaScript
./chromedriver-mcp-client.js execute --script "return document.title"

# Close the driver
./chromedriver-mcp-client.js close
```

### Advantages of ChromeDriver MCP

1. **Better DOM Access**: ChromeDriver provides better access to the DOM of client-side rendered applications like React apps.
2. **Waiting Capabilities**: Selenium WebDriver has built-in waiting mechanisms to wait for elements to be present, visible, or clickable.
3. **More Robust**: ChromeDriver is more robust for complex web applications and can handle dynamic content better than Puppeteer in some cases.
4. **Industry Standard**: Selenium WebDriver is an industry standard for browser automation and has extensive documentation and community support.

## Browser Tools MCP Setup

The Browser Tools MCP requires special setup as it involves a Chrome extension and a server component.

### Step 1: Install the Chrome Extension

1. Download the BrowserTools Chrome extension:
   ```bash
   wget https://github.com/AgentDeskAI/browser-tools-mcp/releases/download/v1.2.0/BrowserTools-1.2.0-extension.zip -O ~/Downloads/BrowserTools-1.2.0-extension.zip
   ```

2. Extract the extension:
   ```bash
   mkdir -p ~/Downloads/BrowserTools-extension
   unzip ~/Downloads/BrowserTools-1.2.0-extension.zip -d ~/Downloads/BrowserTools-extension
   ```

3. Install the extension in Chrome:
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode" (toggle in the top-right corner)
   - Click "Load unpacked" and select the `~/Downloads/BrowserTools-extension/chrome-extension` directory

### Step 2: Start the Browser Tools Server

The Browser Tools Server must be running for the Browser Tools MCP to work. We've created a launch script to make this easier:

```bash
./launch-browser-tools-mcp.sh
```

Alternatively, you can start it directly:

```bash
npx @agentdeskai/browser-tools-server@1.2.0 --port 3040 --user-data-dir="$HOME/.config/stayfu-browser-tools"
```

This server runs on port 3040 by default. Keep this terminal window open while using the Browser Tools MCP.

> **Note**: We use a custom user data directory to avoid conflicts with other Chrome instances and ensure consistent behavior.

### Step 3: Start the Browser Tools MCP

In a separate terminal, start the Browser Tools MCP:

```bash
npx @agentdeskai/browser-tools-mcp@1.2.0
```

### Step 4: Enable Browser Developer Tools

For the Browser Tools MCP to capture logs and interact with the browser:

1. Open Chrome Developer Tools in the tab you want to monitor (Right-click > Inspect or press F12)
2. Navigate to the "BrowserTools" panel in the Developer Tools
3. Configure settings as needed (screenshot path, log limits, etc.)

## Troubleshooting

### Common Issues and Solutions

#### Browser Tools MCP Not Working

1. **Check if the Browser Tools Server is running**
   - Ensure the server is running on port 3040 (or 3025 if using the default configuration)
   - Restart the server using the launch script: `./launch-browser-tools-mcp.sh`
   - Check for any error messages in the server console

2. **Check if the Chrome extension is installed and enabled**
   - Verify in `chrome://extensions/` that the BrowserTools extension is enabled
   - Try reinstalling the extension if issues persist

3. **Check if Chrome Developer Tools is open**
   - The Browser Tools MCP requires Chrome Developer Tools to be open in the tab you want to monitor

#### Rate Limit Errors with Brave Search MCP

- Wait a few minutes before making additional requests
- Consider implementing a delay between requests

#### Puppeteer MCP Issues on Linux

- Ensure xvfb is installed: `sudo apt-get install xvfb`
- Always use the `xvfb-run -a` prefix when starting the Puppeteer MCP

### Checking MCP Server Status

To check if an MCP server is running:

```bash
ps aux | grep -i mcp
```

To check if a specific port is in use:

```bash
lsof -i :3040  # For Browser Tools Server
lsof -i :3025  # For default Browser Tools Server port
```

## Maintenance

### Updating MCP Servers

Regularly update the MCP servers to get the latest features and bug fixes:

```bash
npm update -g @agentdeskai/browser-tools-mcp @agentdeskai/browser-tools-server @modelcontextprotocol/server-brave-search @modelcontextprotocol/server-puppeteer
```

### Monitoring and Logs

- Browser Tools MCP logs are stored in the Chrome Developer Tools console
- Server logs are displayed in the terminal where the server is running

### Best Practices

1. **Start servers in the correct order**:
   - Start the Browser Tools Server before the Browser Tools MCP
   - Keep all server terminals open while using the MCPs

2. **Use specific versions**:
   - If a specific version works well, use that version explicitly (e.g., `@1.2.0`)
   - Test new versions in a development environment before updating production

3. **KDE-specific considerations**:
   - For KDE desktop environment, use the plasma-browser-integration package
   - Ensure the plasma-browser-integration-host process is running

4. **Regular testing**:
   - Periodically test all MCP servers to ensure they're working correctly
   - Document any issues and their solutions

## Current Configuration

As of April 25, 2025, the following MCP servers are configured and running on the StayFu development environment:

### Running MCP Servers

To check the currently running MCP servers:

```bash
ps aux | grep -i mcp
```

Expected output should show processes for:
- Fetch MCP (`/home/<USER>/Documents/Cline/MCP/fetch-mcp/dist/index.js`)
- Brave Search MCP (`mcp-server-brave-search`)
- Browser Tools MCP (`browser-tools-mcp`)
- Puppeteer MCP (`mcp-server-puppeteer`)
- Memory MCP (`/home/<USER>/Documents/Cline/MCP/memory-server/build/index.js`)

### Browser Tools Server

To check if the Browser Tools Server is running:

```bash
ps aux | grep -i browser-tools-server
```

Expected output should show the Browser Tools Server process running on port 3040 (or 3025 if using the default configuration).

You can also check if the port is in use:

```bash
lsof -i :3040
```

### Verifying Browser Tools MCP Connection

To verify that the Browser Tools MCP is properly connected to the Browser Tools Server:

1. Check the Browser Tools Server logs for the message "Chrome extension connected via WebSocket"
2. Run a simple test using the Browser Tools MCP:
   ```bash
   npx @agentdeskai/browser-tools-mcp@1.2.0 getConsoleLogs
   ```
   This should return any console logs from the browser.

### Testing All MCPs

To ensure all MCPs are working correctly, run the following tests:

1. **Fetch MCP**: Test fetching a webpage
2. **Brave Search MCP**: Test performing a web search
3. **Browser Tools MCP**: Test taking a screenshot and running an audit
4. **Puppeteer MCP**: Test navigating to a URL
5. **Memory MCP**: Test creating and retrieving a memory

---

This documentation was last updated on April 25, 2025. For the latest information, refer to the official documentation of each MCP server.
