# Focus Data Loading Fixes

This document outlines the fixes implemented to address the issue of data disappearing when the application regains focus and not reloading properly.

## Overview of Issues

The application was experiencing the following issues:

1. Data would disappear when coming back from focus (when the application regains focus after being in the background)
2. Data would not reload properly when navigating between different views
3. Some views would trigger multiple redundant data fetches
4. Inconsistent data loading behavior across different dashboard views

## Root Causes Identified

1. **Visibility Change Handlers**: The application was using visibility change handlers that were causing data to disappear when the application regained focus.
2. **Inconsistent React Query Configuration**: Different parts of the application had different React Query configurations.
3. **Conflicting Refresh Mechanisms**: Multiple refresh mechanisms were conflicting with each other.
4. **Blacklisted Routes**: Some routes were blacklisted in one place but not in another.

## Implemented Fixes

### 1. Removed Visibility Change Handlers

Removed the visibility change event listener from the `useVisibilityRefresh` hook:

```javascript
// Before:
useEffect(() => {
  const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible') {
      refreshData();
    }
  };

  document.addEventListener('visibilitychange', handleVisibilityChange);

  return () => {
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  };
}, [refreshData]);

// After:
// No visibility change event listener, just return the refreshData function
return { refreshData };
```

### 2. Updated React Query Configuration

Changed the React Query configuration in `AppProviders.tsx`:

```javascript
// Before:
refetchOnWindowFocus: true,
refetchOnMount: true,
refetchOnReconnect: true,
retry: 3,
retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
staleTime: 1000 * 60 * 5,
gcTime: 30 * 60 * 1000,
networkMode: 'always',

// After:
refetchOnWindowFocus: false, // Changed from true to false to prevent data disappearing
refetchOnMount: true,
refetchOnReconnect: true,
retry: 3,
retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
staleTime: 1000 * 60 * 5, // 5 minutes
gcTime: 30 * 60 * 1000, // 30 minutes
networkMode: 'always',
```

### 3. Removed useVisibilityRefresh from GlobalDataRefreshContext

Removed the `useVisibilityRefresh` hook from the `GlobalDataRefreshContext`:

```javascript
// Before:
export const GlobalDataRefreshProvider: React.FC<GlobalDataRefreshProviderProps> = ({ children }) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const queryClient = useQueryClient();
  useVisibilityRefresh();
  
  // ...
};

// After:
export const GlobalDataRefreshProvider: React.FC<GlobalDataRefreshProviderProps> = ({ children }) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const queryClient = useQueryClient();
  
  // ...
};
```

### 4. Standardized REFRESH_BLACKLIST

Updated the `REFRESH_BLACKLIST` in `GlobalDataRefreshContext.tsx` to match the one in `useNavigationRefresh.ts`:

```javascript
// Before:
const REFRESH_BLACKLIST = new Set<string>(['/teams']); // Add problematic routes here

// After:
const REFRESH_BLACKLIST = new Set<string>([]); // Previously included '/teams' but it's now fixed
```

### 5. Converted useVisibilityRefresh to a Manual Refresh Hook

Changed the `useVisibilityRefresh` hook to provide a manual refresh function instead of using visibility change events:

```javascript
// Before:
export const useVisibilityRefresh = () => {
  // ...
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        refreshData();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [refreshData]);
};

// After:
export const useVisibilityRefresh = () => {
  // ...
  // Return the refresh function for manual use
  return { refreshData };
};
```

## Affected Files

The following files were modified:

1. `src/providers/AppProviders.tsx`
   - Changed `refetchOnWindowFocus` from `true` to `false`
   - Removed import of `useVisibilityRefresh`

2. `src/contexts/GlobalDataRefreshContext.tsx`
   - Removed import and usage of `useVisibilityRefresh`
   - Updated `REFRESH_BLACKLIST` to be empty

3. `src/hooks/useVisibilityRefresh.ts`
   - Removed visibility change event listener
   - Converted to a manual refresh hook

## Testing

These changes should be tested by:

1. Opening different dashboard views and checking if data loads correctly
2. Switching between tabs or minimizing the browser, then returning to the application
3. Navigating between different views and ensuring data persists
4. Checking all dashboard views to ensure they load data properly

## Best Practices for Future Development

1. **Avoid Visibility Change Handlers**: Do not use visibility change handlers as they can cause data to disappear.
2. **Use Consistent React Query Configuration**: Ensure all parts of the application use the same React Query configuration.
3. **Prefer Manual Refresh**: Use manual refresh functions instead of automatic refresh based on visibility changes.
4. **Keep Blacklists in Sync**: Ensure that any blacklists or configuration settings are consistent across the application.
5. **Use Hash Router**: Always use hash-based URLs (e.g., `/#/dashboard` instead of `/dashboard`) for navigation.
