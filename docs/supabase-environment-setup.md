# StayFu Supabase Environment Setup

## 🎯 Overview

This document describes the complete setup for using local Supabase for development and remote Supabase for production in the StayFu application.

## 📁 Environment Configuration

### Development Environment (.env.development)
- **Supabase URL**: `http://127.0.0.1:54321` (Local)
- **Purpose**: Local development with full schema
- **Usage**: `npm run dev` or `npm run dev:local`

### Production Environment (.env)
- **Supabase URL**: `https://pwaeknalhosfwuxkpaet.supabase.co` (Remote)
- **Purpose**: Production builds and remote development
- **Usage**: `npm run build` or `npm run dev:remote`

## 🚀 Quick Start

### 1. Start Local Supabase
```bash
# Start local Supabase environment
npm run supabase:start
# or
supabase start

# Check status
npm run supabase:status
# or
supabase status
```

### 2. Development with Local Supabase
```bash
# Start development server with local Supabase
npm run dev
# or explicitly
npm run dev:local
```

### 3. Development with Remote Supabase
```bash
# Start development server with remote Supabase
npm run dev:remote
```

### 4. Production Build
```bash
# Build for production (uses remote Supabase)
npm run build
```

## 📊 Database Schema

### Local Schema
The local environment uses a comprehensive migration file that includes all tables from the remote database:

- **Core Tables**: profiles, teams, team_members, properties
- **Maintenance**: maintenance_requests, maintenance_tasks, maintenance_providers
- **Inventory**: inventory_items, purchase_orders, purchase_order_items
- **Damage Management**: damage_reports, damage_photos, damage_notes, damage_invoices
- **Automation**: automation_rules, automation_queue
- **User Management**: user_permissions, user_preferences, user_settings
- **And more...**

### Schema Sync
```bash
# Generate TypeScript types from local schema
npm run supabase:types:local

# Generate TypeScript types from remote schema
npm run supabase:types
```

## 🔧 Configuration Details

### Environment Variables

#### Development (.env.development)
```env
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_PUBLISHABLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************.qQNWn2VFBnKNw7rtLOeqldWddNfYQZgNqYvlYtHQrQM
VITE_CHROME_EXTENSION_ID=bklaonfklmbjhnmijonmbegdkfamcnfi
NODE_ENV=development
```

#### Production (.env)
```env
VITE_SUPABASE_URL=https://pwaeknalhosfwuxkpaet.supabase.co
VITE_SUPABASE_PUBLISHABLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4
VITE_CHROME_EXTENSION_ID=bklaonfklmbjhnmijonmbegdkfamcnfi
NODE_ENV=production
```

### Supabase Client Configuration

The Supabase client automatically detects the environment and uses the appropriate configuration:

```typescript
// Environment-aware configuration
const isDevelopment = import.meta.env.MODE === 'development' || import.meta.env.DEV;

const SUPABASE_URL = import.meta.env?.VITE_SUPABASE_URL || 
  (isDevelopment 
    ? 'http://127.0.0.1:54321'     // Local Supabase
    : 'https://pwaeknalhosfwuxkpaet.supabase.co'  // Remote Supabase
  );
```

## 📝 Available Scripts

| Script | Description | Environment |
|--------|-------------|-------------|
| `npm run dev` | Development with local Supabase | Development |
| `npm run dev:local` | Explicitly use local Supabase | Development |
| `npm run dev:remote` | Development with remote Supabase | Production |
| `npm run build` | Production build | Production |
| `npm run supabase:start` | Start local Supabase | Local |
| `npm run supabase:stop` | Stop local Supabase | Local |
| `npm run supabase:status` | Check local Supabase status | Local |
| `npm run supabase:types:local` | Generate types from local schema | Local |
| `npm run supabase:types` | Generate types from remote schema | Remote |

## 🔍 Testing Environment

Test your environment configuration:
```bash
# Test development configuration
node scripts/test-environment.js --dev

# Test production configuration
node scripts/test-environment.js
```

## 🛠 Troubleshooting

### Local Supabase Won't Start
1. Check Docker is running: `docker ps`
2. Kill conflicting processes: `sudo ss -tulnp | grep :54321`
3. Clean up containers: `docker system prune -f`
4. Restart Supabase: `supabase start`

### Environment Not Switching
1. Check `.env.development` and `.env` files exist
2. Verify script is using correct mode: `--mode development` or `--mode production`
3. Clear browser cache and restart dev server

### Schema Out of Sync
1. Pull latest remote schema: `supabase db pull --password="[password]"`
2. Apply migrations: `supabase db push`
3. Regenerate types: `npm run supabase:types:local`

## 📚 Additional Resources

- [Supabase Local Development](https://supabase.com/docs/guides/local-development)
- [Vite Environment Variables](https://vitejs.dev/guide/env-and-mode.html)
- [StayFu Database Schema](./schema.md)

## ✅ Verification Checklist

- [ ] Local Supabase starts successfully
- [ ] Development mode uses local Supabase (127.0.0.1:54321)
- [ ] Production mode uses remote Supabase (pwaeknalhosfwuxkpaet.supabase.co)
- [ ] All tables created in local database
- [ ] Environment test script passes
- [ ] TypeScript types generated correctly
