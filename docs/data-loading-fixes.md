# Data Loading Fixes

This document outlines the fixes implemented to address data loading issues when pages lose focus or when navigating between different views in the StayFu application.

## Overview of Issues

The application was experiencing several issues related to data loading:

1. Data would disappear or fail to load when the application regained focus after being in the background
2. Some pages would trigger multiple redundant data fetches
3. The Teams page was blacklisted from refreshing data in the `useNavigationRefresh` hook
4. Inconsistent data loading behavior across different dashboard views

## Implemented Fixes

### 1. Removed Teams from the Navigation Refresh Blacklist

The Teams route was previously blacklisted in the `useNavigationRefresh.ts` hook, preventing it from refreshing data when navigating to the page or when the page regained focus.

```javascript
// Before:
const REFRESH_BLACKLIST = new Set<string>(['/teams']); // Add problematic routes here

// After:
const REFRESH_BLACKLIST = new Set<string>([]); // Previously included '/teams' but it's now fixed
```

### 2. Added Visibility Change Handlers

Added proper visibility change handlers to key components to ensure data refreshes when the page regains focus:

- `TeamDashboard.tsx`
- `TaskAutomation.tsx`
- `Maintenance.tsx`
- `Properties.tsx`

Example implementation:

```javascript
useEffect(() => {
  const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible') {
      console.log('[Component] Page became visible, refreshing data');

      // Invalidate and refetch queries
      queryClient.invalidateQueries({ queryKey: ['relevantQueryKey'] });
      queryClient.refetchQueries({
        queryKey: ['relevantQueryKey'],
        type: 'all' // Force refetch all queries, not just active ones
      });

      // Also directly fetch data to ensure we have the latest
      fetchData();
    }
  };

  document.addEventListener('visibilitychange', handleVisibilityChange);

  return () => {
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  };
}, [queryClient, fetchData]);
```

### 3. Prevented Duplicate Data Fetches

Added refs to track initial data loading and prevent duplicate fetches:

```javascript
const initialLoadDoneRef = useRef(false);

useEffect(() => {
  // Skip if we've already done the initial load
  if (initialLoadDoneRef.current) return;

  // Mark that we've done the initial load
  initialLoadDoneRef.current = true;

  console.log('[Component] Component mounted, fetching data...');
  fetchData();
}, [fetchData]);
```

### 4. Enhanced React Query Configuration

Ensured consistent React Query configuration across all data hooks:

```javascript
{
  // ...
  staleTime: 1000 * 60 * 5, // 5 minutes
  refetchOnWindowFocus: true, // Ensure data refreshes when the page regains focus
  refetchOnMount: true,
  keepPreviousData: true, // Keep previous data while fetching new data
}
```

### 5. Improved Error Handling and Retry Logic

Added better error handling and retry logic to prevent data disappearing during loading:

```javascript
// Keep track of previous data to prevent data disappearing during loading
const [previousData, setPreviousData] = useState([]);

// In the query function
if (error) {
  // Return previous data on error to prevent data disappearing
  if (previousData.length > 0) {
    console.log('[Hook] Returning previous data due to error');
    return previousData;
  }
  throw error;
}

// Update previous data for future use
setPreviousData(formattedData);
```

## Affected Files

The following files were modified:

1. `src/hooks/useNavigationRefresh.ts`
2. `src/pages/TeamDashboard.tsx`
3. `src/components/teams/PermissionManagement.tsx`
4. `src/pages/TaskAutomation.tsx`
5. `src/pages/Maintenance.tsx`
6. `src/pages/Properties.tsx`
7. `src/hooks/useMaintenanceTasksQueryV2.ts`
8. `src/hooks/usePropertiesQueryV2.ts`

## Bug Fixes

### Fixed Missing useQueryClient Import and Reference

In the Properties component, we added the missing useQueryClient import and queryClient reference:

```javascript
// Added import:
import { useQueryClient } from '@tanstack/react-query';

// Before:
const { properties, loading, error, fetchProperties } = usePropertiesQueryV2();

// After:
const { properties, loading, error, fetchProperties } = usePropertiesQueryV2();
const queryClient = useQueryClient();
```

This fixed the "Uncaught ReferenceError: useQueryClient is not defined" error that was occurring in the Properties component.

## Testing

These changes should be tested by:

1. Opening different dashboard views and checking if data loads correctly
2. Switching between tabs or minimizing the browser, then returning to the application
3. Navigating between different views and ensuring data persists
4. Checking the Teams page specifically to ensure it now properly refreshes data

## Future Considerations

1. Consider implementing a unified data refresh strategy across all components
2. Add more comprehensive logging to track data loading issues
3. Implement a global loading state manager to prevent UI flashing during data refreshes
