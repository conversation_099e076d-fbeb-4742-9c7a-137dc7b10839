# Data Loading Debugger - June 17, 2024

This document outlines the implementation of a Data Loading Debugger to help diagnose and fix data loading issues when pages lose focus or when navigating between different views in the StayFu application.

## Overview of Issues

The application was experiencing several issues related to data loading:

1. Data would disappear or fail to load when the application regained focus after being in the background
2. Some pages would load data correctly when returning from out-of-focus, while others wouldn't
3. Specific routes like `/#/properties/[id]`, `/#/maintenance`, `/#/damages`, `/#/teams`, and `/#/settings/appearance` were having issues

## Working Routes vs. Problematic Routes

### Working Routes:
- `/#/dashboard`
- `/#/operations`
- `/#/properties`
- `/#/maintenance/automation`
- `/#/purchase-orders`

### Problematic Routes:
- `/#/properties/375c8d62-63af-4fd7-90b3-b3c3f2efbec6` (and other property detail pages)
- `/#/maintenance`
- `/#/damages`
- `/#/teams`
- `/#/settings/appearance`

## Implemented Debugging Tools

### 1. Data Loading Debugger Component

Created a new `DataLoadingDebugger.tsx` component that:

- Tracks window focus and visibility events
- Shows the current state of the React Query cache
- Provides information about query keys for the current route
- Allows forcing a refresh of the current route

```typescript
const DataLoadingDebugger: React.FC = () => {
  // Implementation details...
};
```

### 2. Debug Page

Created a new `DataLoadingDebugPage.tsx` page that:

- Provides a UI for testing problematic routes
- Allows refreshing specific query keys
- Shows the Data Loading Debugger component
- Provides buttons to navigate to different routes

```typescript
const DataLoadingDebugPage: React.FC = () => {
  // Implementation details...
};
```

### 3. Enhanced Navigation Refresh

Updated the `handleNavigation` function in `VerticalSidebar.tsx` to:

- Use async/await for better error handling
- Add more debug logging
- Dispatch a custom event to notify that navigation has occurred

```typescript
const handleNavigation = useCallback(async (path: string, e: React.MouseEvent) => {
  // Implementation details...
}, [navigate, refreshRouteData, location.pathname]);
```

### 4. Debug Utilities

Created a new `debugUtils.ts` file with functions to:

- Debug the React Query cache for specific query keys
- Force refresh all queries for a specific route
- Check if a query exists in the cache

```typescript
export const debugQueryCache = (
  queryClient: QueryClient,
  queryKey: string | unknown[]
): {
  exists: boolean;
  isStale: boolean;
  lastUpdated: string | null;
  data: any;
  queryCount: number;
} => {
  // Implementation details...
};
```

## How to Use the Debugger

1. Navigate to `/#/debug/data-loading` to access the debug page
2. Use the "Routes" tab to test problematic routes
3. Use the "Query Keys" tab to refresh specific query keys
4. Use the "Custom" tab to test custom routes or query keys
5. Use the Data Loading Debugger component to track window focus and visibility events

## Debugging Process

When debugging data loading issues:

1. Navigate to a problematic route
2. Switch to another tab or minimize the browser
3. Return to the application
4. Check if data loads correctly
5. If not, use the Data Loading Debugger to see which queries are missing or stale
6. Use the "Force Refresh Current Route" button to refresh the data
7. Check if the data loads correctly after refreshing

## Findings and Recommendations

Based on our analysis, we recommend:

1. **Standardize React Query Configuration**: Ensure all QueryClient configurations use the same settings for `refetchOnWindowFocus`.

2. **Enhance Navigation Refresh Mechanism**: Update the `routeQueryKeyMap` in `useNavigationRefresh.ts` to include all necessary query keys for problematic routes.

3. **Remove Conflicting Visibility Change Handlers**: Identify and remove any visibility change handlers that might conflict with React Query's built-in window focus handling.

4. **Add Debug Logging**: Add debug logging to help identify which specific queries are failing to refresh.

5. **Update Special Case Handling**: Ensure special case handling for property detail pages and other problematic routes is working correctly.

## Affected Files

1. `src/components/debug/DataLoadingDebugger.tsx` (new file)
2. `src/pages/DataLoadingDebugPage.tsx` (new file)
3. `src/utils/debugUtils.ts` (new file)
4. `src/components/layout/VerticalSidebar.tsx`
5. `src/App.tsx`
6. `src/hooks/useNavigationRefresh.ts`
