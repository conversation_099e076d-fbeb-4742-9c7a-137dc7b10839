// This script sets up the storage bucket and policies using the Supabase JS client
// NOTE: This is a development setup script. For production, use environment variables.
import { createClient } from '@supabase/supabase-js';

// Supabase URL and key - Development configuration only
// TODO: For production deployments, use environment variables
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || "https://pwaeknalhosfwuxkpaet.supabase.co";
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_PUBLISHABLE_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB3YWVrbmFsaG9zZnd1eGtwYWV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEwMjk1MzMsImV4cCI6MjA1NjYwNTUzM30.buZMBA9gPIkvp3rIGJl-QeYIO2IXUC8ea_N-AvLJID4";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function setupStorage() {
  try {
    console.log('Setting up storage buckets and policies...');
    console.log('Using Supabase URL:', SUPABASE_URL);

    // Check if the inventory bucket exists
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      throw new Error(`Error listing buckets: ${bucketsError.message}`);
    }

    console.log('Existing buckets:', buckets ? buckets.map(b => b.name).join(', ') : 'none');

    // Try to list files in the inventory bucket
    const { data: files, error: filesError } = await supabase.storage
      .from('inventory')
      .list();
    
    if (filesError) {
      console.error('Error listing files in inventory bucket:', filesError.message);
    } else {
      console.log('Files in inventory bucket:', files ? files.map(f => f.name).join(', ') : 'none');
    }

    // Try to create a folder in the inventory bucket
    console.log('Creating inventory-images folder...');
    const { error: folderError } = await supabase.storage
      .from('inventory')
      .upload('inventory-images/.keep', new Blob([''], { type: 'text/plain' }), {
        upsert: true
      });
    
    if (folderError) {
      console.error('Error creating folder:', folderError.message);
    } else {
      console.log('Folder created successfully');
    }

    // Create a test file to verify permissions
    console.log('Creating test file...');
    const testContent = new Blob(['This is a test file to verify storage permissions'], { type: 'text/plain' });
    const { error: uploadError } = await supabase.storage
      .from('inventory')
      .upload('test.txt', testContent, {
        upsert: true
      });
    
    if (uploadError) {
      throw new Error(`Error uploading test file: ${uploadError.message}`);
    }
    
    // Get the public URL of the test file
    const { data: { publicUrl } } = supabase.storage
      .from('inventory')
      .getPublicUrl('test.txt');
    
    console.log('Test file created successfully');
    console.log('Public URL:', publicUrl);
    
    console.log('Storage setup completed successfully');
  } catch (error) {
    console.error('Error setting up storage:', error);
  }
}

setupStorage();
