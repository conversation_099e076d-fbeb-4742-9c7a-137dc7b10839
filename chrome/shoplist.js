// Global variables
let shoppingLists = [];
let currentListId = null;
let currentProducts = [];
let propertiesData = [];
let isConnectedToStayfu = false;
let detailedProductsCache = {};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Set up tabs
    setupTabs();
    
    // Load shopping lists from storage
    loadShoppingLists();
    
    // Load detailed products cache
    loadDetailedProductsCache();
    
    // Set up event listeners
    document.getElementById('searchForm').addEventListener('submit', handleSearchSubmit);
    document.getElementById('addList').addEventListener('click', showAddListDialog);
    document.getElementById('csvFile').addEventListener('change', handleCsvFileSelection);
    document.getElementById('saveSettings').addEventListener('click', saveStayfuSettings);
    document.getElementById('testConnection').addEventListener('click', testStayfuConnection);
    document.getElementById('importToStayfu').addEventListener('click', importToStayfu);
    
    // Initialize custom file upload button
    document.getElementById('customCsvButton').addEventListener('click', function() {
        document.getElementById('csvFile').click();
    });
    
    // Check for import parameter in URL and connection status
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('import') === 'true') {
        checkConnectionStatus().then(() => {
            if (isConnectedToStayfu) {
                document.querySelector('.tab[data-tab="import"]').click();
                loadProductsData();
            } else {
                document.querySelector('.tab[data-tab="settings"]').click();
                showToast('Please configure your StayFu connection first', 'warning');
            }
        });
    } else {
        // Regular initialization
        loadStayfuSettings();
        checkConnectionStatus();
    }
});

// Set up tab functionality
function setupTabs() {
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const targetTab = tab.dataset.tab;
            
            // Update active tab
            tabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            
            // Show corresponding content
            tabContents.forEach(content => {
                content.classList.remove('active');
                if (content.id === `${targetTab}-tab`) {
                    content.classList.add('active');
                }
            });
        });
    });
}

// Load shopping lists from storage
function loadShoppingLists() {
    chrome.storage.local.get('shoppingLists', function(data) {
        if (data.shoppingLists) {
            shoppingLists = data.shoppingLists;
            renderShoppingLists();
        }
    });
}

// Load detailed products cache
function loadDetailedProductsCache() {
    chrome.storage.local.get('detailedProducts', function(data) {
        if (data.detailedProducts) {
            detailedProductsCache = data.detailedProducts;
            console.log('Loaded detailed products cache:', Object.keys(detailedProductsCache).length, 'products');
        }
    });
}

// Render shopping lists in the sidebar
function renderShoppingLists() {
    const listElement = document.getElementById('shoppingLists');
    listElement.innerHTML = '';
    
    shoppingLists.forEach(list => {
        const listItem = document.createElement('li');
        listItem.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                <span>${list.name}</span>
                <div>
                    <button class="load-list-btn" data-id="${list.id}" style="width: auto; padding: 4px 8px; margin-right: 4px">Load</button>
                    <button class="delete-list-btn" data-id="${list.id}" style="width: auto; padding: 4px 8px; background-color: #f87171;">×</button>
                </div>
            </div>
        `;
        listElement.appendChild(listItem);
    });
    
    // Add event listeners to buttons
    document.querySelectorAll('.load-list-btn').forEach(button => {
        button.addEventListener('click', function() {
            loadList(this.dataset.id);
        });
    });
    
    document.querySelectorAll('.delete-list-btn').forEach(button => {
        button.addEventListener('click', function() {
            deleteList(this.dataset.id);
        });
    });
}

// Show dialog to add a new list
function showAddListDialog() {
    const listName = prompt('Enter a name for the new list:');
    if (listName) {
        addNewList(listName);
    }
}

// Add a new shopping list
function addNewList(name) {
    const newList = {
        id: Date.now().toString(),
        name: name,
        items: []
    };
    
    shoppingLists.push(newList);
    saveShoppingLists();
    renderShoppingLists();
}

// Save shopping lists to storage
function saveShoppingLists() {
    chrome.storage.local.set({shoppingLists: shoppingLists});
}

// Load a specific list
function loadList(listId) {
    const list = shoppingLists.find(list => list.id === listId);
    if (list) {
        currentListId = listId;
        
        // Clear existing inputs
        document.getElementById('commaSeparated').value = '';
        document.getElementById('lineSeparated').value = list.items.join('\n');
        
        // Scroll to the form
        document.getElementById('searchForm').scrollIntoView({behavior: 'smooth'});
    }
}

// Delete a shopping list
function deleteList(listId) {
    if (confirm('Are you sure you want to delete this list?')) {
        shoppingLists = shoppingLists.filter(list => list.id !== listId);
        saveShoppingLists();
        renderShoppingLists();
        
        if (currentListId === listId) {
            currentListId = null;
        }
    }
}

// Handle the search form submission
function handleSearchSubmit(event) {
    event.preventDefault();
    
    // Get items from inputs
    let items = [];
    const commaSeparated = document.getElementById('commaSeparated').value.trim();
    const lineSeparated = document.getElementById('lineSeparated').value.trim();
    
    if (commaSeparated) {
        items = commaSeparated.split(',').map(item => item.trim()).filter(item => item);
    } else if (lineSeparated) {
        items = lineSeparated.split('\n').map(item => item.trim()).filter(item => item);
    }
    
    if (items.length === 0) {
        alert('Please enter at least one item to search for.');
        return;
    }
    
    // Save to current list if one is selected
    if (currentListId) {
        const listIndex = shoppingLists.findIndex(list => list.id === currentListId);
        if (listIndex !== -1) {
            shoppingLists[listIndex].items = items;
            saveShoppingLists();
        }
    }
    
    // Start the search
    chrome.runtime.sendMessage({
        action: 'startSearch',
        items: items
    });
    
    // Show searching message
    const searchSection = document.querySelector('.section');
    const searchingMsg = document.createElement('div');
    searchingMsg.id = 'searching-message';
    searchingMsg.className = 'alert alert-warning';
    searchingMsg.innerHTML = '<p><strong>Searching Amazon...</strong></p><p>This may take a few moments. New tabs will open to scrape data and will close automatically when finished.</p>';
    searchSection.appendChild(searchingMsg);
    
    // Remove message after 30 seconds (should be done by then)
    setTimeout(() => {
        const msg = document.getElementById('searching-message');
        if (msg) msg.remove();
    }, 30000);
}

// Handle CSV file selection
function handleCsvFileSelection(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = function(e) {
        const contents = e.target.result;
        processCsvData(contents);
    };
    reader.readAsText(file);
}

// Process CSV data
function processCsvData(csvData) {
    const lines = csvData.split('\n');
    if (lines.length <= 1) {
        alert('CSV file appears to be empty or has only a header row.');
        return;
    }
    
    // Skip header row, extract first column (assumed to be the item name)
    const items = [];
    for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line) {
            const columns = line.split(',');
            if (columns.length > 0 && columns[0].trim()) {
                items.push(columns[0].trim());
            }
        }
    }
    
    if (items.length === 0) {
        alert('No valid items found in the CSV file.');
        return;
    }
    
    // Populate the line-separated textarea
    document.getElementById('lineSeparated').value = items.join('\n');
    
    // If a list is selected, update it
    if (currentListId) {
        const listIndex = shoppingLists.findIndex(list => list.id === currentListId);
        if (listIndex !== -1) {
            shoppingLists[listIndex].items = items;
            saveShoppingLists();
        }
    }
}

// Check StayFu connection status
async function checkConnectionStatus() {
    const statusElement = document.getElementById('connection-status');
    if (!statusElement) return; // Element not found, might be on a different tab
    
    statusElement.className = 'alert alert-warning';
    statusElement.textContent = 'Checking connection to StayFu...';
    
    try {
        const response = await new Promise((resolve) => {
            chrome.runtime.sendMessage({ action: 'checkStayfuConnection' }, resolve);
        });
        
        if (response.connected) {
            statusElement.className = 'alert alert-success';
            statusElement.textContent = 'Connected to StayFu';
            isConnectedToStayfu = true;
            
            // If we're connected, fetch properties
            fetchStayfuProperties();
        } else {
            statusElement.className = 'alert alert-danger';
            statusElement.textContent = response.error || 'Not connected to StayFu';
            isConnectedToStayfu = false;
        }
    } catch (error) {
        statusElement.className = 'alert alert-danger';
        statusElement.textContent = error.message || 'Connection check failed';
        isConnectedToStayfu = false;
    }
    
    return isConnectedToStayfu;
}

// Load StayFu settings
function loadStayfuSettings() {
    chrome.runtime.sendMessage({action: 'getStayfuSettings'}, function(response) {
        if (response && response.url) {
            document.getElementById('stayfuUrl').value = response.url;
        }
    });
}

// Save StayFu settings with improved validation
async function saveStayfuSettings() {
    const url = document.getElementById('stayfuUrl').value.trim();
    const token = document.getElementById('stayfuApiToken').value.trim();
    
    if (!url) {
        showToast('Please enter the StayFu URL', 'error');
        return;
    }
    
    // Show saving indicator
    const saveBtn = document.getElementById('saveSettings');
    const originalText = saveBtn.textContent;
    saveBtn.textContent = 'Saving...';
    saveBtn.disabled = true;
    
    try {
        // Save URL first
        await new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({
                action: 'setStayfuUrl',
                url: url
            }, response => {
                if (!response || !response.success) {
                    reject(new Error('Failed to save URL'));
                } else {
                    resolve();
                }
            });
        });
        
        // If token provided, save it
        if (token) {
            await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    action: 'setStayfuToken',
                    token: token
                }, response => {
                    if (!response || !response.success) {
                        reject(new Error(response?.error || 'Failed to save token'));
                    } else {
                        resolve();
                    }
                });
            });
            
            // Clear the token input for security
            document.getElementById('stayfuApiToken').value = '';
            
            showToast('Settings saved successfully!', 'success');
        } else {
            showToast('URL saved. Please add an API token.', 'warning');
        }
    } catch (error) {
        showToast(error.message || 'Failed to save settings', 'error');
    } finally {
        saveBtn.textContent = originalText;
        saveBtn.disabled = false;
        checkConnectionStatus();
    }
}

// Show toast message
function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.style.position = 'fixed';
        toastContainer.style.bottom = '20px';
        toastContainer.style.right = '20px';
        toastContainer.style.zIndex = '1000';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `alert alert-${type}`;
    toast.style.minWidth = '250px';
    toast.style.marginTop = '10px';
    toast.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
    toast.style.animation = 'fadeIn 0.3s ease-out';
    toast.innerHTML = message;
    
    toastContainer.appendChild(toast);
    
    // Remove toast after 4 seconds
    setTimeout(() => {
        toast.style.animation = 'fadeOut 0.3s ease-out';
        setTimeout(() => {
            if (toastContainer.contains(toast)) {
                toastContainer.removeChild(toast);
            }
        }, 300);
    }, 4000);
}

// Create toast animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    @keyframes fadeOut {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(20px); }
    }
`;
document.head.appendChild(style);

// Test the connection to StayFu
function testStayfuConnection() {
    checkConnectionStatus();
}

// Fetch properties from StayFu
function fetchStayfuProperties() {
    if (!isConnectedToStayfu) return;
    
    const propertySelect = document.getElementById('propertyId');
    propertySelect.innerHTML = '<option value="">Loading properties...</option>';
    propertySelect.disabled = true;

    // Fetch properties from the background script
    chrome.runtime.sendMessage({ action: 'getStayfuProperties' }, function(response) {
        console.log('Properties response:', response);
        if (chrome.runtime.lastError) {
            console.error('Error fetching properties:', chrome.runtime.lastError);
            propertySelect.innerHTML = '<option value="">Error loading properties</option>';
            showToast(`Error fetching properties: ${chrome.runtime.lastError.message}`, 'error');
            propertySelect.disabled = true; // Keep disabled on error
            return;
        }

        if (response && response.success) {
            propertiesData = response.properties || [];
            if (propertiesData.length > 0) {
                populatePropertySelect(propertySelect, propertiesData);
                propertySelect.disabled = false;
            } else {
                propertySelect.innerHTML = '<option value="">No properties found</option>';
                showToast('No properties found in your StayFu account.', 'warning');
            }
        } else {
            console.error('Failed to fetch properties:', response?.error);
            propertySelect.innerHTML = '<option value="">Failed to load properties</option>';
            showToast(`Failed to load properties: ${response?.error || 'Unknown error'}`, 'error');
            propertySelect.disabled = true; // Keep disabled on error
        }
    });
}

// Populate the property select dropdown
function populatePropertySelect(selectElement, properties) {
    console.log('Populating property select with:', properties);
    selectElement.innerHTML = '';
    selectElement.innerHTML += '<option value="">Select a property</option>';
    
    // Verify properties is an array and has items
    if (Array.isArray(properties) && properties.length > 0) {
        properties.forEach(property => {
            // Ensure property has id and name fields
            if (property && property.id && property.name) {
                selectElement.innerHTML += `<option value="${property.id}">${property.name}</option>`;
            }
        });
    } else {
        console.warn('Properties data is not in expected format:', properties);
    }
}

// Load product data if available
function loadProductsData() {
    chrome.storage.local.get(['products', 'selectedProducts'], function(data) {
        let productsToUse = [];
        
        // First check for selected products from list.html
        if (data.selectedProducts && data.selectedProducts.length > 0) {
            productsToUse = data.selectedProducts;
            showToast(`Loaded ${productsToUse.length} selected products for import`, 'success');
        } 
        // Fall back to all products if available
        else if (data.products && data.products.length > 0) {
            // Flatten the product data
            productsToUse = [];
            data.products.forEach(productGroup => {
                productGroup.productDetails.forEach(product => {
                    product.searchTerm = productGroup.searchTerm;
                    productsToUse.push(product);
                });
            });
        }
        
        if (productsToUse.length > 0) {
            // Enrich with detailed data if available
            currentProducts = enrichWithDetailedData(productsToUse);
            updateImportUI();
        }
    });
}

// Enrich product data with detailed information
function enrichWithDetailedData(products) {
    // Enrich with detailed data if available
    return products.map(product => {
        if (detailedProductsCache[product.asin]) {
            // Create a new object that merges the basic product with detailed data
            // but prioritize original data when both have the same fields
            const detailedData = detailedProductsCache[product.asin];
            return {
                ...detailedData,
                ...product,
                // Make sure critical fields from both sources are preserved
                details: {
                    ...detailedData.details,
                    searchTerm: product.searchTerm
                }
            };
        }
        return product;
    });
}

// Update the import UI based on available products
function updateImportUI() {
    const noDataMessage = document.getElementById('no-data-message');
    const importSection = document.getElementById('import-section');
    const productCountElement = document.getElementById('product-count');
    
    if (currentProducts.length > 0) {
        noDataMessage.classList.add('hidden');
        importSection.classList.remove('hidden');
        
        // Update product count
        productCountElement.className = 'alert alert-success';
        productCountElement.textContent = `${currentProducts.length} products found`;
        
        // Display product preview
        displayProductPreview();
        
        // Automatically switch to the import tab if directed to do so
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('import') === 'true') {
            document.querySelector('.tab[data-tab="import"]').click();
        }
    } else {
        noDataMessage.classList.remove('hidden');
        importSection.classList.add('hidden');
    }
}

// Display a preview of products to be imported
function displayProductPreview() {
    const importSection = document.getElementById('import-section');
    
    // Remove any existing preview
    const existingPreview = document.getElementById('product-preview');
    if (existingPreview) {
        existingPreview.remove();
    }
    
    // Create a preview container
    const previewContainer = document.createElement('div');
    previewContainer.id = 'product-preview';
    previewContainer.className = 'mt-4';
    
    // Create preview heading
    const previewHeading = document.createElement('h3');
    previewHeading.className = 'text-lg font-medium mb-2';
    previewHeading.textContent = 'Product Preview';
    previewContainer.appendChild(previewHeading);
    
    // Create product grid
    const productGrid = document.createElement('div');
    productGrid.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';
    
    // Limit to first 6 products for preview
    const previewProducts = currentProducts.slice(0, 6);
    
    previewProducts.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = 'border rounded p-2 flex items-start';
        
        // Product image
        const imgContainer = document.createElement('div');
        imgContainer.className = 'w-16 h-16 mr-2 flex-shrink-0';
        const img = document.createElement('img');
        img.src = product.img || 'placeholder.png';
        img.alt = product.title;
        img.className = 'w-full h-full object-contain';
        imgContainer.appendChild(img);
        
        // Product info
        const infoContainer = document.createElement('div');
        infoContainer.className = 'flex-grow overflow-hidden';
        
        const title = document.createElement('div');
        title.className = 'font-medium text-sm truncate';
        title.textContent = product.title;
        
        const price = document.createElement('div');
        price.className = 'text-gray-700 text-xs';
        price.textContent = product.price || 'Price not available';
        
        const asin = document.createElement('div');
        asin.className = 'text-gray-500 text-xs';
        asin.textContent = `ASIN: ${product.asin}`;
        
        infoContainer.appendChild(title);
        infoContainer.appendChild(price);
        infoContainer.appendChild(asin);
        
        productCard.appendChild(imgContainer);
        productCard.appendChild(infoContainer);
        
        productGrid.appendChild(productCard);
    });
    
    previewContainer.appendChild(productGrid);
    
    // Add "Show more" button if there are more than 6 products
    if (currentProducts.length > 6) {
        const showMoreButton = document.createElement('button');
        showMoreButton.className = 'button-secondary mt-2';
        showMoreButton.textContent = `Show all ${currentProducts.length} products`;
        showMoreButton.addEventListener('click', showAllProducts);
        previewContainer.appendChild(showMoreButton);
    }
    
    // Add the preview to the import section
    importSection.appendChild(previewContainer);
}

// Show all products in a modal
function showAllProducts() {
    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalOverlay.id = 'product-modal';
    
    // Create modal content
    const modalContent = document.createElement('div');
    modalContent.className = 'bg-white rounded-lg p-4 max-w-4xl max-h-[90vh] overflow-auto relative';
    
    // Close button
    const closeButton = document.createElement('button');
    closeButton.className = 'absolute top-2 right-2 text-gray-500 hover:text-gray-800';
    closeButton.innerHTML = '&times;';
    closeButton.addEventListener('click', () => {
        document.body.removeChild(modalOverlay);
    });
    
    // Modal header
    const modalHeader = document.createElement('div');
    modalHeader.className = 'mb-4 border-b pb-2';
    const modalTitle = document.createElement('h3');
    modalTitle.className = 'text-xl font-medium';
    modalTitle.textContent = 'All Products';
    modalHeader.appendChild(modalTitle);
    
    // Product list
    const productList = document.createElement('div');
    productList.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';
    
    currentProducts.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = 'border rounded p-3';
        
        const productImg = document.createElement('img');
        productImg.src = product.img || 'placeholder.png';
        productImg.alt = product.title;
        productImg.className = 'w-full h-32 object-contain mb-2';
        
        const productTitle = document.createElement('div');
        productTitle.className = 'font-medium mb-1';
        productTitle.textContent = product.title;
        
        const productPrice = document.createElement('div');
        productPrice.className = 'text-gray-700';
        productPrice.textContent = product.price || 'Price not available';
        
        const productAsin = document.createElement('div');
        productAsin.className = 'text-gray-500 text-sm mt-1';
        productAsin.textContent = `ASIN: ${product.asin}`;
        
        productCard.appendChild(productImg);
        productCard.appendChild(productTitle);
        productCard.appendChild(productPrice);
        productCard.appendChild(productAsin);
        
        productList.appendChild(productCard);
    });
    
    // Assemble modal
    modalContent.appendChild(closeButton);
    modalContent.appendChild(modalHeader);
    modalContent.appendChild(productList);
    modalOverlay.appendChild(modalContent);
    
    // Add modal to body
    document.body.appendChild(modalOverlay);
    
    // Close when clicking outside the modal
    modalOverlay.addEventListener('click', (event) => {
        if (event.target === modalOverlay) {
            document.body.removeChild(modalOverlay);
        }
    });
}

// Import data to StayFu
function importToStayfu() {
    if (!isConnectedToStayfu) {
        showToast('Not connected to StayFu. Please check your settings.', 'error');
        document.querySelector('.tab[data-tab="settings"]').click();
        return;
    }
    
    if (currentProducts.length === 0) {
        showToast('No products to import. Please search for products first.', 'error');
        return;
    }
    
    const importType = document.getElementById('importType').value;
    const propertyId = document.getElementById('propertyId').value;
    const collection = document.getElementById('collection').value;
    
    if (!propertyId) {
        showToast('Please select a property.', 'error');
        document.getElementById('propertyId').focus();
        return;
    }
    
    // Disable import button and show loading state
    const importButton = document.getElementById('importToStayfu');
    const originalButtonText = importButton.textContent;
    importButton.textContent = 'Importing...';
    importButton.disabled = true;
    
    const importData = {
        importType: importType,
        propertyId: propertyId,
        collection: collection,
        products: currentProducts
    };
    
    const resultElement = document.getElementById('import-result');
    resultElement.innerHTML = 'Importing data to StayFu...';
    resultElement.className = 'alert alert-warning';
    resultElement.classList.remove('hidden');
    
    // Send data to StayFu
    if (importType === 'inventory') {
        chrome.runtime.sendMessage({
            action: 'importToInventory',
            data: importData
        }, response => handleImportResponse(response, importButton, originalButtonText));
    } else {
        chrome.runtime.sendMessage({
            action: 'sendToStayfuPurchaseOrder', 
            data: importData
        }, response => handleImportResponse(response, importButton, originalButtonText));
    }
}

// Handle import response
function handleImportResponse(response, importButton, originalButtonText) {
    // Reset button state
    importButton.textContent = originalButtonText;
    importButton.disabled = false;
    
    const resultElement = document.getElementById('import-result');
    
    if (response && response.success) {
        resultElement.innerHTML = `
            <p><strong>Import successful!</strong></p>
            <p>${response.data?.message || 'Data was successfully imported to StayFu.'}</p>
            ${response.data?.stats ? `
                <div class="mt-2">
                    <p><strong>Import Statistics:</strong></p>
                    <ul>
                        <li>Items imported: ${response.data.stats.imported || 0}</li>
                        <li>Items updated: ${response.data.stats.updated || 0}</li>
                        ${response.data.stats.errors ? `<li>Errors: ${response.data.stats.errors}</li>` : ''}
                    </ul>
                </div>
            ` : ''}
        `;
        resultElement.className = 'alert alert-success';
        
        // Display a link to view the imported items in StayFu
        if (response.data?.url) {
            resultElement.innerHTML += `
                <div class="mt-3">
                    <a href="${response.data.url}" target="_blank" class="button-primary" style="display: inline-block;">
                        View in StayFu
                    </a>
                </div>
            `;
        }
        
        showToast('Import completed successfully!', 'success');
    } else {
        resultElement.innerHTML = `
            <p><strong>Import failed!</strong></p>
            <p>${response?.error || 'An unknown error occurred during import.'}</p>
            ${response?.details ? `<p>Details: ${response.details}</p>` : ''}
            <p class="mt-2">Please check your connection settings and try again. If the problem persists, contact support.</p>
        `;
        resultElement.className = 'alert alert-danger';
        
        showToast('Import failed. See details below.', 'error');
    }
}
