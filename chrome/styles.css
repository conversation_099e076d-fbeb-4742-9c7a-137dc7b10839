/* Reset some default browser styles */
body, h1, div, input, p, h3 {
    margin: 0;
    padding: 0;
}

/* Base styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    background-color: #f9fafb;
    color: #1e293b;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    line-height: 1.5;
}

/* Container for the entire content */
.container {
    max-width: 800px;
    margin: 40px auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3 {
    color: #0f172a;
    margin-top: 0;
}

h1 {
    font-size: 24px;
    font-weight: normal;
    margin-bottom: 30px;
}

h2 {
    font-size: 20px;
}

h3 {
    font-size: 16px;
}

/* Logo */
.logo {
    font-size: 24px;
    font-weight: 600;
    color: #4f46e5;
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 32px;
}

/* Sections */
.section {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid #e2e8f0;
}

/* Form elements */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #334155;
}

.form-group small {
    display: block;
    margin-top: 4px;
    color: #64748b;
    font-size: 12px;
}

.form-control {
    width: 100%;
    padding: 10px;
    border-radius: 6px;
    border: 1px solid #cbd5e1;
    font-size: 14px;
    transition: border-color 0.15s ease;
}

.form-control:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

/* Buttons */
.button-group {
    display: flex;
    gap: 12px;
    margin-top: 24px;
}

.button-primary {
    padding: 10px 16px;
    border-radius: 6px;
    border: none;
    background-color: #4f46e5;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.15s ease;
}

.button-primary:hover {
    background-color: #4338ca;
}

.button-secondary {
    padding: 10px 16px;
    border-radius: 6px;
    border: 1px solid #cbd5e1;
    background-color: white;
    color: #334155;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
}

.button-secondary:hover {
    background-color: #f8fafc;
    border-color: #94a3b8;
}

/* Alerts */
.alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 14px;
}

.alert-success {
    background-color: #dcfce7;
    border: 1px solid #86efac;
    color: #166534;
}

.alert-warning {
    background-color: #fef9c3;
    border: 1px solid #fde047;
    color: #854d0e;
}

.alert-error {
    background-color: #fee2e2;
    border: 1px solid #fca5a5;
    color: #b91c1c;
}

/* Info Grid */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.info-grid label {
    font-weight: 500;
    color: #64748b;
    display: block;
    margin-bottom: 4px;
}

/* Links */
a {
    color: #4f46e5;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 24px;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

th {
    font-weight: 500;
    color: #334155;
    background-color: #f8fafc;
}

/* Loading states */
.loading {
    opacity: 0.5;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: calc(50% - 0.5em);
    left: calc(50% - 0.5em);
    width: 1em;
    height: 1em;
    border: 2px solid #4f46e5;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 0.6s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Utility classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mt-4 {
    margin-top: 16px;
}

.mb-4 {
    margin-bottom: 16px;
}

/* Enhanced styles for a modern and polished look */

/* Common Colors */
:root {
    --primary-color: #007bff;
    --secondary-color: #f9f9f9;
    --border-color: #ccc;
    --text-color: #333;
    --success-color: green;
}

/* Enhanced Product Card */
.product-card {
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.product-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transform: translateY(-5px);
}

.product-card h3 {
    color: var(--text-color);
    font-weight: normal;
}

.product-card p {
    color: var(--success-color);
}

/* Enhanced Button Styles */
.button {
    background-color: var(--primary-color);
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.button:hover {
    background-color: darken(var(--primary-color), 10%);
}

/* Enhanced Layout */
.container {
    padding: 40px;
}