// imageProcessor.js - Utility for downloading and processing images in the Chrome extension

/**
 * Downloads an image from a URL and converts it to a base64 data URL
 * @param {string} imageUrl - The URL of the image to download
 * @returns {Promise<string>} - A Promise that resolves to the base64 data URL of the image
 */
async function downloadImageAsDataUrl(imageUrl) {
  try {
    console.log(`[Extension] Downloading image: ${imageUrl}`);
    
    // Fetch the image
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
    }
    
    // Get the image as a blob
    const imageBlob = await response.blob();
    
    // Convert the blob to a data URL
    return await blobToDataUrl(imageBlob);
  } catch (error) {
    console.error(`[Extension] Error downloading image: ${error}`);
    return null;
  }
}

/**
 * Converts a blob to a base64 data URL
 * @param {Blob} blob - The blob to convert
 * @returns {Promise<string>} - A Promise that resolves to the base64 data URL
 */
function blobToDataUrl(blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.onerror = () => reject(new Error('Failed to convert blob to data URL'));
    reader.readAsDataURL(blob);
  });
}

/**
 * Compresses an image to reduce its size
 * @param {string} dataUrl - The data URL of the image to compress
 * @param {number} maxWidth - The maximum width of the compressed image
 * @param {number} quality - The quality of the compressed image (0-1)
 * @returns {Promise<string>} - A Promise that resolves to the compressed image as a data URL
 */
async function compressImage(dataUrl, maxWidth = 800, quality = 0.8) {
  return new Promise((resolve, reject) => {
    try {
      const img = new Image();
      img.onload = () => {
        // Create a canvas element
        const canvas = document.createElement('canvas');
        
        // Calculate the new dimensions
        let width = img.width;
        let height = img.height;
        
        if (width > maxWidth) {
          const ratio = maxWidth / width;
          width = maxWidth;
          height = height * ratio;
        }
        
        // Set canvas dimensions
        canvas.width = width;
        canvas.height = height;
        
        // Draw the image on the canvas
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0, width, height);
        
        // Convert canvas to data URL
        const compressedDataUrl = canvas.toDataURL('image/jpeg', quality);
        resolve(compressedDataUrl);
      };
      
      img.onerror = () => {
        reject(new Error('Failed to load image for compression'));
      };
      
      img.src = dataUrl;
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Downloads and compresses an image from a URL
 * @param {string} imageUrl - The URL of the image to download and compress
 * @param {number} maxWidth - The maximum width of the compressed image
 * @param {number} quality - The quality of the compressed image (0-1)
 * @returns {Promise<string>} - A Promise that resolves to the compressed image as a data URL
 */
async function downloadAndCompressImage(imageUrl, maxWidth = 800, quality = 0.8) {
  try {
    // Download the image
    const dataUrl = await downloadImageAsDataUrl(imageUrl);
    if (!dataUrl) {
      return null;
    }
    
    // Compress the image
    return await compressImage(dataUrl, maxWidth, quality);
  } catch (error) {
    console.error(`[Extension] Error processing image: ${error}`);
    return null;
  }
}

// Export the functions
window.imageProcessor = {
  downloadImageAsDataUrl,
  compressImage,
  downloadAndCompressImage
};
