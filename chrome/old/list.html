<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>List</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            cursor: pointer;
            margin-right: 10px;
        }
        .product-card {
    border: 1px solid;
    padding: 10px;
    margin: 5px;
    display: inline-block;
    width: 200px; /* Adjust the width */
    height: 200px; /* Adjust the height */
    text-align: center;
    transition: border 0.3s ease;
    overflow: hidden; /* Hide overflow content */
}
        
        .product-image {
            width: 80px;
            height: 80px;
            border-radius: 10px;
        }
        h4 {
            font-size: 10px;
            margin: 5px 0;
        }
        p {
            color: green;
            margin: 5px 0;
        }
        .row {
            display: flex;
            flex-wrap: nowrap; /* Prevent wrapping to allow horizontal scrolling */
            overflow-x: auto; /* Enable horizontal scrolling */
            align-items: center;
        }
        .search-term {
            flex-shrink: 0;
            padding: 10px;
            font-weight: bold;
        }
        .see-more {
            flex-shrink: 0;
            padding: 10px;
            cursor: pointer;
            color: #007bff;
        }
        .button-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background-color: white;
            text-align: center;
            padding: 10px;
            border-bottom: 1px solid #ccc;
            z-index: 1000;
        }
        .content {
            padding-top: 60px; /* Space for the button container */
        }
    </style>
</head>
<body>
    <div class="button-container">
        <button id="newListButton" class="button">New List</button>
        <input type="submit" value="Add to Cart" id="addToCartButton" class="button">
        <button id="addToList" class="button">Add to List</button>
    </div>
    <div class="content">
        <form id="productForm">
            <div id="productTable"></div>
        </form>
    </div>
    <script src="list.js"></script>
<a href="#" id="download-log-link">Download Log</a>
</body>
</html>