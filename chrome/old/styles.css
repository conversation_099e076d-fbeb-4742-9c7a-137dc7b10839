/* Reset some default browser styles */
body, h1, div, input, p, h3 {
    margin: 0;
    padding: 0;
}

/* Container for the entire content */
.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* Heading */
h1 {
    font-family: Arial, sans-serif;
    font-size: 24px;
    margin-bottom: 20px;
}

/* Search Container */
.search-container {
    margin-bottom: 20px;
}

/* Search Input */
.search-input {
    width: 100%;
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

/* Product List */
.product-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

/* Product Card */
.product-card {
    background-color: #f9f9f9;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 10px;
    text-align: center;
}

.product-card h3 {
    font-size: 16px;
    margin-bottom: 10px;
}

.product-card img {
    max-width: 100%;
    height: auto;
    margin-bottom: 10px;
}

.product-card p {
    font-size: 14px;
    margin-bottom: 5px;
}

/* Download Log Link */
#download-log-link {
    display: block;
    margin-top: 20px;
    text-align: center;
    text-decoration: none;
    color: #007bff;
    font-weight: bold;
}
/* Enhanced styles for a modern and polished look */

/* Common Colors */
:root {
    --primary-color: #007bff;
    --secondary-color: #f9f9f9;
    --border-color: #ccc;
    --text-color: #333;
    --success-color: green;
}

/* Enhanced Product Card */
.product-card {
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.product-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transform: translateY(-5px);
}

.product-card h3 {
    color: var(--text-color);
    font-weight: normal;
}

.product-card p {
    color: var(--success-color);
}

/* Enhanced Button Styles */
.button {
    background-color: var(--primary-color);
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.button:hover {
    background-color: darken(var(--primary-color), 10%);
}

/* Enhanced Typography */
h1 {
    color: var(--text-color);
    font-weight: normal;
    margin-bottom: 30px;
}

/* Enhanced Layout */
.container {
    padding: 40px;
}