var globalLog = [];

// Function to retrieve the log
function getLog() {
    return globalLog;
}

chrome.runtime.onInstalled.addListener(function() {
    chrome.tabs.create({url: 'shoplist.html'});
});

let items = [];
let products = [];
let tabCount = 0;
let openedTabs = [];

chrome.action.onClicked.addListener((tab) => {
    chrome.tabs.create({ url: 'shoplist.html' });
  });

chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'startSearch') {
        items = request.items;
        products = [];
        items.forEach(function(item, index) {
            globalLog.push("Opening search tab for term: " + item);
            chrome.tabs.create({url: "https://www.amazon.com/s?k=" + encodeURIComponent(item) + "&searchTerm=" + encodeURIComponent(item)}, function(tab) {
                openedTabs.push(tab.id);
                chrome.tabs.onUpdated.addListener(function listener(tabId, info) {
                    if (info.status === 'complete' && tabId === tab.id) {
                        chrome.tabs.onUpdated.removeListener(listener);
                        // Manifest V3 requires chrome.scripting.executeScript
                        setTimeout(function() {
                             // Check if tab still exists before injecting
                             chrome.tabs.get(tab.id, (existingTab) => {
                                 if (chrome.runtime.lastError || !existingTab) {
                                     console.warn(`BACKGROUND.JS: Tab ${tab.id} not found or closed before script injection for term "${item}". Skipping injection.`);
                                     globalLog.push(`WARN: Tab ${tab.id} closed before injection for term "${item}".`);
                                     // Optional: Handle this case, e.g., decrement expected count if needed
                                 } else {
                                     // Tab exists, proceed with injection
                                     console.log(`BACKGROUND.JS: Attempting to inject content script into tab ${tab.id} for search term "${item}"`);
                                     chrome.scripting.executeScript({
                                         target: { tabId: tab.id },
                                         files: ["content.js"]
                                     }, (injectionResults) => {
                                         if (chrome.runtime.lastError) {
                                             console.error(`BACKGROUND.JS: Script injection failed for tab ${tab.id}: ${chrome.runtime.lastError.message}`);
                                             globalLog.push(`ERROR: Script injection failed for tab ${tab.id}: ${chrome.runtime.lastError.message}`);
                                         } else {
                                             console.log(`BACKGROUND.JS: Content script injected successfully into tab ${tab.id}.`);
                                             globalLog.push(`Successfully injected content script into tab ${tab.id}.`);
                                         }
                                     });
                                 }
                             });
                        }, 2000);
                    }
                });
            });
        });
    } else if (request.action === 'productData') {
        globalLog.push("Received product data with search term: " + request.searchTerm);
        // --- DEBUG LOG ---
        console.log(`BACKGROUND.JS: Received ${request.products.length} products for term "${request.searchTerm}". Titles:`, request.products.map(p => p.title));
        // --- END DEBUG LOG ---
        tabCount += 1;
        products.push({
            searchTerm: request.searchTerm,
            productDetails: request.products // Ensure this structure matches list.js expectation
        });
        if (products.length === items.length) {
            chrome.storage.local.set({products: products}, function() {
                if (chrome.runtime.lastError) {
                    console.error(chrome.runtime.lastError.message);
                } else {
                    openedTabs.forEach(function(tabId) {
                        chrome.tabs.remove(tabId);
                    });
                    chrome.tabs.create({url: chrome.runtime.getURL("list.html")});
                }
            });
        }
    } else if (request.action === 'addToCart') {
        let url = 'https://www.amazon.com/gp/aws/cart/add.html';
        let params = {
            AssociateTag: 'quickshop0760-20'
        };
        request.products.forEach(function(product, index) {
            params['ASIN.' + (index + 1)] = product.asin;
            params['Quantity.' + (index + 1)] = product.quantity;
        });
        url += '?' + new URLSearchParams(params).toString();
        chrome.tabs.create({url: url});
    } else if (request.action === 'saveASINs') {
        let asins = request.asins;
        chrome.storage.local.set({asins: asins}, function() {
            if (chrome.runtime.lastError) {
                console.error(chrome.runtime.lastError.message);
            } else {
                console.log('ASINs saved successfully.');
            }
        });
    }
});
