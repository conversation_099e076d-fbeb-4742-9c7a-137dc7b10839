var globalLog = [];
var searchTerm = decodeURIComponent((new URL(window.location.href)).searchParams.get('searchTerm')) || 'No Search Term';
globalLog.push("Extracted search term from URL: " + searchTerm);
const processedAsins = new Set(); // Keep track of processed ASINs
function getProductData() {
    console.log('Getting product data');
    // Find the product divs
    let productDivs = document.querySelectorAll('div[data-component-type="s-search-result"]');
    let newProducts = []; // Store newly found products in this run
    for (let i = 0; i < productDivs.length; i++) { // Take all product divs
        let div = productDivs[i];
        // Extract the ASIN, image, price, etc.
        let asin = div.getAttribute('data-asin');

        // Skip if ASIN is missing or already processed
        if (!asin || processedAsins.has(asin)) {
            continue;
        }
        let imgElement = div.querySelector('img.s-image');
        let img = imgElement ? imgElement.getAttribute('src') : '';
        let priceElement = div.querySelector('span.a-price span.a-offscreen');
        let price = priceElement ? priceElement.innerText : '';
        // Try various selectors to find the title element, from specific to more general
        // Refined title selectors - prioritize common, specific patterns
        let titleElement =
            div.querySelector('h2 a.a-link-normal span.a-text-normal') || // Most common specific structure
            div.querySelector('h2 a span') ||                   // Structure: h2 > a > span (fallback)
            div.querySelector('h2 span') ||                     // Span directly inside h2 (less common for title)
            div.querySelector('h2 a') ||                        // Link text inside h2 (if no inner span)
            div.querySelector('h2') ||                          // The h2 text itself (broadest)
            // Fallback to original class-based selectors (less likely but kept for safety)
            div.querySelector('span.a-size-medium.a-color-base.a-text-normal') ||
            div.querySelector('span.a-size-base-plus.a-color-base.a-text-normal');

        let title = titleElement ? titleElement.innerText.trim() : '';

        // If the h2 element itself was selected, ensure we didn't grab extra text (like "Sponsored")
        // This is a heuristic and might need refinement based on actual page structure
        if (titleElement && titleElement.tagName === 'H2' && title) {
             // Attempt to find the most likely title span within the h2 if innerText seems too broad
             const innerSpan = titleElement.querySelector('span[dir="auto"]'); // Amazon often uses span[dir="auto"] for titles
             if (innerSpan && innerSpan.innerText) {
                 title = innerSpan.innerText.trim();
             }
             // Simple check to remove common prefixes if they appear at the start
             if (title.startsWith('Sponsored\n')) title = title.substring(10).trim();
        }
        // Add the product to the list
        // Add the new product to the list and mark ASIN as processed
        newProducts.push({asin, img, price, title});
        processedAsins.add(asin);
    }

    // Send the *new* products to the background script if any were found
    if (newProducts.length > 0) {
        console.log('Found new products:', newProducts);
        globalLog.push(`Sending ${newProducts.length} new product(s) with search term: ${searchTerm}`);
            // --- DEBUG LOG ---
            console.log(`CONTENT.JS: Sending ${newProducts.length} products for term "${searchTerm}". Titles:`, newProducts.map(p => p.title));
            // --- END DEBUG LOG ---
            chrome.runtime.sendMessage({action: "productData", products: newProducts, searchTerm: searchTerm});
        } else {
        // console.log('No new products found in this run.');
    }
}

// --- MutationObserver Setup ---
// Callback function to execute when mutations are observed
const observerCallback = (mutationsList, observer) => {
    let relevantChangeDetected = false;
    for (const mutation of mutationsList) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            // Check if any added node IS or CONTAINS a product result container
            for (const node of mutation.addedNodes) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    if (node.matches('div[data-component-type="s-search-result"]') || node.querySelector('div[data-component-type="s-search-result"]')) {
                        relevantChangeDetected = true;
                        break; // Found a relevant change in this mutation's added nodes
                    }
                }
            }
        }
        if (relevantChangeDetected) {
            break; // Found a relevant change in the mutations list
        }
    }

    if (relevantChangeDetected) {
        // console.log('Relevant node change detected, checking for new products...');
        // Use a small delay to allow potentially complex elements to fully render within the container
        setTimeout(getProductData, 100); // 100ms delay
    }
};

// Create an observer instance linked to the callback function
const observer = new MutationObserver(observerCallback);

// Start observing the body for added nodes and subtree changes
observer.observe(document.body, { childList: true, subtree: true });

console.log('MutationObserver started, observing for added nodes...');

// Run once initially to catch products already loaded
console.log('Running initial product check.');
getProductData();