<!DOCTYPE html>
<html>
<head>
    <style>
body {
    font-family: Arial, sans-serif;
    background-color: #303030;
    color: white;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    flex-direction: column;
}
#sidebar {
    position: fixed;
    left: 0;
    width: 25%;
    height: 100vh;
    background-color: #424242;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}
#sidebar h2 {
    color: white;
}
#sidebar ul {
    list-style-type: none;
    padding: 0;
    width: 100%;
}
#sidebar button, #csvFile {
    padding: 5px;
    border-radius: 5px;
    border: none;
    background-color: #3f51b5;
    color: white;
    cursor: pointer;
    margin-bottom: 5px;
    width: 72px;
}
#sidebar button:hover, #csvFile:hover {
    background-color: #283593;
}
#main {
    flex-grow: 1;
    padding-left: 320px;
    display: flex;
    flex-direction: column;
    align-items: center;
}
#main h1, #main p {
    text-align: center;
}
#searchForm {
    display: flex;
    flex-direction: column;
    width: 60%;
}
#commaSeparated, #lineSeparated {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 5px;
    border: none;
    height: 100px;
    width: 100%;
}
input[type="submit"], input[type="reset"] {
    padding: 10px;
    border-radius: 5px;
    border: none;
    background-color: #3f51b5;
    color: white;
    cursor: pointer;
    margin-bottom: 10px;
    width: 100%;
}
input[type="submit"]:hover, input[type="reset"]:hover {
    background-color: #283593;
}

#addList, #customCsvButton {
    padding: 10px;
    border-radius: 5px;
    border: none;
    background-color: #3f51b5;
    color: white;
    text-align: center;
    cursor: pointer;
    margin-bottom: 10px;
}

#addList:hover, #customCsvButton:hover {
    background-color: #283593;
}

    </style>
</head>
<body>
    <div id="sidebar">
        <h2>Shopping Lists</h2>
        <button id="addList" value="add">Add List +</button>
        <ul id="shoppingLists"></ul>
      <!--  <h2>ASIN Lists</h2>
        <ul id="asinLists"></ul>-->
    </div>
    <div id="main">
        <h1>Amazon Shopper</h1>
        <p>Enter your list of items to search for on Amazon. You can enter a comma-separated list, one item per line, or upload a CSV file.</p>
        <form id="searchForm">
            <label for="commaSeparated">Comma-separated list:</label><br>
            <input type="text" id="commaSeparated"><br>
            <label for="lineSeparated">One item per line:</label><br>
            <textarea id="lineSeparated"></textarea><br>
            <label for="csvFile">CSV file: (Row 1 header, then one item per row)</label>            
            <a href="example.csv" download>Example CSV</a>

            <!--  <label for="csvFile">CSV file:</label>-->
            <input type="file" id="csvFile" accept=".csv" style="display: none;"> <!-- Hide default input -->
            <label for="csvFile" id="customCsvButton" class="custom-button">Upload a CSV</label> <!-- Custom Button -->
            <br><br>            
            <input type="submit" value="Start Shopping">
            <input type="reset" value="Clear">
        </form>
    </div>
    <script src="shoplist.js"></script>
</body>
</html>
