function loadShoppingLists() {
    var shoppingLists = JSON.parse(localStorage.getItem('shoppingLists') || '[]');
    var shoppingListsElement = document.getElementById('shoppingLists');
    shoppingListsElement.innerHTML = '';
    shoppingLists.forEach(function(list, index) {
        var li = document.createElement('li');
        li.innerHTML = `
            <input type="text" value="${list.title}">
            <button class="saveButton">Save</button>
            <button class="loadButton">Load</button>
            <button class="deleteButton">Delete</button>
        `;
        li.querySelector('.saveButton').addEventListener('click', function() {
            var title = li.querySelector('input').value;
            var commaSeparated = document.getElementById('commaSeparated').value;
            var lineSeparated = document.getElementById('lineSeparated').value;
            if (commaSeparated) {
                list.items = commaSeparated.split(',').map(function(item) { return item.trim(); });
                list.type = 'comma';
            }
            if (lineSeparated) {
                list.items = lineSeparated.split('\n').map(function(item) { return item.trim(); });
                list.type = 'line';
            }
            shoppingLists[index] = {title: title, items: list.items, type: list.type};
            localStorage.setItem('shoppingLists', JSON.stringify(shoppingLists));
            loadShoppingLists();
        });
        li.querySelector('.loadButton').addEventListener('click', function() {
            if(list.type === 'comma') {
                document.getElementById('commaSeparated').value = list.items.join(', ');
            } else if(list.type === 'line') {
                document.getElementById('lineSeparated').value = list.items.join('\n');
            }
        });
        li.querySelector('.deleteButton').addEventListener('click', function() {
            shoppingLists.splice(index, 1);
            localStorage.setItem('shoppingLists', JSON.stringify(shoppingLists));
            loadShoppingLists();
        });
        shoppingListsElement.appendChild(li);
    });
}

document.getElementById('searchForm').addEventListener('submit', function(event) {
    event.preventDefault();
    var items = [];
    var type = '';
    var commaSeparated = document.getElementById('commaSeparated').value;
    var lineSeparated = document.getElementById('lineSeparated').value;
    var csvFile = document.getElementById('csvFile').files[0];
    if (commaSeparated) {
        items = items.concat(commaSeparated.split(',').map(function(item) { return item.trim(); }));
        type = 'comma';
    }
    if (lineSeparated) {
        items = items.concat(lineSeparated.split('\n').map(function(item) { return item.trim(); }));
        type = 'line';
    }
    if (csvFile) {
        var reader = new FileReader();
        reader.onload = function(event) {
            var csvItems = event.target.result.split('\n').map(function(item) { return item.trim(); });
            if (isNaN(Number(csvItems[0]))) {
                csvItems.shift();
            }
            items = items.concat(csvItems);
            console.log('Items to search:', items);
            chrome.runtime.sendMessage({action: "startSearch", items: items});
        };
        reader.readAsText(csvFile);
    }
    else {
        console.log('Items to search:', items);
        chrome.runtime.sendMessage({action: "startSearch", items: items});
    }
});

document.getElementById('addList').addEventListener('click', function() {
    var title = prompt('Enter the title of the new list:');
    if (title !== null) {
        var shoppingLists = JSON.parse(localStorage.getItem('shoppingLists') || '[]');
        var items = [];
        var type = '';
        var commaSeparated = document.getElementById('commaSeparated').value;
        var lineSeparated = document.getElementById('lineSeparated').value;
        if (commaSeparated) {
            items = items.concat(commaSeparated.split(',').map(function(item) { return item.trim(); }));
            type = 'comma';
        }
        if (lineSeparated) {
            items = items.concat(lineSeparated.split('\n').map(function(item) { return item.trim(); }));
            type = 'line';
        }
        shoppingLists.push({title: title, items: items, type: type});
        localStorage.setItem('shoppingLists', JSON.stringify(shoppingLists));
        loadShoppingLists();
    }
});

window.onload = loadShoppingLists;

chrome.storage.local.get(['asins'], function(result) {
    console.log('Retrieved saved ASINs:', result.asins);
    const asins = result.asins;
    var asinsElement = document.getElementById('savedAsins');

    // Check if asins is an array before iterating
    if (Array.isArray(asins)) {
        asins.forEach(function(asin) {
            var li = document.createElement('li');
            li.textContent = asin;
            // Ensure asinsElement exists before appending
            if (asinsElement) {
                 asinsElement.appendChild(li);
            } else {
                 console.error("Element with ID 'savedAsins' not found.");
            }
        });
    } else {
        console.log('No saved ASINs found or data is not an array.');
        // Optionally display a message in the UI
        if (asinsElement) {
            asinsElement.innerHTML = '<li>No saved ASINs found.</li>';
        }
    }
});
