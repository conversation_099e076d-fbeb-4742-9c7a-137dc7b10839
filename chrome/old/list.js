var globalLog = [];
chrome.storage.local.get(['products'], function(result) {
    const products = result.products;
    const productTable = document.getElementById('productTable');
    let productIndex = 0;

    function createQuantityChangeListener(checkboxId) {
        return function() {
            const checkbox = document.getElementById(checkboxId);
            const card = checkbox.closest('.product-card');
            if (this.value > 0) {
                checkbox.checked = true;
                card.style.border = '5px solid #007bff';
            } else {
                checkbox.checked = false;
                card.style.border = '1px solid';
            }
        };
    }

    document.getElementById('newListButton').addEventListener('click', function() {
        window.location.href = 'shoplist.html'; // Redirect to the shop list page
    });
    
    products.forEach(function(searchTermGroup, index) {
        const row = document.createElement('div');
        row.className = 'row';
        productTable.appendChild(row);

        // Add search term to the left of the row
        const searchTermDiv = document.createElement('div');
        searchTermDiv.className = 'search-term';
        searchTermDiv.textContent = searchTermGroup.searchTerm || 'No Search Term'; // Display search term for the row
        row.appendChild(searchTermDiv);

        let minPrice = Number.POSITIVE_INFINITY;
        let minPriceCard;

        searchTermGroup.productDetails.forEach(function(productDetail, i) {
            const card = document.createElement('div');
            card.className = 'product-card';
            const checkboxId = `checkbox_${index}_${i}`;
            card.dataset.checkboxId = checkboxId;

            // Construct the URL to the product page using the ASIN
            const productLink = `https://amazon.com/dp/${productDetail.asin}/`;

            card.innerHTML = `
                <input type="checkbox" id="${checkboxId}" name="ASIN.${productIndex+1}" value="${productDetail.asin}">
                <label for="${checkboxId}">
                    <a href="${productLink}" target="_blank">
                        <img class="product-image" src="${productDetail.img}">
                    </a>
                    <div class="product-title" style="height: 80px; overflow: hidden;">
                        <h4>${productDetail.title}</h4> <!-- DEBUG: Title should appear here -->
                    </div>
                    <p>${productDetail.price}</p>
                </label>
                <input type="number" id="quantity_${checkboxId}" name="Quantity.${productIndex+1}" min="1" max="99" value="1">
            `;
            // --- DEBUG LOG ---
            console.log(`LIST.JS: Rendering product for term "${searchTermGroup.searchTerm}", ASIN: ${productDetail.asin}, Title: "${productDetail.title}"`);
            // --- END DEBUG LOG ---
            if (i >= 8) card.style.display = 'none'; // Hide additional products initially
            card.style.flex = 'none'; // Ensure proper width for all product cards
            row.appendChild(card);

            const quantityInput = document.getElementById(`quantity_${checkboxId}`);
            quantityInput.addEventListener('input', createQuantityChangeListener(checkboxId));

            const price = parseFloat(productDetail.price.replace('$', ''));
            if (price < minPrice) {
                minPrice = price;
                minPriceCard = card;
            }

            productIndex += 1;
        });

        if (minPriceCard) {
            minPriceCard.style.backgroundColor = '#d4f2d2'; // Light green background
        }

        // Add "See More" button to the right of the row
        const seeMoreDiv = document.createElement('div');
        seeMoreDiv.className = 'see-more';
        seeMoreDiv.textContent = 'See more';
        seeMoreDiv.addEventListener('click', function() {
            // Add logic to fetch more products
        });
        row.appendChild(seeMoreDiv);
        seeMoreDiv.addEventListener('click', function() {
            // Show all products in the row
            row.querySelectorAll('.product-card').forEach(function(card) {
                card.style.display = 'block';
            });
            row.style.overflowX = 'scroll';
        });
    });

    productTable.addEventListener('click', function(event) {
        if (event.target.type !== 'number') {
            const card = event.target.closest('.product-card');
            if (card) {
                const checkboxId = card.dataset.checkboxId;
                const checkbox = document.getElementById(checkboxId);
                if (checkbox && event.target.id !== checkboxId) {
                    checkbox.checked = !checkbox.checked;
                    if (checkbox.checked) {
                        card.style.border = '5px solid #007bff'; // Highlight the border, 5x thickness, blue color
                    } else {
                        card.style.border = '1px solid'; // Reset the border
                    }
                }
            }
        }
    });

    document.getElementById('addToCartButton').addEventListener('click', function(event) {
        event.preventDefault();
        const selectedProducts = [];
        for (let i = 0; i < productIndex; i++) {
            const checkbox = document.querySelector(`input[name="ASIN.${i+1}"]`);
            const quantityInput = document.querySelector(`input[name="Quantity.${i+1}"]`);
            if (checkbox.checked) {
                selectedProducts.push({
                    asin: checkbox.value,
                    quantity: quantityInput.value
                });
            }
        }
        chrome.runtime.sendMessage({ action: "addToCart", products: selectedProducts });
    });

    document.getElementById('newListButton').addEventListener('click', function() {
        window.location.href = 'shoplist.html'; // Redirect to the shop list page
    });

    document.getElementById('addToList').addEventListener('click', function() {
        const selectedASINs = [];
        for (let i = 0; i < productIndex; i++) {
            const checkbox = document.querySelector(`input[name="ASIN.${i+1}"]`);
            if (checkbox.checked) {
                selectedASINs.push(checkbox.value);
            }
        }
        var listTitle = prompt("Enter a title for the list:");
        if (listTitle !== null) {
            var shoppingLists = JSON.parse(localStorage.getItem('shoppingLists') || '[]');
            shoppingLists.push({title: listTitle, items: selectedASINs, type: 'line'});
            localStorage.setItem('shoppingLists', JSON.stringify(shoppingLists));
        }    });
});
function downloadLog() {
    alert('Downloading log...');
    console.log('Global log content:', globalLog);
    var blob = new Blob([globalLog.join('\n')], {type: 'text/plain;charset=utf-8'});
    var url = window.URL.createObjectURL(blob);
    var link = document.createElement('a');
    link.href = url;
    link.download = 'log.txt';
    link.click();
    window.URL.revokeObjectURL(url);
}


window.addEventListener('DOMContentLoaded', function() {
    var downloadLink = document.getElementById('download-log-link');
    if (downloadLink) {
        downloadLink.addEventListener('click', downloadLog);
    }
});

// Removed duplicate downloadLog function definition. The one at line 151 is kept.