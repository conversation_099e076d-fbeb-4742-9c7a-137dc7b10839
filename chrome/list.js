// Global variables
let allProducts = [];
let filteredProducts = [];
let selectedProducts = new Set();
let currentPage = 1;
const productsPerPage = 12;

// When the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Load products from chrome storage
    loadProducts();
    
    // Set up event listeners
    document.getElementById('selectAllBtn').addEventListener('click', toggleSelectAll);
    document.getElementById('importToStayfuBtn').addEventListener('click', importToStayfu);
    document.getElementById('addToCartBtn').addEventListener('click', addToCart);
    document.getElementById('backToSearchBtn').addEventListener('click', goBackToSearch);
    document.getElementById('sortFilter').addEventListener('change', applyFilters);
    document.getElementById('primeFilter').addEventListener('change', applyFilters);
});

// Load products from Chrome storage
function loadProducts() {
    showLoading(true);
    
    chrome.storage.local.get(['products'], function(data) {
        if (data.products && data.products.length > 0) {
            processProducts(data.products);
        } else {
            document.getElementById('productsContainer').innerHTML = `
                <div class="no-results">
                    <p>No products found. Go back to search and try again.</p>
                </div>
            `;
            document.getElementById('resultCount').textContent = '0';
            document.getElementById('searchTerms').textContent = 'your search';
        }
        
        showLoading(false);
    });
}

// Process product data
function processProducts(productData) {
    allProducts = [];
    const searchTerms = new Set();
    
    // Flatten the product data and collect search terms
    productData.forEach(group => {
        searchTerms.add(group.searchTerm);
        
        group.productDetails.forEach(product => {
            // Add search term to each product
            product.searchTerm = group.searchTerm;
            allProducts.push(product);
        });
    });
    
    // Update UI with search terms
    document.getElementById('searchTerms').textContent = Array.from(searchTerms).join(', ');
    document.getElementById('resultCount').textContent = allProducts.length;
    
    // Apply filters (this also renders the products)
    applyFilters();
}

// Apply sorting and filtering
function applyFilters() {
    const sortBy = document.getElementById('sortFilter').value;
    const primeFilter = document.getElementById('primeFilter').value;
    
    // Filter products
    filteredProducts = allProducts.filter(product => {
        if (primeFilter === 'prime' && !product.isPrime) {
            return false;
        }
        return true;
    });
    
    // Sort products
    filteredProducts.sort((a, b) => {
        switch (sortBy) {
            case 'price_low':
                return (a.numericPrice || 0) - (b.numericPrice || 0);
            case 'price_high':
                return (b.numericPrice || 0) - (a.numericPrice || 0);
            case 'rating':
                return (b.rating || 0) - (a.rating || 0);
            default: // relevance - keep original order
                return 0;
        }
    });
    
    // Reset to first page when filters change
    currentPage = 1;
    
    // Render products
    renderProducts();
    renderPagination();
}

// Render products on the page
function renderProducts() {
    const container = document.getElementById('productsContainer');
    container.innerHTML = '';
    
    // Calculate products for current page
    const startIndex = (currentPage - 1) * productsPerPage;
    const endIndex = startIndex + productsPerPage;
    const productsToShow = filteredProducts.slice(startIndex, endIndex);
    
    if (productsToShow.length === 0) {
        container.innerHTML = `
            <div class="no-results">
                <p>No products match your filters. Try changing your search criteria.</p>
            </div>
        `;
        return;
    }
    
    // Create product cards
    productsToShow.forEach(product => {
        const isSelected = selectedProducts.has(product.asin);
        
        const card = document.createElement('div');
        card.className = `product-card ${isSelected ? 'selected' : ''}`;
        card.dataset.asin = product.asin;
        
        // Use the formattedRating from content script, or default to star display
        const displayedRating = product.formattedRating || (product.rating ? `${product.rating} \u2605` : '0.0 \u2605');
        
        card.innerHTML = `
            <div class="select-checkbox ${isSelected ? 'selected' : ''}" data-asin="${product.asin}">
                ${isSelected ? '✓' : ''}
            </div>
            <div class="product-image">
                <img src="${product.img}" alt="${product.title}" onerror="this.src='placeholder.svg'">
            </div>
            <div class="product-details">
                <div class="product-title">${product.title}</div>
                <div class="product-price">${product.price || 'Price not available'}</div>
                <div class="product-rating">
                    <span class="star-rating">${displayedRating}</span>
                    <span>(${product.reviewCount || 0})</span>
                </div>
                <div class="product-meta">
                    <span class="product-asin">ASIN: ${product.asin}</span>
                </div>
            </div>
            <div class="product-badges">
                ${product.isPrime ? '<span class="badge badge-prime">Prime</span>' : ''}
                <span class="badge badge-search">${product.searchTerm}</span>
            </div>
        `;
        
        container.appendChild(card);
        
        // Add click event to toggle selection
        card.addEventListener('click', function(e) {
            if (e.target.tagName === 'A') return;
            const asin = this.dataset.asin;
            toggleProductSelection(asin);
        });
        
        // Add click handler for checkbox
        const checkbox = card.querySelector('.select-checkbox');
        checkbox.addEventListener('click', function(e) {
            e.stopPropagation();
            const asin = this.dataset.asin;
            toggleProductSelection(asin);
        });
    });
}

// Render pagination controls
function renderPagination() {
    const paginationContainer = document.getElementById('pagination');
    paginationContainer.innerHTML = '';
    
    if (filteredProducts.length <= productsPerPage) {
        return; // No need for pagination
    }
    
    const totalPages = Math.ceil(filteredProducts.length / productsPerPage);
    
    // Previous button
    const prevButton = document.createElement('button');
    prevButton.textContent = '←';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            renderProducts();
            renderPagination();
            window.scrollTo(0, 0);
        }
    });
    paginationContainer.appendChild(prevButton);
    
    // Page buttons
    const maxPagesToShow = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);
    
    // Adjust if we're near the end
    if (endPage - startPage + 1 < maxPagesToShow) {
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }
    
    // First page button if not visible
    if (startPage > 1) {
        const firstPageButton = document.createElement('button');
        firstPageButton.textContent = '1';
        firstPageButton.addEventListener('click', () => {
            currentPage = 1;
            renderProducts();
            renderPagination();
            window.scrollTo(0, 0);
        });
        paginationContainer.appendChild(firstPageButton);
        
        if (startPage > 2) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.style.margin = '0 8px';
            paginationContainer.appendChild(ellipsis);
        }
    }
    
    // Page buttons
    for (let i = startPage; i <= endPage; i++) {
        const pageButton = document.createElement('button');
        pageButton.textContent = i;
        pageButton.className = i === currentPage ? 'active' : '';
        pageButton.addEventListener('click', () => {
            currentPage = i;
            renderProducts();
            renderPagination();
            window.scrollTo(0, 0);
        });
        paginationContainer.appendChild(pageButton);
    }
    
    // Last page button if not visible
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.style.margin = '0 8px';
            paginationContainer.appendChild(ellipsis);
        }
        
        const lastPageButton = document.createElement('button');
        lastPageButton.textContent = totalPages;
        lastPageButton.addEventListener('click', () => {
            currentPage = totalPages;
            renderProducts();
            renderPagination();
            window.scrollTo(0, 0);
        });
        paginationContainer.appendChild(lastPageButton);
    }
    
    // Next button
    const nextButton = document.createElement('button');
    nextButton.textContent = '→';
    nextButton.disabled = currentPage === totalPages;
    nextButton.addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            renderProducts();
            renderPagination();
            window.scrollTo(0, 0);
        }
    });
    paginationContainer.appendChild(nextButton);
}

// Toggle product selection
function toggleProductSelection(asin) {
    if (selectedProducts.has(asin)) {
        selectedProducts.delete(asin);
    } else {
        selectedProducts.add(asin);
    }
    
    // Update UI
    const cards = document.querySelectorAll(`.product-card[data-asin="${asin}"]`);
    cards.forEach(card => {
        card.classList.toggle('selected', selectedProducts.has(asin));
        
        const checkbox = card.querySelector('.select-checkbox');
        checkbox.classList.toggle('selected', selectedProducts.has(asin));
        checkbox.innerHTML = selectedProducts.has(asin) ? '✓' : '';
    });
    
    // Update select all button text
    updateSelectAllButtonText();
}

// Toggle select all
function toggleSelectAll() {
    if (selectedProducts.size === filteredProducts.length) {
        // Deselect all
        selectedProducts.clear();
    } else {
        // Select all
        filteredProducts.forEach(product => {
            selectedProducts.add(product.asin);
        });
    }
    
    // Re-render products to update UI
    renderProducts();
    
    // Update select all button text
    updateSelectAllButtonText();
}

// Update select all button text
function updateSelectAllButtonText() {
    const selectAllBtn = document.getElementById('selectAllBtn');
    if (selectedProducts.size === filteredProducts.length) {
        selectAllBtn.textContent = 'Deselect All';
    } else {
        selectAllBtn.textContent = 'Select All';
    }
}

// Import to StayFu with better error handling
async function importToStayfu() {
    if (selectedProducts.size === 0) {
        showToast('Please select at least one product to import', 'error');
        return;
    }
    
    showLoading(true);
    
    try {
        // Save selected products to storage
        const selectedProductsArray = filteredProducts.filter(product => 
            selectedProducts.has(product.asin)
        );
        
        await new Promise((resolve, reject) => {
            chrome.storage.local.set({
                selectedProducts: selectedProductsArray
            }, () => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else {
                    resolve();
                }
            });
        });

        // Verify token before redirecting
        await new Promise((resolve, reject) => {
            chrome.runtime.sendMessage({ action: 'checkStayfuConnection' }, response => {
                if (chrome.runtime.lastError) {
                    reject(chrome.runtime.lastError);
                } else if (!response.connected) {
                    reject(new Error(response.error || 'Not connected to StayFu'));
                } else {
                    resolve();
                }
            });
        });

        // If we get here, token is valid, redirect to import
        window.location.href = 'shoplist.html?import=true';
    } catch (error) {
        showLoading(false);
        showToast(error.message || 'Error preparing import. Please check your StayFu connection.', 'error');
        
        // If token error, redirect to settings
        if (error.message.includes('token') || error.message.includes('connection')) {
            setTimeout(() => {
                chrome.runtime.openOptionsPage();
            }, 2000);
        }
    }
}

// Add to Amazon Cart
function addToCart() {
    if (selectedProducts.size === 0) {
        showToast('Please select at least one product to add to cart', 'error');
        return;
    }
    
    const selectedProductsArray = filteredProducts.filter(product => 
        selectedProducts.has(product.asin)
    ).map(product => ({
        asin: product.asin,
        quantity: 1
    }));
    
    chrome.runtime.sendMessage({
        action: 'addToCart',
        products: selectedProductsArray
    });
    
    showToast('Products added to Amazon cart', 'success');
}

// Go back to search
function goBackToSearch() {
    window.location.href = 'shoplist.html';
}

// Show/hide loading overlay
function showLoading(show) {
    document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
}

// Show toast message
function showToast(message, type) {
    const toastContainer = document.getElementById('toastContainer');
    
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // Add to container
    toastContainer.appendChild(toast);
    
    // Show the toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
    
    // Remove after delay
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            toastContainer.removeChild(toast);
        }, 300);
    }, 3000);
}
