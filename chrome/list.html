<!DOCTYPE html>
<html>
<head>
    <title>StayFu - Amazon Results</title>
    <style>
body {
    font-family: Arial, sans-serif;
    background-color: #f9fafb;
    color: #1e293b;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
#header {
    background-color: #f1f5f9;
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
}
#header h1 {
    margin: 0;
    color: #0f172a;
    font-size: 24px;
    display: flex;
    align-items: center;
}
#header h1 svg {
    margin-right: 12px;
    color: #4f46e5;
}
#container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}
#controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}
.search-info {
    color: #64748b;
    font-size: 14px;
}
.search-term {
    font-weight: 600;
    color: #334155;
}
.action-buttons {
    display: flex;
    gap: 12px;
}
.button {
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
}
.button-primary {
    background-color: #4f46e5;
    color: white;
    border: none;
}
.button-primary:hover {
    background-color: #4338ca;
}
.button-secondary {
    background-color: #f8fafc;
    color: #334155;
    border: 1px solid #cbd5e1;
}
.button-secondary:hover {
    background-color: #f1f5f9;
}
.button-danger {
    background-color: #ef4444;
    color: white;
    border: none;
}
.button-danger:hover {
    background-color: #dc2626;
}
.products-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
}
.product-card {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e2e8f0;
    transition: all 0.2s;
    position: relative;
}
.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
.product-card.selected {
    border: 2px solid #4f46e5;
}
.product-image {
    height: 200px;
    width: 100%;
    object-fit: contain;
    background-color: #f8fafc;
    padding: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.product-image img {
    max-height: 100%;
    max-width: 100%;
}
.product-details {
    padding: 16px;
}
.product-title {
    font-size: 14px;
    color: #0f172a;
    font-weight: 500;
    margin-bottom: 8px;
    height: 42px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
.product-price {
    font-size: 16px;
    color: #0f172a;
    font-weight: 600;
    margin-bottom: 8px;
}
.product-rating {
    font-size: 12px;
    color: #64748b;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}
.star-rating {
    color: #f59e0b;
    margin-right: 4px;
}
.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #64748b;
}
.product-asin {
    background-color: #f1f5f9;
    padding: 2px 6px;
    border-radius: 4px;
}
.select-checkbox {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 22px;
    height: 22px;
    border-radius: 4px;
    border: 2px solid #cbd5e1;
    background-color: white;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}
.select-checkbox.selected {
    background-color: #4f46e5;
    border-color: #4f46e5;
    color: white;
}
.product-badges {
    position: absolute;
    bottom: 8px;
    left: 8px;
    display: flex;
    gap: 4px;
}
.badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
}
.badge-prime {
    background-color: #0f172a;
    color: white;
}
.badge-search {
    background-color: #f1f5f9;
    color: #64748b;
}
.badge-warning {
    background-color: #fef9c3;
    color: #854d0e;
}
.no-results {
    text-align: center;
    padding: 60px 0;
    color: #64748b;
}
.logo {
    font-weight: bold;
    font-size: 24px;
    color: #4f46e5;
    display: flex;
    align-items: center;
}
.logo svg {
    margin-right: 12px;
}
.toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    max-width: 320px;
    z-index: 50;
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.3s ease-out;
}
.toast.show {
    transform: translateY(0);
    opacity: 1;
}
.toast-success {
    background-color: #dcfce7;
    border-left: 4px solid #22c55e;
    color: #166534;
}
.toast-error {
    background-color: #fee2e2;
    border-left: 4px solid #ef4444;
    color: #b91c1c;
}
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
}
.spinner {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4f46e5;
    animation: spin 1s linear infinite;
}
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 32px;
    gap: 8px;
}
.pagination button {
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #cbd5e1;
    background-color: #f8fafc;
    cursor: pointer;
}
.pagination button.active {
    background-color: #4f46e5;
    border-color: #4f46e5;
    color: white;
}
.pagination button:hover:not(.active) {
    background-color: #f1f5f9;
}
.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
.filters {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}
.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}
.filter-label {
    font-size: 14px;
    color: #64748b;
}
.filter-select {
    padding: 8px;
    border-radius: 6px;
    border: 1px solid #cbd5e1;
    background-color: #f8fafc;
    font-size: 14px;
    color: #334155;
}
.stayfuButton {
    display: flex;
    align-items: center;
    gap: 8px;
}
    </style>
</head>
<body>
    <div id="header">
        <h1>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
            StayFu Amazon Products
        </h1>
    </div>

    <div id="container">
        <div id="controls">
            <div class="search-info">
                Found <span id="resultCount">0</span> products for 
                <span id="searchTerms" class="search-term">your search</span>
            </div>
            <div class="action-buttons">
                <button id="selectAllBtn" class="button button-secondary">Select All</button>
                <button id="importToStayfuBtn" class="button button-primary stayfuButton">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>
                    Import to StayFu
                </button>
                <button id="addToCartBtn" class="button button-secondary">Add to Amazon Cart</button>
                <button id="backToSearchBtn" class="button button-secondary">Back to Search</button>
            </div>
        </div>
        
        <div class="filters">
            <div class="filter-group">
                <span class="filter-label">Sort by:</span>
                <select id="sortFilter" class="filter-select">
                    <option value="relevance">Relevance</option>
                    <option value="price_low">Price (Low to High)</option>
                    <option value="price_high">Price (High to Low)</option>
                    <option value="rating">Rating</option>
                </select>
            </div>
            <div class="filter-group">
                <span class="filter-label">Filter:</span>
                <select id="primeFilter" class="filter-select">
                    <option value="all">All Products</option>
                    <option value="prime">Prime Only</option>
                </select>
            </div>
        </div>
        
        <div id="productsContainer" class="products-container">
            <!-- Products will be inserted here -->
        </div>
        
        <div id="pagination" class="pagination">
            <!-- Pagination will be inserted here -->
        </div>
    </div>
    
    <div id="toastContainer"></div>
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="spinner"></div>
    </div>
    
    <script src="list.js"></script>
</body>
</html>