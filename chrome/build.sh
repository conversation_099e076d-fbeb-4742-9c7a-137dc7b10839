#!/bin/bash

# Create a dist directory if it doesn't exist
mkdir -p dist

# Copy all necessary files to the dist directory
cp manifest.json dist/
cp background.js dist/
cp content.js dist/
cp imageProcessor.js dist/
cp settings.html dist/
cp settings.js dist/
cp styles.css dist/
cp icon.png dist/

echo "Extension built successfully in the dist directory"
echo "To install the extension in Chrome:"
echo "1. Go to chrome://extensions/"
echo "2. Enable Developer mode"
echo "3. Click 'Load unpacked'"
echo "4. Select the 'dist' directory"
