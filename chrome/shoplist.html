<!DOCTYPE html>
<html>
<head>
    <title>StayFu - Amazon Data Import</title>
    <style>
body {
    font-family: Arial, sans-serif;
    background-color: #f9fafb;
    color: #1e293b;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    flex-direction: column;
}
#sidebar {
    position: fixed;
    left: 0;
    width: 240px;
    height: 100vh;
    background-color: #f1f5f9;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-right: 1px solid #e2e8f0;
}
#sidebar h2 {
    color: #0f172a;
    margin-bottom: 16px;
}
#sidebar ul {
    list-style-type: none;
    padding: 0;
    width: 100%;
    margin-top: 8px;
}
#sidebar button, #csvFile {
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #cbd5e1;
    background-color: #f8fafc;
    color: #334155;
    cursor: pointer;
    margin-bottom: 8px;
    width: 100%;
    font-size: 14px;
    transition: all 0.2s;
}
#sidebar button:hover, #csvFile:hover {
    background-color: #e2e8f0;
}
#main {
    flex-grow: 1;
    padding: 40px;
    padding-left: 280px;
    max-width: 800px;
}
#main h1 {
    color: #0f172a;
    font-size: 24px;
    margin-bottom: 8px;
}
#main p {
    color: #64748b;
    margin-bottom: 24px;
    line-height: 1.5;
}
#searchForm {
    display: flex;
    flex-direction: column;
    width: 100%;
}
#commaSeparated, #lineSeparated {
    margin-bottom: 16px;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #cbd5e1;
    background-color: #fff;
    font-size: 14px;
}
#lineSeparated {
    height: 120px;
    width: 100%;
    resize: vertical;
}
.button-primary {
    padding: 10px 16px;
    border-radius: 6px;
    border: none;
    background-color: #4f46e5;
    color: white;
    cursor: pointer;
    margin-bottom: 12px;
    width: 100%;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.2s;
}
.button-primary:hover {
    background-color: #4338ca;
}
.button-secondary {
    padding: 10px 16px;
    border-radius: 6px;
    border: 1px solid #cbd5e1;
    background-color: #f8fafc;
    color: #334155;
    cursor: pointer;
    margin-bottom: 12px;
    width: 100%;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.2s;
}
.button-secondary:hover {
    background-color: #e2e8f0;
}
.button-danger {
    background-color: #ef4444;
    color: white;
}
.button-danger:hover {
    background-color: #dc2626;
}
#customCsvButton {
    padding: 10px 16px;
    border-radius: 6px;
    border: 1px solid #cbd5e1;
    background-color: #f8fafc;
    color: #334155;
    text-align: center;
    cursor: pointer;
    margin-bottom: 12px;
    width: 100%;
    display: inline-block;
    font-size: 16px;
}
#customCsvButton:hover {
    background-color: #e2e8f0;
}
.section {
    margin-bottom: 32px;
    padding: 24px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    background-color: #fff;
}
.section h2 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    color: #0f172a;
}
.form-group {
    margin-bottom: 16px;
}
.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #334155;
}
.form-control {
    width: 100%;
    padding: 10px;
    border-radius: 6px;
    border: 1px solid #cbd5e1;
    font-size: 14px;
}
.alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 14px;
}
.alert-success {
    background-color: #dcfce7;
    border: 1px solid #86efac;
    color: #166534;
}
.alert-danger {
    background-color: #fee2e2;
    border: 1px solid #fca5a5;
    color: #b91c1c;
}
.alert-warning {
    background-color: #fef3c7;
    border: 1px solid #fcd34d;
    color: #92400e;
}
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 9999px;
    font-size: 12px;
    font-weight: 500;
}
.badge-success {
    background-color: #dcfce7;
    color: #166534;
}
.badge-danger {
    background-color: #fee2e2;
    color: #b91c1c;
}
.badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}
.hidden {
    display: none;
}
.tabs {
    display: flex;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 24px;
}
.tab {
    padding: 12px 16px;
    cursor: pointer;
    font-weight: 500;
    color: #64748b;
    border-bottom: 2px solid transparent;
}
.tab.active {
    color: #4f46e5;
    border-bottom: 2px solid #4f46e5;
}
.tab-content {
    display: none;
}
.tab-content.active {
    display: block;
}
.logo {
    font-weight: bold;
    font-size: 24px;
    color: #4f46e5;
    display: flex;
    align-items: center;
    margin-bottom: 24px;
}
.logo img {
    height: 32px;
    margin-right: 12px;
}
    </style>
</head>
<body>
    <div id="sidebar">
        <div class="logo">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
            StayFu
        </div>
        <h2>Saved Lists</h2>
        <button id="addList" class="button-secondary">Add New List</button>
        <ul id="shoppingLists"></ul>
    </div>
    <div id="main">
        <div class="tabs">
            <div class="tab active" data-tab="search">Amazon Search</div>
            <div class="tab" data-tab="settings">StayFu Settings</div>
            <div class="tab" data-tab="import">Import to StayFu</div>
        </div>
        
        <div id="search-tab" class="tab-content active">
            <h1>Amazon Data Import Tool</h1>
            <p>Search Amazon for items and import the data directly into your StayFu inventory or create purchase orders.</p>
            
            <div class="section">
                <h2>Search Items</h2>
                <form id="searchForm">
                    <div class="form-group">
                        <label for="commaSeparated">Comma-separated list:</label>
                        <input type="text" id="commaSeparated" class="form-control" placeholder="Item 1, Item 2, Item 3">
                    </div>
                    
                    <div class="form-group">
                        <label for="lineSeparated">One item per line:</label>
                        <textarea id="lineSeparated" class="form-control" placeholder="Item 1&#10;Item 2&#10;Item 3"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="csvFile">CSV file: (Row 1 header, then one item per row)</label>
                        <a href="example.csv" download class="button-secondary" style="width: auto; display: inline-block; padding: 6px 12px; margin-left: 8px;">Download Example CSV</a>
                        <input type="file" id="csvFile" accept=".csv" style="display: none;">
                        <label for="csvFile" id="customCsvButton" class="form-control">Upload a CSV</label>
                    </div>
                    
                    <button type="submit" class="button-primary">Start Searching</button>
                    <button type="reset" class="button-secondary">Clear Form</button>
                </form>
            </div>
        </div>
        
        <div id="settings-tab" class="tab-content">
            <h1>StayFu Integration Settings</h1>
            <p>Configure the connection between this extension and your StayFu account.</p>
            
            <div class="section">
                <h2>Connection Setup</h2>
                <div id="connection-status" class="alert alert-warning">
                    Checking connection to StayFu...
                </div>
                
                <div class="form-group">
                    <label for="stayfuUrl">StayFu URL:</label>
                    <input type="url" id="stayfuUrl" class="form-control" placeholder="https://yourdomain.com">
                </div>
                
                <div class="form-group">
                    <label for="stayfuApiToken">API Token:</label>
                    <input type="password" id="stayfuApiToken" class="form-control" placeholder="Your StayFu API token">
                    <small>You can generate an API token in your StayFu app settings.</small>
                </div>
                
                <button id="saveSettings" class="button-primary">Save Settings</button>
                <button id="testConnection" class="button-secondary">Test Connection</button>
            </div>
        </div>
        
        <div id="import-tab" class="tab-content">
            <h1>Import to StayFu</h1>
            <p>Import scraped Amazon data directly into your StayFu inventory or create purchase orders.</p>
            
            <div id="no-data-message" class="alert alert-warning">
                No product data available. Please search for products first.
            </div>
            
            <div id="import-section" class="section hidden">
                <h2>Available Products</h2>
                <div id="product-count" class="alert alert-success">
                    0 products found
                </div>
                
                <div class="form-group">
                    <label for="importType">Import as:</label>
                    <select id="importType" class="form-control">
                        <option value="inventory">Add to Inventory</option>
                        <option value="purchase">Create Purchase Order</option>
                    </select>
                </div>
                
                <div id="property-selection" class="form-group">
                    <label for="propertyId">Select Property:</label>
                    <select id="propertyId" class="form-control">
                        <option value="">Loading properties...</option>
                    </select>
                </div>
                
                <div id="collection-selection" class="form-group">
                    <label for="collection">Collection:</label>
                    <input type="text" id="collection" class="form-control" placeholder="e.g., Kitchen, Bathroom">
                </div>
                
                <button id="importToStayfu" class="button-primary">Import Selected Products</button>
                <div id="import-result" class="hidden"></div>
            </div>
        </div>
    </div>
    <script src="shoplist.js"></script>
</body>
</html>
