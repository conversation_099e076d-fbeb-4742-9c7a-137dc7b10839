# Security Specification

This document outlines the security measures and best practices to be implemented in the StayFu application. Security is a critical aspect and will be considered at all stages of development and deployment.

## Guiding Principles for Development

This document, and the overall StayFu rebuild project, adheres to the following core principles to ensure a streamlined, maintainable, and scalable application:

*   **KISS (Keep It Simple, Stupid):** Implement fundamental and proven security practices. Avoid complex security measures unless strictly necessary.
*   **DRY (Don't Repeat Yourself):** Centralize security controls and policies where possible.
*   **Modularity:** Security considerations should be applied at a modular level, ensuring each part of the application is secured appropriately.
*   **Phased Database Strategy:**
    *   **Initial Development (SQLite):** Security focus will be on application-level vulnerabilities, as SQLite is for local dev.
    *   **MVP and Beyond (Supabase):** Leverage Supabase features like RLS, Auth, and PostgreSQL security best practices.
*   **MVP Focus:** Implement essential security measures for the MVP, including input validation, authentication, authorization, and protection against common web vulnerabilities (OWASP Top 10).
*   **Simplified Documentation:** All documentation will be concise and clear.

## 1. Introduction
This document outlines the security model for the StayFu application, focusing on authentication, authorization, data security, and Row Level Security (RLS) implementation with Supabase. The primary goal is to protect user data and ensure users can only access and modify data they are permitted to.

## 2. Authentication
*   **Provider:** Supabase Auth will be used for user authentication.
*   **Methods:** Email/password login, secure password reset.
*   **Session Management:** Handled by Supabase client libraries, using secure tokens (JWTs).
*   **Account Lockout/Throttling:** Leverage Supabase Auth's built-in protections against brute-force attacks.

## 3. Authorization - User Roles and Permissions ("Teams" Feature)

The application implements a hierarchical role-based access control (RBAC) system. This is critical for the "Teams" feature and addresses the RLS challenges mentioned in previous development efforts.

### 3.1. User Roles
1.  **Super Admin (`super_admin`):**
    *   Highest level of authority.
    *   Can manage all aspects of the system.
    *   **Key Permissions:** Create, read, update, delete (CRUD) any data; manage Admin users (create, deactivate, assign role); manage system settings; view all user activity.
2.  **Admin (`admin`):**
    *   Comprehensive control over the system and its data.
    *   **Key Permissions:** CRUD operations on most data (properties, users, tasks, etc., excluding Super Admins); manage Property Managers, Service Providers, and Staff; manage system settings.
    *   **Restriction:** Cannot create, modify, or delete Super Admin users or other Admin users.
3.  **Property Manager (`property_manager`):**
    *   Default role assigned upon user sign-up.
    *   Manages their own properties and associated data.
    *   **Key Permissions:**
        *   CRUD operations on their own properties, collections, inventory items, maintenance tasks, damage reports, purchase requests.
        *   Invite, add, and manage Service Provider and Staff users associated with their properties.
        *   Assign Service Providers and Staff to specific properties they (the PM) own/manage.
        *   Define permissions for their Service Providers and Staff within the scope of assigned properties (e.g., can view tasks, can update task status).
        *   View data related only to their properties and managed users.
4.  **Service Provider (`service_provider`):**
    *   Invited and managed by a Property Manager.
    *   Access is limited to tasks and properties they are explicitly assigned to by a Property Manager.
    *   **Key Permissions (scoped by Property Manager assignments):**
        *   View and update details of maintenance tasks or damage reports assigned to them.
        *   View limited details of properties they are assigned to work on.
        *   Cannot create new properties, invite other users, or access data outside their assignments.
        *   May have a login to the system or operate via email notifications and direct links.
5.  **Staff (`staff`):**
    *   Invited and managed by a Property Manager. Similar to Service Providers but typically internal to the PM's operations.
    *   Access is limited to tasks and properties they are explicitly assigned to by a Property Manager.
    *   **Key Permissions (scoped by Property Manager assignments):**
        *   Perform actions defined by the Property Manager (e.g., update inventory, view maintenance schedules, add notes to damage reports for assigned properties).
        *   Cannot create new properties, invite other users, or access data outside their assignments.

### 3.2. Role Assignment
*   `super_admin` and `admin` roles are assigned manually by existing Super Admins or through a secure initial setup process.
*   `property_manager` is the default role on signup.
*   `service_provider` and `staff` roles are assigned by Property Managers when inviting/adding these users. The inviting Property Manager's ID should be linked to these users.

## 4. Row Level Security (RLS) with Supabase

RLS is paramount for enforcing the permissions defined above. Policies will be created on tables in Supabase.

### 4.1. General RLS Principles
*   **Default Deny:** Tables should have RLS enabled with no policies initially, or a default DENY ALL policy.
*   **Explicit Allow:** Create specific policies for each role (or combinations) for SELECT, INSERT, UPDATE, DELETE operations.
*   **Use `auth.uid()` and `auth.role()`:** Policies will heavily rely on `auth.uid()` to get the current user's ID and `auth.role()` to get their role (requires custom claims or a roles table).
*   **Helper Functions:** Supabase SQL functions can be created to simplify complex RLS conditions (KISS, DRY).

### 4.2. RLS Strategy Examples:
*   **`properties` table:**
    *   Property Managers can SELECT, INSERT, UPDATE, DELETE their own properties (e.g., `USING (auth.uid() = owner_user_id) WITH CHECK (auth.uid() = owner_user_id)`).
    *   Service Providers/Staff can SELECT properties they are assigned to (via a join with `property_user_assignments` table where `user_id = auth.uid()`).
    *   Admins/Super Admins can SELECT, UPDATE, DELETE any property.
*   **`maintenance_tasks` table:**
    *   Property Managers can CRUD tasks for their properties.
    *   Assigned Service Providers/Staff can SELECT and UPDATE (e.g., status) tasks assigned to them.
    *   Admins/Super Admins can CRUD any task.
*   **`users` table (for profile management):**
    *   Users can SELECT and UPDATE their own profile (`auth.uid() = id`).
    *   Property Managers can SELECT profiles of Service Providers/Staff they invited.
    *   Admins/Super Admins can SELECT/UPDATE most user profiles (respecting hierarchy).
*   **`property_user_assignments` table:**
    *   Property Managers can CRUD assignments for their own properties and users they manage.
    *   Admins/Super Admins can CRUD any assignment.

### 4.3. Storing Roles
*   The `users.role` column (as defined in `05_Data_Model_Specification.md`) will store the user's role.
*   Alternatively, Supabase custom claims in JWTs can store the role, accessible via `auth.jwt() ->> 'user_role'`. Using a table column is often simpler for RLS policies that join tables. For MVP, `users.role` column is preferred.

## 5. Data Security
*   **Data in Transit:** HTTPS enforced by Supabase.
*   **Data at Rest:** Handled by Supabase's underlying cloud provider (AWS/Azure) encryption.
*   **Input Validation:** Rigorous validation on both client-side and server-side (Supabase functions or database constraints) to prevent injection attacks (SQLi, XSS).
*   **Output Encoding:** Ensure proper encoding of data displayed in the UI to prevent XSS. MUI components generally handle this well.
*   **API Security:** Supabase APIs are secured by JWTs. RLS enforces data access. Additional server-side validation in Supabase Functions if custom business logic is complex.

## 6. Sensitive Data
*   Passwords are not stored directly (Supabase Auth handles hashing).
*   Personally Identifiable Information (PII) should be handled with care, access restricted by RLS.
*   No storage of payment card information directly; use third-party processors if needed post-MVP.

## 7. Regular Security Audits (Post-MVP)
*   Periodically review RLS policies, dependencies, and code for vulnerabilities.

## 8. Addressing Past RLS Issues
*   The detailed role definitions and a clear strategy of linking users (PMs to SPs/Staff, SPs/Staff to specific properties via `property_user_assignments`) are designed to provide the necessary granularity for effective RLS.
*   Thorough testing of RLS policies for each role will be a critical part of development.

## 9. Authentication and Authorization

*   **Authentication Provider:** Supabase Auth will be the primary authentication provider.
    *   Secure password policies enforced by Supabase Auth.
    *   Email confirmation for new user registrations.
    *   Secure password reset mechanisms.
    *   Consider Multi-Factor Authentication (MFA) support in Supabase for enhanced security if available and appropriate for the user base.
*   **Session Management:**
    *   JWTs (JSON Web Tokens) issued by Supabase Auth will be used for session management.
    *   Tokens will have appropriate expiry times (e.g., 72 hours, as in the previous system, but configurable).
    *   Automatic session refresh mechanisms, ensuring tokens are refreshed securely before expiry.
    *   Secure storage of tokens on the client-side (e.g., `HttpOnly` cookies if server-side rendering or a BFF pattern is used; otherwise, careful `localStorage` usage with XSS mitigation).
    *   Idle timeout with user warnings and automatic logout.
*   **Authorization (Access Control):**
    *   **Role-Based Access Control (RBAC):** Define clear user roles (e.g., `super_admin`, `admin`, `property_manager`, `staff`, `service_provider`) with specific permissions.
    *   **Supabase Row Level Security (RLS):** This is the cornerstone of data access control. RLS policies will be meticulously defined for every table to ensure users can only access and modify data they are explicitly permitted to.
        *   Policies will be based on `auth.uid()`, user roles, team memberships, and other relevant context.
        *   Thoroughly test RLS policies to prevent data leakage or unauthorized access.
    *   **API Endpoint Authorization:** All API endpoints (Supabase Edge Functions) must validate that the authenticated user has the necessary permissions to perform the requested action.

## 10. Data Security

*   **Data at Rest:**
    *   Supabase encrypts data at rest by default.
    *   Sensitive data within the database (if any beyond standard PII) should be identified, and additional encryption measures considered if Supabase defaults are insufficient (though generally they are robust).
*   **Data in Transit:**
    *   All communication between the client, frontend server, and Supabase backend will be over HTTPS (TLS).
    *   Supabase enforces HTTPS for its APIs and database connections.
*   **Input Validation:**
    *   Validate all user inputs on both the client-side (for UX) and server-side (Edge Functions, database functions) to prevent common vulnerabilities like XSS, SQL Injection (Supabase client libraries help prevent SQLi, but care is needed with raw queries or dynamic string construction in SQL functions).
    *   Use parameterized queries or Supabase client libraries for database interactions.
*   **Output Encoding:**
    *   Ensure that data rendered in the UI is properly encoded to prevent XSS vulnerabilities. Modern frontend frameworks (React, Vue, Svelte) provide this by default for most cases, but vigilance is needed when dealing with `dangerouslySetInnerHTML` or similar.
*   **File Uploads (Supabase Storage):**
    *   Validate file types and sizes.
    *   Scan uploaded files for malware if feasible and appropriate for the risk profile.
    *   Set appropriate `Content-Type` headers and use `Content-Disposition: attachment` for downloads where direct rendering in the browser is not intended, to prevent XSS via uploaded files.
    *   Use Supabase Storage RLS policies to control access to uploaded files.

## 11. Infrastructure and Network Security

*   **Supabase Security:** Rely on Supabase's managed infrastructure security.
    *   Keep Supabase projects updated.
    *   Use strong, unique passwords for Supabase project access.
    *   Limit access to the Supabase dashboard to authorized personnel.
*   **Frontend Hosting (Vercel/Netlify):**
    *   These platforms provide DDoS mitigation and managed SSL/TLS.
    *   Configure security headers (see section below).
*   **API Security (Edge Functions):**
    *   Rate limiting for API endpoints to prevent abuse (Supabase may offer some, or implement custom logic in Edge Functions if needed).
    *   Protect against common API vulnerabilities (e.g., OWASP API Security Top 10).

## 12. Application Security Best Practices

*   **Security Headers:** Implement appropriate HTTP security headers on the frontend application:
    *   `Content-Security-Policy (CSP)`: To control resources the browser is allowed to load.
    *   `Strict-Transport-Security (HSTS)`: To enforce HTTPS.
    *   `X-Content-Type-Options: nosniff`: To prevent MIME-sniffing.
    *   `X-Frame-Options: DENY` or `SAMEORIGIN`: To prevent clickjacking.
    *   `Referrer-Policy`: To control referrer information.
    *   `Permissions-Policy`: To control browser features.
*   **Dependency Management:**
    *   Regularly scan dependencies for known vulnerabilities (`npm audit`, GitHub Dependabot).
    *   Update vulnerable dependencies promptly.
*   **Secrets Management:**
    *   Never hardcode secrets (API keys, database credentials, Supabase service keys) in client-side code or commit them to version control.
    *   Use environment variables provided by the hosting platform (Vercel, Netlify) for the frontend.
    *   Use Supabase secrets management for Edge Functions.
    *   CI/CD pipelines must use secure secret stores (e.g., GitHub Secrets).
*   **Least Privilege Principle:** Grant users and services only the minimum permissions necessary to perform their tasks.
*   **Regular Security Audits & Code Reviews:**
    *   Conduct security-focused code reviews.
    *   Consider periodic security audits or penetration testing, especially for critical applications.

## 13. Logging and Monitoring for Security

*   **Audit Trails:** Log important security-related events (e.g., logins, failed login attempts, password changes, role changes, access to sensitive data).
    *   Supabase Auth logs some of these events. Custom logging may be needed for application-specific actions.
*   **Intrusion Detection/Prevention:** While largely handled by Supabase and frontend hosting providers, monitor logs for suspicious activity.
*   **Alerting:** Set up alerts for critical security events or anomalies.

## 14. Incident Response Plan (High-Level)

*   Define a basic plan for how to respond to a security incident:
    *   Identify and contain the incident.
    *   Eradicate the cause.
    *   Recover affected systems.
    *   Post-incident analysis and lessons learned.
    *   Communication plan (internal and external, if necessary).

## 15. PWA Security

*   **Service Worker:** Ensure the service worker script itself is secure and cannot be tampered with. Serve it over HTTPS from a controlled path.
*   **Caching:** Be cautious about caching sensitive user-specific data in a way that could be accessed by other users of the same device if not properly managed.

By adhering to these security principles and practices, StayFu aims to provide a secure and trustworthy platform for its users.
