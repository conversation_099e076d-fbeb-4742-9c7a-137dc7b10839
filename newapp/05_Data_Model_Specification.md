# Data Model Specification (Supabase)

This document outlines the data model for the StayFu application. The model is designed to support all core features identified in `01_Core_Features.md`, adhering to normalization principles (DRY) and simplicity (KISS). It is designed for initial development with SQLite and seamless migration to Supabase (PostgreSQL) for MVP and beyond.

## 1. Introduction
This document outlines the data model for the StayFu application. The model is designed to support all core features identified in `01_Core_Features.md`, adhering to normalization principles (DRY) and simplicity (KISS). It is designed for initial development with SQLite and seamless migration to Supabase (PostgreSQL) for MVP and beyond.

### 1.1. General Principles
*   **Relationships:** Primarily one-to-many and many-to-many relationships will be used.
*   **Naming Conventions:** Tables will be plural (e.g., `properties`), columns will use snake_case (e.g., `created_at`).
*   **Timestamps:** Standard `created_at` and `updated_at` columns for most tables.
*   **Foreign Keys:** Enforce referential integrity.
*   **UUIDs:** Use UUIDs for primary keys where appropriate, especially for user-facing IDs or tables involved in public URLs. Supabase handles this well.

### 1.2. Database Choice and Rationale

The primary database for MVP and production will be **Supabase (PostgreSQL)**, chosen for its robustness, scalability, and rich feature set including Row Level Security (RLS) and integrated Authentication.

However, for **initial development and local environments, SQLite will be utilized.** This decision is based on:
*   **Simplicity and Speed:** SQLite offers a file-based, serverless setup, making it extremely easy and fast to get started with development and testing.
*   **Developer Experience:** Reduces local setup complexity for developers.

**Design Considerations for Phased Approach:**
*   The schema will be designed with PostgreSQL compatibility in mind from the start (e.g., data types, constraints).
*   An Object-Relational Mapper (ORM) or data access layer will be used that supports both SQLite and PostgreSQL, or can be easily adapted, to minimize code changes during the transition.
*   Data seeding and migration scripts will be developed to work with both database systems where feasible, or with clear steps for transitioning.

## 2. Core Entities

### 2.1. `users`
*   `id` (UUID, Primary Key) - Handled by Supabase Auth
*   `email` (TEXT, Unique, Not Null)
*   `role` (TEXT, Not Null) - e.g., 'super_admin', 'admin', 'property_manager', 'service_provider', 'staff'
*   `full_name` (TEXT)
*   `invited_by_user_id` (UUID, Foreign Key to `users.id`, Nullable) - For tracking Property Manager invitations
*   `organization_id` (UUID, Foreign Key to `organizations.id`, Nullable) - If implementing multi-tenancy for distinct organizations of users. For MVP, can be simplified or deferred. Assume a single "team" or implicit organization per Property Manager initially.
*   `theme_preference` (TEXT) - 'light' or 'dark'
*   `created_at` (TIMESTAMP WITH TIME ZONE)
*   `updated_at` (TIMESTAMP WITH TIME ZONE)
*   *(Other fields managed by Supabase Auth, like `encrypted_password`)*

### 2.2. `properties`
*   `id` (UUID, Primary Key)
*   `owner_user_id` (UUID, Foreign Key to `users.id`) - The Property Manager who owns/manages this property.
*   `name` (TEXT, Not Null)
*   `address` (TEXT)
*   `property_type` (TEXT) - e.g., 'Apartment', 'House'
*   `description` (TEXT)
*   `images_urls` (JSONB or TEXT[]) - Array of URLs for property images
*   `ical_feed_url` (TEXT, Nullable) - For calendar integration
*   `created_at` (TIMESTAMP WITH TIME ZONE)
*   `updated_at` (TIMESTAMP WITH TIME ZONE)

### 2.3. `collections` (Categories within Properties)
*   `id` (UUID, Primary Key)
*   `property_id` (UUID, Foreign Key to `properties.id`)
*   `name` (TEXT, Not Null)
*   `budget_amount` (DECIMAL, Nullable) - Simplified budget tracking
*   `created_at` (TIMESTAMP WITH TIME ZONE)
*   `updated_at` (TIMESTAMP WITH TIME ZONE)

### 2.4. `inventory_items`
*   `id` (UUID, Primary Key)
*   `property_id` (UUID, Foreign Key to `properties.id`)
*   `collection_id` (UUID, Foreign Key to `collections.id`, Nullable)
*   `name` (TEXT, Not Null)
*   `description` (TEXT)
*   `quantity_on_hand` (INTEGER, Not Null, Default: 0)
*   `reorder_level` (INTEGER, Nullable) - For future auto-suggestions
*   `supplier_info` (TEXT)
*   `unit_price` (DECIMAL)
*   `image_url` (TEXT, Nullable)
*   `amazon_product_link` (TEXT, Nullable)
*   `reorder_frequency_days` (INTEGER, Nullable) - For future planning
*   `last_ordered_at` (TIMESTAMP WITH TIME ZONE, Nullable)
*   `created_at` (TIMESTAMP WITH TIME ZONE)
*   `updated_at` (TIMESTAMP WITH TIME ZONE)

### 2.5. `service_providers`
*   `id` (UUID, Primary Key)
*   `invited_by_user_id` (UUID, Foreign Key to `users.id`) - The Property Manager who added them.
*   `user_id` (UUID, Foreign Key to `users.id`, Nullable) - If the provider has a login.
*   `name` (TEXT, Not Null)
*   `contact_email` (TEXT)
*   `phone_number` (TEXT)
*   `service_type` (TEXT) - e.g., 'Plumbing', 'Electrical'
*   `notes` (TEXT)
*   `created_at` (TIMESTAMP WITH TIME ZONE)
*   `updated_at` (TIMESTAMP WITH TIME ZONE)

### 2.6. `maintenance_tasks`
*   `id` (UUID, Primary Key)
*   `property_id` (UUID, Foreign Key to `properties.id`)
*   `inventory_item_id` (UUID, Foreign Key to `inventory_items.id`, Nullable) - If task relates to a specific item
*   `reported_by_user_id` (UUID, Foreign Key to `users.id`)
*   `assigned_to_service_provider_id` (UUID, Foreign Key to `service_providers.id`, Nullable)
*   `assigned_to_staff_user_id` (UUID, Foreign Key to `users.id`, Nullable) - If assigned to internal staff
*   `title` (TEXT, Not Null)
*   `description` (TEXT)
*   `priority` (TEXT) - e.g., 'High', 'Medium', 'Low'
*   `status` (TEXT) - e.g., 'New', 'In Progress', 'Completed', 'Blocked'
*   `due_date` (DATE)
*   `completed_at` (TIMESTAMP WITH TIME ZONE, Nullable)
*   `is_recurring` (BOOLEAN, Default: False)
*   `recurrence_rule` (TEXT, Nullable) - e.g., 'monthly on day 15', 'every 2 weeks on Monday' (simplified for MVP)
*   `created_at` (TIMESTAMP WITH TIME ZONE)
*   `updated_at` (TIMESTAMP WITH TIME ZONE)

### 2.7. `damage_reports`
*   `id` (UUID, Primary Key)
*   `property_id` (UUID, Foreign Key to `properties.id`)
*   `reported_by_user_id` (UUID, Foreign Key to `users.id`)
*   `assigned_to_service_provider_id` (UUID, Foreign Key to `service_providers.id`, Nullable)
*   `assigned_to_staff_user_id` (UUID, Foreign Key to `users.id`, Nullable)
*   `description` (TEXT, Not Null)
*   `image_urls` (JSONB or TEXT[]) - Array of URLs for damage photos
*   `status` (TEXT) - e.g., 'Reported', 'Assessment', 'Repair_In_Progress', 'Resolved'
*   `estimated_cost` (DECIMAL, Nullable)
*   `actual_cost` (DECIMAL, Nullable)
*   `invoice_url` (TEXT, Nullable) - Link to attached invoice PDF/image
*   `booking_reference_url` (TEXT, Nullable) - Link to booking associated with the damage
*   `resolved_at` (TIMESTAMP WITH TIME ZONE, Nullable)
*   `created_at` (TIMESTAMP WITH TIME ZONE)
*   `updated_at` (TIMESTAMP WITH TIME ZONE)

### 2.8. `damage_report_notes`
*   `id` (UUID, Primary Key)
*   `damage_report_id` (UUID, Foreign Key to `damage_reports.id`)
*   `user_id` (UUID, Foreign Key to `users.id`)
*   `note` (TEXT, Not Null)
*   `created_at` (TIMESTAMP WITH TIME ZONE)

### 2.9. `purchase_requests` (Shopping List Items)
*   `id` (UUID, Primary Key)
*   `property_id` (UUID, Foreign Key to `properties.id`)
*   `collection_id` (UUID, Foreign Key to `collections.id`, Nullable)
*   `requested_by_user_id` (UUID, Foreign Key to `users.id`)
*   `item_name` (TEXT, Not Null)
*   `quantity` (INTEGER, Not Null)
*   `estimated_price_each` (DECIMAL, Nullable)
*   `amazon_product_link` (TEXT, Nullable)
*   `image_url` (TEXT, Nullable)
*   `notes` (TEXT)
*   `status` (TEXT) - e.g., 'Requested', 'Approved', 'Ordered', 'Received', 'Cancelled'
*   `ordered_at` (TIMESTAMP WITH TIME ZONE, Nullable)
*   `received_at` (TIMESTAMP WITH TIME ZONE, Nullable)
*   `created_at` (TIMESTAMP WITH TIME ZONE)
*   `updated_at` (TIMESTAMP WITH TIME ZONE)

### 2.10. `property_user_assignments` (For Staff/Service Provider access to specific properties)
*   `id` (UUID, Primary Key)
*   `property_id` (UUID, Foreign Key to `properties.id`)
*   `user_id` (UUID, Foreign Key to `users.id`) - The Staff or Service Provider user
*   `can_view` (BOOLEAN, Default: True)
*   `can_edit` (BOOLEAN, Default: False)
*   `can_manage_tasks` (BOOLEAN, Default: False)
*   `assigned_by_user_id` (UUID, Foreign Key to `users.id`) - The Property Manager who made the assignment
*   `created_at` (TIMESTAMP WITH TIME ZONE)
*   `updated_at` (TIMESTAMP WITH TIME ZONE)
*   *(This table is crucial for RLS to limit Service Provider/Staff access)*

## 3. Relationships Summary (Illustrative)
*   `users` (1) <-> `properties` (M) (A user (PM) has many properties)
*   `properties` (1) <-> `collections` (M)
*   `properties` (1) <-> `inventory_items` (M)
*   `properties` (1) <-> `maintenance_tasks` (M)
*   `properties` (1) <-> `damage_reports` (M)
*   `users` (PM) (1) <-> `service_providers` (M) (PM invites/manages many SPs)
*   `users` (Staff/SP) (M) <-> `properties` (M) via `property_user_assignments`
*   `maintenance_tasks` (M) <-> `service_providers` (1) (optional assignment)
*   `damage_reports` (M) <-> `service_providers` (1) (optional assignment)

## 4. Data Migration Considerations (SQLite to Supabase)
*   Use ORM or query builder that supports both SQLite and PostgreSQL.
*   Schema definitions should use types compatible with PostgreSQL (e.g., TEXT for strings, INTEGER/BIGINT for numbers, DECIMAL for currency, BOOLEAN, TIMESTAMP WITH TIME ZONE).
*   JSONB for Supabase, TEXT for SQLite (store as JSON string) if direct JSON type is an issue in SQLite via ORM.
*   Develop seeding scripts that can be adapted for both.

## 5. Important Considerations

*   **RLS Policies:** These are critical for security and data isolation. Each table will need carefully crafted RLS policies.
*   **Database Functions (RPC):** Complex logic (e.g., accepting an invitation and creating related records atomically) should be implemented as PostgreSQL functions callable via Supabase RPC.
*   **Indexes:** Add indexes to frequently queried columns and foreign keys to optimize query performance.
*   **Triggers:** Use triggers for `updated_at` timestamps and potentially for other derived data or audit trails.
*   **Data Integrity:** Use `NOT NULL` constraints, foreign keys, and check constraints to maintain data integrity.
*   **Normalization vs. Denormalization:** This model leans towards normalization. Consider denormalization for specific performance-critical read queries if necessary, but start with a normalized design.
*   **Evolution:** This schema will evolve. Use migrations (Supabase CLI or other tools) to manage schema changes.
